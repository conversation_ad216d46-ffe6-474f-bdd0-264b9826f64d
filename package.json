{"name": "cms-admin", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve --port 8001", "build": "vue-cli-service build --modern", "lint": "eslint --fix --ext .js,.vue src", "i18n:report": "vue-cli-service i18n:report --src \"./src/**/*.?(js|vue)\" --locales \"./src/undefined/**/*.json\""}, "dependencies": {"@tinymce/tinymce-vue": "^3.0.1", "core-js": "^3.6.5", "echarts": "^4.9.0", "element-ui": "^2.4.5", "jquery": "^3.6.0", "sass-loader": "^12.2.0", "scss": "^0.2.4", "spark-md5": "^3.0.2", "svg-sprite-loader": "^6.0.11", "tinymce": "^5.1.0", "vue": "^2.6.11", "vue-codemirror": "^4.0.5", "vue-i18n": "^8.26.3", "vue-property-decorator": "^9.1.2", "vue-router": "^3.2.0", "vuedraggable": "^2.24.3", "vuex": "^3.4.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "^4.5.19", "@vue/eslint-config-standard": "^5.1.2", "axios": "^0.18.0", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^6.2.2", "less": "^3.0.4", "less-loader": "^5.0.0", "vue-cli-plugin-axios": "^0.0.4", "vue-cli-plugin-codemirror": "^0.0.6", "vue-cli-plugin-element": "^1.0.1", "vue-cli-plugin-i18n": "^2.3.1", "vue-template-compiler": "^2.6.11"}, "volta": {"node": "10.14.2"}}