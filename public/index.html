<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="icon" href="<%= BASE_URL %>s_favicon.ico">
    <title>鑫源汽车EPC后台管理系统</title>
    <script>
      if (getBrowserType() === "IE" || getBrowserType() === "Opera" || getBrowserType() === "Safari") {
        location.href = "update.html";
      }
      function getBrowserType() {
        if (window.ActiveXObject || "ActiveXObject" in window || window.navigator.userAgent.indexOf("MSIE") >= 0) {
          return "IE";
        } else if (navigator.userAgent.toLowerCase().match(/rv:([\d.]+)\) like gecko/)) {
          return 'IE';
        } else if (navigator.userAgent.indexOf("Edge") > -1) {
          return 'Edge';
        } else if (document.getBoxObjectFor||window.navigator.userAgent.indexOf("Firefox") >= 0) {
          return "Firefox";
        } else if (window.MessageEvent && !document.getBoxObjectFor) {
          return "Chrome";
        } else if (window.opera) {
          return "Opera";
        } else if (window.openDatabase) {
          return "Safari";
        }
      }
  </script>
  </head>
  <body>
    <noscript>
      <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app"></div>
    <!-- built files will be auto injected -->
  </body>
  <script src="./static/svg/js/jquery.min.js"></script>
  <script src="./static/svg/js/jquery.svg.js"></script>
  <script src="./static/svg/js/jquery.panzoom.js"></script>
  <script src="./static/svg/js/jquery.svghotpoint.js"></script>
</html>
