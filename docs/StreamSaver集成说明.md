# StreamSaver 集成说明

## 概述

本项目已成功集成StreamSaver库，用于支持大文件的流式下载，避免传统下载方式可能导致的内存溢出问题。

## 主要改进

### 1. StreamSaver工具类 (`src/utils/streamDownload.js`)

创建了专用的StreamSaver工具类，提供以下功能：
- **流式下载**: 支持GET和POST请求的流式下载
- **自动文件名解析**: 从响应头的`content-disposition`中提取文件名
- **JWT认证支持**: 自动从sessionStorage获取JWT token并添加到所有请求
- **认证验证**: 在下载前检查token是否存在，提供友好错误提示
- **灵活认证**: 支持指定特定token或使用默认认证
- **生产环境配置**: 自动配置mitm.html路径

#### 主要方法

```javascript
// GET请求流式下载
downloadStream(url, params, fileName, headers)

// POST请求流式下载
downloadStreamPost(url, data, fileName, headers)

// 携带认证信息的流式下载
downloadStreamWithAuth(url, params, fileName, token)
```

### 2. 请求层改进 (`src/plugins/request.js`)

新增了两个支持StreamSaver的请求方法：
- `streamDownload`: GET请求，返回fetch Response对象
- `streamDownloadPost`: POST请求，返回fetch Response对象

这些方法使用fetch API替代axios，以便与StreamSaver兼容。

### 3. API层扩展 (`src/api/material.js`)

在material.js中新增：
- `feedbackExportStream`: 流式导出反馈列表的新API方法
- 导入了新的流式下载方法

### 4. 组件层改进 (`src/views/statistics/feedback/list.vue`)

**反馈统计页面的下载功能升级：**

#### 新的导出方法
```javascript
// 主要导出方法 - 使用StreamSaver
async exportData() {
  // 使用StreamSaver进行流式下载
  const result = await streamDownloadUtil.downloadStreamPost(
    sysServerUrl + 'sys/epc/feedback/export',
    params,
    '反馈数据导出.xlsx'
  );
}

// 降级方案 - 传统下载方式
exportDataFallback() {
  // 如果StreamSaver失败，使用传统axios下载
}
```

#### 特性
- **参数构建**: 新增`buildExportParams()`方法，统一处理导出参数
- **错误处理**: 如果StreamSaver失败，自动降级到传统下载方式
- **用户体验**: 保持原有的loading状态和消息提示

## 生产环境支持

### Service Worker文件
创建了必要的Service Worker文件以支持生产环境：

- `public/streamsaver/mitm.html`: StreamSaver的中间人页面
- `public/streamsaver/sw.js`: Service Worker脚本

这些文件确保StreamSaver在生产环境中能够正常工作。

## 使用优势

### 传统下载方式的问题
- 需要将整个文件加载到内存中
- 大文件可能导致浏览器内存溢出
- 用户体验差，长时间等待

### StreamSaver的优势
- **流式处理**: 边下载边写入磁盘，不占用内存
- **大文件支持**: 理论上支持任意大小的文件下载
- **更好的用户体验**: 立即开始下载，实时进度反馈
- **浏览器兼容性**: 支持现代浏览器

## 技术细节

### JWT认证处理
- **自动认证**: 所有下载请求自动包含sessionStorage中的JWT token
- **认证验证**: 下载前检查token是否存在，避免401错误
- **错误处理**: 当认证失败时提供清晰的错误信息
- **兼容性**: 与现有axios拦截器认证机制保持一致

### 兼容性处理
- 保留原有的下载方法作为降级方案
- 在StreamSaver失败时自动切换到传统方式
- 确保老旧浏览器的向后兼容

### 参数传递
- 保持与原有axios请求相同的参数结构
- 支持查询参数和POST数据
- 自动处理JWT认证头

### 错误处理
- 完整的try-catch错误捕获
- 详细的错误日志记录
- 用户友好的错误提示
- 认证失败时的特殊处理

## 部署注意事项

1. **Service Worker文件**: 确保`public/streamsaver/`目录下的文件能够被正确访问
2. **HTTPS要求**: 生产环境需要HTTPS支持（Service Worker要求）
3. **跨域配置**: 确保下载API的CORS配置正确

## 测试建议

1. **小文件测试**: 验证基本下载功能
2. **大文件测试**: 测试流式下载的内存使用情况
3. **网络中断测试**: 验证错误处理和降级机制
4. **不同浏览器测试**: 确保兼容性

## 后续扩展

此StreamSaver集成为一个通用解决方案，可以轻松扩展到项目中的其他下载功能：

1. 复制`streamDownloadUtil`工具类的使用模式
2. 在相应的API文件中添加流式下载方法
3. 在组件中引入并使用新的下载方法

这样的设计确保了整个项目下载功能的一致性和可维护性。
