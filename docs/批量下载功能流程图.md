# 批量下载功能流程图

## 功能概述
为statistics目录下的所有页面添加批量下载功能，用户点击批量下载按钮即可直接下载统计数据，无需传递额外参数。

## 流程图

```mermaid
flowchart TD
    A[用户点击批量下载按钮] --> B[显示加载状态]
    B --> C[构建导出参数]
    C --> D{页面类型判断}
    
    D -->|feedback页面| E[收集筛选条件]
    D -->|service页面| F[收集筛选条件]
    D -->|comment页面| G[收集筛选条件]
    D -->|number页面| H[收集筛选条件]
    D -->|people页面| I[收集筛选条件]
    
    E --> J[服务店、车型、问题分类、VIN属性、日期范围]
    F --> K[区域、服务店名称、日期范围]
    G --> L[车型、日期范围]
    H --> M[品牌、车型、年款、日期范围]
    I --> N[品牌、车型、年款、日期范围]
    
    J --> O[调用对应导出API]
    K --> O
    L --> O
    M --> O
    N --> O
    
    O --> P{API调用结果}
    P -->|成功| Q[隐藏加载状态]
    P -->|失败| R[隐藏加载状态]
    
    Q --> S[处理下载响应]
    R --> T[显示错误提示]
    
    S --> U[解析文件名]
    U --> V[创建Blob对象]
    V --> W[创建下载链接]
    W --> X[触发下载]
    X --> Y[清理临时资源]
    Y --> Z[显示成功提示]
    
    T --> AA[结束]
    Z --> AA
```

## 实现步骤

### 1. 添加批量下载按钮
在每个页面的搜索表单中添加批量下载按钮：
```vue
<el-button icon="bulkDown-icon" plain @click="exportData()">批量下载</el-button>
```

### 2. 实现exportData方法
为每个页面添加exportData方法，自动收集当前筛选条件：
```javascript
exportData() {
  this.$loading.show();
  const params = new URLSearchParams();
  
  // 根据页面类型收集不同的筛选条件
  if (this.valueDate.start) {
    params.append('start', getMyDate(this.valueDate.start));
  }
  // ... 其他筛选条件
  
  // 调用对应的导出API
  pageExport(params).then(res => {
    this.$loading.hide();
    this.downloadRes(res);
    this.$message.success('导出成功');
  }).catch(err => {
    this.$loading.hide();
    this.$message.error('导出失败');
  });
}
```

### 3. 实现downloadRes方法
统一的下载响应处理方法：
```javascript
downloadRes(res) {
  let fileName = decodeURI(res.headers['content-disposition']);
  if (fileName) {
    fileName = fileName.substring(fileName.indexOf('=') + 1);
  }
  let blob = new Blob([res.data]);
  let url = window.URL.createObjectURL(blob);
  let aLink = document.createElement("a");
  aLink.style.display = "none";
  aLink.href = url;
  aLink.setAttribute("download", fileName);
  document.body.appendChild(aLink);
  aLink.click();
  document.body.removeChild(aLink);
  window.URL.revokeObjectURL(url);
}
```

### 4. 添加API接口导入
在每个页面中导入对应的导出API：
```javascript
import { pageExport } from '@/api/statistics.js'
```

## 页面状态对比

| 页面 | 批量下载按钮 | exportData方法 | downloadRes方法 | API接口 |
|------|-------------|----------------|-----------------|---------|
| feedback/list.vue | ✅ | ✅ | ✅ | feedbackExport |
| service/list.vue | ✅ | ✅ | ✅ | serviceDown |
| comment/list.vue | ✅ | ✅ | ✅ | commentExport |
| number/list.vue | ✅ | ✅ | ✅ | numberExport |
| people/list.vue | ✅ | ✅ | ✅ | peopleExport |

## 注意事项

1. **API接口依赖**：需要确保后端提供对应的导出接口
2. **文件格式**：所有导出文件都支持Excel格式
3. **参数验证**：导出前会自动收集当前页面的筛选条件
4. **错误处理**：包含完整的错误处理和用户提示
5. **资源清理**：下载完成后自动清理临时URL资源
