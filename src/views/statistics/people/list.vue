<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" :rules="rules" ref="formInline" :label-width="formLabelWidth" :model="formInline" class="demo-form-inline">
        <el-form-item label="品牌" prop="brandId">
          <el-select v-model="formInline.brandId" clearable filterable @change="getTrainYearList">
            <el-option v-for="(item,index) in brandList" :key="index" :label="item.brandName" :value="item.brandId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="车型" prop="trainId">
          <el-select v-model="formInline.trainId" clearable filterable @change="getYearList">
            <el-option v-for="(item,index) in trainList" :key="index" :label="item.trainName" :value="item.trainId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="年款" prop="trainYear">
          <el-select v-model="formInline.trainYear" clearable filterable>
            <el-option v-for="(item,index) in yearList" :key="index" :label="item.year" :value="item.year"></el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="查询时间" prop="trainYear">
          <el-date-picker
            v-model="valueDate"
            type="monthrange"
            style="width: 240px;"
            value-format='yyyy-MM'
            @change="selectDate()"
            range-separator="至"
            start-placeholder="开始月份"
            end-placeholder="结束月份">
          </el-date-picker>
        </el-form-item> -->
        <el-form-item label="日期范围">
          <el-date-picker :clearable="false" :editable="false" prop="begintime" align="center" value-format="yyyy-MM" type="month" placeholder="开始日期" v-model="valueDate.start" :picker-options="pickerBeginTime"></el-date-picker>
            <span class="line">至</span>
          <el-date-picker :clearable="false" :editable="false" prop="endtime" align="center" value-format="yyyy-MM" type="month" placeholder="结束日期" v-model="valueDate.end" :picker-options="pickerEndTime"></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit" icon="el-icon-search">搜索</el-button>
          <el-button plain @click="reset('formInline')">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-row class="statisticalArea">
      <div id="peopleline"  style="width: 100%; height: 500px"></div>
    </el-row>
  </div>
</template>
<script>
import {getMyDate, statisticalHeight} from "@/assets/js/common.js"
import {getCarTrainModelList, people} from '@/api/statistics.js'

export default {
    name: 'statistics_people_list',
    data () {
      return {
        // 时间段
        valueDate: {
          start: '',
          end: '',
        },
        lineX: [], // 横坐标: 数量
        lineY: [], // 纵坐标: 总成名称
        formInline: {
          brandId: '',  // 品牌
          trainId: '',  // 车型
          trainYear: '',  // 年款
        },
        brandList: [],  // 品牌
        trainList: [],  // 车型
        yearList: [],   // 年款
        formLabelWidth: '70px',
        pickerBeginTime: {
          disabledDate: (time) => {
            return this.valueDate.end != null ? time.getTime() > new Date(this.valueDate.end) : false //只能选结束日期之前的日期
            //返回---结束时间是否有值？   可选时间小于结束时间   ：  任意时间都可选
          }
        },
        pickerEndTime: {
          disabledDate: (time) => {
            return this.valueDate.start != null ? time.getTime() < new Date(this.valueDate.start) : false //只能选开始时间之后的日期
            //返回---开始时间是否有值？   可选时间大于开始时间   ：  任意时间都可选
          }
        },
      }
    },
    computed: {
      rules() {
        return {
          /*   brandId: [
               {
                 required: true,
                 message: "请选择品牌",
                 trigger: ["blur", "change"],
               },
             ],*/
        };
      },
    },
    methods: {
      // 获取品牌
      getTrainList(){
        getCarTrainModelList().then(res => {
          if(res.data.code == 100){
            this.brandList = res.data.data
            // this.formInline.brandId = this.brandList[0].brandId;
            this.getTrainYearList();
            this.initialData();
          }
        });
      },
      // 获取品牌下的车型
      getTrainYearList(){
        this.trainList = []
        this.yearList = []
        let _this = this
        this.formInline.trainId = ''
        this.formInline.trainYear = ''
        _this.brandList.forEach(item => {
          if (item.brandId == this.formInline.brandId) {
            _this.trainList = item.children
          }
        })
      },
      // 获取车型下的年款
      getYearList(){
        this.yearList = []
        this.formInline.trainYear = ''
        let _this = this
        _this.trainList.forEach(item => {
          if (item.trainId == this.formInline.trainId) {
            _this.yearList = item.children
          }
        })
      },
      // 初始化
      initialData() {
        this.intervalTime();
        this.onSubmit();
      },
      // 获取查询的
      dataList(){
        this.$loading.show();
        var params = new URLSearchParams()
        params.append('brandId', this.formInline.brandId)
        params.append('trainId', this.formInline.trainId)
        params.append('trainYear', this.formInline.trainYear)
        params.append('start', getMyDate(this.valueDate.start))
        params.append('end', getMyDate(this.valueDate.end))
        people(params).then(res => {
          this.lineX = []
          this.lineY = []
          if (res.data.code == 100) {
            let list = res.data.data
            if (list && list.length > 0) {
              for (let i = 0 ; i < list.length; i++) {
                this.lineX.push(list[i].month)
                this.lineY.push(list[i].count)
              }
            }
            this.lineBar(this.lineX, this.lineY)
          }
          this.$loading.hide();
        })
      },
      // 搜索
      keyDown(e) {
        if (e.keyCode === 13) {
          this.onSubmit()
        }
      },
      onSubmit(){
        /*  if(this.formInline.brandId == "") {
            handleAlert('warning',"请选择品牌");
            return;
          }*/
        this.dataList();
      },
      // 重置
      reset(){
        this.formInline.brandId = "";
        this.formInline.trainId = "";
        this.formInline.trainYear = "";
        this.trainList = []
        this.yearList = []
        this.intervalTime()
        $("#peopleline").removeAttr("_echarts_instance_");
        $("#peopleline").empty();
        this.dataList();
      },
      lineBar(x, y){
        let myChart = this.$echarts.init(document.getElementById('peopleline'));
        let option = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          legend: {
            type: "scroll",
            right: '5%',
            top: '10',
            data: ['访问人数'],
            itemGap: 25,
            itemWidth: 16,
            itemHeight: 16,
            textStyle: {
              fontSize: '13',
              color: '#666666',
            },
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: [
            {
                type: 'category',
                data: x,
                axisTick: {
                    alignWithLabel: true
                }
            }
          ],
          yAxis: [
            {
              type: 'value'
            }
          ],
          series: [
            {
              name: '访问人数',
              type: 'bar',
              stack: 'total',
              label: {
                show: true,
              },
              itemStyle: {
                color: '#4A90E2'
              },
              barWidth: '60%',
              data: y
            }
          ]
        };
        window.addEventListener("resize", function () {
          myChart.resize();
        });
        myChart.setOption(option);
      },
      // 获取当前时间的近一年区间
      intervalTime(){
        // 获取当前日期
        let currentDate = new Date();
        // 获取上个月的年份和月份
        let lastMonthYear = currentDate.getFullYear();
        var lastMonth = currentDate.getMonth() - 1;
        if (lastMonth < 0) {
            lastMonthYear--;
            lastMonth = 11;
        }
        // 上个月的第一天
        let e = new Date(lastMonthYear, lastMonth, 1);
        // 上一年
        let s = new Date(--lastMonthYear, lastMonth, 1);
        this.valueDate = {
            start: s,
            end: e,
        }
      }
    },
    mounted () {
      this.getTrainList();
      statisticalHeight();
      window.addEventListener('keydown', this.keyDown)
    },
    destroyed() {
      window.removeEventListener('keydown', this.keyDown, false)
    },
  }
</script>
<style>
  .introduce{
    border: 1px solid #cfd5de;
    box-sizing: border-box;
    margin: 10px 0;
    padding:15px 3%;
    border-radius: 5px;
  }


</style>
