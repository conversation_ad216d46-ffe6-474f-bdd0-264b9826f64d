<template>
<div class="layoutContainer">
    <div class="secondFloat">
        <el-form :inline="true" :label-width="formLabelWidth" class="demo-form-inline">

            <el-form-item label="车型" prop="trainId">
                <el-select v-model="trainId" placeholder="请选择车型" clearable filterable>
                    <el-option-group v-for="group in trainList" :key="group.id" :label="group.nameCh">
                        <el-option v-for="item in group.children" :key="item.id" :label="item.nameCh" :value="item.id"></el-option>
                    </el-option-group>
                </el-select>
            </el-form-item>

            <!-- <el-form-item label="查询时间" prop="trainYear">
                <el-date-picker
                v-model="valueDate"
                type="monthrange"
                style="width: 240px;"
                value-format='yyyy-MM'
                @change="selectDate()"
                range-separator="至"
                start-placeholder="开始月份"
                end-placeholder="结束月份">
                </el-date-picker>
            </el-form-item> -->
            <el-form-item label="日期范围">
                <el-date-picker :clearable="false" :editable="false" prop="begintime" align="center" value-format="yyyy-MM" type="month" placeholder="开始日期" v-model="valueDate.start" :picker-options="pickerBeginTime"></el-date-picker>
                <span class="line">至</span>
                <el-date-picker :clearable="false" :editable="false" prop="endtime" align="center" value-format="yyyy-MM" type="month" placeholder="结束日期" v-model="valueDate.end" :picker-options="pickerEndTime"></el-date-picker>
            </el-form-item>

            <el-form-item>
                <el-button  type="primary" @click="onSubmit" icon="el-icon-search">搜索</el-button>
                <el-button  plain @click="reset()">重置</el-button>
            </el-form-item>
        </el-form>
    </div>
    <el-row class="statisticalArea">
        <el-col :span="10">
            <div id="commentline"  style="width: 100%; height: 500px"></div>
        </el-col>
        <el-col :span="1"></el-col>
        <el-col :span="13">
            <div style="width: 80%; height: 500px; " class="statisticsContent">
                <table width="50%" border="1">
                    <tr>
                        <th width="70%">问题分类</th>
                        <th width="25%">占比</th>
                    </tr>
                    <tr v-for="(item, index) in resultList" :key="item.name">
                        <td width="70%" style="height: 10px;">
                            <div style="height: 10px; margin: 2px;">
                                <div :style="{'background-color':colorList[index]}" class="color-card"></div>
                                <div style="float: left;margin: 2px;"> {{ item.name }}</div>
                            </div>
                        </td>
                        <td width="25%"> <div style="margin: 2px;">{{(sum != 0 ? ((item.value/sum)*100).toFixed(2)  : 0.00) }}%</div> </td>
                    </tr>
                </table>
            </div>
            <!-- <div style="width: 50%; height: 500px; " >

            </div> -->
        </el-col>
    </el-row>
</div>



</template>

<script>
  import { statisticalHeight, getMyDate } from "@/assets/js/common.js"
  import { commentProportion, getCarTrainList } from '@/api/statistics.js'
  export default {
    name: 'statistics_comment_list',
    data () {
      return {
        // 时间段
        valueDate: {
            start: '',
            end: '',
        },

        trainId: '',
        trainList: [],
        resultList: [],
        sum: 0,
        // 定义颜色
        colorList: ['#1890FF', '#43D3E5', '#92C520', '#F8B01C', '#EE5C5B',
          '#00FFFF', '#FF00FF', '#99CC00', '#FF6600', '#996600',
          '#003333', '#666600', '#333300', '#FF99FF'
        ],
        formLabelWidth: '70px',

        pickerBeginTime: {
            disabledDate: (time) => {
            return this.valueDate.end != null ? time.getTime() > new Date(this.valueDate.end) : false //只能选结束日期之前的日期
            //返回---结束时间是否有值？   可选时间小于结束时间   ：  任意时间都可选
            }
        },
        pickerEndTime: {
            disabledDate: (time) => {
            return this.valueDate.start != null ? time.getTime() < new Date(this.valueDate.start) : false //只能选开始时间之后的日期
            //返回---开始时间是否有值？   可选时间大于开始时间   ：  任意时间都可选
            }
        },
      }
    },

    methods: {
        // selectDate(){
        //     this.start = this.valueDate.start;
        //     this.end = this.valueDate.end;
        // },

        // 搜索
        keyDown(e) {
          if (e.keyCode === 13) {
            this.onSubmit()
          }
        },
        onSubmit(){
          this.dataList()
        },

        // 重置
        reset(){
            this.intervalTime()
            this.start = ''
            this.end = ''
            this.trainId = ''

            this.dataList()
        },

        // 查询 品牌 - 车系
        getTrainList () {

            getCarTrainList().then(res => {
                this.trainList = res.data.data
            })
        },

        // 获取数据
        dataList(){
            this.$loading.show();
            var params = new URLSearchParams()
            params.append('trainId', this.trainId)
            params.append('start', getMyDate(this.valueDate.start))
            params.append('end', getMyDate(this.valueDate.end))
            commentProportion(params).then(res => {
              if(res.data.code == 100){
                let list = res.data.data
                this.resultList = res.data.data
                this.sum = 0;
                if (list && list.length > 0) {
                   this.linePie(list)
                   for (let i = 0; i < list.length; i++) {
                    this.sum += list[i].value;
                   }
                }
              }
              this.$loading.hide();
            })
        },


        // 饼图
        linePie(data){
            let myChart = this.$echarts.init(document.getElementById('commentline'));
            let color = this.colorList;
            let option = {
                color,
                tooltip: {
                    trigger: 'item'
                },
                series: [
                    {
                    type: 'pie',
                    radius: '55%',
                    label: {
                        show: true,
                        position: 'outside',
                        formatter: '{d}%',
                        rich: {
                            a: {
                                padding: [0,0,-5,0]
                            }
                        }
                    },
                    labelLine: {
                        // show: false
                                normal: {
                            length: 10,
                            length2: 10,
                            lineStyle: {
                                width: 1
                            }
                        }
                    },
                    center: ['50%', '50%'],
                    data: data,

                    }
                ]
            };
            window.addEventListener("resize", function () {
              myChart.resize();
            });
            myChart.setOption(option);
        },




        // 获取当前时间的近一年区间
        intervalTime(){
            // 获取当前日期
            let currentDate = new Date();
            // 获取上个月的年份和月份
            let lastMonthYear = currentDate.getFullYear();
            var lastMonth = currentDate.getMonth() - 1;
            if (lastMonth < 0) {
                lastMonthYear--;
                lastMonth = 11;
            }
            // 上个月的第一天
            let e = new Date(lastMonthYear, lastMonth, 1);
            // 上一年
            let s = new Date(--lastMonthYear, lastMonth, 1);
            this.valueDate = {
                start: s,
                end: e,
            }
        }

    },
    mounted () {
      this.reset();
      this.getTrainList();
      statisticalHeight();
      window.addEventListener('keydown', this.keyDown)
    },
    destroyed() {
      window.removeEventListener('keydown', this.keyDown, false)
    },
  }
  </script>


<style>
  /* .introduce{
    border: 1px solid #cfd5de;
    box-sizing: border-box;
    margin: 10px 0;
    padding:15px 3%;
    border-radius: 5px;
  } */

.statisticsContent{
    display: flex;
    justify-content: center;
    align-items: center;
}
.color-card {
    width: 15px;
    height: 15px;
    float:left;
    margin: 5px 2px;
  }
</style>
