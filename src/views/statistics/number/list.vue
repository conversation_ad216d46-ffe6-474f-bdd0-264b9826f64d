<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" ref="formInline" :label-width="formLabelWidth" :model="formInline" class="demo-form-inline" :rules="rules">
        <el-form-item label="品牌" prop="brandId">
          <el-select v-model="formInline.brandId" clearable filterable @change="getTrainYearList">
            <el-option v-for="(item,index) in brandList" :key="index" :label="item.brandName" :value="item.brandId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="车型" prop="trainId">
          <el-select v-model="formInline.trainId" clearable filterable @change="getYearList">
            <el-option v-for="(item,index) in trainList" :key="index" :label="item.trainName" :value="item.trainId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="年款" prop="trainYear">
          <el-select v-model="formInline.trainYear" clearable filterable>
            <el-option v-for="(item,index) in yearList" :key="index" :label="item.year" :value="item.year"></el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="查询时间" prop="trainYear">
          <el-date-picker style="width: 260px;" v-model="valueDate" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
          :clearable="false" value-format='yyyy-MM-dd' @change="selectDate()" :picker-options="pickerOptions"></el-date-picker>
        </el-form-item> -->
        <el-form-item label="日期范围">
          <el-date-picker :clearable="false" :editable="false" prop="begintime" align="center" value-format="yyyy-MM-dd" type="date" placeholder="开始日期" v-model="valueDate.start" :picker-options="startTime"></el-date-picker>
          <span class="line">至</span>
          <el-date-picker :clearable="false" :editable="false" prop="endtime" align="center" value-format="yyyy-MM-dd" type="date" placeholder="结束日期" v-model="valueDate.end" :picker-options="endTime"></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit" icon="el-icon-search">搜索</el-button>
          <el-button plain @click="reset('formInline')">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <el-row class="statisticalArea">
      <div id="numberline"  style="width: 100%; height: 500px"></div>
    </el-row>
  </div>
</template>

<script>
  import { statisticalHeight, getMyDate, handleAlert } from "@/assets/js/common.js"
  import { visits, getCarTrainModelList } from '@/api/statistics.js'
  export default {
    name: 'statistics_number_list',
    data () {
      return {
        // 时间段
        valueDate: {
          start: '',
          end: '',
        },
        lineX: [], // 横坐标: 数量
        lineY: [], // 纵坐标: 总成名称
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() > Date.now() - 3600 * 1000 * 24 * 1
          }
        },
        formInline: {
          brandId: '',  // 品牌
          trainId: '',  // 车型
          trainYear: '',  // 年款
        },
        brandList: [],  // 品牌
        trainList: [],  // 车型
        yearList: [],   // 年款
        formLabelWidth: '70px',
        startTime: {
          disabledDate: (time) => {
            return this.valueDate.end != null ? time.getTime() > new Date(this.valueDate.end) : false //只能选结束日期之前的日期
            //返回---结束时间是否有值？   可选时间小于结束时间   ：  任意时间都可选
          }
        },
        endTime: {
          disabledDate: (time) => {
            return this.valueDate.start != null ? time.getTime() < new Date(this.valueDate.start) : false //只能选开始时间之后的日期
            //返回---开始时间是否有值？   可选时间大于开始时间   ：  任意时间都可选
          }
        },
      }
    },
    computed: {
      rules() {
        return {
          brandId: [
            {
              required: true,
              message: "请选择品牌",
              trigger: ["blur", "change"],
            },
          ],
        };
      },
    },
    methods: {
      // 获取品牌
      getTrainList(){
        getCarTrainModelList().then(res => {
          if(res.data.code == 100){
            this.brandList = res.data.data;
            this.formInline.brandId = this.brandList[0].brandId;
            this.getTrainYearList();
            this.initialData();
          }
        })
      },
      // 获取品牌下的车型
      getTrainYearList(){
        let _this = this
        _this.trainList = []
        _this.yearList = []
        _this.formInline.trainId = ''
        _this.formInline.trainYear = ''
        _this.brandList.forEach(item => {
          if (item.brandId == _this.formInline.brandId) {
            _this.trainList = item.children
          }
        })
      },
      // 获取车型下的年款
      getYearList(){
        let _this = this
        _this.yearList = []
        _this.formInline.trainYear = ''
        _this.trainList.forEach(item => {
          if (item.trainId == _this.formInline.trainId) {
            _this.yearList = item.children
          }
        })
      },
      // 默认时间
      defaultTime() {
        const end = new Date();
        const start = new Date();
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
        end.setTime(end.getTime() - 3600 * 1000 * 24 * 1);
        this.valueDate.start = start
        this.valueDate.end = end
      },
      // 初始化
      initialData() {
        this.defaultTime();
        this.onSubmit();
      },
      // 获取查询的
      dataList(){
        this.$loading.show();
        var params = new URLSearchParams()
        params.append('brandId', this.formInline.brandId)
        params.append('trainId', this.formInline.trainId)
        params.append('trainYear', this.formInline.trainYear)
        params.append('start', getMyDate(this.valueDate.start))
        params.append('end', getMyDate(this.valueDate.end))
        visits(params).then(res => {
          this.lineX = []
          this.lineY = []
          if(res.data.code == 100) {
            let list = res.data.data
            if (list && list.length > 0) {
              for (let i = list.length -1 ; i >=0; i--) {
                this.lineX.push(list[i].count)
                this.lineY.push(list[i].name)
              }
            }
            this.lineBar(this.lineX, this.lineY)
          }
          this.$loading.hide();
        })
      },
      // 搜索
      keyDown(e) {
        if (e.keyCode === 13) {
          this.onSubmit()
        }
      },
      onSubmit(){
        if(this.formInline.brandId == "") {
          handleAlert('warning',"请选择品牌");
          return;
        }
        this.dataList();
      },
      // 重置
      reset(){
        this.formInline.brandId = "";
        this.formInline.trainId = "";
        this.formInline.trainYear = "";
        this.trainList = [];
        this.yearList = [];
        $("#numberline").removeAttr("_echarts_instance_");
        $("#numberline").empty();
      },
      lineBar(x, y){
        let myChart = this.$echarts.init(document.getElementById('numberline'));
        let option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {
                    type: "scroll",
                    right: '5%',
                    top: '10',
                    data: ['访问次数'],
                    itemGap: 25,
                    itemWidth: 16,
                    itemHeight: 16,
                    textStyle: {
                        fontSize: '13',
                        color: '#666666',
                    },
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'value'
                },
                yAxis: {
                    type: 'category',
                    data: y
                },
                series: [
                    {
                        name: '访问次数',
                        type: 'bar',
                        stack: 'total',
                        label: {
                            show: true,
                            position: 'right',
                        },
                        emphasis: {
                            focus: 'series'
                        },
                        itemStyle: {
                            color: '#4A90E2'
                        },
                        data: x
                    },
                ]
        };
        window.addEventListener("resize", function () {
          myChart.resize();
        });
        myChart.setOption(option);
      },
      dateFormat (date) {
        const Y = date.getFullYear() + '-'
        const M = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) + '-' : date.getMonth() + 1 + '-'
        const D = date.getDate() < 10 ? '0' + date.getDate() + '' : date.getDate() + ''
        return Y + M + D
      },
    },
    mounted () {
      this.getTrainList();
      statisticalHeight();
      window.addEventListener('keydown', this.keyDown)
    },
    destroyed() {
      window.removeEventListener('keydown', this.keyDown, false)
    },

  }
  </script>
