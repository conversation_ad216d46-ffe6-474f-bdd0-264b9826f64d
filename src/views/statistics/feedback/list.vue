<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" :label-width="formLabelWidth" class="demo-form-inline">
        <el-form-item label="服务店" prop="stationName">
          <el-input v-model.trim="searchForm.stationName" placeholder="请输入服务店名称"></el-input>
        </el-form-item>
        <el-form-item label="车型" prop="trainId">
          <el-select v-model="searchForm.trainId" clearable filterable placeholder="请选择车型">
            <el-option-group v-for="group in trainList" :key="group.id" :label="group.nameCh">
              <el-option v-for="item in group.children" :key="item.id" :label="item.nameCh" :value="item.id"></el-option>
            </el-option-group>
          </el-select>
        </el-form-item>
        <el-form-item label="问题分类" prop="type">
          <el-select v-model="searchForm.type" clearable filterable placeholder="请选择问题类型">
            <el-option-group v-for="group in problemTypeList" :key="group.code" :label="group.name">
              <el-option v-for="item in group.children" :key="item.code" :label="item.name" :value="item.code"></el-option>
            </el-option-group>
          </el-select>
        </el-form-item>
        <el-form-item label="VIN属性" prop="vinStatisticStatus">
          <el-select v-model="searchForm.vinStatisticStatus" clearable placeholder="请选择">
            <el-option v-for="item in vinStatusOptions" :key="item.code" :label="item.name" :value="item.code"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="日期范围">
          <el-date-picker v-model="searchForm.start" :clearable="false" :editable="false" :picker-options="startTime" align="center" placeholder="开始日期" prop="begintime" type="date" value-format="yyyy-MM-dd"></el-date-picker>
          <span class="line">至</span>
          <el-date-picker v-model="searchForm.end" :clearable="false" :editable="false" :picker-options="endTime" align="center" placeholder="结束日期" prop="endtime" type="date" value-format="yyyy-MM-dd"></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button icon="el-icon-search" type="primary" @click="onSubmit">搜索</el-button>
          <el-button plain @click="onReset">重置</el-button>
          <el-button icon="bulkDown-icon" plain @click="exportData()">批量下载</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-row :gutter="8" class="statisticalArea top-row" style="overflow:auto;">
      <el-col :span="12">
        <div id="chart-station" class="chart-container"></div>
      </el-col>
      <el-col :span="12">
        <div id="chart-trainId" class="chart-container"></div>
      </el-col>
    </el-row>

    <el-row :gutter="8" class="statisticalArea bottom-row" style="margin-top: 4px; overflow:auto;">
      <el-col :span="8">
        <div id="chart-type" class="chart-container"></div>
      </el-col>
      <el-col :span="8">
        <div id="chart-vinStatus" class="chart-container"></div>
      </el-col>
      <el-col :span="8">
        <div id="chart-day" class="chart-container"></div>
      </el-col>
    </el-row>
  </div>

</template>

<script>
import {getCarTrainList, getFeedbackList, getStatisticsUnion} from '@/api/statistics.js'
import {getMyDate, handleAlert} from "@/assets/js/common.js"
import {feedbackExport, feedbackVinPropType} from "@/api/material";

export default {
  name: 'statistics_feedback_list',
  data() {
    return {
      formLabelWidth: '80px',

      /* 共享筛选条件 */
      searchForm: {
        stationName: '',
        trainId: '',
        type: '',
        vinStatisticStatus: '',
        start: '',
        end: '',
      },

      /* 下拉数据 */
      trainList: [],
      problemTypeList: [],
      vinStatusOptions: [],

      /* 日期联动校验 */
      startTime: {
        disabledDate: (time) => {
          return this.searchForm.end != null ? time.getTime() > new Date(this.searchForm.end) : false
        }
      },
      endTime: {
        disabledDate: (time) => {
          return this.searchForm.start != null ? time.getTime() < new Date(this.searchForm.start) : false
        }
      },

      /* 图表分页与实例管理：用于减少初始展示数量并支持横向滚动加载 */
      chartPage: {
        station: {current: 1, size: 20, total: 0, items: [], loading: false, finished: false},
        trainId: {current: 1, size: 20, total: 0, items: [], loading: false, finished: false},
        type: {current: 1, size: 50, total: 0, items: [], loading: false, finished: false},
        day: {current: 1, size: 50, total: 0, items: [], loading: false, finished: false},
      },
      chartInstances: {
        station: null,
        trainId: null,
        type: null,
        day: null,
      },
    }
  },
  methods: {
    /*
     * 将分组名称格式化为仅保留日期部分
     * 输入可能为 'YYYY-MM-DD HH:mm:ss'，输出为 'YYYY-MM-DD'
     */
    formatGroupName(name) {
      const str = String(name == null ? '' : name)
      const m = str.match(/^(\d{4}-\d{2}-\d{2})/)
      if (m && m[1]) {
        return m[1]
      }
      if (str.indexOf(' ') > -1) {
        return str.split(' ')[0]
      }
      return str
    },
    /* 初始化下拉数据 */
    getTrainList() {
      getCarTrainList().then(res => {
        this.trainList = res.data.data || []
      })
    },
    getFeedbackTypeList() {
      getFeedbackList().then(res => {
        this.problemTypeList = res.data.data || []
      })
    },
    getVinStatusList() {
      feedbackVinPropType().then(res => {
        this.vinStatusOptions = res.data.data || []
      });
    },

    /* 搜索与重置 */
    onSubmit() {
      this.fetchAllCharts()
    },
    onReset() {
      this.searchForm.stationName = ''
      this.searchForm.trainId = ''
      this.searchForm.type = ''
      this.searchForm.vinStatisticStatus = ''
      const now = new Date();
      const start = new Date(now.getFullYear(), now.getMonth(), 1);
      const end = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      this.searchForm.start = start
      this.searchForm.end = end
      this.fetchAllCharts()
    },
    keyDown(e) {
      if (e.keyCode === 13) {
        this.onSubmit()
      }
    },

    /* 汇总拉取 5 张图 */
    fetchAllCharts() {
      this.$loading.show();
      this.resetChartPages();
      Promise.all([
        this.fetchChart('station'),
        this.fetchChart('trainId'),
        this.fetchChart('type'),
        this.fetchChart('vinStatisticStatus'),
        this.fetchChart('day'),
      ]).finally(() => {
        this.$loading.hide();
      })
    },

    /* 重置分页状态 */
    resetChartPages() {
      this.chartPage.station = {current: 1, size: 20, total: 0, items: [], loading: false, finished: false}
      this.chartPage.trainId = {current: 1, size: 20, total: 0, items: [], loading: false, finished: false}
      this.chartPage.type = {current: 1, size: 50, total: 0, items: [], loading: false, finished: false}
      this.chartPage.day = {current: 1, size: 50, total: 0, items: [], loading: false, finished: false}
    },

    /* 调用分页接口，按 groupBy 获取数据 */
    fetchChart(groupBy) {
      const pageCfg = this.chartPage[groupBy]
      const params = {
        groupBy: groupBy,
        current: pageCfg && pageCfg.current ? pageCfg.current : 1,
        size: pageCfg && pageCfg.size ? pageCfg.size : 50,
      };
      if (this.searchForm.start) {
        params.start = getMyDate(this.searchForm.start);
      }
      if (this.searchForm.end) {
        params.end = getMyDate(this.searchForm.end);
      }
      if (this.searchForm.stationName) {
        params.stationName = this.searchForm.stationName;
      }
      if (this.searchForm.trainId) {
        params.trainId = this.searchForm.trainId;
      }
      if (this.searchForm.type) {
        params.type = this.searchForm.type;
      }
      if (this.searchForm.vinStatisticStatus) {
        params.vinStatisticStatus = this.searchForm.vinStatisticStatus;
      }

      // 饼图不参与分页
      if (!pageCfg) {
        return getStatisticsUnion(params).then(res => {
          const data = (res && res.data && res.data.data && (res.data.data.records || res.data.data)) || [];
          this.renderChartByGroup(groupBy, data);
        });
      }

      if (pageCfg.loading || pageCfg.finished) {
        return Promise.resolve();
      }
      pageCfg.loading = true;
      return getStatisticsUnion(params).then(res => {
        const raw = (res && res.data && res.data.data) || {};
        const list = (raw && (raw.records || raw)) || [];
        const total = raw && typeof raw.total === 'number' ? raw.total : undefined;

        // 追加数据
        if (Array.isArray(list) && list.length > 0) {
          pageCfg.items = pageCfg.items.concat(list);
        }

        // 完结条件：有 total 则比较条数；否则以返回条数<size 判定
        if (typeof total === 'number') {
          pageCfg.total = total;
          if (pageCfg.items.length >= total) {
            pageCfg.finished = true;
          }
        } else {
          if (!Array.isArray(list) || list.length < params.size) {
            pageCfg.finished = true;
          }
        }

        // 渲染累计数据
        this.renderChartByGroup(groupBy, pageCfg.items);
      }).finally(() => {
        pageCfg.loading = false;
      });
    },

    /* 图表渲染分发 */
    renderChartByGroup(groupBy, list) {
      /*
       * 统一适配后端分组返回格式：{ groupName, count }
       * - 饼图：使用 groupName 作为 name，count 作为 value
       * - 柱状图：X 轴使用 groupName，Y 轴使用 count
       */
      if (groupBy === 'vinStatisticStatus') {
        const seriesData = (list || []).map(item => {
          return {
            name: item.groupName,
            value: Number(item.count || 0)
          }
        })
        this.pieChart('chart-vinStatus', seriesData, 'VIN属性')
        return
      }

      let x = []
      let y = []

      switch (groupBy) {
        case 'station':
          list.forEach(item => {
            const name = item.groupName
            x.push(name)
            y.push(Number(item.count || 0))
          })
          this.barChart('chart-station', x, y, '反馈数量', '服务店', 'station')
          break
        case 'trainId':
          list.forEach(item => {
            const name = item.groupName === null ? '' : item.groupName
            x.push(name)
            y.push(Number(item.count || 0))
          })
          this.barChart('chart-trainId', x, y, '反馈数量', '车型', 'trainId')
          break
        case 'type':
          list.forEach(item => {
            const name = item.groupName
            x.push(name)
            y.push(Number(item.count || 0))
          })
          this.barChart('chart-type', x, y, '反馈数量', '问题分类', 'type')
          break
        case 'day':
          list.forEach(item => {
            const name = this.formatGroupName(item.groupName)
            x.push(name)
            y.push(Number(item.count || 0))
          })
          this.barChart('chart-day', x, y, '反馈数量', '日期统计', 'day')
          break
      }
    },

    /* 通用柱状图 */
    barChart(id, x, y, seriesName, titleText, groupBy) {
      const dom = document.getElementById(id)
      let myChart = (groupBy && this.chartInstances[groupBy]) ? this.chartInstances[groupBy] : this.$echarts.init(dom)
      const isUpdate = !!(groupBy && this.chartInstances[groupBy])
      let initialWindow = x && x.length > 0 ? x.length - 1 : 0
      if (!isUpdate && (groupBy === 'station' || groupBy === 'trainId')) {
        initialWindow = Math.min(19, Math.max(0, x.length - 1))
      }
      let option = {
        /* 图表标题字号调小 */
        title: {text: titleText || '', left: 'left', top: 4, textStyle: {fontSize: 12}},
        backgroundColor: '#fff',
        tooltip: {trigger: 'axis', axisPointer: {type: 'shadow'}},
        legend: {top: 4, right: 8, data: [seriesName], textStyle: {fontSize: 10}},
        grid: {left: '8%', right: '8%', top: 40, bottom: '16%', containLabel: true},
        xAxis: [{
          type: 'category',
          data: x,
          axisLabel: {rotate: 45, fontSize: 10, interval: 0},
          axisTick: {length: 3}
        }],
        yAxis: [{type: 'value', axisLabel: {fontSize: 10}}],
        /* 横向滚动条与滚动缩放 */
        ...(isUpdate ? {} : {
          dataZoom: [
            {type: 'slider', xAxisIndex: 0, bottom: 4, height: 14, startValue: 0, endValue: initialWindow},
            {type: 'inside', xAxisIndex: 0}
          ],
        }),
        series: [{
          name: seriesName,
          type: 'bar',
          barWidth: 16,
          label: {normal: {show: true, position: 'top', fontSize: 10}},
          itemStyle: {color: '#4A90E2'},
          data: y
        }]
      }
      window.addEventListener("resize", function () {
        myChart.resize();
      });
      myChart.setOption(option);

      // 保存实例并绑定滚动加载
      if (groupBy) {
        this.chartInstances[groupBy] = myChart
        const self = this
        myChart.off('dataZoom')
        myChart.on('dataZoom', function () {
          const pageCfg = self.chartPage[groupBy]
          if (!pageCfg || pageCfg.loading || pageCfg.finished) return
          const opt = myChart.getOption()
          const dz = (opt && opt.dataZoom && opt.dataZoom[0]) || {}
          let endIdx = 0
          if (typeof dz.endValue === 'number') {
            endIdx = dz.endValue
          } else if (typeof dz.end === 'number') {
            const len = (x && x.length) ? x.length : 0
            endIdx = Math.round(dz.end / 100 * Math.max(0, len - 1))
          }
          const threshold = (x && x.length) ? x.length - 3 : 0
          if (endIdx >= threshold) {
            self.loadMore(groupBy)
          }
        })
      }
    },

    /* 滚动触底加载下一页 */
    loadMore(groupBy) {
      const pageCfg = this.chartPage[groupBy]
      if (!pageCfg || pageCfg.loading || pageCfg.finished) return
      pageCfg.current += 1
      this.fetchChart(groupBy)
    },

    /* VIN属性饼图 */
    pieChart(id, seriesData, titleText) {
      let myChart = this.$echarts.init(document.getElementById(id));
      let option = {
        /* 图表标题字号调小 */
        title: {text: titleText || '', left: 'left', top: 4, textStyle: {fontSize: 12}},
        backgroundColor: '#fff',
        tooltip: {trigger: 'item'},
        legend: {top: 4, right: 8, textStyle: {fontSize: 10}},
        series: [{
          type: 'pie',
          radius: ['35%', '65%'],
          avoidLabelOverlap: true,
          label: {show: true, formatter: '{b}: {c}', fontSize: 10},
          data: seriesData
        }]
      }
      window.addEventListener("resize", function () {
        myChart.resize();
      });
      myChart.setOption(option);
    },
    // 导出数据
    exportData() {
      this.$loading.show();

      const formData = new FormData();

      // 搜索条件参数映射
      if (this.searchForm.start) {
        formData.append('startTime', getMyDate(this.searchForm.start));
      }
      if (this.searchForm.end) {
        formData.append('endTime', getMyDate(this.searchForm.end));
      }
      if (this.searchForm.stationName) {
        formData.append('stationName', this.searchForm.stationName);
      }
      if (this.searchForm.trainId) {
        formData.append('trainId', this.searchForm.trainId);
      }
      if (this.searchForm.type) {
        formData.append('problemType', this.searchForm.type);
      }
      if (this.searchForm.vinStatisticStatus) {
        formData.append('vinStatisticStatus', this.searchForm.vinStatisticStatus);
      }

      feedbackExport(formData).then((res) => {
        this.$loading.hide();
        this.downloadRes(res);
        handleAlert('success', '导出成功');
      }).catch(err => {
        this.$loading.hide();
        handleAlert('error', '导出失败');
      });
    },
    downloadRes(res) {
      let fileName = decodeURI(res.headers['content-disposition'])
      if (fileName) {
        fileName = fileName.substring(fileName.indexOf('=') + 1)
      }
      let blob = new Blob([res.data]);
      let url = window.URL.createObjectURL(blob);
      let aLink = document.createElement("a");
      aLink.style.display = "none";
      aLink.href = url;
      aLink.setAttribute("download", fileName);
      document.body.appendChild(aLink);
      aLink.click();
      document.body.removeChild(aLink); //下载完成移除元素
      window.URL.revokeObjectURL(url); //释放掉blob对象
      this.$loading.hide();
    },
  },
  mounted() {
    this.getTrainList();
    this.getFeedbackTypeList();
    this.getVinStatusList();
    const now = new Date();
    const start = new Date(now.getFullYear(), now.getMonth(), 1);
    const end = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    this.searchForm.start = start
    this.searchForm.end = end

    // statisticalHeight();
    this.fetchAllCharts()
    window.addEventListener('keydown', this.keyDown)
  },
  destroyed() {
    window.removeEventListener('keydown', this.keyDown, false)
  },
}
</script>

<style scoped>
/**
 * 统计页面样式
 * 实现上下两排图表平分页面高度
 */
.statisticalArea {
  display: flex;
  flex-direction: row;
}

.top-row {
  height: calc(50% - 20px); /* 减去一半的上下间距 */
}

.bottom-row {
  height: calc(50% - 20px); /* 减去一半的上下间距 */
}

.chart-container {
  width: 100%;
  height: 100%;
  min-height: 280px; /* 设置最小高度，确保在小屏幕上仍能正常显示 */
  padding: 4px;
  box-sizing: border-box;
}

/* 确保父容器有足够的高度 */
.el-main {
  height: calc(100vh - 120px); /* 减去头部和其他元素的高度 */
  display: flex;
  flex-direction: column;
}

/* 让图表区域占据剩余空间 */
.statisticalArea.top-row,
.statisticalArea.bottom-row {
  flex: 1;
}
</style>
