<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" label-width="65px" class="demo-form-inline">

        <el-form-item label="区域" prop="name">
          <el-select v-model="region" placeholder="请选择区域" clearable filterable>
            <el-option-group v-for="group in userCountryList" :key="group.id" :label="group.name">
              <el-option v-for="item in group.children" :key="item.id" :label="item.name" :value="item.code"></el-option>
            </el-option-group>
          </el-select>
        </el-form-item>
        <el-form-item label="服务店名称" prop="realName" :label-width=formLabelWidth>
          <el-input v-model.trim="realName" placeholder="请输入服务店名称"></el-input>
        </el-form-item>
        <!-- <el-form-item label="查询时间" prop="code">
          <el-date-picker style="width: 260px;" v-model="valueDate" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                :clearable="false" value-format='yyyy-MM-dd' @change="selectDate()" :picker-options="pickerOptions"></el-date-picker>
        </el-form-item> -->
        <el-form-item label="日期范围">
          <el-date-picker v-model="valueDate.start" :clearable="false" :editable="false" :picker-options="startTime" align="center" placeholder="开始日期" prop="begintime" type="date" value-format="yyyy-MM-dd"></el-date-picker>
          <span class="line">至</span>
          <el-date-picker v-model="valueDate.end" :clearable="false" :editable="false" :picker-options="endTime" align="center" placeholder="结束日期" prop="endtime" type="date" value-format="yyyy-MM-dd"></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button icon="el-icon-search" type="primary" @click="onSubmitSecond">搜索</el-button>
          <el-button plain @click="resetFirst()">重置</el-button>
        </el-form-item>
      </el-form>
    </div>


    <div class="tableDetail">
      <div class="tableHandle">
        <el-button type="text" icon="bulkDown-icon" @click="batchExport()">批量下载</el-button>
      </div>
      <!-- 分页显示列表 -->
      <!-- <el-table style="width:100%" border :data="resultList" :header-cell-style="{'text-align':'center'}" @selection-change="handleSelectionChange"> -->
      <el-table
        @sort-change="sortChange"
        style="width:100%"
        border
        stripe
        highlight-current-row
        :max-height="maximumHeight"
        :data="resultList"
        :header-cell-style="{
          'text-align': 'center',
          'background-color': 'var(--other-color)',
        }"
      >
        <el-table-column align="center" label="序号" type="index" width="60"></el-table-column>
        <el-table-column label="服务店名称" prop="realName" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column align="left" label="访问量" min-width="300" prop="count" sortable="custom">
          <template v-slot="{ row }">
            <div class="service-visit-bar">
              <div class="service-visit-bar__bg">
                <div :style="{ width: computeVisitPercent(row.count) + '%' }" class="service-visit-bar__fill"></div>
              </div>
              <div class="service-visit-bar__value">{{ row.count }}</div>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total>0" :total="total" :page.sync="currentPage" :limit.sync="pagesize" @pagination="dataList"/>

    </div>
  </div>
</template>
<script>

import {getMyDate, tableHeight, tableMaxHeight} from '@/assets/js/common.js'
import Pagination from '@/components/Pagination'
import {serviceDown, serviceNumber} from '@/api/statistics.js'
import {userCompleteCountryData} from '@/api/sysmgt.js'

export default {
  name: 'statistics_service_list',
  // components: { Pagination ,SelectTree },
  components: {Pagination},
  data() {
    return {

      // 时间段
      // 时间段
      valueDate: {
        start: '',
        end: '',
      },

      flag: '1',
      region: '',
      realName: '',

      // 国家
      userCountryList: [],

      // 当前页
      currentPage: 1,
      // 每页显示的条数
      pagesize: 15,
      // 总条数
      total: 0,
      // 数据集合
      resultList: [],
      // 当前页访问量最大值（用于柱状比例计算）
      pageMaxVisit: 0,
      formLabelWidth: '100px',
      maximumHeight: 0,

      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now() - 3600 * 1000 * 24 * 1
        }
      },
      startTime: {
        disabledDate: (time) => {
          return this.valueDate.end != null ? time.getTime() > new Date(this.valueDate.end) : false //只能选结束日期之前的日期
          //返回---结束时间是否有值？   可选时间小于结束时间   ：  任意时间都可选
        }
      },
      endTime: {
        disabledDate: (time) => {
          return this.valueDate.start != null ? time.getTime() < new Date(this.valueDate.start) : false //只能选开始时间之后的日期
          //返回---开始时间是否有值？   可选时间大于开始时间   ：  任意时间都可选
        }
      },
    }
  },
  methods: {
    // 重置
    resetFirst() {
      this.flag = '1';
      this.region = '';
      this.realName = '';
      const now = new Date();
      const start = new Date(now.getFullYear(), now.getMonth(), 1);
      const end = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      this.valueDate.start = start
      this.valueDate.end = end
      this.onSubmitSecond()
    },
    // 搜索
    onSubmitSecond() {
      this.currentPage = 1;
      this.dataList()
    },


    // 分页查询数据
    dataList() {
      this.$loading.show();
      var params = new URLSearchParams();
      params.append('page', this.currentPage);
      params.append('limit', this.pagesize);
      params.append('start', getMyDate(this.valueDate.start))
      params.append('end', getMyDate(this.valueDate.end))
      params.append('region', this.region);
      params.append('realName', this.realName);
      params.append('flag', this.flag);
      serviceNumber(params).then(res => {
        this.total = res.data.total    // 总条数
        this.resultList = res.data.data   // 数据
        // 计算本页访问量最大值
        this.pageMaxVisit = (this.resultList || []).reduce((maxValue, item) => {
          const value = Number(item && item.count);
          if (isNaN(value)) {
            return maxValue;
          }
          return value > maxValue ? value : maxValue;
        }, 0)
        this.tableHeightArea()
        this.$loading.hide();
      })
    },

    sortChange({column, prop, order}) {
      if (order == 'ascending') {
        this.flag = "2";
      } else if (order == 'descending') {
        this.flag = "1";
      }
      this.dataList()
    },


    // 搜索
    keyDown(e) {
      if (e.keyCode === 13) {
        this.onSubmitSecond()
      }
    },

    // 获取国家
    getUserCountryList() {
      userCompleteCountryData().then(res => {
        this.userCountryList = res.data.data
      })
    },

    // 批量下载
    batchExport() {
      var params = new URLSearchParams()
      params.append('start', getMyDate(this.valueDate.start))
      params.append('end', getMyDate(this.valueDate.end))
      params.append('region', this.region);
      params.append('realName', this.realName);
      params.append('flag', this.flag);
      serviceDown(params).then(res => {
        if (!res.data) {
          return
        }
        var header = res.headers["content-disposition"].split("filename=")[1];
        var name = decodeURI(header);
        var blob = new Blob([res.data]);
        var url = window.URL.createObjectURL(blob);
        var aLink = document.createElement("a");
        aLink.style.display = "none";
        aLink.href = url;
        aLink.setAttribute("download", name);
        document.body.appendChild(aLink);
        aLink.click();
        document.body.removeChild(aLink); //下载完成移除元素
        window.URL.revokeObjectURL(url); //释放掉blob对象
      })
    },

    maximumArea() {
      setTimeout(() => {
        tableHeight()
        setTimeout(() => {
          this.maximumHeight = tableMaxHeight;
        }, 50)
      })
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    },
    dateFormat(date) {
      const Y = date.getFullYear() + '-'
      const M = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) + '-' : date.getMonth() + 1 + '-'
      const D = date.getDate() < 10 ? '0' + date.getDate() + '' : date.getDate() + ''
      return Y + M + D
    },
    // 计算访问量柱状宽度所占百分比
    computeVisitPercent(value) {
      if (!this.pageMaxVisit) {
        return 0
      }
      const numericValue = Number(value)
      if (isNaN(numericValue) || numericValue <= 0) {
        return 0
      }
      const percent = (numericValue / this.pageMaxVisit) * 100
      // 控制在 0-100 范围内
      return percent > 100 ? 100 : (percent < 0 ? 0 : percent)
    },
  },
  mounted() {
    this.tableHeightArea()
    this.getUserCountryList()
    // 初始化：默认本月
    const now = new Date();
    const start = new Date(now.getFullYear(), now.getMonth(), 1);
    const end = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    this.valueDate.start = start
    this.valueDate.end = end
    this.onSubmitSecond()
    window.addEventListener('keydown', this.keyDown);
  },
  destroyed() {
    window.removeEventListener('keydown', this.keyDown, false);
  },

}
</script>
<style>
.fileImg {
  width: 100%;
  height: 100%;
}

.image {
  /*设置图片宽度和浏览器宽度一致*/
  width: 100%;
  height: inherit;
}

.el-tooltip__popper {
  max-width: 800px;
}

/* 设置输入框的长度 */
/* .el-dialog .el-input {
    width: 100% !important;
} */

.el-progress-bar__innerText {
  color: #fff;
}

#improt_info {
  margin: 0px;
}

.el-message-box__message {
  overflow-x: auto;
  max-height: 500px;
}

/* 访问量柱状条 */
.service-visit-bar {
  display: flex;
  align-items: center;
}

.service-visit-bar__bg {
  position: relative;
  flex: 1;
  height: 14px;
  background-color: #e6ebf5;
  border-radius: 7px;
  overflow: hidden;
}

.service-visit-bar__fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background-color: #409eff;
}

.service-visit-bar__value {
  min-width: 48px;
  text-align: right;
  padding-left: 8px;
}
</style>
