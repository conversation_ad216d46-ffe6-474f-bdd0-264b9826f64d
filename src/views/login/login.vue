<template>
  <div class="login">
    <div class="loginHeader">
      <div class="logoArea">
        <img src="../../assets/image/logo.png"/>
      </div>
      <!-- <div class="languageArea">
        <el-select v-model="selectValue" @change="langChange">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </div> -->
    </div>
    <el-form rel="loginForm" :model="loginform" label-width="120px" :rules="loginrules" class="login-form" autocomplete="on" label-position="left">
      <p class="login-title">{{ $t('login.title') }}</p>
      <el-form-item :label="$t('login.userName')" prop="username">
        <el-input type="text" :placeholder="$t('login.userInput')" v-model="loginform.username"></el-input>
      </el-form-item>
      <el-form-item :label="$t('login.password')" prop="password">
        <el-input type="password" :placeholder="$t('login.passInput')" v-model="loginform.password"></el-input>
      </el-form-item>
      <el-form-item :label="$t('login.code')" class="captCha" prop="captCha">
        <el-input type="text" :placeholder="$t('login.codeInput')" v-model="loginform.captCha"></el-input>
        <img @click="getCaptCha" :src="loginform.src" class="CaptChaImg">
        <input type="hidden" v-model="loginform.id">
      </el-form-item>
      <el-form-item>
        <el-button class="button" @click="login" type="primary">{{ $t('login.button') }}</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import {handleAlert, languageType} from '@/assets/js/common.js'
import {onLogin, currentUserInfo, captCha} from '@/api/sysmgt.js'

export default {
  name: 'login',
  data() {
    return {
      selectValue: '',
      options: [
        {
          value: 'cn',
          label: '中文'
        },
        // {
        //   value: 'en',
        //   label: 'English'
        // }
      ],
      loginform: {
        username: '',
        password: '',
        captCha: '',
        id: null,
        src: ''
      },
      // 表单验证，需要在el-form-item元素中增加prop属性
      loginrules: {
        username: [
          {required: true, message: this.$t('login.userTip'), trigger: ['blur', 'change']}
        ],
        password: [
          {required: true, message: this.$t('login.passTip'), trigger: ['blur', 'change']}
        ],
        captCha: [
          {required: true, message: this.$t('login.codeTip'), trigger: ['blur', 'change']}
        ]
      },
      // 对话框显示和隐藏
      dialogVisible: false
    }
  },
  created() {
    var that = this
    that.selectValue = 'cn'
    // that.selectValue = localStorage.lang === undefined ? 'cn' : localStorage.lang
    // localStorage.lang = that.selectValue
  },
  methods: {
    // 语言切换
    langChange(e) {
      localStorage.setItem('lang', e)
      this.$i18n.locale = e
      location.reload()
    },
    getCaptCha() {
      captCha().then(res => {
        if (res.data.code === 100) {
          this.loginform.id = res.data.data.id
          this.loginform.src = res.data.data.image
        }
      })
    },
    getUserDetail() {
      currentUserInfo().then(res => {
        if (res.data.code == 100) {
          this.$store.commit('SET_ROLES', res.data.data.roleList)
          this.$store.commit('SET_PERMS', res.data.data.permissionList)
          this.$router.push('/home')
          languageType()
        }
      }).catch(err => {
        if (err != null && err !== '' && err.responseText !== null) {
          handleAlert('error', '系统登录异常')
        }
      })
    },
    login() {
      var _this = this
      if (!_this.loginform.username) {
        handleAlert('error', _this.$t('login.userTip'))
        return
      }
      if (!_this.loginform.password) {
        handleAlert('error', _this.$t('login.passTip'))
        return
      }
      if (!_this.loginform.captCha) {
        handleAlert('error', _this.$t('login.codeTip'))
        return
      }
      var params = {
        username: _this.loginform.username,
        password: _this.loginform.password,
        vid: _this.loginform.id,
        verifycode: _this.loginform.captCha,
        type: '1'
      }
      onLogin(params).then(res => {
        if (res.data.code === 100) {
          var tokenVal = res.data.data.tokenHead + res.data.data.token
          this.$store.commit('set_token', tokenVal)
          this.$store.commit("set_userName", params.username);
          this.$store.commit("setRefreshToken",res.data.data.refreshToken);
          sessionStorage.setItem("store", JSON.stringify(this.$store.state));
          this.getUserDetail()
          languageType()
          if(window.bc){
             window.bc.postMessage(JSON.stringify(this.$store.state))
           }
        } else if (res.data.code === 1011) {
          handleAlert('error', res.data.msg)
        } else if (res.data.code === 1012) {
          handleAlert('error', res.data.msg)
        } else if (res.data.code === 400) {
          handleAlert('error', res.data.msg)
        } else if (res.data.code === 101) {
          handleAlert('error', res.data.msg)
        } else {
          handleAlert('error', '系统登录失败')
        }
      }).catch(err => {
        if (err != null && err !== '' && err.responseText !== null) {
          handleAlert('error', '系统登录异常')
        }
      })
    },
    keyDown(e) {
      if (e.keyCode === 13) {
        this.login()
      }
    },
  },
  mounted() {
    this.$store.commit('del_token');
    this.getCaptCha()
    window.addEventListener('keydown', this.keyDown)
  },
  destroyed() {
    window.removeEventListener('keydown', this.keyDown, false)
  },
}
</script>
<style scoped>
.login {
  height: 100%;
  width: 100%;
  overflow: hidden;
  background: url('../../assets/image/login.png') no-repeat;
  background-size: cover;
  position: relative;
}

.login-form {
  width: 500px;
  position: absolute;
  right: 20%;
  top: 50%;
  transform: translate(0%, -50%);
}

.login-form .login-title {
  font-size: 32px;
  color: #1950a0;
  margin-bottom: 40px;
  text-align: center;
}

/* 验证码 */
.CaptChaImg {
  width: 80px;
  height: 45px;
  margin-left: 20px;
}

/* 顶部标题 */
.loginHeader {
  height: 55px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 2%;
  background: var(--font-color);
  z-index: 5;
  position: relative;
}

/* logo */
.loginHeader .logoArea {
  height: 55px;
  display: flex;
  align-items: center;
}

.loginHeader .logoArea img {
  height: 100%;
}

.languageArea {
  width: 100px;
  height: 38px;
}

.loginHeader .el-input .el-input__inner {
  padding: 0 20px 0 10px;
  height: 38px;
  line-height: 38px;
  width: 100%;
  border: 1px solid #eee !important;
}

.loginHeader .el-input__icon {
  line-height: 38px;
  width: auto !important;
}

.loginHeader .el-input--suffix .el-input__inner {
  padding-right: 20px;
}
.login .el-button--primary {
  color: #FFF;
  background-color: #409EFF !important;
  border-color: #409EFF !important;
}
.login .el-button--primary:hover{
  color: #FFF;
  background-color: #409EFF !important;
  border-color: #409EFF !important;
  opacity: 0.7;
}
.login .login-form .el-input__inner:hover,
.login .login-form .el-input__inner:focus {
  /* border: 1px solid #409EFF !important; */
  border-color: #409EFF !important;
}
@media screen and (max-width: 1600px) and (min-width: 1440px) {
  /* 顶部标题 */
  .loginHeader {
    height: 50px;
  }

  /* logo */
  .loginHeader .logoArea {
    height: 50px;
  }

  .languageArea {
    width: 92px;
    height: 34px;
  }

  .loginHeader .el-input .el-input__inner,
  .loginHeader .el-select .el-input .el-select__caret {
    font-size: 14px;
  }

  .loginHeader .el-input .el-input__inner {
    height: 34px;
    line-height: 34px;
  }

  .loginHeader .el-input__icon {
    line-height: 34px;
  }
}

@media screen and (max-width: 1440px) and (min-width: 1366px) {
  /* 顶部标题 */
  .loginHeader {
    height: 50px;
  }

  /* logo */
  .loginHeader .logoArea {
    height: 50px;
  }

  .languageArea {
    width: 90px;
    height: 34px;
  }

  .loginHeader .el-input .el-input__inner,
  .loginHeader .el-select .el-input .el-select__caret {
    font-size: 14px;
  }

  .loginHeader .el-input .el-input__inner {
    padding: 0 18px 0 8px;
    height: 34px;
    line-height: 34px;
  }

  .loginHeader .el-input__icon {
    line-height: 34px;
  }
}

@media screen and (max-width: 1366px) and (min-width: 1280px) {
  /* 顶部标题 */
  .loginHeader {
    height: 40px;
  }

  /* logo */
  .loginHeader .logoArea {
    height: 40px;
  }

  .languageArea {
    width: 80px;
    height: 32px;
  }

  .loginHeader .el-input .el-input__inner,
  .loginHeader .el-select .el-input .el-select__caret {
    font-size: 13px;
  }

  .loginHeader .el-input .el-input__inner {
    padding: 0 18px 0 8px;
    height: 32px;
    line-height: 32px;
  }

  .loginHeader .el-input__icon {
    line-height: 32px;
  }
}

@media screen and (max-width: 1280px) and (min-width: 1024px) {
  /* 顶部标题 */
  .loginHeader {
    height: 40px;
  }

  /* logo */
  .loginHeader .logoArea {
    height: 40px;
  }

  .languageArea {
    width: 75px;
    height: 30px;
  }

  .loginHeader .el-input .el-input__inner,
  .loginHeader .el-select .el-input .el-select__caret {
    font-size: 12px;
  }

  .loginHeader .el-input .el-input__inner {
    padding: 0 18px 0 8px;
    height: 30px;
    line-height: 30px;
  }

  .loginHeader .el-input__icon {
    line-height: 30px;
  }
}

@media screen and (max-width: 1024px) {
  /* 顶部标题 */
  .loginHeader {
    height: 40px;
  }

  /* logo */
  .loginHeader .logoArea {
    height: 40px;
  }

  .languageArea {
    width: 75px;
    height: 30px;
  }

  .loginHeader .el-input .el-input__inner,
  .loginHeader .el-select .el-input .el-select__caret {
    font-size: 12px;
  }

  .loginHeader .el-input .el-input__inner {
    padding: 0 18px 0 8px;
    height: 30px;
    line-height: 30px;
  }

  .loginHeader .el-input__icon {
    line-height: 30px;
  }
}
</style>
<style>
.login .el-form-item__content {
  display: flex;
}

/* 输入框 */
.login-form .el-form-item__label {
  text-align: right;
  vertical-align: middle;
  float: left;
  font-size: 20px !important;
  color: #333 !important;
  line-height: 45px;
  padding: 0 12px 0 0;
  white-space: nowrap;
  text-align-last: justify;
}

.login-form .el-input__inner {
  -webkit-appearance: none;
  background-color: rgba(255, 255, 255, 0.5) !important;
  background-image: none;
  border-radius: 2px !important;
  border: 1px solid #eee !important;
  height: 45px !important;
  line-height: 45px !important;
  color: #333 !important;
  font-size: 16px;
}

.login-form .el-input__inner::placeholder {
  color: rgb(117, 117, 117) !important;
}

/* 登录按钮 */
.login-form .el-button {
  font-size: 17px !important;
  letter-spacing: 5px;
  width: 100%
}

.login-form .el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap > .el-form-item__label:before,
.login-form .el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label:before {
  content: '*';
  color: transparent !important;
  margin-right: 4px;
}
</style>
