<template>
  <div class="layoutContainer">
    <div class="infoDetail taskContent">
      <el-row>
        <!-- 车型年款 -->
        <el-col :span="4" class="leftData">
          <div>
            <div class="topButton">
              <span style="height:42px;line-height:42px;margin-left: 10px;">车型年款</span>
            </div>
            <div class="scrollClass taskdetail">
              <el-scrollbar>
                <el-tree ref="tree" node-key="id" :data="listdata" :props="defaultProps" @node-click="handleNodeClick">
                  <span slot-scope="{ data }" class="custom-tree-node">
                    <span>{{ data.name }}</span>
                    <!-- <span v-show="data.isCurrent" class="attribute" @click="attributeClick(data)">
                      <i class="el-icon-more" style="transform: rotate(90deg);" title="属性"></i>
                    </span> -->
                  </span>
                </el-tree>
              </el-scrollbar>
            </div>
          </div>
        </el-col>
        <!-- 配件目录 -->
        <el-col :span="9" class="leftData manualTask">
          <div>
            <div class="taskCenter">
              <div style="margin-left: 10px;height:42px;line-height:42px">
                <!-- 模板 -->
                <el-dropdown v-if="yearFlag && hasPerm('menuAsimss5A5B_107')">
                  <el-button type="text" size="small" icon="el-icon-download">下载模板</el-button>&nbsp;&nbsp;&nbsp;
                  <el-dropdown-menu slot="dropdown" style="margin-top: -3px">
                    <el-dropdown-item @click.native="template('1')">下载目录模板</el-dropdown-item>
                    <el-dropdown-item @click.native="template('2')">下载配件模板</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
                <!-- 导入 -->
                <el-dropdown v-if="yearFlag && hasPerm('menuAsimss5A5B_107')">
                  <el-button type="text" size="small" icon="el-icon-upload2">导入数据</el-button>&nbsp;&nbsp;&nbsp;
                  <el-dropdown-menu slot="dropdown" style="margin-top: -3px">
                    <el-dropdown-item @click.native="importFile('1')">导入目录</el-dropdown-item>
                    <el-dropdown-item @click.native="importFile('2')">导入配件</el-dropdown-item>

                    <!-- 清空数据 -->
                    <el-dropdown-item v-if="hasPerm('menuAsimss5A5B_121')"
                      @click.native="wipeData()">清空数据</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
                <!-- <el-button type="text" v-if="yearFlag" icon="import-icon" @click="importFile">导入目录</el-button>
                <el-button type="text" v-if="yearFlag" icon="import-icon" @click="importParts">导入配件</el-button> -->
                <!-- 删除 -->
                <el-button type="text" v-if="yearFlag && hasPerm('menuAsimss5A5B_102')" icon="el-icon-delete"
                  @click="del">删除</el-button>
                <el-button type="text" v-if="hasPerm('menuAsimss5A5B_107')" icon="el-icon-odometer"
                  @click="progress">上传进度</el-button>
                <el-button type="text" v-if="hasPerm('menuAsimss5A5B_107')" icon="el-icon-search"
                  @click="partsCodeSearch">搜索</el-button>

                &nbsp;&nbsp;
                <el-dropdown v-if="yearFlag && hasPerm('menuAsimss5A5B_107')">
                  <el-button type="text" v-if="yearFlag && hasPerm('menuAsimss5A5B_108')"
                    icon="el-icon-download">导出数据</el-button>
                  <el-dropdown-menu slot="dropdown" style="margin-top: -3px">
                    <el-dropdown-item @click.native="downPackZip()">下载数据包</el-dropdown-item>
                    <el-dropdown-item v-if="hasPerm('menuAsimss5A5B_110')"
                      @click.native="downPackExcel()">下载EXCEL版</el-dropdown-item>
                    <el-dropdown-item @click.native="downSplitZip()">下载拆分关系</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            </div>
            <div class="scrollClass taskManualList">
              <el-scrollbar>
                <el-tree :data="manualdata" node-key="id" ref="tree" :props="defaultProp"
                  :default-expanded-keys="nodeKeyList" @node-click="handleManualClick" show-checkbox>
                </el-tree>
              </el-scrollbar>
            </div>
          </div>
        </el-col>
        <!-- 基本信息 -->
        <el-col :span="11" class="fromRight">
          <div class="rightTitle">
            <span>信息结构属性</span>
          </div>
          <div class="detailInfo">
            <!-- 品牌 -->
            <div class="brandArea">
              <div v-if="brandList.name !== ''">
                <span><span>品牌</span>：</span>
                <span>{{ brandList.name }}</span>
              </div>
            </div>
            <!-- 车系 -->
            <div class="projectArea">
              <div v-if="projectList.brand !== ''">
                <span><span>品牌</span>：</span>
                <span>{{ projectList.brand }}</span>
              </div>
              <div v-if="projectList.name !== ''">
                <span><span>车型</span>：</span>
                <span>{{ projectList.name }}</span>
              </div>
              <div v-if="projectList.trainAlias !== '' && projectList.trainAlias !== null">
                <span><span>内部代号</span>：</span>
                <span>{{ projectList.trainAlias }}</span>
              </div>
              <div v-if="projectList.remark !== '' && projectList.remark !== null">
                <span><span>说明</span>：</span>
                <span>{{ projectList.remark }}</span>
              </div>
            </div>
            <!-- 年款 -->
            <div class="manualArea">
              <div v-if="manualList.brand !== ''">
                <span><span>品牌</span>：</span>
                <span>{{ manualList.brand }}</span>
              </div>
              <div v-if="manualList.train !== ''">
                <span><span>车型</span>：</span>
                <span>{{ manualList.train }}</span>
              </div>
              <div v-if="manualList.trainAlias !== '' && manualList.trainAlias !== null">
                <span><span>内部代号</span>：</span>
                <span>{{ manualList.trainAlias }}</span>
              </div>
              <div v-if="manualList.name !== ''">
                <span><span>年款</span>：</span>
                <span>{{ manualList.name }}</span>
              </div>
              <div v-if="manualList.remark !== '' && manualList.remark !== null">
                <span><span>说明</span>：</span>
                <span>{{ manualList.remark }}</span>
              </div>
            </div>

            <!-- 目录属性 -->
            <div class="catalogArea">
              <div v-if="manualList.brand !== ''">
                <span><span>品牌</span>：</span>
                <span>{{ manualList.brand }}</span>
              </div>
              <div v-if="manualList.train !== ''">
                <span><span>车型</span>：</span>
                <span>{{ manualList.train }}</span>
              </div>
              <div v-if="manualList.trainAlias !== '' && manualList.trainAlias !== null">
                <span><span>内部代号</span>：</span>
                <span>{{ manualList.trainAlias }}</span>
              </div>
              <div v-if="manualList.name !== ''">
                <span><span>年款</span>：</span>
                <span>{{ manualList.name }}</span>
              </div>
              <div v-if="catalogList.link && (!catalogList.children || catalogList.children.length <= 0)">
                <span><span>编码</span>：</span>
                <span>{{ directoryCode }} &nbsp;&nbsp;</span>
                <!-- <span v-if="!codeFlag">{{directoryCode}} &nbsp;&nbsp; <i class="el-icon-edit-outline" style="color: #409EFF; cursor: pointer; font-size: 17px;" @click="editCode"></i></span> -->
                <!-- <el-input v-if="codeFlag" type="textarea" :rows="1" v-model.trim="directoryCode" @change="updateCode($event)" placeholder="请输入编码"  style="width: 350px;height: 20px;"></el-input>
                <span v-if="codeFlag" style="margin: 3px;"> &nbsp; <i class="el-icon-refresh-left" style="color: #409EFF; cursor: pointer; font-size: 20px;" @click="saveCode"></i></span> -->
              </div>
              <div>
                <span><span>名称</span>：</span>
                <span v-if="!nameFlag">{{ catalogList.name }} &nbsp;&nbsp; <i class="el-icon-edit-outline"
                    style="color: #409EFF; cursor: pointer; font-size: 17px;" @click="editName"></i></span>
                <el-input v-if="nameFlag" type="textarea" :rows="1" v-model.trim="catalogList.name"
                  @change="updateName($event)" placeholder="请输入名称" style="width: 350px;margin-bottom:0"></el-input>
                <span v-if="nameFlag" style="margin: 3px;"> &nbsp; <i class="el-icon-refresh-left"
                    style="color: #409EFF; cursor: pointer; font-size: 20px;" @click="saveName"></i></span>
              </div>
              <div v-if="catalogList.link && (!catalogList.children || catalogList.children.length <= 0)">
                <span><span>爆炸图</span>：</span>
                <!-- <div> -->
                <span @click="showSvg" style="color: #409EFF; cursor:pointer">
                  <i class="svgLook-icon"></i>查看
                </span>
                <!-- </div> -->
              </div>

              <div v-if="catalogList.link && (!catalogList.children || catalogList.children.length <= 0)">
                <span><span>适用配置</span>：</span>
                <span @click="showModel" style="color: #409EFF; cursor:pointer">
                  <i class="el-icon-view"></i>查看
                </span>
              </div>

              <div class="wrapper" v-if="catalogList.link && (!catalogList.children || catalogList.children.length <= 0)">
                <div style="width:100%;height: 20px;">
                  <span><span>配件</span>：</span>
                  <span style="text-align: right; ">
                    <span v-if="hasPerm('menuAsimss5A5B_301')" @click="partsAdd()"
                      style="color: #409EFF; cursor:pointer;"><i class="el-icon-plus"></i>新增配件</span>
                    <span v-if="hasPerm('menuAsimss5A5B_301')">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
                    <span v-if="hasPerm('menuAsimss5A5B_301') && partsList.length > 0" @click="emptyPartsData"
                      style="color: #D91C1C; cursor:pointer;"><i class="deleteRed-icon"></i>清空配件</span>
                  </span>
                </div>

                <div style="width:100%;" class="parts_table">

                  <el-table style="width:100%;display: block;" border stripe highlight-current-row :data="partsList"
                    :header-cell-style="{
                      'text-align': 'center',
                      'background-color': 'var(--other-color)',
                    }">
                    <el-table-column label="序号" prop="partsSign" width="60" align="center"></el-table-column>
                    <el-table-column label="配件编码" prop="partsCode" min-width="90"
                      show-overflow-tooltip></el-table-column>
                    <el-table-column label="配件名称" prop="cnName" min-width="120" show-overflow-tooltip></el-table-column>
                    <el-table-column label="操作" width="95" align="center"
                      v-if="hasPerm('menuAsimss5A5B_301') || hasPerm('menuAsimss5A5B_302')">
                      <template slot-scope="{row}">
                        <el-button type="text" size="small" @click="handelLook(row)">编辑</el-button>
                      </template>
                    </el-table-column>
                  </el-table>

                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>

      <el-dialog v-dialogDrag width="800px !important" :title="sysTitle" :visible.sync="dialogBigImageVisible"
        :close-on-click-modal="false">
        <div style="width: 100%;margin-bottom:15px">
          <el-button icon="el-icon-download" type="text" @click="down"
            v-if="catalogList.svgPath && catalogList.svgPath !== '' && hasPerm('menuAsimss5A5B_108')">
            下载
          </el-button>
          <el-button type="text" @click="replace" icon="el-icon-refresh" v-if="hasPerm('menuAsimss5A5B_103')">
            替换
          </el-button>
        </div>
        <div class="fileImg">
          <div id="mysvg" style="height:100%"></div>
        </div>
      </el-dialog>

      <el-dialog v-dialogDrag title="上传" :visible.sync="dialoguploadVisible" :close-on-click-modal="false">
        <div style="width: 100%;">
          <el-upload class="upload-demo" style="max-width: 379px;" action="#" :http-request="uploadAttach"
            :on-success="handleOnSuccess" :on-remove="handleOnRemove" :before-remove="beforeOnRemove"
            :before-upload="beforeAvatarUpload" :on-exceed="handleOnExceed" multiple :limit="1" :file-list="imgList"
            accept=".svg" list-type="picture">
            <el-button size="min" icon="el-icon-upload" type="primary">选择图片</el-button>
          </el-upload>
        </div>
        <div class="submitArea">
          <el-button type="primary" @click="updateImageInfo">
            立即提交
          </el-button>
          <el-button @click="dialoguploadVisible = false">
            取消
          </el-button>
        </div>
      </el-dialog>


      <el-dialog v-dialogDrag :title="uploadTitle" :visible.sync="dialogFileVisible" :close-on-click-modal="false">
        <div class="manualArea" style="font-size: 1.1em;">
          <div v-if="manualList.brand !== ''">
            <span><span>品牌</span>：</span>
            <span>{{ manualList.brand }}</span>
          </div>
          <div v-if="manualList.train !== ''">
            <span><span>车型</span>：</span>
            <span>{{ manualList.train }}</span>
          </div>
          <div v-if="manualList.name !== ''">
            <span><span>年款</span>：</span>
            <span>{{ manualList.name }}</span>
          </div>
        </div>

        <div style="width: 100%; height: 10px;">&nbsp;&nbsp;&nbsp;</div>

        <div style="width: 100%;">
          <el-upload class="upload-demo inline-block" ref="elUpload" action="#" :show-file-list="false" multiple
            :limit="1" :before-upload="onBeforeUpload" :accept="accept">
            <el-button size="min" icon="el-icon-upload" type="primary">选择文件</el-button>
          </el-upload>
          <el-progress id="el_progress1" style="width:350px;margin-top:50px;margin-left:10px" v-show="showUploadProcess"
            color="green" type="line" text-color="#ffffff" :text-inside="true" :stroke-width="20"
            :percentage="percentage"></el-progress>
          <el-upload ref="elUploadResult" action="#" :show-file-list="true" :file-list="zipList" :limit="1"
            :before-remove="handleBeforeRemove">
          </el-upload>
        </div>
        <span v-if="isParts" style="color: red;">* 在原来的数据上添加新的数据，如果相同的数据会修改为新数据</span>
        <el-radio-group v-else v-model="isAppend">
          <el-radio label="1">追加
            <el-tooltip class="item" effect="dark" placement="top">
              <i class="el-icon-question" style="font-size: 16px; vertical-align: middle;"></i>
              <div slot="content">
                <p>追加是在原来的数据上添加新的数据，如果相同的数据会修改为新数据</p>
              </div>
            </el-tooltip>
          </el-radio>
          <el-radio label="2">覆盖
            <el-tooltip class="item" effect="dark" placement="top">
              <i class="el-icon-question" style="font-size: 16px; vertical-align: middle;"></i>
              <div slot="content">
                <p>覆盖是把原来的数据全部清除，再添加新的数据</p>
              </div>
            </el-tooltip>
          </el-radio>
        </el-radio-group>
        <div class="submitArea">
          <el-button type="primary" @click="uploadData">
            立即提交
          </el-button>
          <el-button @click="dialogFileVisible = false">
            取消
          </el-button>
        </div>
      </el-dialog>

      <el-dialog v-dialogDrag title="上传进度" width="1000px !important" :visible.sync="dialogUploadProgressVisible"
        :close-on-click-modal="false">
        <el-table style="width:100%;" border highlight-current-row :data="progressData" :header-cell-style="{
          'text-align': 'center',
          'background-color': 'var(--other-color)',
        }">
          <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
          <el-table-column label="品牌" prop="brandName" min-width="100" show-overflow-tooltip></el-table-column>
          <el-table-column label="车型" prop="trainName" min-width="100" show-overflow-tooltip></el-table-column>
          <el-table-column label="年款" prop="year" min-width="100" show-overflow-tooltip></el-table-column>
          <el-table-column label="类型" prop="type" width="100" align="center">
            <template slot-scope="{row}">
              <span v-if="row.type === 1">导入目录</span>
              <span v-if="row.type === 2">导入配件</span>
            </template>
          </el-table-column>
          <el-table-column label="操作人" prop="realName" min-width="100" show-overflow-tooltip></el-table-column>
          <el-table-column label="结果" prop="result" min-width="100" show-overflow-tooltip></el-table-column>
          <el-table-column label="上传时间" prop="createdTime" width="150" align="center">
            <template slot-scope="{row}">
              <div>
                {{ row.createdTime | conversion("yyyy-MM-dd HH:mm:ss") }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" fixed="right" width="130" align="center">
            <template slot-scope="{row}">
              <el-button type="text" size="small" @click="copyResult(row)">复制结果</el-button>
              <el-button type="text" v-if="hasPerm('menuAsimss5A5B_102')" size="small"
                @click="deleteProgress(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="progressTotal > 0" :total="progressTotal" :page.sync="progressCurrentPage"
          :limit.sync="progressPagesize" @pagination="progress" />
      </el-dialog>

      <el-dialog v-dialogDrag title="下载数据包" :visible.sync="dialogLanguageFormVisible">
        <el-form ref="alloter" label-width="100px" :validate-on-rule-change="false">
          <el-form-item label="选择语言">
            <el-select v-model="language" placeholder="请选择" filterable clearable>
              <el-option v-for="item in langeList" :key="item.code" :label="item.name" :value="item.code">
              </el-option>
            </el-select>
          </el-form-item>
          <div class="submitArea">
            <el-button type="primary" @click="submitPack()">
              下载
            </el-button>
            <el-button @click="dialogLanguageFormVisible = false">
              取消
            </el-button>
          </div>
        </el-form>
      </el-dialog>


      <el-dialog v-dialogDrag width="850px !important" title="配件信息" :visible.sync="dialogPartsEntryFormVisible"
        :close-on-click-modal="false">

        <!-- 添加和修改 -->
        <el-form :model="partsEntry" label-position="center" :validate-on-rule-change="false">

          <el-row v-if="hasPerm('menuAsimss5A5B_301')">
            <el-col :span="12">
              <el-form-item label="配件名称" prop="cnName" :label-width="formLabeldetailsWidth">
                <el-input v-model.trim="partsEntry.cnName" placeholder="请输入配件名称"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="配件编码" prop="partsCode" :label-width="formLabeldetailsWidth">
                <el-input v-model.trim="partsEntry.partsCode" placeholder="请输入配件编码"></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row v-if="hasPerm('menuAsimss5A5B_301')">
            <el-col :span="12">
              <el-form-item label="序号" prop="partsSign" :label-width="formLabeldetailsWidth">
                <el-input v-model.trim="partsEntry.partsSign"
                  oninput="value=value.replace(/[^\d.]/g, '').replace(/\.{2,}/g, '.').replace('.', '$#$').replace(/\./g, '').replace('$#$', '.').replace(/^(\-)*(\d+)\.(\d\d\d).*$/, '$1$2.$3').replace(/^\./g, '')"
                  placeholder="请输入序号"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="装车用量" prop="useCount" :label-width="formLabeldetailsWidth">
                <el-input v-model.trim="partsEntry.useCount" value=""
                  onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}"
                  onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}"
                  placeholder="请输入装车用量"></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row v-if="hasPerm('menuAsimss5A5B_301')">
            <el-col :span="24" class="inputItemStyle">
              <el-form-item label="适用配置" prop="modelIds" :label-width="formLabeldetailsWidth">

                <el-select v-model="partsEntry.modelIds" multiple>
                  <el-option v-for="item in models" :key="item.id" :label="item.nameCh" :value="item.id">
                    <span class="checkbox"></span>
                    <span class="label-name-box" style="margin-left: 8px;">{{ item.nameCh }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row v-if="hasPerm('menuAsimss5A5B_301') || hasPerm('menuAsimss5A5B_302')">
            <el-col :span="24" class="inputItemStyle">
              <el-form-item label="备注" prop="directoryRemark" :label-width="formLabeldetailsWidth">
                <el-input v-model.trim="partsEntry.directoryRemark" rows="2" show-word-limit maxlength="500"
                  placeholder="请输入备注"></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <div class="submitArea">
            <el-button type="primary" @click="saveData('temp')">
              保存
            </el-button>
            <el-button type="danger" v-if="!partsAddFlag && hasPerm('menuAsimss5A5B_301')" @click="deleteData('temp')">
              删除
            </el-button>
          </div>
        </el-form>
      </el-dialog>

      <el-dialog v-dialogDrag width="600px !important" :title="titleDirectory"
        :visible.sync="dialogDirectoryFormVisible" :close-on-click-modal="false">
        <el-table style="width:100%" border stripe ref="applytable" highlight-current-row max-height="500px"
          :data="modelList" :header-cell-style="{
            'text-align': 'center',
            'background-color': 'var(--other-color)',
          }" @selection-change="handleSelectionChange">
          <!-- <el-table-column label="序号" type="index" width="60" align="center"></el-table-column> -->
          <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>
          <el-table-column label="序号" type="index" width="100" align="center"></el-table-column>

          <el-table-column label="配置" prop="nameCh" min-width="100" align="center"></el-table-column>
        </el-table>
        <div style="height: 30px;"></div>
        <div v-if="hasPerm('menuAsimss5A5B_301')"
          style="width: 100%; display: flex;  justify-content: center;  align-items: center;">
          <el-button type="primary" @click="getCheckedKeys()">立即提交</el-button>
          <el-button @click="dialogDirectoryFormVisible = false">取消</el-button>
        </div>
      </el-dialog>

      <!-- 配件查询 -->
      <el-dialog v-dialogDrag title="配件查询" width="1200px !important" :visible.sync="dialogPratsSearchVisible"
        :close-on-click-modal="false">
        <div class="secondFloat">
          <el-form :inline="true" ref="formInline" label-width="70px" :model="formInline" class="demo-form-inline">
            <el-form-item label="配件编码" prop="code">
              <el-input v-model.trim="formInline.code" placeholder="请输入配件编码"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onPratsSubmit" icon="el-icon-search">搜索</el-button>
            </el-form-item>
          </el-form>
        </div>

        <el-table style="width:100%;" border highlight-current-row :data="pratsList" :header-cell-style="{
          'text-align': 'center',
          'background-color': 'var(--other-color)',
        }">
          <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
          <el-table-column label="品牌" prop="brandName" min-width="80" show-overflow-tooltip></el-table-column>
          <el-table-column label="车型" prop="trainName" min-width="80" show-overflow-tooltip></el-table-column>
          <el-table-column label="年款" prop="year" min-width="80" show-overflow-tooltip></el-table-column>
          <el-table-column label="系统名称" prop="directoryName" min-width="100" show-overflow-tooltip></el-table-column>
          <el-table-column label="配件编码" prop="partsCode" min-width="100" show-overflow-tooltip></el-table-column>
          <el-table-column label="配件名称" prop="partsName" min-width="100" show-overflow-tooltip></el-table-column>
          <el-table-column label="操作" fixed="right" width="120" align="center"
            v-if="hasPerm('menuAsimss5A5B_301') || hasPerm('menuAsimss5A5B_302')">
            <template slot-scope="{row}">
              <el-button type="text" size="small" @click="updatedRemark(row)">修改备注</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="pratsTotal > 0" :total="pratsTotal" :page.sync="pratsCurrentPage" :limit.sync="pratsPagesize"
          @pagination="partsSearch" />
      </el-dialog>


      <el-dialog v-dialogDrag width="850px !important" title="配件信息" :visible.sync="dialogUpdatedRemarkFormVisible"
        :close-on-click-modal="false">

        <el-form :model="partsEntry" label-position="center" :validate-on-rule-change="false">

          <el-row>
            <el-col :span="12">
              <el-form-item label="品牌" prop="brandName" :label-width="formLabeldetailsWidth">
                <el-input v-model.trim="partsObject.brandName" readonly="readonly"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="车型" prop="trainName" :label-width="formLabeldetailsWidth">
                <el-input v-model.trim="partsObject.trainName" readonly="readonly"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="年款" prop="year" :label-width="formLabeldetailsWidth">
                <el-input v-model.trim="partsObject.year" readonly="readonly"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="系统" prop="directoryName" :label-width="formLabeldetailsWidth">
                <el-input v-model.trim="partsObject.directoryName" readonly="readonly"></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="配件名称" prop="partsName" :label-width="formLabeldetailsWidth">
                <el-input v-model.trim="partsObject.partsName" readonly="readonly"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="配件编码" prop="partsCode" :label-width="formLabeldetailsWidth">
                <el-input v-model.trim="partsObject.partsCode" readonly="readonly"></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24" class="inputItemStyle">
              <el-form-item label="备注" prop="remark" :label-width="formLabeldetailsWidth">
                <el-input v-model.trim="partsObject.remark" rows="2" show-word-limit maxlength="500"
                  placeholder="请输入备注"></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <div class="submitArea">
            <el-button type="primary" @click="saveRemark()">
              保存
            </el-button>
          </div>
        </el-form>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { cmsServerUrl, contentSize, handleAlert } from '@/assets/js/common.js'
import {
  catalogCarList, catalogDataList, getPartsLists, downSvg, uploadSvg, catalogUpdate, importCatalog, importParts, updateShow,
  catalogClearAll, catalogDel, catalogFinishUpload, uploadProgress, catalogTemplate, partsTemplate, packZip, delProgress, atlasExcel, atlasSplit,
  emptyData, catalogLangeList, findRelModel, catalogModel, catalogSaveParts, catalogDelParts, updateDirectoryModel, accessoriesSearch
} from '@/api/epcmgt.js'
import { loadSvg } from '@/plugins/mysvg.js'
import { procSplitFile, checkUploadProgress, importAttach, uploadProgressApi } from '@/api/sysmgt.js'
import $ from 'jquery'
import Pagination from '@/components/Pagination'
import SparkMD5 from 'spark-md5'
import { Loading } from "element-ui";

/** 计算文件md5值
 * @param file 文件
 * @param chunkSize 分片大小
 * @returns Promise
 */
function getmd5(file, chunkSize) {
  return new Promise((resolve, reject) => {
    let blobSlice = File.prototype.slice || File.prototype.mozSlice || File.prototype.webkitSlice;
    let chunks = Math.ceil(file.size / chunkSize);
    let currentChunk = 0;
    let spark = new SparkMD5.ArrayBuffer();
    let fileReader = new FileReader();
    fileReader.onload = function (e) {
      spark.append(e.target.result);
      currentChunk++;
      if (currentChunk < chunks) {
        loadNext();
      } else {
        let md5 = spark.end();
        resolve(md5);

      }
    };
    fileReader.onerror = function (e) {
      reject(e);
    };
    function loadNext() {
      let start = currentChunk * chunkSize;
      let end = start + chunkSize;
      if (end > file.size) {
        end = file.size;
      }
      fileReader.readAsArrayBuffer(blobSlice.call(file, start, end));
    }
    loadNext();
  });
}

export default {
  name: "epcmgtcataloglist",
  components: { Pagination },
  data() {
    return {

      language: 'zh-CHS',
      langeList: [],
      dialogLanguageFormVisible: false,


      defaultProps: {
        children: 'children',
        label: 'name',
      },
      defaultProp: {
        children: 'children',
        label: 'name',
      },

      partsList: [],
      yearFlag: false,
      nodeKeyList: [],

      listdata: [],
      manualdata: [],
      // 品牌
      brandList: {
        name: "",
      },
      // 车系
      projectList: {
        name: "",
        brand: "",
        id: "",
        trainAlias: "",
        remark: ""
      },
      // 年款
      manualList: {
        name: "",
        brand: "",
        train: "",
        id: "",
        trainAlias: "",
        remark: ""
      },
      catalogList: {
        name: "",
        svgPath: "",
        id: '',
        link: '',
        children: [],
        code: '',
      },
      // 编码
      directoryCode: '',
      repeat: true,
      // 编辑编码的标志
      codeFlag: false,
      // 编辑名称的标志
      nameFlag: false,

      // 显示图片
      dialogBigImageVisible: false,
      sysTitle: '',

      // 上传SVG图
      dialoguploadVisible: false,
      uploadUrl: '',
      imgList: [],
      newpath: '',

      // 上传文件
      dialogFileVisible: false,
      uploadTitle: '导入目录',
      isAppend: '2',
      isParts: false,

      // 上传进度
      dialogUploadProgressVisible: false,
      progressData: [],

      // 进度条
      showUploadProcess: false,  // 显示进度条
      percentage: 0,  // 进度条

      accept: '.zip',

      isfinish: true,
      // 分片上传
      fileList: [],
      zipList: [],
      //切片文件
      fileShard: {},
      //当前文件
      curFile: {},
      //文件分割的开始位置
      start: 0,
      //文件分割的结束位置
      end: 0,
      //文件大小
      fileSize: 0,
      fileKey: '',
      fileShardIndex: 0,
      fileShardTotal: 0,
      fileShardSize: 0,
      switchC: false,
      showUploadBtn: false,
      flagType: 'temp/catalog',

      // 上传进度记录
      progressCurrentPage: 1,
      progressPagesize: 10,
      progressTotal: 0,


      // 配件信息
      partsEntry: {
        // 配件编码
        partsCode: "",
        // 配件名称
        cnName: "",
        // 长度
        length: 0.00,
        // 宽度
        width: 0.00,
        // 高度
        height: 0.00,
        // 最小起订量
        minOrder: 1,
        // 厂家
        factory: "",
        // 材料
        material: "",
        // 作用
        role: "",
        // 重量
        partsWeight: 0.00,
        // 库存
        partsInventory: 0,
        // 关联的ID
        directoryId: "",
        basicDirectoryId: '',
        // 备注
        directoryRemark: '',
        // 序号
        partsSign: "",
        // 用量
        useCount: 1,
        // 适用车型
        modelIds: [],
        sltModel: '',


      },
      dialogPartsEntryFormVisible: false,
      dialogDirectoryFormVisible: false,
      titleDirectory: '',
      formLabeldetailsWidth: '100px',
      modelList: [],
      models: [],
      partsAddFlag: false,
      selectModel: [],

      // 配件查询
      formInline: {
        code: '',
      },
      dialogPratsSearchVisible: false,
      pratsCurrentPage: 1,
      pratsPagesize: 10,
      pratsTotal: 0,
      pratsList: [],
      dialogUpdatedRemarkFormVisible: false,
      partsObject: {},
    }
  },

  methods: {
    // 目录
    dataList() {
      catalogCarList().then(res => {
        this.listdata = res.data.data

      })
    },

    // 修改名称
    editName() {

      this.nameFlag = true;
    },
    saveName() {
      if (!this.catalogList.name || this.catalogList.name.length <= 0) {
        handleAlert("error", "名称不能为空")
        return false
      }
      this.nameFlag = false;
    },
    updateName() {

      var params = new URLSearchParams()
      params.append('id', this.catalogList.id)
      params.append('name', this.catalogList.name)
      if (!this.catalogList.name || this.catalogList.name.length <= 0) {
        handleAlert("error", "名称不能为空")
        return false
      }

      if (this.directoryCode && this.directoryCode != 'undefined') {
        params.append('code', this.directoryCode)
        params.append('link', this.catalogList.link)
      }

      catalogUpdate(params).then(res => {
        if (res.data.code != 100) {
          handleAlert('error', res.data.msg)
        }
      })
    },

    // 修改编码
    editCode() {

      this.codeFlag = true;
    },
    saveCode() {
      if (!this.directoryCode || this.directoryCode.length <= 0) {
        handleAlert("error", "编码不能为空")
        return false
      }
      if (!this.repeat) {
        return false
      }
      this.codeFlag = false;
    },
    updateCode() {
      if (!this.directoryCode || this.directoryCode.length <= 0) {
        handleAlert("error", "编码不能为空")
        return false
      }
      this.repeat = true;
      var params = new URLSearchParams()
      params.append('id', this.catalogList.id)
      params.append('code', this.directoryCode)
      params.append('link', this.catalogList.link)
      catalogUpdate(params).then(res => {
        if (res.data.code != 100) {
          this.repeat = false;
          handleAlert('error', res.data.msg)
        }
      })
    },

    // 配件目录
    manualDetail(yearId) {
      var params = {
        yearId: yearId
      }
      catalogDataList(params).then((res) => {
        this.manualdata = res.data.data
      })
    },
    handleNodeClick(data) {
      this.yearFlag = false
      this.manualdata = []

      if (data.type == "brand") {
        this.manualdata = []
        this.resettingBrand();
        this.resettingTrain();
        this.resettingYear();
        this.resettingCatalog();
        // $(".taskContent .taskCenter").hide()
        $(".manualTask").hide()
        $(".taskContent .fromRight").css("width", "79%")
        $(".detailInfo .brandArea").show()
        $(".detailInfo .projectArea,.detailInfo .manualArea,.detailInfo .catalogArea").hide()
        this.brandList = Object.assign({}, data)
      } else if (data.type == 'train') {
        this.manualdata = []
        this.resettingTrain();
        this.resettingYear();
        this.resettingCatalog();
        // $(".taskContent .taskCenter").hide()
        $(".manualTask").hide()
        $(".taskContent .fromRight").css("width", "79%")
        $(".detailInfo .projectArea").show()
        $(".detailInfo .brandArea,.detailInfo .manualArea,.detailInfo .catalogArea").hide()
        this.projectList = Object.assign({}, data)
      } else if (data.type == 'year') {
        // 点击年款，查询年款下的配置
        let pm = new URLSearchParams()
        pm.append('yearId', data.id)
        catalogModel(pm).then(res => {
          this.modelList = res.data.data

        })
        this.resettingYear();
        this.resettingCatalog();
        this.yearFlag = true
        // 获取配件目录
        this.manualDetail(data.id)
        $(".manualTask").show()
        $(".taskContent .fromRight").removeAttr("style")
        $(".detailInfo .manualArea").show()
        $(".detailInfo .brandArea,.detailInfo .projectArea,.detailInfo .catalogArea").hide()
        this.manualList = Object.assign({}, data)
      }
      this.areaSize();
    },

    handleManualClick(data) {
      this.nameFlag = false;
      $(".detailInfo .catalogArea").show()
      $(".detailInfo .brandArea,.detailInfo .projectArea,.detailInfo .manualArea").hide()
      this.catalogList = data
      this.directoryCode = ''
      // 获取总成下的配件
      this.partsList = []
      if (data.link && (!data.children || data.children.length <= 0)) {
        this.getPartsList(data.link);
      }

    },

    // 重置对象
    resettingBrand() {
      // 品牌
      this.brandList = {
        name: "",
      };
    },
    // 重置对象
    resettingTrain() {
      // 车系
      this.projectList = {
        name: "",
        brand: "",
        id: "",
        trainAlias: "",
        remark: ""
      };
    },

    // 重置对象
    resettingYear() {
      // 年款
      this.manualList = {
        name: "",
        brand: "",
        train: "",
        id: "",
        trainAlias: "",
        remark: ""
      };
    },
    // 重置对象
    resettingCatalog() {
      this.catalogList = {
        name: "",
        svgPath: "",
        id: '',
        link: '',
        children: [],
        code: ''
      };
    },


    // 解析进度列表
    progress() {
      var params = new URLSearchParams()
      params.append('type', "1")
      params.append('page', this.progressCurrentPage)
      params.append('limit', this.progressPagesize)
      uploadProgress(params).then(res => {

        this.progressData = res.data.data
        this.progressTotal = res.data.total
        this.dialogUploadProgressVisible = true;
      })
    },

    // 下载数据包
    downPackZip() {
      this.dialogLanguageFormVisible = true;
      this.language = 'zh-CHS';
    },
    submitPack() {
      let _this = this
      window.loading = Loading.service({ fullscreen: true, text: "数据处理中，过程可能缓慢，请耐心等待！" })
      var params = new URLSearchParams()
      params.append('yearId', this.manualList.id)
      params.append('language', this.language)
      packZip(params).then(res => {
        if (res.data.code === 100) {
          // 查询什么时候上传结束
          setTimeout(() => {
            _this.getUploadProgress(res.data.data)
          }, 1000);
        } else {
          _this.$nextTick(() => { // 以服务的方式调用的 Loading 需要异步关闭
            window.loading.close();
            window.loading = undefined;
          });
          handleAlert("error", "系统开小差了...")
        }
        this.$loading.hide()
      }).catch(e => {
        _this.$nextTick(() => { // 以服务的方式调用的 Loading 需要异步关闭
          window.loading.close();
          window.loading = undefined;
        });
        this.$loading.hide()
        handleAlert("error", "系统开小差了...")
      })
    },


    // 查询上传进度
    getUploadProgress(params) {
      let _this = this;
      uploadProgressApi(params).then(res => {
        if (res.data.code === 100 && res.data.data) {
          if (!res.data.data.success) {
            loading.setText(res.data.data.error)
            setTimeout(() => {
              _this.getUploadProgress(params);
            }, 5000)
          } else {
            _this.$nextTick(() => { // 以服务的方式调用的 Loading 需要异步关闭
              window.loading.close();
              window.loading = undefined;
            });
            this.dialogLanguageFormVisible = false;
            if (res.data.data.data.length > 0) {
              let path = res.data.data.data
              let name = path.substring(path.lastIndexOf("/") + 1)
              downSvg(res.data.data.data).then(resc => {
                this.$loading.hide()
                if (!resc.data) {
                  _this.$nextTick(() => { // 以服务的方式调用的 Loading 需要异步关闭
                    window.loading.close();
                    window.loading = undefined;
                  });
                  handleAlert("error", "数据打包错误")
                  return false
                }
                let blob = new Blob([resc.data]);
                let url = window.URL.createObjectURL(blob);
                let aLink = document.createElement("a");
                aLink.style.display = "none";
                aLink.href = url;
                aLink.setAttribute("download", name);
                document.body.appendChild(aLink);
                aLink.click();
                document.body.removeChild(aLink); //下载完成移除元素
                window.URL.revokeObjectURL(url); //释放掉blob对象
              }).catch(e => {
                _this.$nextTick(() => { // 以服务的方式调用的 Loading 需要异步关闭
                  window.loading.close();
                  window.loading = undefined;
                });
                handleAlert("error", res.data.data.error)
              })
            } else {
              _this.$nextTick(() => { // 以服务的方式调用的 Loading 需要异步关闭
                window.loading.close();
                window.loading = undefined;
              });
              handleAlert("error", "数据打包错误; " + res.data.msg)
            }
          }
        }
      }).catch(e => {
        console.log(e)
        _this.$nextTick(() => { // 以服务的方式调用的 Loading 需要异步关闭
          window.loading.close();
          window.loading = undefined;
        });
        _this.$loading.hide();
        _this.progressPercent = 100;
        // handleAlert('success','导入成功');
        _this.$alert("处理结束", '处理结束', { dangerouslyUseHTMLString: true })
        _this.dataList();
      })
    },


    // 下载 excel 文件
    downPackExcel() {
      let _this = this;
      window.loading = Loading.service({ fullscreen: true, text: "数据处理中，过程可能缓慢，请耐心等待！" })
      var params = new URLSearchParams()
      params.append('yearId', this.manualList.id)
      atlasExcel(params).then(res => {
        if (res.data.code === 100) {
          // 查询什么时候上传结束
          setTimeout(() => {
            _this.getUploadProgress(res.data.data)
          }, 1000);
        } else {
          _this.$nextTick(() => { // 以服务的方式调用的 Loading 需要异步关闭
            window.loading.close();
            window.loading = undefined;
          });
          handleAlert("error", "数据打包错误")
        }
      }).catch(e => {
        _this.$nextTick(() => { // 以服务的方式调用的 Loading 需要异步关闭
          window.loading.close();
          window.loading = undefined;
        });
        this.$loading.hide()
        handleAlert("error", "系统开小差了...")
      })
    },
    // 下载拆分关系
    downSplitZip() {
      let _this = this;
      var params = new URLSearchParams()
      params.append('yearId', this.manualList.id)
      window.loading = Loading.service({ fullscreen: true, text: "数据处理中，过程可能缓慢，请耐心等待！" })
      atlasSplit(params).then(res => {
        this.$loading.hide()
        if (res.data.code === 100) {
          // 查询什么时候上传结束
          setTimeout(() => {
            _this.getUploadProgress(res.data.data)
          }, 1000);
        } else {
          _this.$nextTick(() => { // 以服务的方式调用的 Loading 需要异步关闭
            window.loading.close();
            window.loading = undefined;
          });
          handleAlert("error", "数据打包错误")
        }
      }).catch(e => {
        _this.$nextTick(() => { // 以服务的方式调用的 Loading 需要异步关闭
          window.loading.close();
          window.loading = undefined;
        });
        this.$loading.hide()
        handleAlert("error", "系统开小差了...")
      })
    },

    // 所有VIN全显示
    showVin(row) {
      let params = new URLSearchParams()
      params.append('did', row.directoryId)
      params.append('type', row.alwaysShow ? 1 : 0)
      updateShow(params).then(res => {
        if (res.data.code != 100) {
          handleAlert('error', '操作失败')
        }
      })
    },


    // 获取总成下的配件
    getPartsList(directoryId) {
      let params = {
        directoryId: directoryId
      }
      getPartsLists(params).then(res => {
        this.partsList = res.data.data.partsBasics
        this.directoryCode = res.data.data.partsDirectory.code
        this.catalogList.svgPath = res.data.data.partsDirectory.svgimgupload

      })
    },


    // 删除
    del() {
      let list = this.$refs.tree.getCheckedKeys()
      if (!list || list.length <= 0) {
        handleAlert('error', '请选择要删除的目录')
        return false
      }


      this.$confirm('目录删除就找不回来了哦，您确定要删除吗？<br> <span style="color: red;">* 如果目录下存在数据不会被删除</span>', '删除目录', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        dangerouslyUseHTMLString: true,
        type: 'warning'
      }).then(() => {

        var params = new URLSearchParams()
        params.append('ids', list.toString())
        catalogDel(params).then(res => {

          if (res.data.code === 100) {

            if (res.data.data) {
              this.$alert(res.data.data, '信息提示', { dangerouslyUseHTMLString: true })
            } else {
              handleAlert('success', '操作成功')
            }

            this.handleNodeClick(this.manualList)
          } else {
            handleAlert('error', '操作失败，' + res.data.msg)
          }
        }).catch((error) => {

          handleAlert('error', '系统开小差了，刷新一下吧')
        })
      })
    },

    // 显示SVG图
    showSvg() {
      this.sysTitle = this.catalogList.name

      loadSvg()
      let svgPath = this.catalogList.svgPath;
      setTimeout(function () {
        loadSvg(svgPath)
      }, 0);
      this.dialogBigImageVisible = true;
    },

    // 下载SVG图
    down() {

      downSvg(this.catalogList.svgPath).then(res => {
        if (!res.data) {
          return
        }
        var name = this.catalogList.name + ".svg";
        var blob = new Blob([res.data]);
        var url = window.URL.createObjectURL(blob);
        var aLink = document.createElement("a");
        aLink.style.display = "none";
        aLink.href = url;
        aLink.setAttribute("download", name);
        document.body.appendChild(aLink);
        aLink.click();
        document.body.removeChild(aLink); //下载完成移除元素
        window.URL.revokeObjectURL(url); //释放掉blob对象
      })
    },

    // 替换
    replace() {
      this.dialoguploadVisible = true;
      this.imgList = []
      this.isFlag = true;
    },

    updateImageInfo() {
      if (!this.newpath || this.newpath == '') {
        handleAlert("请上传图片");
        return false;
      }

      let path = this.newpath.substring(0, this.newpath.lastIndexOf(".") + 1) + "svg"
      let params = new URLSearchParams()
      params.append('id', this.catalogList.link)
      params.append('path', path)
      params.append('fileName', this.imgList[0].fileName)
      uploadSvg(params).then(res => {
        setTimeout(() => {
          this.catalogList.svgPath = res.data.data.path
          this.showSvg()
          this.dialoguploadVisible = false
        });
      })
    },

    // 上传SVG图 - 成功
    handleOnSuccess(res, obj) {
      this.newpath = res.data.fileUrl

      this.imgList = []
      var img = { url: cmsServerUrl + 'sys/upload/display?filePath=' + this.newpath }
      this.imgList.push(img)
      this.isFlag = true;
    },
    // 上传SVG图 - 文件移除时的钩子
    handleOnRemove(file, fileList) {
      this.imgList = []
      this.isFlag = true;
    },
    // 上传SVG图 - 文件移除前的钩子
    beforeOnRemove(file, fileList) {
      if (this.isFlag) {
        return this.$confirm(`确定移除选择文件？`, '删除', { type: 'warning' });
      }
    },
    // 上传SVG图 - 文件上传前的钩子
    beforeAvatarUpload(file) {
      this.uploadUrl = cmsServerUrl + 'sys/upload/attach?flag=temp/partsDirectory/' + this.catalogList.link + "/svg"
      const suffix = file.name.substring(file.name.lastIndexOf(".") + 1).toLowerCase();
      const extension5 = suffix === 'svg'
      const isLt2M = file.size / 1024 / 1024 < 50
      if (!extension5) {
        handleAlert('warning', '上传图片只能是 svg 格式!')
        this.isFlag = false;
        return false;
      }
      if (!isLt2M) {
        handleAlert('warning', '上传图片大小不能超过 50MB!')
        this.isFlag = false;
        return false;
      }
    },
    uploadAttach(param) {
      var _this = this
      var formData = new FormData();
      formData.append('file', param.file);
      formData.append('flag', "temp/partsDirectory/" + this.catalogList.link + "/svg");
      importAttach(formData).then(res => {
        if (res.data.code === 100) {
          _this.newpath = res.data.data.fileUrl
          _this.imgList = []
          var img = { url: cmsServerUrl + 'sys/upload/display?filePath=' + this.newpath, "fileName": res.data.data.fileName }
          _this.imgList.push(img)
          _this.isFlag = true;
        }
      }).catch(function (error) {
      })
    },
    // 上传SVG图 - 文件数量限制
    handleOnExceed(files, fileList) {
      handleAlert('warning', `当前限制选择1张图片，本次选择了 ${files.length} 张图片，共选择了 ${files.length + fileList.length} 张图片`)
    },



    // 导入, 分为追加和覆盖
    importFile(type) {
      // console.log("-=-", type);
      if (!this.manualList.id) {
        handleAlert("warning", "请选择年款")
        return false;
      }
      this.dialogFileVisible = true;
      this.isParts = type == '2'
      this.accept = this.isParts ? '.xls, .xlsx' : '.zip'
      this.uploadTitle = this.isParts ? '导入配件' : '导入目录'
      // 进度条
      this.showUploadProcess = false  // 显示进度条
      this.percentage = 0  // 进度条
      // this.accept= '.zip'
      this.isfinish = true
      // 分片上传
      this.fileList = []
      this.zipList = []
      //切片文件
      this.fileShard = {}
      //当前文件
      this.curFile = {}
      //文件分割的开始位置
      this.start = 0
      //文件分割的结束位置
      this.end = 0
      //文件大小
      this.fileSize = 0
      this.fileKey = ''
      this.fileShardIndex = 0
      this.fileShardTotal = 0
      this.fileShardSize = 0
      this.switchC = false
      this.showUploadBtn = false
      this.flagType = 'temp/catalog'
    },
    // 下载模板
    template(type) {

      if (type == "1") {
        // 下载目录模板
        catalogTemplate().then(res => {
          if (!res.data) {
            handleAlert("error", "下载失败")
            return
          }
          let name = "目录导入模板.zip";
          let blob = new Blob([res.data]);
          let url = window.URL.createObjectURL(blob);
          let aLink = document.createElement("a");
          aLink.style.display = "none";
          aLink.href = url;
          aLink.setAttribute("download", name);
          document.body.appendChild(aLink);
          aLink.click();
          document.body.removeChild(aLink); //下载完成移除元素
          window.URL.revokeObjectURL(url); //释放掉blob对象
        })
      } else {
        // 下载配件模板
        partsTemplate().then(res => {
          if (!res.data) {
            handleAlert("error", "下载失败")
            return
          }
          let name = "配件导入模板.xlsx";
          let blob = new Blob([res.data]);
          let url = window.URL.createObjectURL(blob);
          let aLink = document.createElement("a");
          aLink.style.display = "none";
          aLink.href = url;
          aLink.setAttribute("download", name);
          document.body.appendChild(aLink);
          aLink.click();
          document.body.removeChild(aLink); //下载完成移除元素
          window.URL.revokeObjectURL(url); //释放掉blob对象
        })
      }
    },

    // 提交文件
    uploadData() {
      if (this.zipList == null || this.zipList == undefined || this.zipList[0] == null || this.zipList[0] == undefined) {
        handleAlert('error', '请上传文件');
        return;
      }
      let uploadFileName = this.zipList[0].name
      let uploadFilePath = this.zipList[0].filePath
      if (uploadFilePath == null || uploadFilePath == undefined || uploadFilePath.length <= 0) {
        handleAlert('error', '请上传文件');
        return;
      }
      var params = new URLSearchParams()
      params.append('yearId', this.manualList.id)
      params.append('fileName', uploadFileName)
      params.append('filePath', uploadFilePath)
      this.$loading.show();
      if (this.isParts) {
        // 导入配件
        importParts(params).then(res => {
          if (res.data.code == 100) {
            handleAlert('success', "正在解析，点击上传进度，查看解析情况")
            this.dialogFileVisible = false;
          } else {
            handleAlert('error', "解析失败" + res.data.msg)
          }
          this.$loading.hide();
        })
      } else {
        // 导入目录
        params.append('type', this.isAppend)
        importCatalog(params).then(res => {
          if (res.data.code == 100) {
            handleAlert('success', "正在解析，点击上传进度，查看解析情况")
            this.dialogFileVisible = false;
          } else {
            handleAlert('error', "解析失败：" + res.data.msg)
          }
          this.$loading.hide();
        })
      }
    },

    // 清空数据
    emptyPartsData() {
      this.$confirm('数据清空就找不回来了哦，您确定要清空吗？', '清空【' + this.catalogList.name + '】配件', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var params = new URLSearchParams()
        params.append('directoryId', this.catalogList.link)
        this.$loading.show();
        catalogClearAll(params).then(res => {
          if (res.data.code === 100) {
            handleAlert('success', '操作成功')
            this.partsList = []
          } else {
            handleAlert('error', '操作失败，' + res.data.msg)
          }
          this.$loading.hide();
        }).catch((error) => {
          this.$loading.hide();
          handleAlert('error', '系统开小差了，刷新一下吧')
        })
      })
    },


    // ======================= 分片上传 ==================

    // 上传前的校验
    onBeforeUpload(file) {
      let text = ""
      if (this.zipList.length > 0) {
        text = "当前限制选择1个文件";
        this.$message.error(text)
        return false
      }

      this.flagType = this.isParts ? 'temp/parts' : 'temp/catalog'
      // 获取文件后缀
      var fileExt = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase();
      if (this.isParts) {
        // 文件后缀是否是 zip
        const zipExt = (fileExt === 'xls' || fileExt === 'xlsx')
        if (!zipExt) {
          text = "上传文件只能是 .xls、.xlsx 格式!";
          this.$message.error(text)
          return false;
        }
      } else {
        // 文件后缀是否是 zip
        const zipExt = fileExt === 'zip'
        if (!zipExt) {
          text = "上传文件只能是 zip 格式!";
          this.$message.error(text)
          return false;
        }
      }

      // 文件大小不能超过1G
      const isLimit = file.size / 1024 / 1024 < 1024

      if (!isLimit) {
        text = "上传文件大小不能超过 1GB!";
        this.$message.error(text)
        return false;
      }
      this.fileShardSize = 1 * 1024 * 1024; //每片文件大小
      this.isfinish = false;
      //点击后隐藏上传按钮 ，防止重复点击
      this.showUploadBtn = false
      this.showUploadProcess = true
      this.percentage = 1
      var _this = this
      getmd5(file, _this.fileShardSize).then(e => {
        _this.switchC = false;
        _this.fileShardIndex = 1;//分片索引
        _this.curFile = file;
        _this.fileKey = e;
        _this.fileSize = file.size;
        _this.fileShardTotal = Math.ceil(file.size / _this.fileShardSize);//分片总数
        var fileFullName = file.name;
        _this.fileSuffix = fileFullName.substr(fileFullName.lastIndexOf('.') + 1);
        _this.fileName = fileFullName.substr(0, fileFullName.lastIndexOf('.'));

        //上传参数
        var params = new FormData()
        params.append('fileName', _this.fileName)
        params.append('fileShardTotal', _this.fileShardTotal)
        params.append('fileKey', _this.fileKey)
        params.append('fileSuffix', _this.fileSuffix)
        params.append('fileShardSize', _this.fileShardSize)
        params.append('fileSize', _this.fileSize)
        params.append('fileFlag', _this.flagType)

        _this.updateProgress(file, params)

      })
    },
    // 批量上传
    uploadFile(formData) {
      var _this = this
      // 上传
      procSplitFile(formData).then(res => {
        if (res.data.code == 200) {
          //上传分片完成
          if (res.data.shardIndex < _this.fileShardTotal) {
            _this.fileShardIndex = _this.fileShardIndex + 1;
            _this.start = (_this.fileShardIndex - 1) * _this.fileShardSize;
            _this.end = Math.min(_this.curFile.size, _this.start + _this.fileShardSize);
            _this.fileSize = _this.curFile.size;
            var params = new FormData()
            params.append('fileName', _this.fileName)
            params.append('fileShardTotal', _this.fileShardTotal)
            params.append('fileKey', _this.fileKey)
            params.append('fileSuffix', _this.fileSuffix)
            params.append('fileShardSize', _this.fileShardSize)
            params.append('fileSize', _this.fileSize)
            params.append('fileFlag', _this.flagType)
            params.append('fileShardIndex', _this.fileShardIndex)
            var fileShardtem = _this.curFile.slice(_this.start, _this.end);//从文件中获取当前分片数据
            let fileReader = new FileReader();
            //异步读取本地文件分片数据并转化为base64字符串
            fileReader.readAsDataURL(fileShardtem);
            //本地异步读取成功后，进行上传
            fileReader.onload = function (e) {
              let base64str = e.target.result;
              params.append('base64', base64str)
              _this.uploadFile(params)
            }
            let perentNum = Math.ceil(this.fileShardIndex * 100 / this.fileShardTotal)
            if (perentNum > 100) {
              this.percentage = 100
            } else {
              this.percentage = perentNum
            }
          }
        } else if (res.data.code == 100) {
          var fileId = res.data.id
          //上传完成
          _this.percentage = 100
          _this.switchC = true

          _this.finishUpload(fileId)
        }

      }).catch((error) => {
        if (error.response) {
          console.log(error.response.data)
          console.log(error.response.status)
          console.log(error.response.headers)
        } else {
          console.log(error.message)
        }
      })

    },
    updateProgress(file, params) {
      var _this = this
      var param = new URLSearchParams()
      param.append('shardKey', _this.fileKey)
      // 批量上传
      checkUploadProgress(param).then(res => {
        if (res.data.code == 200) {
          //新文件
          _this.start = (_this.fileShardIndex - 1) * _this.fileShardSize;
          _this.end = Math.min(file.size, _this.start + _this.fileShardSize);
          _this.fileShard = file.slice(_this.start, _this.end);//从文件中获取当前分片数据
        } else if (res.data.code == 220) {
          _this.fileShardIndex = res.data.ShardIndex;
          //有上传未完成的
          _this.start = (_this.fileShardIndex - 1) * _this.fileShardSize;
          _this.end = Math.min(file.size, _this.start + _this.fileShardSize);
          _this.fileShard = file.slice(_this.start, _this.end);//从文件中获取当前分片数据
        } else if (res.data.code == 240) {
          //急速上传
          var fileId = res.data.id
          _this.percentage = 100
          _this.switchC = true

          _this.finishUpload(fileId)
          return false;
        }
        //读取base64str
        let fileReader = new FileReader();
        //异步读取本地文件分片并转化为base64字符串
        fileReader.readAsDataURL(_this.fileShard);
        //本地异步读取成功，进行上传
        fileReader.onload = function (e) {
          let base64str = e.target.result;
          params.append('base64', base64str)
          params.append('fileShardIndex', _this.fileShardIndex)
          if (_this.switchC == false) {
            _this.uploadFile(params)
          }
        }
      }).catch((error) => {
        this.$message.error('上传错误')
      })

    },
    // 上传完成
    finishUpload(fileId) {
      var _this = this
      //进行保存提醒
      _this.uploadMsg = _this.$message({
        duration: 0,
        message: "请稍等，正在保存...",
        type: "warning"
      });
      var param = new URLSearchParams()
      param.append('trainId', this.projectList.id)
      param.append('yearId', this.manualList.id)
      param.append('fileId', fileId)
      catalogFinishUpload(param).then(res => {
        //关闭消息提醒
        _this.uploadMsg.close()
        if (res.data.code == 100) {
          let uploadFileName = res.data.data.fileName
          let uploadFilePath = res.data.data.filePath
          _this.zipList = []
          if (uploadFileName != null && uploadFileName.length > 0) {
            var fileObj = { name: uploadFileName, filePath: uploadFilePath }
            _this.zipList.push(fileObj)
          }
          //上传完成提示
          _this.$message({
            duration: 2000,
            message: '上传已完成',
            type: 'success'
          })
          _this.showUploadProcess = false
          _this.showUploadBtn = false
        }
      })

    },
    // 文件移除
    handleBeforeRemove() {
      if (this.zipList != null && this.zipList.length > 0) {
        // var filePath= this.zipList[0].filePath;
        var _this = this
        this.$confirm('确认删除文件？', '删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          _this.zipList = [];
          _this.showUploadBtn = true;
          _this.isfinish = true;
        }).catch((error) => {
          handleAlert('info', '取消删除')
          return false;
        })
      }
      return false;
    },


    // 上传进度-复制结果
    copyResult(row) {
      const input = document.createElement('input')
      input.style.position = 'absolute'
      input.style.left = '-9999px'
      input.style.top = '-9999px'
      document.body.appendChild(input)
      input.value = row.result
      input.select()
      document.execCommand('copy')

      document.body.removeChild(input)
      handleAlert('success', '结果已复制')
    },
    // 上传进度-删除
    deleteProgress(row) {
      this.$confirm('上传记录删除就找不回来了哦，您确定要删除吗', '删除上传记录', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var params = new URLSearchParams()
        params.append('id', row.id)
        delProgress(params).then(res => {
          if (res.data.code === 100) {
            handleAlert('success', '操作成功')
            this.progress();
          } else {
            handleAlert('error', '操作失败，' + res.data.msg)
          }
        }).catch((error) => {
          handleAlert('error', '系统开小差了，刷新一下吧')
        })
      })
    },

    areaSize() {
      setTimeout(() => {
        let butHeight = ""
        if ($(".taskCenter").css("display") == "none") {
          butHeight = '0'
        } else {
          butHeight = $('.taskCenter').outerHeight(true)
        }
        let distance = $('.leftData').outerHeight(true) - butHeight;
        $(".taskManualList").css('height', distance)
        window.addEventListener("resize", function () {
          let butHeight = ""
          if ($(".taskCenter").css("display") == "none") {
            butHeight = '0'
          } else {
            butHeight = $('.taskCenter').outerHeight(true)
          }
          let distance = $('.leftData').outerHeight(true) - butHeight;
          $(".taskManualList").css('height', distance)
        })
      })
    },


    wipeData() {
      this.$confirm('数据清空就找不回来了哦，您确定要清空吗？', '清空【' + this.manualList.name + '】数据', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var param = new URLSearchParams()
        param.append('yearId', this.manualList.id)
        console.log("年款ID", this.manualList.id);
        emptyData(param).then(res => {
          if (res.data.code == 100) {
            handleAlert("success", "清空成功")
            this.handleNodeClick(this.manualList)
          } else {
            handleAlert("error", res.data.msg)
          }
        }).catch(e => {
          handleAlert("error", "系统繁忙，请稍后再试")
        })
      })
    },

    // 获取语言
    getLangeList() {
      catalogLangeList().then(res => {
        this.langeList = res.data.data
      })
    },

    // 2024-03-09 可以编辑配件
    handelLook(row) {
      this.partsAddFlag = false
      row.modelIds = []
      this.models = []
      this.partsEntry = Object.assign({}, row)
      let params = new URLSearchParams()
      params.append('partsCode', row.partsCode)
      params.append('directoryId', this.catalogList.link)
      findRelModel(params).then(res => {
        let map = res.data.data;
        this.partsEntry.modelIds = map.partsCode;
        let list = map.directory
        for (let i = 0; i < this.modelList.length; i++) {
          if (list.includes(this.modelList[i].id)) {
            this.models.push(this.modelList[i]);
          }
        }
        this.dialogPartsEntryFormVisible = true;
      })
    },

    // 新增
    partsAdd() {
      this.partsAddFlag = true;
      this.partsEntry = {
        // 配件编码
        partsCode: "",
        // 配件名称
        cnName: "",
        // 长度
        length: 0.00,
        // 宽度
        width: 0.00,
        // 高度
        height: 0.00,
        // 最小起订量
        minOrder: 1,
        // 厂家
        factory: "",
        // 材料
        material: "",
        // 作用
        role: "",
        // 重量
        partsWeight: 0.00,
        // 库存
        partsInventory: 0,
        // 关联的ID
        directoryId: "",
        basicDirectoryId: '',
        // 备注
        directoryRemark: '',
        // 序号
        partsSign: "",
        // 用量
        useCount: 1,
        // 适用车型
        modelIds: [],
        sltModel: '',
      }
      let params = new URLSearchParams()
      params.append('partsCode', "")
      params.append('directoryId', this.catalogList.link)
      this.models = []
      findRelModel(params).then(res => {
        let list = res.data.data.directory;
        for (let i = 0; i < this.modelList.length; i++) {
          const t = this.modelList[i];
          if (list.includes(t.id)) {
            this.models.push(t)
          }
        }
        this.dialogPartsEntryFormVisible = true;
      })
    },

    // 查看适用车型配置
    showModel() {
      let params = new URLSearchParams()
      params.append('partsCode', "")
      params.append('directoryId', this.catalogList.link)
      this.models = []
      this.dialogDirectoryFormVisible = true;
      this.titleDirectory = this.catalogList.name
      findRelModel(params).then(res => {
        let list = res.data.data.directory;
        this.models = list
        this.$refs.applytable.clearSelection();
        for (let i = 0; i < this.modelList.length; i++) {
          const el = this.modelList[i];
          if (list.includes(el.id)) {
            this.$refs.applytable.toggleRowSelection(el, true)
          }
        }

      })
    },

    // 保存
    saveData() {

      if (!this.partsEntry.partsCode) {
        handleAlert('error', '配件编码不能为空');
        return;
      }
      if (!this.partsEntry.cnName) {
        handleAlert('error', '配件名称不能为空');
        return;
      }
      if (!this.partsEntry.partsSign) {
        handleAlert('error', '序号不能为空');
        return;
      }
      if (!this.partsEntry.useCount) {
        handleAlert('error', '装车用量不能为空');
        return;
      }
      if (!this.partsEntry.modelIds || this.partsEntry.modelIds.length <= 0) {
        handleAlert('error', '适用车型不能为空');
        return;
      }

      this.partsEntry.basicDirectoryId = this.partsEntry.directoryId
      this.partsEntry.directoryId = this.catalogList.link
      this.partsEntry.sltModel = this.partsEntry.modelIds.toString()
      this.$loading.show()
      catalogSaveParts(this.partsEntry).then(res => {
        if (res.data.code == 100) {
          this.dialogPartsEntryFormVisible = false;
          handleAlert('success', '操作成功')
          this.getPartsList(this.catalogList.link);
        } else {
          _this.$alert(res.data.msg, '信息提示', { dangerouslyUseHTMLString: true })

        }
        this.$loading.hide();
      }).catch(e => {
        this.$loading.hide();
        _this.$alert(res.data.msg, '信息提示', { dangerouslyUseHTMLString: true })
      })
    },

    // 删除
    deleteData() {
      let _this = this
      _this.$confirm('确定删除【' + _this.partsEntry.partsCode + '】的数据吗?', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        _this.$loading.show()
        catalogDelParts(_this.partsEntry.directoryId).then(res => {
          if (res.data.code === 100) {
            handleAlert('success', '删除成功')
            _this.getPartsList(this.catalogList.link);
            this.dialogPartsEntryFormVisible = false;
          } else {
            _this.$alert(res.data.msg, '信息提示', { dangerouslyUseHTMLString: true })
          }
          _this.$loading.hide()
        }).catch(e => {
          _this.$alert('系统出现异常，导入失败', '信息提示', { dangerouslyUseHTMLString: true })
          _this.$loading.hide()
        })
      }).catch((error) => {
        _this.$loading.hide()
      })
    },

    // 选中的配置
    handleSelectionChange(val) {
      this.selectModel = val
      this.models = []
      for (let i = 0; i < this.selectModel.length; i++) {
        const m = this.selectModel[i];
        this.models.push(m.id)
      }

    },
    // 修改总成的适用车型配置 确定
    getCheckedKeys() {
      if (this.models.length <= 0) {
        handleAlert('error', "适用配置不能为空");
        return;
      }
      let params = new URLSearchParams()
      params.append('modelIds', this.models.toString())
      params.append('directoryId', this.catalogList.link)

      updateDirectoryModel(params).then(res => {
        if (res.data.code === 100) {
          handleAlert('success', '修改成功')
          this.dialogDirectoryFormVisible = false;
        } else {
          _this.$alert(res.data.msg, '信息提示', { dangerouslyUseHTMLString: true })
        }
      })
    },

    // 配件搜索
    partsCodeSearch() {
      this.dialogPratsSearchVisible = true;
      this.formInline.code = '';
      this.onPratsSubmit();
    },

    onPratsSubmit() {
      this.pratsCurrentPage = 1;
      this.pratsPagesize = 10;
      this.pratsTotal = 0;
      this.partsSearch();
    },

    partsSearch() {
      let params = new URLSearchParams()
      params.append('page', this.pratsCurrentPage)
      params.append('limit', this.pratsPagesize)
      params.append('partsCode', this.formInline.code)
      accessoriesSearch(params).then(res => {
        this.pratsTotal = res.data.total
        this.pratsList = res.data.data
      })
    },
    // 修改备注
    updatedRemark(row) {
      this.partsObject = row;
      this.dialogUpdatedRemarkFormVisible = true;
    },
    saveRemark() {
      let obj = {
        basicDirectoryId: this.partsObject.basicDirectoryId,
        directoryRemark: this.partsObject.remark,
      }
      catalogSaveParts(obj).then(res => {
        if (res.data.code == 100) {
          this.dialogUpdatedRemarkFormVisible = false;
          handleAlert('success', '操作成功')
        } else {
          _this.$alert(res.data.msg, '信息提示', { dangerouslyUseHTMLString: true })

        }
      }).catch(e => {
        _this.$alert(res.data.msg, '信息提示', { dangerouslyUseHTMLString: true })
      })
    }

  },
  mounted() {
    contentSize();
    this.dataList();
    this.areaSize();
    this.getLangeList();
    this.uploadUrl = cmsServerUrl + 'sys/upload/attach?flag=temp/partsDirectory/svg'
  },
}
</script>
<style>
.detailInfo {
  padding: 10px 15px;
  font-size: 15px;
  height: calc(100% - 72px);
  overflow-y: auto
}

.detailInfo div {
  margin-bottom: 15px;
  display: flex;
}

.detailInfo div>span:first-child {
  color: #bababa;
  margin-right: 10px;
}

.detailInfo div>span:first-child>span {
  display: inline-block;
  width: 100px;
  white-space: nowrap;
  text-align-last: justify;
  text-align: justify;
}

.detailInfo div>span:last-child {
  display: inline-block;
  flex: 1;
  color: #000;
  align-items: center;
}

.taskdetail .el-tree>.el-tree-node>.el-tree-node__children>.el-tree-node>.el-tree-node__content>.custom-tree-node,
.el-tree>.el-tree-node>.el-tree-node__children>.el-tree-node>.el-tree-node__content>.custom-tree-node,
.taskdetail .el-tree>.el-tree-node>.el-tree-node__children>.el-tree-node .el-tree-node>.el-tree-node__content>.custom-tree-node {
  text-decoration: underline
}

.el-checkbox__inner {
  width: 16px;
  height: 16px;
}

.taskContent .el-dialog .el-tree .el-tree-node .el-checkbox .el-checkbox__inner {
  display: inline-block;
}

.taskContent .el-dialog .el-tree .el-tree-node__content {
  margin: 6px 0;
}

.taskContent .powerDetail {
  overflow: auto;
}

.manualList {
  overflow-x: hidden;
  overflow-y: auto;
}

.detailInfo .brandArea,
.detailInfo .projectArea,
.detailInfo .manualArea,
.detailInfo .catalogArea {
  display: none
}

.catalogArea .el-textarea__inner {
  margin-bottom: 0px !important;
}

.wrapper {
  display: flex;
  flex-direction: column;
}

/* .body{
    flex:auto;
  } */
.fileImg {
  width: 100%;
  height: 100%;
}

.el-tooltip__popper {
  max-width: 800px;
}

#el_progress1 .el-progress-bar__innerText {
  color: #ffffff;
}
</style>
<!-- 配件表格 -->
<style id="table_style">
.parts_table .el-table th.el-table__cell.is-leaf {
  /* background-color:#EBF0F4; */
}

.parts_table div {
  margin: 0;
  display: block;
}

.parts_table .is-active span {
  color: #409EFF;
}


.parts_table .el-table tbody tr:hover>td {
  background-color: #F0F3F5 !important;
}


.el-message-box__message {
  overflow-x: auto;
  max-height: 500px;
}

.el-dialog .inputItemStyle .el-input {
  width: 94% !important;
}
</style>
