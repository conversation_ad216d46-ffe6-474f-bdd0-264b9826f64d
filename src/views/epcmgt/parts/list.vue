<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" ref="formInline" label-width="65px" :model="formInline" class="demo-form-inline">
        <el-form-item label="车型" prop="carTrainId">
            <el-select v-model="formInline.carTrainId" placeholder="请选择车型" @change="getTrainYearList" clearable filterable>
            <el-option-group v-for="group in trainList" :key="group.id" :label="group.nameCh">
              <el-option v-for="item in group.children" :key="item.id" :label="item.nameCh" :value="item.id"></el-option>
            </el-option-group>
          </el-select>
          </el-form-item>
        <el-form-item label="年款" prop="modelYear">
            <el-select v-model="formInline.modelYear" clearable filterable @change="getModelList">
              <el-option v-for="(item,index) in yearList" :key="index" :label="item" :value="item"></el-option>
            </el-select>
          </el-form-item>
        <el-form-item label="配置" prop="carModelId">
            <el-select v-model="formInline.carModelId" clearable filterable>
              <el-option v-for="(item,index) in modelList" :key="index" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>

        <el-form-item label="配件名称" prop="cnName">
          <el-input v-model.trim="formInline.cnName" placeholder="请输入配件名称"></el-input>
        </el-form-item>
        <el-form-item label="配件编码" prop="partsCodeReload">
          <el-input v-model.trim="formInline.partsCodeReload" placeholder="请输入配件编码"></el-input>
        </el-form-item>
        <el-form-item >
          <el-button type="primary" @click="onSubmit" icon="el-icon-search">搜索</el-button>
          <el-button plain @click="reset('formInline')">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <div class="tableHandle" >
        <!-- <el-button type="text" v-if="hasPerm('menuAsimss5A3B_101')" icon="el-icon-download" @click="downTemplateClick()">下载实物图模板</el-button>
        <el-button type="text" v-if="hasPerm('menuAsimss5A3B_101')" icon="el-icon-upload2" @click="batchupload()">上传实物图</el-button> -->
        <el-dropdown v-if="hasPerm('menuAsimss5A3B_107')" placement="bottom">
          <el-button  type="text" size="small" style="font-size: 14px;" icon="el-icon-download">下载模板</el-button>&nbsp;&nbsp;&nbsp;
          <el-dropdown-menu slot="dropdown" style="margin-top: -3px">
            <el-dropdown-item  @click.native="downTemplateClick()">下载实物图模板</el-dropdown-item>
            <el-dropdown-item  @click.native="downInfoClick()">下载配件明细模板</el-dropdown-item>
            <el-dropdown-item  @click.native="downUpdateInfoClick()">下载修改配件模板</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <!-- 导入 -->
        <el-dropdown v-if="hasPerm('menuAsimss5A3B_107')">
          <el-button  type="text" size="small" style="font-size: 14px;" icon="el-icon-upload2">导入数据</el-button>&nbsp;&nbsp;&nbsp;
          <el-dropdown-menu slot="dropdown" style="margin-top: -3px">
            <el-dropdown-item  @click.native="batchupload()" >导入实物图</el-dropdown-item>
            <el-upload id="improt_info"
          class="upload-demo inline-block"
          style="display:block;"
          ref="elUpload"
          action="#"
          :show-file-list="false"
          multiple
          :limit="1"
          :file-list="infoList"
          :before-upload="infoUpload"
          :http-request="importInfoClick"
          :on-change="infoChange"
        >
            <el-dropdown-item >导入配件明细</el-dropdown-item>
          </el-upload>
          <el-upload id="update_info"
              class="upload-demo inline-block"
              style="display:block;margin: 0;"
              ref="elUpload"
              action="#"
              :show-file-list="false"
              multiple
              :limit="1"
              :file-list="infoList"
              :before-upload="infoUpload"
              :http-request="updateInfoClick"
              :on-change="infoChange"
              >
              <el-dropdown-item >修改配件名称</el-dropdown-item>
            </el-upload>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button type="text" v-if="hasPerm('menuAsimss5A3B_107')" @click="progress()"><i class="el-icon-odometer" style="color: #409EFF;"></i>上传进度</el-button>
        <el-dropdown v-if="hasPerm('menuAsimss5A3B_107')" placement="bottom">
                <el-button  type="text" size="small" style="font-size: 14px;" icon="el-icon-download">导出数据</el-button>&nbsp;&nbsp;&nbsp;
                <el-dropdown-menu slot="dropdown" style="margin-top: -3px">
                  <el-dropdown-item  @click.native="exportClick('image')">导出实物图</el-dropdown-item>
                  <el-dropdown-item  @click.native="exportClick('moq')">导出最小起订量</el-dropdown-item>
                  <el-dropdown-item  @click.native="exportClick('price')">导出价格</el-dropdown-item>
                  <el-dropdown-item  @click.native="exportClick('info')">导出明细</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
      </div>
      <!-- 分页显示列表 -->
      <!-- <el-table style="width:100%" border :data="resultList" :header-cell-style="{'text-align':'center'}" @selection-change="handleSelectionChange"> -->
      <el-table
        style="width:100%"
        border
        stripe
        ref="table"
        highlight-current-row
        :max-height="maximumHeight"
        :data="resultList"
        :header-cell-style="{
          'text-align': 'center',
          'background-color': 'var(--other-color)',
        }"
        @header-dragend="changeColWidth"
      >
        <el-table-column label="序号" type="index" width="60"  align="center"></el-table-column>
        <el-table-column label="配件名称" prop="cnName" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column label="配件编码" prop="partsCode" min-width="100" show-overflow-tooltip></el-table-column>

        <el-table-column label="长度" align="center" prop="length" width="100"></el-table-column>
        <el-table-column label="宽度" align="center" prop="width" width="100"></el-table-column>
        <el-table-column label="高度" align="center" prop="height" width="100"></el-table-column>
        <el-table-column label="重量" align="center" prop="partsWeight" width="100"></el-table-column>
        <el-table-column label="最小起订量" align="center" prop="minOrder" width="100"></el-table-column>

        <el-table-column label="替换关系" align="center" prop="isChang" width="100">
          <template slot-scope="{row}">
            <el-button type="text" v-if="row.isChang ==1" @click="showReplacement(row)">查看</el-button>
            <span v-else>无</span>
          </template>
        </el-table-column>
        <el-table-column label="实物图" align="center" prop="isImage" width="100">
          <template slot-scope="{row}">
            <el-button type="text" v-if="row.isImage ==1" @click="showImages(row)">查看</el-button>
            <span v-else>无</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" fixed="right" width="230">
          <template slot-scope="{row}">
            <el-button type="text" v-if="hasPerm('menuAsimss5A3B_104')" size="small" @click="handelDetail(row)">适用车型配置</el-button>
            <el-button type="text" v-if="hasPerm('menuAsimss5A3B_104')" size="small" @click="handelLook(row)">查看</el-button>
            <el-button type="text" v-if="hasPerm('menuAsimss5A3B_103')" size="small" @click="handelEdit(row)">编辑</el-button>
            <el-button type="text" v-if="hasPerm('menuAsimss5A3B_102')" size="small" @click="handelDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total>0" :total="total" :page.sync="currentPage" :limit.sync="pagesize" @pagination="dataList"/>


      <el-dialog  v-dialogDrag width="850px !important" :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" :close-on-click-modal="false">
        <div v-if="dialogStatus === 'detail'" >
          <!-- 查看 -->
          <table width="100%" class="tabtop13">

          <tr>
            <td class="tdTitle" width="15%">配件名称</td>
            <td width="35%">{{ temp.cnName }}</td>
            <td class="tdTitle" width="15%">配件编码</td>
            <td width="35%">{{ temp.partsCode }}</td>
          </tr>
          <tr>
            <td class="tdTitle" width="15%">最小起订量</td>
            <td width="35%">{{ temp.minOrder }}</td>
            <td class="tdTitle" width="15%">长度(mm)</td>
            <td width="35%">{{ temp.length }}</td>
          </tr>
          <tr>
            <td class="tdTitle" width="15%">宽度(mm)</td>
            <td width="35%">{{ temp.width }}</td>
            <td class="tdTitle" width="15%">高度(mm)</td>
            <td width="35%">{{ temp.height }}</td>
          </tr>
          <tr>
            <td class="tdTitle" width="15%">库存</td>
            <td width="35%">{{ temp.partsInventory }}</td>
            <td class="tdTitle" width="15%">重量(kg)</td>
            <td width="35%">{{ temp.partsWeight }}</td>
          </tr>
          <tr>
            <td class="tdTitle" width="15%">价格($)</td>
            <td width="85%" colspan="3">
             {{ temp.partsPrice }}
            </td>
          </tr>
          <tr>
            <td class="tdTitle" width="15%">厂家</td>
            <td width="85%" colspan="3">
             {{ temp.factory }}
            </td>
          </tr>
          <tr>
            <td class="tdTitle" width="15%">材料</td>
            <td width="85%" colspan="3">
             {{ temp.material }}
            </td>
          </tr>
          <tr>
            <td class="tdTitle" width="15%">作用</td>
            <td width="85%" colspan="3">
             {{ temp.role }}
            </td>
          </tr>

        </table>
        </div>

        <!-- 添加和修改 -->
        <el-form v-if="dialogStatus === 'add' || dialogStatus === 'edit' " ref='temp' :model="temp" :rules="rules"  label-position="center" :validate-on-rule-change="false">

          <el-row>
            <el-col :span="12">
              <el-form-item label="配件名称" prop="cnName" :label-width="formLabeldetailsWidth">
                <el-input  v-model.trim="temp.cnName" placeholder="请输入配件名称"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="配件编码" prop="partsCode" :label-width="formLabeldetailsWidth">
                <el-input readonly="readonly" disabled v-model.trim="temp.partsCode" placeholder="请输入配件编码"></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="最小起订量" prop="minOrder" :label-width="formLabeldetailsWidth">
                <el-input v-model.trim="temp.minOrder" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}" placeholder="请输入最小起订量"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="长度(mm)" prop="length" :label-width="formLabeldetailsWidth" >
                <el-input v-model.trim="temp.length" oninput="value=value.replace(/[^\d.]/g, '').replace(/\.{2,}/g, '.').replace('.', '$#$').replace(/\./g, '').replace('$#$', '.').replace(/^(\-)*(\d+)\.(\d\d\d).*$/, '$1$2.$3').replace(/^\./g, '')" placeholder="请输入长度"></el-input>
              </el-form-item>
            </el-col>
          </el-row>


          <el-row>
            <el-col :span="12">
              <el-form-item label="宽度(mm)" prop="width" :label-width="formLabeldetailsWidth">
                <el-input v-model.trim="temp.width" oninput="value=value.replace(/[^\d.]/g, '').replace(/\.{2,}/g, '.').replace('.', '$#$').replace(/\./g, '').replace('$#$', '.').replace(/^(\-)*(\d+)\.(\d\d\d).*$/, '$1$2.$3').replace(/^\./g, '')" placeholder="请输入宽度"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="高度(mm)" prop="height" :label-width="formLabeldetailsWidth">
                <el-input v-model.trim="temp.height" oninput="value=value.replace(/[^\d.]/g, '').replace(/\.{2,}/g, '.').replace('.', '$#$').replace(/\./g, '').replace('$#$', '.').replace(/^(\-)*(\d+)\.(\d\d\d).*$/, '$1$2.$3').replace(/^\./g, '')" placeholder="请输入高度"></el-input>
              </el-form-item>
            </el-col>
          </el-row>



          <el-row>
            <el-col :span="12">
              <el-form-item label="库存" prop="partsInventory" :label-width="formLabeldetailsWidth">
                <el-input v-model.trim="temp.partsInventory" oninput="value=value.replace(/[^\d]/g,'')" placeholder="请输入库存"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="重量(kg)" prop="partsWeight" :label-width="formLabeldetailsWidth">
                <el-input v-model.trim="temp.partsWeight" oninput="value=value.replace(/[^\d.]/g, '').replace(/\.{2,}/g, '.').replace('.', '$#$').replace(/\./g, '').replace('$#$', '.').replace(/^(\-)*(\d+)\.(\d\d\d).*$/, '$1$2.$3').replace(/^\./g, '')" placeholder="请输入重量"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="价格($)" prop="partsPrice" :label-width="formLabeldetailsWidth">
                <el-input v-model.trim="temp.partsPrice" oninput="value=value.replace(/[^\d]/g,'')" placeholder="请输入价格，单位：分"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24" class="inputItemStyle">
              <el-form-item label="厂家" prop="factory" :label-width="formLabeldetailsWidth">
                <el-input v-model.trim="temp.factory"  rows="2" show-word-limit maxlength="100" placeholder="请输入厂家"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24" class="inputItemStyle">
              <el-form-item label="材料" prop="material" :label-width="formLabeldetailsWidth">
                <el-input v-model.trim="temp.material"  rows="2" show-word-limit maxlength="100" placeholder="请输入材料"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24" class="inputItemStyle">
              <el-form-item label="作用" prop="role" :label-width="formLabeldetailsWidth">
                <el-input v-model.trim="temp.role"  rows="2" show-word-limit maxlength="500" placeholder="请输入作用"></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <div class="submitArea">
            <el-button type="primary" @click="dialogStatus === 'add' ? addClick('temp') : updateData('temp')">
              立即提交
            </el-button>
          </div>
        </el-form>
      </el-dialog>


      <el-dialog v-dialogDrag width="850px !important" title="适用车型配置" :visible.sync="dialogApplyFormVisible" destroy-on-close v-if="dialogApplyFormVisible" :close-on-click-modal="false">
        <!-- 适用车型 -->
          <div>
            <el-table
              style="width:100%;"
              max-height="400px"
              border
              stripe
              highlight-current-row
              :data="applyModelList"
              :header-cell-style="{
                'text-align': 'center',
                'background-color': 'var(--other-color)',
              }"
            >
              <el-table-column label="序号" type="index" width="60"  align="center"></el-table-column>
              <el-table-column label="品牌" prop="brandName" min-width="100" show-overflow-tooltip></el-table-column>
              <!-- <el-table-column label="国家" prop="countryName" min-width="100" show-overflow-tooltip></el-table-column> -->
              <el-table-column label="车型" prop="trainName" min-width="100" show-overflow-tooltip></el-table-column>
              <el-table-column label="年款" prop="year" min-width="100" show-overflow-tooltip></el-table-column>
              <el-table-column label="配置" prop="nameCh" min-width="100" show-overflow-tooltip></el-table-column>
            </el-table>
          </div>
      </el-dialog>


      <el-dialog v-dialogDrag title="配件替换关系" width="1000px !important" :visible.sync="dialogReplacementFormVisible" :close-on-click-modal="false">
        <el-table
          style="width:100%"
          border
          stripe
          highlight-current-row
          :data="resultReplacementList"
          :header-cell-style="{
            'text-align': 'center',
            'background-color': 'var(--other-color)',
          }"
        >
          <el-table-column label="序号" type="index" width="60"  align="center"></el-table-column>
          <el-table-column label="旧配件编码" prop="originalCode" min-width="150" show-overflow-tooltip></el-table-column>
          <el-table-column label="旧配件名称" prop="originalName" min-width="150" show-overflow-tooltip></el-table-column>
          <el-table-column label="新配件编码" prop="alternateCode" min-width="150" show-overflow-tooltip></el-table-column>
          <el-table-column label="新配件名称" prop="alternateName" min-width="150" show-overflow-tooltip></el-table-column>
          <el-table-column label="替换日期" prop="alternateTime" min-width="150" show-overflow-tooltip></el-table-column>
          <el-table-column label="备注" prop="remark" min-width="150" show-overflow-tooltip></el-table-column>
        </el-table>
      </el-dialog>

      <el-dialog v-dialogDrag :title="imgTitle" width="600px !important"  :visible.sync="dialogImageFormVisible" :close-on-click-modal="false">
        <div style="width: 100%;margin-bottom:15px">
          <el-button icon="el-icon-download" type="text" @click="downImage">下载</el-button>
          <el-button icon="el-icon-delete" type="text" @click="replaceImage">清空</el-button>
        </div>
        <div style="width: 100%;">
          <el-carousel indicator-position="outside" height="400px">
            <el-carousel-item v-for="(item, index) in imagebox" :key="index">
              <img :src="item.path" class="image">
            </el-carousel-item>
          </el-carousel>
        </div>
      </el-dialog>

      <el-dialog v-dialogDrag title="批量导入配件实物图" :visible.sync="dialogImportFormVisible" :close-on-click-modal="false">
        <el-form ref="importfile" :model="importfile" class="demo-form-inline">
          <el-form-item>
            <el-upload
                  class="upload-demo inline-block"
                  ref="elUpload"
                  action="#"
                  :show-file-list="false"
                  multiple
                  :limit="1"
                  :before-upload="onBeforeUpload"
                >
                <el-button type="primary" v-show="showUploadBtn">选择文件</el-button>
                <div slot="tip" class="el-upload__tip" v-if="isfinish">只能上传zip文件</div>

                </el-upload>
                <el-progress style="width:350px;margin-top:50px;margin-left:10px" text-color="#ffffff" v-show="showUploadProcess" color="green" type="line"  :text-inside="true" :stroke-width="20" :percentage="percentage"></el-progress>
                 <el-upload
                  ref="elUploadResult"
                  action="#"
                  :show-file-list="true"
                  :file-list="zipList"
                  :limit="1"
                  :before-remove="handleBeforeRemove"
                  :on-remove="handleRemove"
                >
               </el-upload>
          </el-form-item>
          <!-- <el-form-item> -->
          <el-radio-group v-model="isAppend">
            <el-radio label="1">追加
              <el-tooltip class="item" effect="dark"  placement="top">
                <i class="el-icon-question" style="font-size: 16px; vertical-align: middle;"></i>
                <div slot="content">
                  <p>追加是在原来的数据上添加新的数据，如果相同的数据会修改为新数据</p>
                </div>
              </el-tooltip>
            </el-radio>
            <el-radio label="2">重新上传
              <el-tooltip class="item" effect="dark" placement="top">
                <i class="el-icon-question" style="font-size: 16px; vertical-align: middle;"></i>
                <div slot="content">
                  <p>把数据包中配件原来的实物图全部清除，再添加新的数据</p>
                </div>
              </el-tooltip>
            </el-radio>
          </el-radio-group>
          <!-- </el-form-item> -->
          <div class="submitArea">
            <el-button type="primary" @click="importSubmit">提交</el-button>
            <el-button plain @click="resetimport()">取消</el-button>
          </div>
        </el-form>
      </el-dialog>

      <el-dialog v-dialogDrag title="上传进度" width="850px !important" height="500px" :visible.sync="dialogUploadProgressVisible" :close-on-click-modal="false">
        <el-table
          style="width:100%"
          border
          stripe
          ref="progressTable"
          highlight-current-row
          :data="progressData"
          :header-cell-style="{
            'text-align': 'center',
            'background-color': 'var(--other-color)',
          }"
          @header-dragend="changeColWidth"
        >
          <el-table-column label="序号" type="index" width="60"  align="center"></el-table-column>
          <el-table-column label="操作人" prop="realName" min-width="40" show-overflow-tooltip></el-table-column>
          <el-table-column label="类型" prop="type" width="100">
            <template slot-scope="{row}">
              <span v-if="row.type === 3">导入实物图</span>
              <span v-if="row.type === 4">导入明细</span>
            </template>
          </el-table-column>
          <el-table-column label="结果" prop="result" min-width="100" show-overflow-tooltip></el-table-column>
          <el-table-column label="上传时间" prop="createdTime" width="160" align="center">
            <template slot-scope="{row}">
              <div>
                {{ row.createdTime | conversion("yyyy-MM-dd HH:mm:ss") }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" fixed="right" width="130" align="center">
            <template slot-scope="{row}">
              <el-button type="text" size="small" @click="copyResult(row)">复制结果</el-button>
              <el-button type="text" v-if="hasPerm('menuAsimss5A5B_102')" size="small" @click="deleteProgress(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="progressTotal>0" :total="progressTotal" :page.sync="progressCurrentPage" :limit.sync="progressPagesize" @pagination="progress"/>
      </el-dialog>



    </div>
  </div>
</template>
<script>

import { sysServerUrl, handleAlert, tableHeight, tableMaxHeight } from '@/assets/js/common.js'
// import SelectTree from '@/components/TreeView/SelectTree.vue';
import Pagination from '@/components/Pagination'
import { basicList, basicTemplate, basicEdit, basicDel, basicDels, directoryTreeList, uploadProgress, partsImportInfo, partsInfoTemplate,partsUpdateInfoTemplate,
  directoryTrainYear, basicDirectoryCarModelList, basicFinishUpload, basicreplaceImage, basicdownImage, partsImageTemplate,partsUpdateInfo,
 basicReplaceChain, basicImage, basicImportImage, delProgress, partsExportInfo, downSvg, basicEditPage} from '@/api/epcmgt.js';
import {  procSplitFile, checkUploadProgress } from '@/api/sysmgt.js'

import SparkMD5 from 'spark-md5'

/** 计算文件md5值
 * @param file 文件
 * @param chunkSize 分片大小
 * @returns Promise
 */
function getmd5(file, chunkSize) {
    return new Promise((resolve, reject) => {
        let blobSlice = File.prototype.slice || File.prototype.mozSlice || File.prototype.webkitSlice;
        let chunks = Math.ceil(file.size / chunkSize);
        let currentChunk = 0;
        let spark = new SparkMD5.ArrayBuffer();
        let fileReader = new FileReader();
        fileReader.onload = function(e) {
            spark.append(e.target.result);
            currentChunk++;
            if (currentChunk < chunks) {
                loadNext();
            } else {
                let md5 = spark.end();
                resolve(md5);

            }
        };
        fileReader.onerror = function(e) {
            reject(e);
        };
        function loadNext() {
            let start = currentChunk * chunkSize;
            let end = start + chunkSize;
            if (end > file.size){
                end = file.size;
            }
            fileReader.readAsArrayBuffer(blobSlice.call(file, start, end));
        }
        loadNext();
    });
}


export default {
  name: 'epcmgtpartslist',
  // components: { Pagination ,SelectTree },
  components: { Pagination },
  data () {
    return {
      groupList:[], // 主组数据
      trainList: [],  // 车系数据
      yearList: [],  // 年款数据
      modelList:[],  // 车型数据
      // 车系的数据默认字段
      defaultProps: {
        parent: 'pid',   // 父级唯一标识
        value: 'id',          // 唯一标识
        label: 'nameCh',       // 标签显示
        children: 'children', // 子级
      },

      formInline: {
        carTrainId: '',  // 车系
        modelYear: '',  // 年款
        carModelId: '',  // 车型
        mainGroupListId: '',   // 主组
        cnName: '',  // 配件名称
        partBranchCode: '',  // 分组编码
        partsCodeReload: '', // 配件编码
        alternateTypeReload: '',  // 替换关系
      },

      deliverStatusList:[
        { name: '有货', code: 1 },
        { name: '无货', code: 2 },
      ],
      alternateTypeReloadList:[
        { name: '旧配件', code: 1 },
        { name: '新替换件', code: 2 },
        { name: '无', code: 3 },
      ],

      // 分组数据
      grouping: {
        id: '',   // 主组
        cnName: '',  // 分组名称
        code: '',  // 分组编码
      },

      temp: {
        id: '',   // 主键ID
        directoryId: '',   // 分组(目录)ID
        directoryname: '',  // 分组名称
        cnName: '',  // 配件名称
        code: '',  // 分组编码
        sltModelId: '',  // 适用车型的ID
        sltModelname: '', // 适用车型名称
        carTrainId: '',  // 车系
        carModelYear: '',  // 年款
        carModelId: '',  // 车型
        sort: 1,   // 排序
        basicDirectoryId: '',
        partsSign: '',  // 序号
        partsCode: '', // 配件编码
        useCount: '', // 装车用量
        deliverStatus: '', // 供货状态
        workHour: '', // 工时
        directoryRemark: '',   // 备注
        height: '',  // 高度
        width: '', // 宽度
        length: '', // 长度

        partsMainName: '',  // 主组名称
        directoryName: '', // 分组名称

        minOrder: '',
        partsPrice: '',
        partsInventory: '',
        partsWeight: '',
        factory: '',
        material: '',
        role: '',
      },

      // 上传进度
      dialogUploadProgressVisible: false,
      progressData: [],

      // 适用车型查询
      applyInfo:{
        carTrainId: '',  // 车系
        modelYear: '',  // 年款
      },
      pagesizeGroup: 20,
      currentPageGroup: 1,
      totalGroup: 0,


      applyModelLists:[],
      applyModelList:[],  // 适用车型列表
      selectapplymodelList: [],  // 选中的适用车型
      imgList: [],  // 图片列表
      isFlag: true,
      sltModelYear: '',
      imageurl: '',
      deleteList:[], // 批量选中
      dialogBigImageVisible: false,


      imageupload: [],

      dialogStatus: '',
      editStatus:false,
      textMap: {
        detail: '配件明细',
        edit: '编辑配件',
        add: '新增配件',
      },
      resultList: [],
      firmList: [],
      isApply: false,  // 点击适用车型
      memberList: [],

      applyModelTotalLists: [],

      dialogReplacementFormVisible: false,   // 替换件弹窗
      dialogGroupingFormVisible: false,   // 分组弹窗
      dialogApplyFormVisible: false,    // 适用车型弹窗
      dialogSelectApplyFormVisible: false,    // 适用车型弹窗
      dialogFormVisible: false,   // 新增编辑查看弹窗
      resultReplacementList:[], //替换件数据
      resultGroupingList:[],  // 分组数据
      manHourTitle:'', // 工时弹窗的标题
      dialogManHourFormVisible: false,   // 工时弹窗
      dialogaddHourVisible: false,  // 添加工时弹窗
      resultManHourList:[],  // 工时数据
      formAdditional: {
        dynamicItem: []
      },
      hourDirectoryId: '',
      hourSign: '',
      manhour: {
        name: '',
        code: '',
        useCount: ''
      },

      infoList: [],

      // 实物图
      imagebox: [],
      imgTitle: '',
      dialogImageFormVisible: false,   // 清空数据弹窗

      dialogclearFormVisible: false,   // 清空数据弹窗
      clearData:{
        carTrainId: '',  // 车系
        modelYear: '',  // 年款
        mainGroupListId: '',  // 主组
      },

      principalList: [],
      modelName: "",
      pagesize: 20,
      currentPage: 1,

      total: 0,
      formLabelWidth: '60px',
      formLabeldetailsWidth: '100px',
      rules: {
        carTrainId: [{ required: true, message: '车系不能为空', trigger: ['blur', 'change'] }],
        carModelYear: [{ required: true, message: '年款名称不能为空', trigger:['blur', 'change'] }],
        cnName: [{ required: true, message: '名称不能为空', trigger: ['blur', 'change'] }],
        code: [{ required: true, message: '编码不能为空', trigger: ['blur', 'change'] }],
      },
      // 批量上传
      importfile:{
        carTarinId: '',
        carModelYear: '',
      },
      isfinish: false,
      dialogImportFormVisible: false,
      fileList: [],
      zipList:[],
      //切片文件
      fileShard:{},
      //当前文件
      curFile:{},
        //文件分割的开始位置
      start: 0,
      //文件分割的结束位置
      end: 0,
      //文件大小
      fileSize: 0,
      fileKey: '',
      fileShardIndex: 0,
      fileShardTotal: 0,
      fileShardSize: 0,
      switchC: false,
      percentage: 0,
      showUploadBtn:false,
      showUploadProcess:false,
      flagType: 'temp/parts',

      //
      isAppend: '1',

      // 上传进度记录
      progressCurrentPage: 1,
      progressPagesize: 10,
      progressTotal: 0,
      maximumHeight: 0,
    }
  },

  methods: {
    // 监听表格列宽变化
    changeColWidth(){
      this.$nextTick(() => {
        this.$refs.table.doLayout();
        if(this.$refs.progressTable){
          this.$refs.progressTable.doLayout();
        }
      })
    },
    // 分页查询数据
    dataList () {
      this.isApply = false
      this.$loading.show();
      var params = new URLSearchParams()
      params.append('page', this.currentPage)
      params.append('limit', this.pagesize)
      params.append('trainId', this.formInline.carTrainId)
      params.append('yearName', this.formInline.modelYear)
      params.append('modelId', this.formInline.carModelId)
      params.append('cnName', this.formInline.cnName)
      params.append('partsCode', this.formInline.partsCodeReload)

      basicList(params).then(res => {
        if(res.data.code == 100){
          this.total = res.data.total    // 总条数
          this.resultList = res.data.data   // 数据
        } else {
          handleAlert('error',res.data.msg)
        }
        this.tableHeightArea()
        this.$loading.hide();
      })
    },

    // 获取实物图
    showImages(row){
      this.dialogImageFormVisible = true
      this.imgTitle = row.partsCode
      this.imagebox = []
      basicImage(row.partsCode).then(res => {
        this.imagebox = res.data.data
      })
    },

      // 上传进度-删除
      deleteProgress(row){
        this.$confirm('上传记录删除就找不回来了哦，您确定要删除吗', '删除上传记录', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          var params = new URLSearchParams()
          params.append('id', row.id)
          delProgress(params).then(res => {
            if (res.data.code === 100) {
              handleAlert('success','操作成功')
              this.progress();
            }else{
              handleAlert('error','操作失败，' + res.data.msg)
            }
          }).catch((error)=>{
            handleAlert('error','系统开小差了，刷新一下吧')
          })
        })
      },


    // 查询 品牌 - 车系
    getTrainList () {
      directoryTreeList().then(res => {
        this.trainList = res.data.data
      })
    },

    // 查询 年款 - 车型
    getTrainYearList(trainId){

      this.temp.directoryId = '';   // 分组(目录)ID
      this.temp.directoryname='';  // 分组名称
      this.temp.code= '';  // 分组编码
      this.temp.sltModelId= '';  // 适用车型的ID
      this.temp.sltModelname=''; // 适用车型名称
      this.temp.carModelYear= '';  // 年款
      this.temp.carModelId= '';  // 车型
      this.temp.directoryName = '';
      this.formInline.modelYear = '';
      this.formInline.carModelId = '';
      this.formInline.mainGroupListId = '';
      this.clearData.modelYear = '';
      this.clearData.mainGroupListId = '';
      this.temp.basicDirectoryId = '',
      this.importfile.carModelYear = '';
      this.yearList = [];
      this.modelList = [];
      this.groupList = [];
      if(!trainId){
        // handleAlert('error','请选择车系');
        return false;
      }
      var params = {
        trainId: trainId
      }
      directoryTrainYear(params).then(res => {
        this.yearTree = res.data.data.cmList
        let list = [];
        if(this.yearTree){
          for (let i = 0; i < this.yearTree.length; i++) {
            list.push(this.yearTree[i].year) ;
          }
        }
        this.yearList = list
      })
    },

    // 查询车型
    getModelList(year){
      this.temp.directoryId = '';   // 分组(目录)ID
      this.temp.directoryname='';  // 分组名称
      this.temp.code= '';  // 分组编码
      this.temp.sltModelId= '';  // 适用车型的ID
      this.temp.sltModelname=''; // 适用车型名称
      this.temp.directoryName = '';
      this.temp.basicDirectoryId = '';
      this.formInline.carModelId = '';
      this.formInline.mainGroupListId = '';
      this.clearData.mainGroupListId = '';
      this.modelList = [];
      this.groupList = [];
      if(!year){
        // handleAlert('error','请选择年款')
        return false;
      }
      let list = [];
      for (let i = 0; i < this.yearTree.length; i++) {
        if(this.yearTree[i].year == year){
          list = this.yearTree[i].ychild
          break;
        }
      }

      this.modelList = list;
    },

    // 查看明细
    handelLook(row){

      this.dialogFormVisible = true
      this.dialogStatus = 'detail'
      this.resetTemp()
      this.temp = Object.assign({}, row)

    },


    // 选中 JSON.parse(JSON.stringify(val))
    selectapplymodel(val){
      this.selectapplymodelList = val
      this.selectapplymodelList.forEach(function (item) {
        item.isEpcPart = true;
      })
    },


    // 搜索
    keyDown(e) {
      if (e.keyCode === 13) {
        this.onSubmit()
      }
    },
    onSubmit () {
      this.currentPage=1
      this.dataList();
    },
    // 下载配件明细模板
    downInfoClick(){
      console.log("==下载配件明细模板==");
      partsInfoTemplate().then(res => {
        if(!res.data){
            handleAlert("error", "下载失败")
            return
        }
        let name = "配件明细模板.xlsx";
        let blob = new Blob([res.data]);
        let url = window.URL.createObjectURL(blob);
        let aLink = document.createElement("a");
        aLink.style.display = "none";
        aLink.href = url;
        aLink.setAttribute("download", name);
        document.body.appendChild(aLink);
        aLink.click();
        document.body.removeChild(aLink); //下载完成移除元素
        window.URL.revokeObjectURL(url); //释放掉blob对象
      }).catch(e => {
        handleAlert("error", "系统开小差，请稍后再试")
      })
    },
    // 下载配件明细模板
    downUpdateInfoClick(){
      partsUpdateInfoTemplate().then(res => {
        if(!res.data){
            handleAlert("error", "下载失败")
            return
        }
        let name = "修改配件名称模板.xlsx";
        let blob = new Blob([res.data]);
        let url = window.URL.createObjectURL(blob);
        let aLink = document.createElement("a");
        aLink.style.display = "none";
        aLink.href = url;
        aLink.setAttribute("download", name);
        document.body.appendChild(aLink);
        aLink.click();
        document.body.removeChild(aLink); //下载完成移除元素
        window.URL.revokeObjectURL(url); //释放掉blob对象
      }).catch(e => {
        handleAlert("error", "系统开小差，请稍后再试")
      })
    },

    infoUpload(){

    },
    infoChange(){

    },
    // 导入配件明细
    importInfoClick(param){
      var _this= this
			var formData = new FormData();
			formData.append('file', param.file);
      partsImportInfo(formData).then(res => {
        if (res.data.code === 100) {
          if (res.data.msg) {
            _this.$alert("警告：" + res.data.msg,'导入成功',{dangerouslyUseHTMLString:true})
          }else{
            handleAlert('success','批量导入成功')
          }

          this.dataList()
        }else{
          _this.$alert(res.data.msg,'导入失败',{dangerouslyUseHTMLString:true})
        }
        _this.infoList = []
      }).catch(function(error){
        _this.infoList = []
        _this.$alert('系统出现异常，导入失败','信息提示',{dangerouslyUseHTMLString:true})
      })
    },

     // 修改配件信息
     updateInfoClick(param){
      var _this= this
			var formData = new FormData();
			formData.append('file', param.file);
      partsUpdateInfo(formData).then(res => {
        if (res.data.code === 100) {
            handleAlert('success','批量导入成功')
            this.dataList()
        }else{
          _this.$alert(res.data.msg,'导入失败',{dangerouslyUseHTMLString:true})
        }
        _this.infoList = []
      }).catch(function(error){
        _this.infoList = []
        _this.$alert('系统出现异常，导入失败','信息提示',{dangerouslyUseHTMLString:true})
      })
    },

    // 下载模板
    downTemplateClick () {
      var name = "实物图导入模板.zip";
      // 下载目录模板
      partsImageTemplate().then(res => {
          if(!res.data){
            handleAlert("error", "下载失败")
            return
          }
          var blob = new Blob([res.data]);
          var url = window.URL.createObjectURL(blob);
          var aLink = document.createElement("a");
          aLink.style.display = "none";
          aLink.href = url;
          aLink.setAttribute("download", name);
          document.body.appendChild(aLink);
          aLink.click();
          document.body.removeChild(aLink); //下载完成移除元素
          window.URL.revokeObjectURL(url); //释放掉blob对象
        }).catch(e => {
        handleAlert("error", "系统开小差，请稍后再试")
      })
    },

    // 重置
    resetTemp () {
      this.temp = {
        id: '',   // 主键ID
        directoryId: '',   // 分组(目录)ID
        cnName: '',  // 配件名称
        code: '',  // 分组编码
        sltModelId: '',  // 适用车型的ID
        sltModelname: '', // 适用车型名称
        carTrainId: '',  // 车系
        carModelYear: '',  // 年款
        carModelId: '',  // 车型
        sort: 1,   // 排序
        partsSign: '',  // 序号
        partsCode: '', // 配件编码
        useCount: '', // 装车用量
        deliverStatus: '', // 供货状态
        workHour: '', // 工时
        directoryRemark: '',   // 备注
        height: '',  // 高度
        width: '', // 宽度
        length: '', // 长度
        basicDirectoryId: '',
        partsMainName: '',  // 主组名称
        directoryName: '', // 分组名称
        minOrder: '',
        partsPrice: '',
        partsInventory: '',
        partsWeight: '',
        factory: '',
        material: '',
        role: '',
      }
      // if (this.$refs.temp) {
      //   this.$nextTick(function() {
      //     this.$refs.temp.clearValidate();
      //   })
      // }

    },

    resetForm (temp) {
      if(this.dialogStatus == 'edit'){
        this.temp.id= '',   // 主键ID
        this.temp.pid= '',   // 主组ID
        this.temp.cnName= '',  // 分组名称
        this.temp.code= '',  // 分组编码
        this.temp.sltModelId= '',  // 适用车型的ID
        this.temp.carTrainId= '',  // 车系
        this.temp.carModelYear= '',  // 年款
        this.temp.carModelId= '',  // 车型
        this.temp.sort= 1   // 排序

        // this.$nextTick(function() {
        //   this.$refs.temp.clearValidate();
        // })
      } else {
        this.resetTemp()
      }
    },


    // 编辑
    handelEdit (row) {
      this.currentPageApply = 1;
      this.pagesizeApply = 15;
      var _this=this
      // 清除表单
      _this.resetTemp();
      // 赋值表单
      setTimeout(() => {
        _this.temp = Object.assign({}, row);

        _this.temp.id = row.id;
        _this.temp.partsCode = row.partsCode;
      })
      this.determineModelList = [];
       setTimeout(() => {
        _this.dialogFormVisible = true;
        _this.isApply = false
        _this.dialogStatus = 'edit';
        _this.editStatus=true;
      })

    },

    // 编辑
    updateData (temp) {
      var _this = this

      if(!_this.temp.partsCode || "" == _this.temp.partsCode.trim()){
        handleAlert('error','配件编码不能为空')
        return false;
      }
      if(!_this.temp.cnName || "" == _this.temp.cnName.trim()){
        handleAlert('error','配件名称不能为空')
        return false;
      }

      if(!_this.temp.minOrder || "" == _this.temp.minOrder || _this.temp.minOrder == 0){
        handleAlert('error','最小起订量不能为0')
        return false;
      }
      var params = new URLSearchParams()
      params.append('id', _this.temp.id)  // id
      params.append('partsCode', _this.temp.partsCode)  // 编码
      params.append('cnName', _this.temp.cnName)  // 名称
      params.append('useCount', _this.temp.useCount)  // 装车用量
      params.append('partsSign', _this.temp.partsSign)  // 序号
      params.append('length', _this.temp.length)  // 长，宽，高
      params.append('width', _this.temp.width)
      params.append('height', _this.temp.height)
      params.append('minOrder', _this.temp.minOrder)
      if(_this.temp.factory != null && _this.temp.factory != undefined &&  _this.temp.factory != 'null' &&  _this.temp.factory != 'undefined'){
        params.append('factory', _this.temp.factory)
      }
      if(_this.temp.role != null && _this.temp.role != undefined &&  _this.temp.role != 'null' &&  _this.temp.role != 'undefined'){
        params.append('role', _this.temp.role)
      }
      if(_this.temp.material != null && _this.temp.material != undefined && _this.temp.material != 'null' &&  _this.temp.material != 'undefined'){
        params.append('material', _this.temp.material)
      }

      if (_this.temp.partsPrice &&  _this.temp.partsPrice != 'null' &&  _this.temp.partsPrice != 'undefined') {
        params.append('partsPrice', _this.temp.partsPrice)
      }

      if (_this.temp.partsInventory &&  _this.temp.partsInventory != 'null' &&  _this.temp.partsInventory != 'undefined') {
        params.append('partsInventory', _this.temp.partsInventory)
      }
      if (_this.temp.partsWeight &&  _this.temp.partsWeight != 'null' &&  _this.temp.partsWeight != 'undefined') {
        if (_this.temp.partsWeight.toString().startsWith('00')) {
          handleAlert('error','重量格式错误')
          return false;
        }
        params.append('partsWeight', _this.temp.partsWeight)
      }

      this.$refs[temp].validate((valid) => {
        if(valid){
          this.$loading.show()
          basicEdit(params).then(res => {
            if (res.data.code === 100) {
              handleAlert('success','保存成功')
              this.dataList()
              this.dialogFormVisible = false
            } else {
              handleAlert('error',res.data.msg)
            }
            this.$loading.hide()
          }).catch(err => {
            this.$loading.hide()
            if (err !== null && err !== '' && err.responseText !== null) {
              handleAlert('error','提交失败,请重试')
            }
          })
        } else {
          this.$loading.hide()
          handleAlert('error','请完善项目信息')
        }
      })
    },


    // 替换件弹窗
    showReplacement(row){

      this.dialogReplacementFormVisible = true
      var params = new URLSearchParams()
      params.append('partsCode', row.partsCode)
      this.resultReplacementList = []
      basicReplaceChain(params).then(res => {
        this.resultReplacementList = res.data.data
      })
    },


    resetCode(){
      this.roleState()
    },
    // 删除
    handelDelete (row) {

      var _this=this
      this.$confirm('确定删除【' + row.partsCode + '】配件吗?', '删除配件', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var params = new URLSearchParams()
        params.append('id', row.id)
        basicDel(params).then(res => {
          if (res.data.code === 100) {
            handleAlert('success','删除成功')
            if(this.resultList!=null&&this.resultList.length==1){
              this.currentPage =this.currentPage-1
            }
            this.dataList()
          }else{
            _this.$alert(res.data.msg,'信息提示',{dangerouslyUseHTMLString:true})
          }
        })
      }).catch((error)=>{
        //  _this.$alert(res.data.msg,'信息提示',{dangerouslyUseHTMLString:true})
      })
    },

    // 批量删除
    handleSelectionChange (val) {
      this.deleteList = val
    },
    batchDelClick(){
      let list = []

      this.deleteList.forEach(function (item) {
        list.push(item.id)
      })
      this.$confirm('确定删除所选中的配件吗?', '删除配件', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var params = new URLSearchParams()
        params.append('ids', list.toString())
        basicDels(params).then(res => {
          if (res.data.code === 100) {
            handleAlert('success','删除成功'+ res.data.msg)
            if(this.resultList!=null&&this.resultList.length==1){
              this.currentPage =this.currentPage-1
            }
            this.dataList()
          }else{
            handleAlert('error','删除失败，' + res.data.msg)
          }
        })
      }).catch((error)=>{
        handleAlert('info','取消删除')
      })
    },

    handleRemove (file, fileList) {

      this.isFlag = true;
      if(fileList.length == "0"){
        this.imgList=[]
      }else{
         this.imgList = fileList
      }

    },


    // 搜索的重置
    reset (formInline) {
      if (this.$refs[formInline].resetFields() !== undefined) {
        this.$refs[formInline].resetFields()
      }
      // this.$refs.subSelectTree.initSelected('','')
       this.formInline.carTrainId= '',  // 车系
        this.formInline.modelYear= '',  // 年款
        this.formInline.carModelId= '',  // 车型
        this.formInline.mainGroupListId= '',   // 主组
        this.formInline.cnName= '',  // 配件名称
        this.formInline.partBranchCode= '',  // 分组编码
        this.formInline.partsCodeReload= '', // 配件编码
        this.formInline.alternateTypeReload= '',  // 替换关系
      this.yearList = []
      this.modelList = []
      this.currentPage = 1
      this.dataList()
    },

    // 批量上传, 打开弹窗
    batchupload(){
      setTimeout(() => {
        this.resfImport();
      })
      this.isfinish = true;
      this.dialogImportFormVisible = true;
      this.flagType = 'temp/partsImage';
      this.resfImport();
    },


    // 上传实物图
    importSubmit(){
      if (this.zipList.length <= 0) {
        handleAlert('warning','请选择文件')
        return false
      }
      let uploadFileName= this.zipList[0].name
      let uploadFilePath= this.zipList[0].filePath
      this.$loading.show()
      var params = new URLSearchParams()
      params.append('fileName', uploadFileName)
      params.append('filePath', uploadFilePath)
      params.append('type', this.isAppend)
      basicImportImage(params).then(res => {
        if(res.data.code == 100){
          handleAlert('success', "正在解析，点击上传进度，查看解析情况")
          this.dialogImportFormVisible = false;
        }else{
          handleAlert('error', "解析失败：" + res.data.msg)
        }
        this.$loading.hide()
      })
    },

    // 下载实物图
    downImage(){

      var params = new URLSearchParams()
      params.append('partsCode', this.imgTitle)
      basicdownImage(params).then(res => {

        if(!res.data){
          return
        }
        var msg = decodeURI(res.headers['content-type']);
        handleAlert("info", msg);
        var name = this.imgTitle + "_实物图.zip";
        var blob = new Blob([res.data]);
        var url = window.URL.createObjectURL(blob);
        var aLink = document.createElement("a");
        aLink.style.display = "none";
        aLink.href = url;
        aLink.setAttribute("download", name);
        document.body.appendChild(aLink);
        aLink.click();
        document.body.removeChild(aLink); //下载完成移除元素
        window.URL.revokeObjectURL(url); //释放掉blob对象
      })
    },

    // 清空实物图
    replaceImage(){

      this.$confirm('确定清空【'+this.imgTitle+'】配件的实物图吗? 清空后就找不回来喽', '清空实物图', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var params = new URLSearchParams()
        params.append('partsCode', this.imgTitle)
        basicreplaceImage(params).then(res => {
          if (res.data.code === 100) {
            handleAlert('success','操作成功'+ res.data.msg)
            this.dialogImageFormVisible = false
            this.dataList()
          }else{
            handleAlert('error','操作失败' + res.data.msg)
          }
        })
      }).catch((error)=>{

      })

    },


    // 解析进度列表
    progress(){
      var params = new URLSearchParams()
      params.append('type', "2")
      params.append('page', this.progressCurrentPage)
      params.append('limit', this.progressPagesize)
      uploadProgress(params).then(res => {
        this.dialogUploadProgressVisible = true;
        this.progressData = res.data.data
        this.progressTotal = res.data.total
      })
    },

    // 取消，关闭弹窗
    resetimport(){
      this.dialogImportFormVisible = false;
      this.resfImport();
    },

    resfImport(){
      this.importfile.carTarinId = '';
      // this.$refs.importSelectTree.initSelected('','')
      this.importfile.carModelYear = '';
      this.fileList = [];
      this.zipList = [];
      //切片文件
      this.fileShard={};
      //当前文件
      this.curFile={};
        //文件分割的开始位置
      this.start= 0;
      //文件分割的结束位置
      this.end= 0;
      //文件大
      this.fileSize= 0;
      this.fileKey= '';
      this.fileShardIndex= 0;
      this.fileShardTotal= 0;
      this.fileShardSize= 0;
      this.switchC= false;
      this.percentage= 0;
      this.showUploadBtn=true;
      this.showUploadProcess=false;
    },

     // 文件上传之前
    onBeforeUpload(file){


        let text=""
        // 获取文件后缀
        var fileExt =file.name.substring(file.name.lastIndexOf('.')+1).toLowerCase();
        // 文件后缀是否是 zip
        const zipExt = fileExt === 'zip'
        // 文件大小不能超过1G
        const isLimit = file.size / 1024 / 1024 < 1024
        if(!zipExt) {
        text="上传文件只能是 zip 格式!";
        this.$message.error(text)
        return false;
        }
        if (!isLimit) {
        text="上传文件大小不能超过 1GB!";
        this.$message.error(text)
        return false;
        }
        this.fileShardSize = 1*1024 * 1024; //每片文件大小

        //点击后隐藏上传按钮 ，防止重复点击
        //$("#fileUpload").css('visibility','hidden');
        this.showUploadBtn=false
        this.showUploadProcess=true
        this.percentage=1
        var _this=this
        getmd5(file,_this.fileShardSize).then(e =>{
                _this.switchC=false;
                _this.fileShardIndex=1;//分片索引
                _this.curFile=file;
                _this.fileKey=e;
                _this.fileSize=file.size;
                _this.fileShardTotal=Math.ceil(file.size/_this.fileShardSize);//分片总数
                var fileFullName=file.name;
                _this.fileSuffix = fileFullName.substr(fileFullName.lastIndexOf('.') + 1);
                _this.fileName = fileFullName.substr(0, fileFullName.lastIndexOf('.'));

                //上传参数
                var params =  new FormData()
                params.append('fileName', _this.fileName)
                params.append('fileShardTotal', _this.fileShardTotal)
                params.append('fileKey', _this.fileKey)
                params.append('fileSuffix', _this.fileSuffix)
                params.append('fileShardSize', _this.fileShardSize)
                params.append('fileSize', _this.fileSize)
                params.append('fileFlag', _this.flagType)

                _this.updateProgress(file,params)


            })


    },
    // 批量上传
    uploadFile (formData) {
      var _this=this
      // 上传
      procSplitFile(formData).then(res => {
        if(res.data.code==200){
          //上传分片完成
          if(res.data.shardIndex<_this.fileShardTotal){
            _this.fileShardIndex=_this.fileShardIndex+1;
            _this.start=(_this.fileShardIndex-1)*_this.fileShardSize;
            _this.end =Math.min(_this.curFile.size,_this.start+_this.fileShardSize);
            _this.fileSize=_this.curFile.size;
            var params =  new FormData()
            params.append('fileName', _this.fileName)
            params.append('fileShardTotal', _this.fileShardTotal)
            params.append('fileKey', _this.fileKey)
            params.append('fileSuffix', _this.fileSuffix)
            params.append('fileShardSize', _this.fileShardSize)
            params.append('fileSize', _this.fileSize)
            params.append('fileFlag', _this.flagType)
            params.append('fileShardIndex', _this.fileShardIndex)
            var fileShardtem=_this.curFile.slice(_this.start,_this.end);//从文件中获取当前分片数据
            let fileReader = new FileReader();
            //异步读取本地文件分片数据并转化为base64字符串
            fileReader.readAsDataURL(fileShardtem);
            //本地异步读取成功后，进行上传
            fileReader.onload = function (e) {
              let  base64str = e.target.result;
              params.append('base64', base64str)
              _this.uploadFile(params)
            }
            let perentNum=Math.ceil(this.fileShardIndex * 100 / this.fileShardTotal)
            if(perentNum>100){
              this.percentage=100
            }else{
              this.percentage=perentNum
            }
          }
        }else if(res.data.code==100){
          var fileId= res.data.id
          //上传完成
          _this.percentage=100
          _this.switchC=true

          _this.finishUpload(fileId)
        }

      }).catch((error)=>{
        if(error.response){
        console.log(error.response.data)
        console.log(error.response.status)
        console.log(error.response.headers)
        }else{
          console.log(error.message)
        }
      })

    },
    updateProgress(file,params){
      this.isfinish = false;
      var _this= this
      var param = new URLSearchParams()
      param.append('shardKey', _this.fileKey)
      // 批量上传
      checkUploadProgress(param).then(res => {
        if(res.data.code==200){
          //新文件
          _this.start=(_this.fileShardIndex-1)*_this.fileShardSize;
          _this.end =Math.min(file.size,_this.start+_this.fileShardSize);
          _this.fileShard=file.slice(_this.start,_this.end);//从文件中获取当前分片数据
        }else if(res.data.code==220){
          _this.fileShardIndex=res.data.ShardIndex;
          //有上传未完成的
          _this.start=(_this.fileShardIndex-1)*_this.fileShardSize;
          _this.end =Math.min(file.size,_this.start+_this.fileShardSize);
          _this.fileShard=file.slice(_this.start,_this.end);//从文件中获取当前分片数据
        }else if (res.data.code==240){
          //急速上传
          var fileId= res.data.id
          _this.percentage=100
          _this.switchC=true

          // this.$message({
          //   message: '极速上传成功',
          //   type: 'success'
          // })
          _this.finishUpload(fileId)
          return false;
        }
        //读取base64str
        let fileReader = new FileReader();
        //异步读取本地文件分片并转化为base64字符串
        fileReader.readAsDataURL(_this.fileShard);
        //本地异步读取成功，进行上传
        fileReader.onload=function (e){
          let  base64str=e.target.result;
          params.append('base64', base64str)
          params.append('fileShardIndex', _this.fileShardIndex)
          if(_this.switchC==false){
              _this.uploadFile(params)
          }
        }
      }).catch((error)=>{
        this.$message.error('上传错误')
        this.isfinish = true;
      })

    },
    finishUpload(fileId){
         var _this=this
           //进行保存提醒
          _this.uploadMsg = _this.$message({
                              duration:0,
                              message: "请稍等，正在保存...",
                              type: "warning"
                            });
          var param = new URLSearchParams()
          param.append('fileId', fileId)
          basicFinishUpload(param).then(res => {
            if(res.data.code==100){
                let uploadFileName= res.data.data.fileName
                let uploadFilePath= res.data.data.filePath
                _this.zipList = []
                // _this.zipList = []
                if(uploadFileName != null && uploadFileName.length > 0){
                  var fileObj = { name: uploadFileName , filePath:uploadFilePath}
                  _this.zipList.push(fileObj)
                  // var file = {name: uploadFileName, url: uploadFilePath}
                  // _this.zipList.push(file)
                }
                //关闭消息提醒
                _this.uploadMsg.close()

                //上传完成提示
                _this.$message({
                  duration:2000,
                  message: '上传已完成',
                  type: 'success'
                })
              _this.showUploadProcess=false
              // _this.showUploadBtn=true
            }
          })

    },
    handleBeforeRemove(){
     if(this.zipList!=null&&this.zipList.length>0){
        // var filePath= this.zipList[0].filePath;
          var _this=this
                this.$confirm('确认删除文件？', '删除',{
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => {
                _this.zipList=[];
                _this.showUploadBtn=true
                _this.isfinish = true;
              }).catch((error)=>{
                handleAlert('info','取消删除')
                 return false;
              })
      }
      return false;
    },


    exportClick(param){
      this.$loading.show();
      partsExportInfo(param).then(res => {
        if (res.data.code == 100) {
          let path = res.data.data
          let name = path.substring(path.lastIndexOf("/") + 1)
          // handleAlert("info", "打包已完成，下载中...")
          downSvg(path).then(resc => {
              if(!resc.data){
                handleAlert("error", "下载失败")
                this.$loading.hide();
                return
              }
              var blob = new Blob([resc.data]);
              var url = window.URL.createObjectURL(blob);
              var aLink = document.createElement("a");
              aLink.style.display = "none";
              aLink.href = url;
              aLink.setAttribute("download", name);
              document.body.appendChild(aLink);
              aLink.click();
              document.body.removeChild(aLink); //下载完成移除元素
              window.URL.revokeObjectURL(url); //释放掉blob对象
              this.$loading.hide();
            }).catch(e => {
              this.$loading.hide();
              handleAlert("error", "系统异常，请稍后再试")
          })
        }else{
          this.$loading.hide();
          handleAlert("error", "数据打包错误; " + res.data.msg)
        }
      }).catch(e => {
        this.$loading.hide();
        handleAlert("error", "系统异常，请稍后再试")
      })
    },

     // 复制结果
     copyResult(row){

      const input = document.createElement('input')
      input.style.position = 'absolute'
      input.style.left = '-9999px'
      input.style.top = '-9999px'
      document.body.appendChild(input)
      input.value = row.result
      input.select()
      document.execCommand('copy')

      document.body.removeChild(input)
      handleAlert('success', '结果已复制')
    },
    maximumArea() {
      setTimeout(() => {
        tableHeight()
        setTimeout(() => {
          this.maximumHeight = tableMaxHeight;
        }, 50)
      })
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    },
    selRowKey(row){
      return row.id
    },
     // 查看  适用车型
    handelDetail (row) {
      let _this = this
      this.applyModelList = []

      // 查询适用车型
      var params = new URLSearchParams()
      params.append('partsCode', row.partsCode)
      basicDirectoryCarModelList(params).then(res => {
        _this.applyModelList = res.data.data
        _this.dialogApplyFormVisible = true
      }).catch(e => {
        console.log(e);
        _this.applyModelList = []
      })
    },




  },
  mounted () {
    this.tableHeightArea()
    this.imageurl = sysServerUrl
    this.dataList()
    this.getTrainList()
    this.flagType='temp/parts'
  },
  activated() {
    window.addEventListener('keydown', this.keyDown);
  },
  deactivated() {
    window.removeEventListener('keydown', this.keyDown, false);
  },
}
</script>
<style>
.fileImg {
  width: 100%;
  height: 100%;
}

.image{
/*设置图片宽度和浏览器宽度一致*/
  width:100%;
  height:inherit;
}

.el-tooltip__popper {
    max-width: 800px;
}

/* 设置输入框的长度 */
/* .el-dialog .el-input {
    width: 100% !important;
} */

.el-progress-bar__innerText {
  color: #fff;
}

#improt_info{
  margin: 0px;
}

.el-message-box__message {
  overflow-x: auto;
  max-height: 500px;
}
.el-dialog .inputItemStyle .el-input {
  width: 94% !important;
}
</style>
<style>
</style>
