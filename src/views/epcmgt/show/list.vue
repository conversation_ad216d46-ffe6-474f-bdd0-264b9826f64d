<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" ref="formInline" label-width="65px" :model="formInline" class="demo-form-inline">
        <el-form-item label="配件名称" prop="name">
          <el-input v-model.trim="formInline.name" placeholder="请输入配件名称"></el-input>
        </el-form-item>
        <el-form-item label="配件编码" prop="code">
          <el-input v-model.trim="formInline.code" placeholder="请输入配件编码"></el-input>
        </el-form-item>
        <el-form-item >
          <el-button type="primary" @click="onSubmit" icon="el-icon-search">搜索</el-button>
          <el-button plain @click="reset('formInline')">重置</el-button>
        </el-form-item>
      </el-form>
    </div>


    <div class="tableDetail">
      <div class="tableHandle">
        <el-button type="text" v-if="hasPerm('menuAsimss5A6B_101')" icon="el-icon-plus" @click="addClcik()">新增</el-button>&nbsp;&nbsp;
        <el-upload
          class="upload-demo inline-block"
          ref="elUpload"
          action="#"
          :show-file-list="false"
          multiple
          :limit="1"
          :file-list="fileList"
          :before-upload="onBeforeUpload"
          :http-request="uploadFile"
          :on-change="onUploadChange"
        >
          <el-button type="text" v-if="hasPerm('menuAsimss5A6B_107')" size="min" icon="bulkImport-icon">批量上传</el-button>
        </el-upload>
        &nbsp;&nbsp;<el-button v-if="hasPerm('menuAsimss5A6B_108')" type="text" icon="bulkDown-icon" @click="batchExport()">批量下载</el-button>
        <el-button type="text" v-if="hasPerm('menuAsimss5A6B_107')" icon="el-icon-download" @click="downTemplateClick()">下载模板</el-button>
      </div>
      <!-- 分页显示列表 -->
      <!-- <el-table style="width:100%" border :data="resultList" :header-cell-style="{'text-align':'center'}" @selection-change="handleSelectionChange"> -->
      <el-table
        style="width:100%"
        border
        stripe
        ref="table"
        highlight-current-row
        :max-height="maximumHeight"
        :data="resultList"
        :header-cell-style="{
          'text-align': 'center',
          'background-color': 'var(--other-color)',
        }"
        @header-dragend="changeColWidth"
      >
        <el-table-column label="序号" type="index" width="60"  align="center"></el-table-column>
        <el-table-column label="配件名称" prop="name" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column label="配件编码" prop="code" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column label="更新人员" prop="username" min-width="80" align="center"></el-table-column>
        <el-table-column label="更新日期" prop="createdTime" width="200" align="center">
          <template slot-scope="{row}">
            <div>
              {{ row.createdTime | conversion("yyyy-MM-dd HH:mm:ss")}}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="100">
          <template slot-scope="{row}">
            <el-button type="text" v-if="hasPerm('menuAsimss5A6B_102')" size="small" @click="handelDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total>0" :total="total" :page.sync="currentPage" :limit.sync="pagesize" @pagination="dataList"/>


      <el-dialog v-dialogDrag width="700px !important" title="新增常显件" :visible.sync="dialogFormVisible" :close-on-click-modal="false" v-if="dialogFormVisible">
        <el-form :rules="rules" :label-width="formLabelWidth" :model="temp" ref="rules">
          <el-form-item label="配件编码" prop="code">
            <el-input v-model.trim="temp.code" show-word-limit maxlength="50" placeholder="请输入配件编码"></el-input>
          </el-form-item>
          <el-form-item label="配件名称" prop="name">
            <el-input v-model.trim="temp.name" show-word-limit maxlength="50" placeholder="请输入配件名称"></el-input>
          </el-form-item>

          <div class="submitArea">
            <el-button type="primary" @click="addPartsClick()">
              立即提交
            </el-button>
          </div>
        </el-form>
      </el-dialog>

    </div>
  </div>
</template>
<script>

import { sysServerUrl, handleAlert, tableHeight, tableMaxHeight } from '@/assets/js/common.js'
import Pagination from '@/components/Pagination'
import { showList, showAdd, showDel, partsShowTemplate, showImport, showDown } from '@/api/epcmgt.js';

export default {
  name: 'epcmgtshowlist',
  // components: { Pagination ,SelectTree },
  components: { Pagination },
  data () {
    return {
      // 搜索条件
      formInline:{
        coed: '',
        name: '',
      },
      // 当前页
      currentPage: 1,
      // 每页显示的条数
      pagesize: 15,
      // 总条数
      total: 0,
      // 数据集合
      resultList: [],
      formLabelWidth: '100px',
      temp:{
        coed: '',
        name: '',
      },

      dialogFormVisible: false,
      // 校验
      rules: {
        code: [{ required: true, message: '配件编码不能为空', trigger: ['blur', 'change'] }],
        name: [{ required: true, message: '配件名称不能为空', trigger: ['blur', 'change'] }],
      },
      // 文件
      fileList: [],
      maximumHeight: 0,
    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth(){
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    // 分页查询数据
    dataList () {
      this.$loading.show();
      var params = new URLSearchParams();
      params.append('page', this.currentPage);
      params.append('limit', this.pagesize);
      let code = this.formInline.code;
      let name = this.formInline.name;
      if (!(!code || code.length <= 0)) {
        params.append('code', code);
      }
      if (!(!name || name.length <= 0)) {
        params.append('name', name);
      }
      showList(params).then(res => {
        this.total = res.data.total    // 总条数
        this.resultList = res.data.data   // 数据
        this.tableHeightArea()
        this.$loading.hide();
      })
    },

    // 搜索
    keyDown(e) {
      if (e.keyCode === 13) {
        this.onSubmit();
      }
    },
    onSubmit() {
      this.currentPage = 1;
      this.dataList();
    },
    // 重置
    reset(){
      this.formInline ={
        coed: '',
        name: '',
      };
      this.onSubmit();
    },


    // 添加
    addClcik(){
      this.temp ={
        coed: '',
        name: '',
      };
      this.dialogFormVisible = true;
    },

    addPartsClick(){
      let code = this.temp.code;
      let name = this.temp.name;
      if (!code || code.length <= 0) {
        handleAlert('error', '配件编码不能为空');
        return false;
      }
      if (!name || name.length <= 0) {
        handleAlert('error', '配件名称不能为空');
        return false;
      }
      this.$loading.show();
      var params = new URLSearchParams();
      params.append('code', code);
      params.append('name', name);
      showAdd(params).then(res => {
        if (res.data.code === 100) {
          handleAlert('success','添加成功')
          this.dialogFormVisible = false;
          this.dataList()
        }else{
          handleAlert('error',res.data.msg)
        }
        this.$loading.hide();
      }).catch(function(error){
        this.$loading.hide();
        handleAlert('error', '系统出现异常，添加失败')
      })

    },

    handelDelete (row) {

      this.$confirm('确定删除常显件【' + row.code + '】吗?', '删除常显件', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // var params = new URLSearchParams()
        // params.append('id', row.id)
        showDel(row.id).then(res => {
          if (res.data.code === 100) {
            handleAlert('success','删除成功')
            if(this.resultList!=null&&this.resultList.length==1){
              this.currentPage =this.currentPage-1 > 0 ? this.currentPage-1 : this.currentPage
            }
            this.dataList()
          }else{
            handleAlert('error',res.data.msg)
          }
        })
      })
    },

    // 下载模板
    downTemplateClick () {
      var name = "常显件导入模板.xlsx";
      // 下载目录模板
      partsShowTemplate().then(res => {
          if(!res.data){
            handleAlert("error", "下载失败")
            return
          }
          var blob = new Blob([res.data]);
          var url = window.URL.createObjectURL(blob);
          var aLink = document.createElement("a");
          aLink.style.display = "none";
          aLink.href = url;
          aLink.setAttribute("download", name);
          document.body.appendChild(aLink);
          aLink.click();
          document.body.removeChild(aLink); //下载完成移除元素
          window.URL.revokeObjectURL(url); //释放掉blob对象
        }).catch(e => {
        handleAlert("error", "系统开小差，请稍后再试")
      })

    },


    // 附件上传
    onBeforeUpload(file) {
      var fileExt =file.name.substring(file.name.lastIndexOf('.')+1).toLowerCase();
      var text=""
      const docxExt = fileExt === 'xlsx'
      const isLimit = file.size / 1024 / 1024 < 2
      if(!docxExt) {
        text="上传文件只能是xlsx格式!";
        handleAlert('warning',text)
        return false;
      }
      if (!isLimit) {
        text="上传文件大小不能超过 2MB!";
        handleAlert('warning',text)
        return false;
      }
      return true;
    },
    // 批量上传
    uploadFile (param) {
      var _this= this
      _this.$loading.show()
			var formData = new FormData();
			formData.append('file', param.file);
      showImport(formData).then(res => {
        if (res.data.code === 100) {
          handleAlert('success','批量导入成功')
          this.dataList()
        }else{
          _this.$alert(res.data.msg,'信息提示',{dangerouslyUseHTMLString:true})
        }
        _this.fileList = []
        _this.$loading.hide()
      }).catch(function(error){
        _this.fileList = []
        _this.$alert('系统出现异常，导入失败','信息提示',{dangerouslyUseHTMLString:true})
        _this.$loading.hide()
      })
		},
    onUploadChange(file){
    },

    // 批量下载
    batchExport () {
      var params = new URLSearchParams();
      let code = this.formInline.code;
      let name = this.formInline.name;
      if (!(!code || code.length <= 0)) {
        params.append('code', code);
      }
      if (!(!name || name.length <= 0)) {
        params.append('name', name);
      }

      showDown(params).then(res => {
        if(!res.data){
          return
        }
        var name = "常显件列表.xlsx";
        var blob = new Blob([res.data]);
        var url = window.URL.createObjectURL(blob);
        var aLink = document.createElement("a");
        aLink.style.display = "none";
        aLink.href = url;
        aLink.setAttribute("download", name);
        document.body.appendChild(aLink);
        aLink.click();
        document.body.removeChild(aLink); //下载完成移除元素
        window.URL.revokeObjectURL(url); //释放掉blob对象
      })
    },
    maximumArea() {
      setTimeout(() => {
        tableHeight()
        setTimeout(() => {
          this.maximumHeight = tableMaxHeight;
        }, 50)
      })
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    }
  },
  mounted () {
    this.tableHeightArea()
    this.imageurl = sysServerUrl
    this.dataList()
  },
  activated() {
    window.addEventListener('keydown', this.keyDown);
  },
  deactivated() {
    window.removeEventListener('keydown', this.keyDown, false);
  },
}
</script>
<style>
.fileImg {
  width: 100%;
  height: 100%;
}

.image{
/*设置图片宽度和浏览器宽度一致*/
  width:100%;
  height:inherit;
}

.el-tooltip__popper {
    max-width: 800px;
}

/* 设置输入框的长度 */
/* .el-dialog .el-input {
    width: 100% !important;
} */

.el-progress-bar__innerText {
  color: #fff;
}

#improt_info{
  margin: 0px;
}

.el-message-box__message {
    overflow-x: auto;
    max-height: 500px;
  }
</style>
