<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" ref="formInline" :model="formInline" class="demo-form-inline">
        <el-form-item label="车系" prop="carTrainId" :label-width="formLabelWidth">
          <el-select v-model="formInline.carTrainId" placeholder="请选择车系" @change="getTrainYearList" clearable filterable>
            <el-option-group v-for="group in trainList" :key="group.id" :label="group.nameCh">
              <el-option v-for="item in group.children" :key="item.id" :label="item.nameCh" :value="item.id"></el-option>
            </el-option-group>
          </el-select>
            <!-- <select-tree ref="subSelectTree"
            :options="trainList"
            v-model="formInline.carTrainId"
            :props="defaultProps"
            @slectNode="getTrainYearList"
            placeholder="请选择车系"  /> -->
          </el-form-item>
        <el-form-item label="年款" prop="carModelYear" :label-width="formLabelWidth">
            <el-select v-model="formInline.carModelYear" clearable filterable @change="getModelList">
              <el-option v-for="(item,index) in yearList" :key="index" :label="item" :value="item"></el-option>
            </el-select>
          </el-form-item>
        <el-form-item label="车型" prop="carModelId" :label-width="formLabelWidth">
            <el-select v-model="formInline.carModelId" clearable filterable>
              <el-option v-for="(item,index) in modelList" :key="index" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        <el-form-item label="主组" prop="catalogId" :label-width="formLabelWidth">
            <el-select v-model="formInline.catalogId" clearable filterable >
              <el-option v-for="(item,index) in groupList" :key="index" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        <el-form-item label="分组名称" prop="cnName" :label-width="formLabelWidth">
          <el-input v-model.trim="formInline.cnName" placeholder="请输入分组名称"></el-input>
        </el-form-item>
        <el-form-item label="分组编码" prop="code" :label-width="formLabelWidth">
          <el-input v-model.trim="formInline.code" placeholder="请输入分组编码"></el-input>
        </el-form-item>

        <el-form-item :label-width="formLabelWidth">
          <el-button v-if="hasPerm('menuAsimss5A2B_104')" type="primary" @click="onSubmit" icon="el-icon-search">搜索</el-button>
          <el-button v-if="hasPerm('menuAsimss5A2B_104')" plain @click="reset('formInline')">重置</el-button>
        </el-form-item>
      </el-form>
    </div>


    <div class="tableDetail">
      <div class="tableHandle" >
        <el-button type="text" v-if="hasPerm('menuAsimss5A2B_101')" icon="el-icon-plus" @click="handelAdd()">新增</el-button>
        <el-button type="text" v-if="hasPerm('menuAsimss5A2B_102')" icon="el-icon-delete" @click="batchDelClick()">批量删除</el-button>
        <el-button type="text" v-if="hasPerm('menuAsimss5A2B_101')" icon="el-icon-download" @click="downTemplateClick()">下载模板</el-button>
        <el-button type="text" v-if="hasPerm('menuAsimss5A2B_102')" icon="el-icon-delete" @click="clearAll()">清空数据</el-button>
        <el-button type="text" v-if="hasPerm('menuAsimss5A2B_101')" icon="bulkImport-icon" @click="batchupload()">批量上传</el-button>
      </div>
      <!-- 分页显示列表 -->
      <el-table
        style="width:100%"
        border
        stripe
        highlight-current-row
        :data="resultList"
        :header-cell-style="{
          'text-align': 'center',
          'background-color': 'var(--other-color)',
        }"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>

        <el-table-column label="主组" prop="topCnName" min-width="150" show-overflow-tooltip></el-table-column>
        <el-table-column label="分组" prop="cn_name" min-width="150" show-overflow-tooltip></el-table-column>
        <el-table-column label="分组编码" prop="code" min-width="150" show-overflow-tooltip></el-table-column>
        <el-table-column label="SVG图" prop="path" width="100" align="center">
          <template slot-scope="{row}">
            <el-button type="text" v-if="hasPerm('menuAsimss5A2B_104')" size="small" @click="bigImg(row)">查看</el-button>
          </template>
        </el-table-column>

        <el-table-column label="操作" fixed="right" width="230">
          <template slot-scope="{row}">
            <el-button type="text" v-if="hasPerm('menuAsimss5A2B_104')" size="small" @click="handelDetail(row)">适用车型</el-button>
            <el-button type="text" v-if="hasPerm('menuAsimss5A2B_103')" size="small" @click="handelEdit(row)">编辑</el-button>
            <el-button type="text" v-if="hasPerm('menuAsimss5A2B_102')" size="small" @click="handelDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total>0" :total="total" :page.sync="currentPage" :limit.sync="pagesize" @pagination="dataList"/>


      <el-dialog v-dialogDrag :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" :close-on-click-modal="false">

        <!-- 添加和修改 -->
        <el-form v-if="dialogStatus === 'add' || dialogStatus === 'edit' " ref='temp' :model="temp"  label-position="center" :validate-on-rule-change="false">

          <el-form-item label="车系" prop="carTrainId" :label-width="formLabelWidth" v-if="dialogStatus === 'add'">
            <!-- <select-tree ref="addSelectTree"
            :options="trainList"
            v-model="temp.carTrainId"
            :props="defaultProps"
            @slectNode="getTrainYearList"
            placeholder="请选择"  /> -->
            <el-select v-model="temp.carTrainId" placeholder="请选择车系" @change="getTrainYearList" clearable filterable>
            <el-option-group v-for="group in trainList" :key="group.id" :label="group.nameCh">
              <el-option v-for="item in group.children" :key="item.id" :label="item.nameCh" :value="item.id"></el-option>
            </el-option-group>
          </el-select>
          </el-form-item>

          <el-form-item label="年款" prop="carModelYear" :label-width="formLabelWidth" v-if="dialogStatus === 'add'"  placeholder="请选择">
            <el-select v-model="temp.carModelYear" clearable filterable @change="getModelList">
              <el-option v-for="(item,index) in yearList" :key="index" :label="item" :value="item"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="主组" prop="id" :label-width="formLabelWidth">
              <el-select v-model="temp.pid" clearable filterable >
                <el-option v-for="(item,index) in groupList" :key="index" :label="item.name" :value="item.id"></el-option>
              </el-select>
          </el-form-item>

          <el-form-item label="适用车型" prop="carModelId" :label-width="formLabelWidth" >
               <el-input v-model="temp.sltModelname" placeholder="请选择车型" @focus="selectmodel"></el-input>
            </el-form-item>

          <el-form-item label="分组名称" prop="cnName" :label-width="formLabelWidth">
            <el-input v-model.trim="temp.cnName" placeholder="请输入分组名称"></el-input>
          </el-form-item>
          <el-form-item label="分组编码" prop="code" :label-width="formLabelWidth">
            <el-input v-model.trim="temp.code" placeholder="请输入分组编码"></el-input>
          </el-form-item>
          <el-form-item label="排序" prop="sort" :label-width="formLabelWidth">
            <el-input type="number"  :min="1" :max="9999"  @input="e => temp.sort=parserNumber(e,1,9999)"  v-model="temp.sort" :value="temp.sort" placeholder="请输入排序号"></el-input>
          </el-form-item>
          <el-form-item label="SVG图" prop="image" :label-width="formLabelWidth">
            <el-upload
              class="upload-demo"
              style="max-width: 379px;"
              :action="uploadUrl"
              :headers="importHeader"
              :on-success="handlesuccess"
              :on-remove="handleRemove"
              :before-remove="beforeRemove"
              :before-upload="beforeAvatarUpload"
              :on-exceed="handleExceed"
              multiple
              :limit="1"
              :file-list="imgList"
              accept=".SVG, .svg"
              list-type="picture"
            >
              <el-button size="min" icon="el-icon-upload" type="primary">选择图片</el-button>
            </el-upload>
          </el-form-item>
          <div class="submitArea">
            <el-button type="primary" @click="dialogStatus === 'add' ? addClick('temp') : updateData('temp')">
              立即提交
            </el-button>
            <el-button @click="resetForm('temp')">
              重置
            </el-button>
          </div>
        </el-form>
      </el-dialog>

      <el-dialog v-dialogDrag width="850px !important" title="适用车型" :visible.sync="dialogApplyFormVisible" destroy-on-close v-if="dialogApplyFormVisible">
        <!-- 适用车型 -->
        <el-form ref='apply' :model="temp" label-position="center" :validate-on-rule-change="false">
          <div class="secondFloat">
            <el-form :inline="true" ref="applymodel" :model="applymodel" class="demo-form-inline">
              <el-form-item label="车型编码" prop="code">
              <el-input v-model="applymodel.code" placeholder="请输入车型编码"></el-input>
              </el-form-item>
              <el-form-item label="车型名称" prop="nameCh">
                <el-input v-model="applymodel.nameCh" placeholder="请输入车型名称"></el-input>
              </el-form-item>
              <div class="submitArea">
                <el-button type="primary" @click="applysearch" icon="el-icon-search">搜索</el-button>
                <el-button plain @click="resetApply()">重置</el-button>
              </div>
            </el-form>
          </div>
          <div class="tableDetail">
            <el-table
              ref="applytable"
              :row-key="getRowKeys"
              style="width:100%"
              border
              stripe
              highlight-current-row
              :data="applyModelList"
              tooltip-effect="dark"
              :header-cell-style="{
                'text-align': 'center',
                'background-color': 'var(--other-color)',
              }"
              @selection-change="selectapplymodel"
            >
              <el-table-column v-if="!isApply" type="selection" width="40" fixed="left" align="center" :reserve-selection="true"></el-table-column>
              <el-table-column v-if="isApply" label="序号" type="index" width="60" align="center"></el-table-column>

              <el-table-column label="品牌" prop="brandName" min-width="100"></el-table-column>
              <el-table-column label="车系" prop="trainName" min-width="100"></el-table-column>
              <el-table-column label="车型年款" prop="year" min-width="100"></el-table-column>
              <el-table-column label="车型编码" prop="code" min-width="150"></el-table-column>
              <el-table-column label="中文名称" prop="nameCh" min-width="150"></el-table-column>
            </el-table>
            <!-- <div > -->
              <el-button v-if="!isApply" type="primary"  @click="selectapply">确定</el-button>
            <!-- </div> -->
          </div>
        </el-form>
      </el-dialog>
      <el-dialog v-dialogDrag title="SVG图" :visible.sync="dialogBigImageVisible">
        <div class="fileImg">
          <div id="mysvg" style="height:100%"></div>
        </div>
      </el-dialog>
      <el-dialog v-dialogDrag title="批量导入配件" :visible.sync="dialogImportFormVisible">
        <el-form ref="importfile" :model="importfile" class="demo-form-inline">
          <el-form-item label="主组名称" prop="topCnName" >
            <el-input v-model="importfile.topCnName" readonly="readonly"></el-input>
          </el-form-item>
          <el-form-item label="总成编码" prop="code" >
            <el-input v-model="importfile.code" readonly="readonly"></el-input>
          </el-form-item>
          <el-form-item label="总成名称" prop="cnName" >
            <el-input v-model="importfile.cnName" readonly="readonly"></el-input>
          </el-form-item>
          <el-form-item>
            <el-upload
                  class="upload-demo inline-block"
                  ref="elUpload"
                  action="#"
                  :show-file-list="false"
                  multiple
                  :limit="1"
                  :before-upload="onBeforeUpload"
                >
                <el-button type="primary" v-show="showUploadBtn" >选择文件</el-button>
                <div slot="tip" class="el-upload__tip" v-if="isfinish">只能上传zip文件，且文件以总成编码命名</div>

                </el-upload>
                <el-progress style="width:350px;margin-top:50px;margin-left:10px"  v-show="showUploadProcess" color="green" type="line"  :text-inside="true" :stroke-width="20" :percentage="percentage" ></el-progress>
                 <el-upload
                  ref="elUploadResult"
                  action="#"
                  :show-file-list="true"
                  :file-list="zipList"
                  :limit="1"
                  :before-remove="handleBeforeRemove"
                  :on-remove="handleRemove"
                >
               </el-upload>
              <!-- <el-button type="text" v-if="hasPerm('menuAsimss1A2B_107')" size="min" icon="bulkImport-icon">批量上传</el-button> -->
            <!-- </el-upload> -->
          </el-form-item>
          <div class="submitArea">
            <el-button type="primary" @click="importSubmit()" >提交</el-button>
            <el-button plain @click="resetimport()">取消</el-button>
          </div>
        </el-form>
      </el-dialog>
      <!-- 清空数据 -->
      <el-dialog v-dialogDrag title="清空数据" :visible.sync="dialogclearFormVisible">
        <el-form ref="importfile" :model="importfile" class="demo-form-inline">
          <el-form-item label="主组名称" prop="topCnName" >
            <el-input v-model="importfile.topCnName" readonly="readonly"></el-input>
          </el-form-item>
          <el-form-item label="总成编码" prop="code" >
            <el-input v-model="importfile.code" readonly="readonly"></el-input>
          </el-form-item>
          <el-form-item label="总成名称" prop="cnName" >
            <el-input v-model="importfile.cnName" readonly="readonly"></el-input>
          </el-form-item>
          <div class="submitArea">
            <el-button type="primary" @click="clearDataFun()" >提交</el-button>
          </div>
        </el-form>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { loadSvg } from '@/plugins/mysvg.js'
import { sysServerUrl, handleAlert } from '@/assets/js/common.js'
// import SelectTree from '@/components/TreeView/SelectTree.vue';
import Pagination from '@/components/Pagination'
import { groupAll, directoryList,directorySvg, directoryAdd, directoryEdit,directoryEditPage, directoryDel, directoryDels,
  directoryTreeList, directoryTrainYear, basicDirectoryCarModelList, directoryFinishUpload, directoryImportZip, directoryTemplate, directoryClearAll} from '@/api/epcmgt.js';
  import {  procSplitFile, checkUploadProgress } from '@/api/sysmgt.js'
import SparkMD5 from 'spark-md5'

/** 计算文件md5值
 * @param file 文件
 * @param chunkSize 分片大小
 * @returns Promise
 */
 function getmd5(file, chunkSize) {
    return new Promise((resolve, reject) => {
        let blobSlice = File.prototype.slice || File.prototype.mozSlice || File.prototype.webkitSlice;
        let chunks = Math.ceil(file.size / chunkSize);
        let currentChunk = 0;
        let spark = new SparkMD5.ArrayBuffer();
        let fileReader = new FileReader();
        fileReader.onload = function(e) {
            spark.append(e.target.result);
            currentChunk++;
            if (currentChunk < chunks) {
                loadNext();
            } else {
                let md5 = spark.end();
                resolve(md5);
                //  console.log(md5);
            }
        };
        fileReader.onerror = function(e) {
            reject(e);
        };
        function loadNext() {
            let start = currentChunk * chunkSize;
            let end = start + chunkSize;
            if (end > file.size){
                end = file.size;
            }
            fileReader.readAsArrayBuffer(blobSlice.call(file, start, end));
        }
        loadNext();
    });
}


export default {
  name: 'epcmgtgroupinglist',
  // components: { Pagination ,SelectTree },
  components: { Pagination },
  data () {
    return {
      groupList:[], // 主组数据
      trainList: [],  // 车系数据
      yearList: [],  // 年款数据
      modelList:[],  // 车型数据
      yearTree: [],
      // 车系的数据默认字段
      defaultProps: {
        parent: 'pid',   // 父级唯一标识
        value: 'id',          // 唯一标识
        label: 'nameCh',       // 标签显示
        children: 'children', // 子级
      },

      formInline: {
        carTrainId: '',  // 车系
        carModelYear: '',  // 年款
        carModelId: '',  // 车型
        id: '',
        cnName: '',  // 分组名称
        code: '',  // 分组编码
        catalogId: '',// 主组
      },

      temp: {
        id: '',   // 主键ID
        pid: '',   // 主组ID
        cnName: '',  // 分组名称
        code: '',  // 分组编码
        sltModelId: '',  // 适用车型的ID
        sltModelname: '', // 适用车型名称
        carTrainId: '',  // 车系
        carModelYear: '',  // 年款
        carModelId: '',  // 车型
        sort: 1,   // 排序
        svgimgupload: '',  // 图片路径
      },
      dialogclearFormVisible: false,
      dialogApplyFormVisible: false,
      // 适用车型查询
      applymodel:{
        nameCh: '',
        code: '',
      },
      applyModelLists:[],
      applyModelList:[],  // 适用车型列表
      selectapplymodelList: [],  // 选中的适用车型
      imgList: [],  // 图片列表
      uploadUrl: '',
      isFlag: true,
      sltModelYear: '',
      imageurl: '',
      deleteList:[], // 批量选中
      dialogBigImageVisible: false,
      svgimgupload:"",  // 图片路径
      imgupload:"",
      dialogFormVisible: false,
      dialogStatus: '',
      editStatus:false,
      textMap: {
        edit: '编辑分组',
        add: '新增分组',
      },
      resultList: [],
      firmList: [],
      isApply: false,  // 点击适用车型
      memberList: [],

      dialogImportFormVisible: false,
      importfile:{
        topCnName: '',
        code: '',
        id: '',
        cnName: ''
      },

      principalList: [],
      modelName: "",
      pagesize: 10,
      currentPage: 1,
      total: 0,
      formLabelWidth: '100px',
      // rules: {
      //   carTrainId: [{ required: true, message: '车系不能为空', trigger: ['blur', 'change'] }],
      //   carModelYear: [{ required: true, message: '年款名称不能为空', trigger:['blur', 'change'] }],
      //   carModelId: [{ required: true, message: '车型不能为空', trigger: ['blur', 'change'] }],
      //   id: [{ required: true, message: '主组不能为空', trigger: ['blur', 'change'] }],
      //   cnName: [{ required: true, message: '名称不能为空', trigger: ['blur', 'change'] }],
      //   code: [{ required: true, message: '编码不能为空', trigger: ['blur', 'change'] }],
      //   // image: [{ required: true, message: '图片不能为空', trigger: ['blur', 'change'] }]
      // },
      isfinish: true,
      fileList: [],
      zipList:[],
      //切片文件
      fileShard:{},
      //当前文件
      curFile:{},
        //文件分割的开始位置
      start: 0,
      //文件分割的结束位置
      end: 0,
      //文件大小
      fileSize: 0,
      fileKey: '',
      fileShardIndex: 0,
      fileShardTotal: 0,
      fileShardSize: 0,
      switchC: false,
      percentage: 0,
      showUploadBtn:false,
      showUploadProcess:false,
      flagType: 'temp/parts',
    }
  },

  computed: {
    importHeader: function () {
      return { Authorization: sessionStorage.token };
    }
  },

  methods: {
    // 分页查询数据
    dataList () {
      this.isApply = false
      var params = {
        page: this.currentPage,   // 当前页
        limit: this.pagesize,     // 每页显示的条数
        carTrainId: this.formInline.carTrainId,  // 车系
        carModelYear: this.formInline.carModelYear,  // 年款
        carModelId: this.formInline.carModelId,  // 车型
        catalogId: this.formInline.catalogId,   // 主组
        cnName: this.formInline.cnName,  // 分组名称
        code: this.formInline.code,  // 分组编码
      }
      directoryList(params).then(res => {
        this.total = res.data.total    // 总条数
        this.resultList = res.data.data   // 数据
      })
    },

    // 主组数据查询, 根据车系年款
    getGroupList(){
      let trainIdParam = ''
      let yearParam = ''
      this.groupList = []
      if(this.dialogFormVisible){
        // 添加修改
        trainIdParam = this.temp.carTrainId;
        yearParam = this.temp.carModelYear;
      }else{
        // 查询
        trainIdParam = this.formInline.carTrainId;
        yearParam = this.formInline.carModelYear;
      }
      if (!trainIdParam || !yearParam) {
        // handleAlert('error','请选择车系年款');
        return false;
      }
      var params = {
        carTarinId: trainIdParam,
        carModelYear: yearParam
      }
      groupAll(params).then(res => {

        this.groupList = res.data.data;
      })
    },

    // 查询 品牌 - 车系
    getTrainList () {
      directoryTreeList().then(res => {
        this.trainList = res.data.data
      })
    },

    // 查询 年款 - 车型
    getTrainYearList(trainId){

      this.temp.sltModelId = '';
      this.temp.sltModelname = '';
      this.temp.carModelYear = '';
      this.temp.pid = '';
      this.formInline.carModelYear = '';
      this.formInline.carModelId = '';
      this.formInline.catalogId = '';
      this.yearList = [];
      this.modelList = [];
      this.groupList = [];
      if(!trainId){
        return false;
      }
      var params = {
        trainId: trainId
      }
      directoryTrainYear(params).then(res => {
        this.yearTree = res.data.data.cmList
        let list = [];
        if(this.yearTree){
          for (let i = 0; i < this.yearTree.length; i++) {
            list.push(this.yearTree[i].year) ;
          }
        }
        this.yearList = list
      })
    },

    // 查询车型
    getModelList(year){

      this.temp.sltModelId = '';
      this.temp.sltModelname = '';
      this.temp.pid = '';
      this.formInline.carModelId = '';
      this.formInline.catalogId = '';
      this.modelList = [];
      this.groupList = []
      if(!year){
        // handleAlert('error','请选择年款')
        return false;
      }
      let list = [];
      for (let i = 0; i < this.yearTree.length; i++) {
        if(this.yearTree[i].year == year){
          list = this.yearTree[i].ychild
          break;
        }
      }
      this.modelList = list;
      // 获取主组
      this.getGroupList();
    },

    // 重置 查询适用车型
    resetApply(){
      this.applymodel.code = '';
      this.applymodel.nameCh = '';
      let list = []
      this.applyModelLists.forEach(function (item) {
       list.push(item)
      })
      this.applyModelList = list
    },

    getRowKeys(row){
      return row.id;
    },

     // 查看  适用车型
    handelDetail (row) {

      let _this = this
      _this.selectapplymodelList = []
      _this.isApply = true
      _this.resetApply()

      // this.dialogStatus = 'detail'
      setTimeout(() => {
        _this.isApply = true
      })
      _this.dialogApplyFormVisible = true
      _this.temp.carTrainId= '',  // 车系
      _this.temp.carModelYear= '',  // 年款
      _this.getDirectoryCarModelList(row.id, "1")
    },
    // 查询适用车型
    getDirectoryCarModelList(id, type){
      var params = new URLSearchParams()
      params.append('trainId', this.temp.carTrainId)
      params.append('year', this.temp.carModelYear)
      params.append('directoryId', id)
      params.append('partsId', '')
      params.append('type', type)
      basicDirectoryCarModelList(params).then(res => {
        this.applyModelList = res.data.data
        let list = []
        this.applyModelList.forEach(function (item) {
          item.nameCh = item.nameCh + item.alias
          list.push(item)
        })
        this.applyModelLists = list
      })
    },
    // 搜索适用车型
    applysearch(){
      let name = this.applymodel.nameCh
      let code = this.applymodel.code
      if(!name && !code){
        this.resetApply();
        return false;
      }
      let list = []
      this.applyModelLists.forEach(function (item) {
          if (name && name.length>0 && (!code || code.length<=0) && item.nameCh.indexOf(name) != -1) {// 查询名称
            list.push(item)
          }else if ((!name || name.length<=0) && code && code.length>0 && item.code.indexOf(code) != -1) {// 查询编码
            list.push(item)
          }else if(item.code.indexOf(code) != -1 && item.nameCh.indexOf(name) != -1){ // 查询名称和编码
            list.push(item)
          }
      })
      this.applyModelList = list

    },

    // 选中
    selectapplymodel(val){
      this.selectapplymodelList = val
    },
    // 确定适用车型
    selectapply(){
      let sltModelIds = []
      let sltModelnames = []
      this.selectapplymodelList.forEach(function (item) {
        sltModelIds.push(item.id)
        sltModelnames.push(item.nameCh)
      })
      this.temp.sltModelId = sltModelIds.toString()
      this.temp.sltModelname = sltModelnames.toString()
      this.dialogApplyFormVisible = false
    },

    // 选择车型
    selectmodel(){
      if (this.temp.carTrainId == '' && !this.editStatus) {
        handleAlert('error','请选择车系')
        return false;
      }
      if (this.temp.carModelYear == '' && !this.editStatus) {
        handleAlert('error','请选择年款')
        return false;
      }
      setTimeout(() => {
        this.isApply = false

        if (this.editStatus) {
          this.applyModelList.forEach(item => {
            if(item.isEpcPart){
              this.$refs.applytable.toggleRowSelection(item);
            }
          })
        }
      })

      this.resetApply()
      this.dialogApplyFormVisible = true
      if (!this.editStatus) {
        this.getDirectoryCarModelList('', "1")
      }
    },

    // 搜索
    onSubmit () {
      this.currentPage=1
      this.dataList();
    },



    resetTemp () {
      this.temp = {
        id: '',   // 主键ID
        pid: '',   // 主组ID
        cnName: '',  // 分组名称
        code: '',  // 分组编码
        sltModelId: '',  // 适用车型的ID
        sltModelname: '', // 适用车型名称
        carTrainId: '',  // 车系
        carModelYear: '',  // 年款
        carModelId: '',  // 车型
        sort: 1,   // 排序
        // svgimgupload: '',  // 图片路径
      }
      // this.$nextTick(function() {
      //   this.$refs.temp.clearValidate();
      // })
    },

    resetForm (temp) {
      if(this.dialogStatus == 'edit'){
        this.temp.id= '',   // 主键ID
        this.temp.pid= '',   // 主组ID
        this.temp.cnName= '',  // 分组名称
        this.temp.code= '',  // 分组编码
        this.temp.sltModelId= '',  // 适用车型的ID
        this.temp.carTrainId= '',  // 车系
        this.temp.carModelYear= '',  // 年款
        this.temp.carModelId= '',  // 车型
        this.temp.sort= 1   // 排序

        // this.$nextTick(function() {
        //   this.$refs.temp.clearValidate();
        // })
      } else {
        this.resetTemp()
      }
    },
    // 增加
    handelAdd () {
      var _this = this
      // 清空图片
      _this.imgupload = ''
      _this.imgList = []
      // 清空年款
      _this.trainYear=[]
      // 打开弹窗
      _this.dialogFormVisible = true
      _this.dialogStatus = 'add'
      _this.editStatus = false
      setTimeout(() => {
        // 清空车系
        // _this.$refs.addSelectTree.initSelected('','')
        _this.resetTemp()
      })
    },
    addClick (temp) {
      var _this = this
      if (_this.temp.sltModelId == '') {
         handleAlert('error', "请选择适用车型")
         return false
      }
      if (_this.imgupload == '') {
         handleAlert('error', "请选择SVG图")
         return false
      }
      if (_this.temp.pid == '') {
         handleAlert('error', "请选择主组")
         return false
      }
      if (_this.temp.cnName == '') {
         handleAlert('error', "请输入分组名称")
         return false
      }
      if (_this.temp.code == '') {
         handleAlert('error', "请输入分组编码")
         return false
      }

      var params = new URLSearchParams()
      params.append('carTrainId', _this.temp.carTrainId)  // 车系
      params.append('carModelYear', _this.temp.carModelYear)  // 年款
      params.append('catalogId', _this.temp.pid)   // 主组
      params.append('cnName', _this.temp.cnName)  // 名称
      params.append('code', _this.temp.code)   // 编码
      params.append('sort', _this.temp.sort)   // 排序
      params.append('svgimgupload', _this.imgupload)   // 图片
      params.append('sltModelId', _this.temp.sltModelId)   // 适用车型
      directoryAdd(params).then(res => {
        if (res.data.code === 100) {
          handleAlert('success',res.data.msg)
          _this.dataList()
          _this.dialogFormVisible = false
          _this.dialogStatus = ''
        } else {
          handleAlert('error',res.data.msg)
        }
      })

    },


    // 编辑
    handelEdit (row) {
      var _this=this

      // 清除数据
      _this.resetTemp()

      // 记录原图片
      _this.svgimgupload = row.path
      _this.imgupload = row.path
      _this.imgList = []
      var img = {url: this.imageurl + 'sys/upload/display?filePath=' + row.path }
      _this.imgList.push(img)

      // 赋值
      _this.temp.carTrainId = row.carTrainId
      _this.temp.cnName = row.cn_name
      _this.temp.code = row.code
      _this.temp.pid = row.pid
      _this.temp.id = row.id

      setTimeout(() => {
        _this.isApply = false
        _this.getGroupingModel(_this.temp.id)
      })

      // 打开弹窗
      _this.dialogFormVisible = true
      _this.dialogStatus = 'edit'
      _this.editStatus=true
    },
    updateData (temp) {
      var _this=this
      if (this.temp.cnName == '') {
        handleAlert('error', '名称不能为空')
        return false;
      }
      if (this.temp.code == '') {
        handleAlert('error', '编码不能为空')
        return false;
      }
      if(this.imgupload == ''){
        handleAlert('error', '图片不能为空')
        return false;
      }
      var params = new URLSearchParams()
      params.append('id', this.temp.id)
      params.append('carTrainId', _this.temp.carTrainId)  // 车系
      params.append('carModelYear', _this.temp.carModelYear)  // 年款
      if (this.temp.sltModelId != '') {
         params.append('sltModelId', this.temp.sltModelId)
      }
      if (this.svgimgupload != this.imgupload && this.imgupload != '') {  // 更新了图片
        params.append('svgimgupload', this.imgupload)
      }

      if (this.temp.pid != '') {
        params.append('catalogId', this.temp.pid)
      }

      if (this.temp.cnName != '') {
         params.append('cnName', this.temp.cnName)
      }
      if (this.temp.code != '') {
         params.append('code', this.temp.code)
      }
      params.append('sort', this.temp.sort)

      directoryEdit(params).then(res => {
        if (res.data.code === 100) {
          handleAlert('success','保存成功')
          _this.dataList()
          _this.dialogFormVisible = false
          _this.dialogStatus = ''
        } else {
          handleAlert('error',res.data.msg)
        }
      }).catch(err => {
        if (err !== null && err !== '' && err.responseText !== null) {
          handleAlert('error','提交失败,请重试')
        }
      })


    },

    // 打开编辑页，获取选中的车型
    getGroupingModel(id){
       var params = new URLSearchParams()
        params.append('id', id)
        directoryEditPage(params).then(res => {
          this.temp.pid = res.data.data.catalogId
          this.temp.carTrainId = res.data.data.trainId
          this.temp.carModelYear = res.data.data.year
          this.applyModelList = res.data.data.carModelList
          this.getGroupList()

          let list = [];
          let lts = [];
          let ltes = [];
          this.applyModelList.forEach(function (item) {
            item.nameCh = item.nameCh + item.alias
            if (item.isEpcPart) {
              list.push(item.nameCh)
              ltes.push(item)
            }
            lts.push(item)
          })
          this.applyModelLists = lts

          this.temp.sltModelname = list.toString()
        })
    },

    resetCode(){
      this.roleState()
    },
    // 删除
    handelDelete (row) {

      var _this=this
      this.$confirm('确定删除【' + row.cn_name + '】分组吗?', '删除分组', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var params = new URLSearchParams()
        params.append('id', (row.id))
        directoryDel(params).then(res => {
          if (res.data.code === 100) {
            handleAlert('success','删除成功')
            if(this.resultList!=null&&this.resultList.length==1){
              this.currentPage =this.currentPage-1
            }
            this.dataList()
          }else{
            handleAlert('error','删除失败，' + res.data.msg)
          }
        })
      }).catch((error)=>{
        handleAlert('info','取消删除')
      })
    },

    // 批量删除
    handleSelectionChange (val) {
      this.deleteList = val
    },
    batchDelClick(){
      let list = []

      this.deleteList.forEach(function (item) {
        list.push(item.id)
      })
      this.$confirm('确定删除所选中的分组吗?', '删除分组', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var params = new URLSearchParams()
        params.append('ids', list.toString())
        directoryDels(params).then(res => {
          if (res.data.code === 100) {
            handleAlert('success','删除成功' + res.data.msg)
            if(this.resultList!=null&&this.resultList.length==1){
              this.currentPage =this.currentPage-1
            }
            this.dataList()
          }else{
            handleAlert('error','删除失败，' + res.data.msg)
          }
        })
      }).catch((error)=>{
        handleAlert('info','取消删除')
      })
    },

    // 添加svg图
    handlesuccess (file, fileList) {
      console.log("======", file);
      this.imgupload = file.data.fileUrl

      this.imgList = []
      if(this.imgupload != null && this.imgupload.length > 0){
        var img = {url: this.imageurl + 'sys/upload/display?filePath=' + this.imgupload }
        this.imgList.push(img)
      }
      this.isFlag = true;
    },
    // 文件上传前的钩子
    beforeAvatarUpload (file) {
      // this.imgList = []
      // 先上传到临时文件夹  temp/svg
      this.uploadUrl = this.imageurl + 'sys/upload/attach?flag=temp/svg';

      var fileName = file.name.substring(file.name.lastIndexOf(".")+1).toLowerCase()
      const extension1 = fileName === 'svg'
      const extension2 = fileName === 'SVG'
      const isLt2M = file.size / 1024 / 1024 < 100
      if (!extension1 && !extension2) {
        handleAlert('warning','上传图片只能是svg格式!')
        this.isFlag = false;
        return false;
      }
      if (!isLt2M) {
        handleAlert('warning','上传图片大小不能超过 100MB!')
        this.isFlag = false;
        return false;
      }
      console.log("========");
      return true;
      // return isLt2M
    },
    handleRemove (file, fileList) {

      this.imgList=[]
        this.imgupload = ''
        this.isFlag = true;
    },
    handleExceed (files, fileList) {
      handleAlert('warning',`当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
    },
    beforeRemove (file, fileList) {
      if (this.isFlag) {
        return this.$confirm(`确定移除选择文件？`, '删除',{type: 'warning'});
      }
    },

    // 显示svg图
    bigImg(row){
      var params = {
        groupId: row.id
      }
      directorySvg(params).then(res => {
        if (!res.data.data) {
          return
        }
        let svgPath = this.imageurl + 'sys/upload/display?filePath=' + res.data.data[0].path;

        setTimeout(function(){
          loadSvg(svgPath)
        },0);
      })

      this.dialogBigImageVisible = true

    },

    // 清空总成下的配件信息
    clearAll(){
      if(this.deleteList.length != 1){
        handleAlert('warning','请选择一个总成');
        return false;
      }
      this.importfile.topCnName = this.deleteList[0].topCnName;
      this.importfile.code = this.deleteList[0].code;
      this.importfile.id = this.deleteList[0].id;
      this.importfile.cnName = this.deleteList[0].cn_name;
      this.dialogclearFormVisible = true;
    },
    // 提交清空数据
    clearDataFun(){
      this.$confirm('数据清空就找不回来了哦，您确定要清空总成为【'+this.importfile.code+'】的数据吗？', '清空数据', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
          var params = new URLSearchParams()
          params.append('id', this.importfile.id)
          directoryClearAll(params).then(res => {
              if (res.data.code === 100) {
                this.dialogclearFormVisible = false;
                handleAlert('success','操作成功')
                if(this.resultList!=null&&this.resultList.length==1){
                  this.currentPage =this.currentPage-1
                }
                this.dataList()
              }else{
                handleAlert('error','操作失败，' + res.data.msg)
              }
            }).catch((error)=>{
            handleAlert('info','取消删除')
          })
      }).catch((error)=>{
        this.dialogclearFormVisible = false;
        handleAlert('info','取消删除')
      })
    },


    // 下载模板
    downTemplateClick () {
      var params ='';
      directoryTemplate(params).then(res => {
        if(!res.data){
          return
        }
        var name = "配件批量导入模板.zip";
        var blob = new Blob([res.data]);
        var url = window.URL.createObjectURL(blob);
        var aLink = document.createElement("a");
        aLink.style.display = "none";
        aLink.href = url;
        aLink.setAttribute("download", name);
        document.body.appendChild(aLink);
        aLink.click();
        document.body.removeChild(aLink); //下载完成移除元素
        window.URL.revokeObjectURL(url); //释放掉blob对象
      })
    },


    // 搜索的重置
    reset (formInline) {
      if (this.$refs[formInline].resetFields() !== undefined) {
        this.$refs[formInline].resetFields()
      }
      // this.$refs.subSelectTree.initSelected('','')
      this.formInline.carTrainId= ''  // 车系
      this.formInline.carModelYear= ''  // 年款
      this.formInline.carModelId= ''  // 车型
      this.formInline.id= ''   // 主组
      this.formInline.cnName= ''  // 分组名称
      this.formInline.code= ''  // 分组编码
      this.formInline.catalogId= ''
      this.currentPage = 1
      this.dataList()
    },

    // 取消，关闭弹窗
    resetimport(){
      this.fileList = []
      //切片文件
      this.fileShard={};
      //当前文件
      this.curFile={};
        //文件分割的开始位置
      this.start= 0;
      //文件分割的结束位置
      this.end= 0;
      //文件大
      this.fileSize= 0;
      this.fileKey= '';
      this.fileShardIndex= 0;
      this.fileShardTotal= 0;
      this.fileShardSize= 0;
      this.switchC= false;
      this.percentage= 0;
      this.showUploadBtn=true;
      this.showUploadProcess=false;
      this.dialogImportFormVisible = false;
    },

    // 批量上传, 打开弹窗
    batchupload(){
      if(this.deleteList.length != 1){
        handleAlert('warning','请选择一个总成');
        return false;
      }
      this.isfinish = true;
      this.importfile.topCnName = this.deleteList[0].topCnName;
      this.importfile.code = this.deleteList[0].code;
      this.importfile.id = this.deleteList[0].id;
      this.importfile.cnName = this.deleteList[0].cn_name;
      this.resetimport()
      this.dialogImportFormVisible = true;
    },
    // 提交
    importSubmit(){
      if (this.zipList.length <= 0) {
        handleAlert('warning','请选择文件')
        return false
      }
      let uploadFileName= this.zipList[0].name
      let uploadFilePath= this.zipList[0].filePath
      if ((uploadFileName && uploadFileName.length <= 0) || (uploadFilePath && uploadFilePath.length <= 0)) {
        handleAlert('warning','文件上传错误')
        return false
      }
      var params = new URLSearchParams()
      params.append('fileName', uploadFileName)
      params.append('filePath', uploadFilePath)
      params.append('id', this.importfile.id)
      params.append('code', this.importfile.code)
      directoryImportZip(params).then(res => {
        if (res.data.code == 100) {
          handleAlert('success', '导入成功')
          this.onSubmit()
          this.dialogImportFormVisible = false
        }else{
          handleAlert('error',res.data.msg)
        }
      }).catch(err => {
        handleAlert('error','提交失败,请重试')
      })

    },
    // 文件上传之前
    onBeforeUpload(file){
      let text=""
        // 校验文件名称
        let code = this.importfile.code
        let name = file.name.substring(0, file.name.lastIndexOf('.'))
        if(name != code){
          text="文件名要与总成编码【"+code+"】一致哦";
          this.$message.error(text);
          return false;
        }

        this.flagType='temp/parts'

          // 获取文件后缀
          var fileExt =file.name.substring(file.name.lastIndexOf('.')+1).toLowerCase();
          // 文件后缀是否是 zip
          const zipExt = fileExt === 'zip'
          // 文件大小不能超过1G
          const isLimit = file.size / 1024 / 1024 < 1024
          if(!zipExt) {
          text="上传文件只能是 zip 格式!";
          this.$message.error(text)
          return false;
          }
          if (!isLimit) {
          text="上传文件大小不能超过 1GB!";
          this.$message.error(text)
          return false;
          }
          this.fileShardSize = 1*1024 * 1024; //每片文件大小

          //点击后隐藏上传按钮 ，防止重复点击
          //$("#fileUpload").css('visibility','hidden');
          this.showUploadBtn=false
          this.showUploadProcess=true
          this.percentage=1
          var _this=this
          getmd5(file,_this.fileShardSize).then(e =>{
                  _this.switchC=false;
                  _this.fileShardIndex=1;//分片索引
                  _this.curFile=file;
                  _this.fileKey=e;
                  _this.fileSize=file.size;
                  _this.fileShardTotal=Math.ceil(file.size/_this.fileShardSize);//分片总数
                  var fileFullName=file.name;
                  _this.fileSuffix = fileFullName.substr(fileFullName.lastIndexOf('.') + 1);
                  _this.fileName = fileFullName.substr(0, fileFullName.lastIndexOf('.'));

                  //上传参数
                  var params =  new FormData()
                  params.append('fileName', _this.fileName)
                  params.append('fileShardTotal', _this.fileShardTotal)
                  params.append('fileKey', _this.fileKey)
                  params.append('fileSuffix', _this.fileSuffix)
                  params.append('fileShardSize', _this.fileShardSize)
                  params.append('fileSize', _this.fileSize)
                  params.append('fileFlag', _this.flagType)

                  _this.updateProgress(file,params)


              })


      },
      // 批量上传
      uploadFile (formData) {
        var _this=this
        // 上传
        procSplitFile(formData).then(res => {
          if(res.data.code==200){
            //上传分片完成
            if(res.data.shardIndex<_this.fileShardTotal){
              _this.fileShardIndex=_this.fileShardIndex+1;
              _this.start=(_this.fileShardIndex-1)*_this.fileShardSize;
              _this.end =Math.min(_this.curFile.size,_this.start+_this.fileShardSize);
              _this.fileSize=_this.curFile.size;
              var params =  new FormData()
              params.append('fileName', _this.fileName)
              params.append('fileShardTotal', _this.fileShardTotal)
              params.append('fileKey', _this.fileKey)
              params.append('fileSuffix', _this.fileSuffix)
              params.append('fileShardSize', _this.fileShardSize)
              params.append('fileSize', _this.fileSize)
              params.append('fileFlag', _this.flagType)
              params.append('fileShardIndex', _this.fileShardIndex)
              var fileShardtem=_this.curFile.slice(_this.start,_this.end);//从文件中获取当前分片数据
              let fileReader = new FileReader();
              //异步读取本地文件分片数据并转化为base64字符串
              fileReader.readAsDataURL(fileShardtem);
              //本地异步读取成功后，进行上传
              fileReader.onload = function (e) {
                let  base64str = e.target.result;
                params.append('base64', base64str)
                _this.uploadFile(params)
              }
              let perentNum=Math.ceil(this.fileShardIndex * 100 / this.fileShardTotal)
              if(perentNum>100){
                this.percentage=100
              }else{
                this.percentage=perentNum
              }
            }
          }else if(res.data.code==100){
            var fileId= res.data.id
            //上传完成
            _this.percentage=100
            _this.switchC=true

            _this.finishUpload(fileId)
          }
          console.log(this.percentage)
        }).catch((error)=>{
          if(error.response){
          console.log(error.response.data)
          console.log(error.response.status)
          console.log(error.response.headers)
          }else{
            console.log(error.message)
          }
        })

      },
      updateProgress(file,params){
        this.isfinish = false;
        var _this= this
        var param = new URLSearchParams()
        param.append('shardKey', _this.fileKey)
        // 批量上传
        checkUploadProgress(param).then(res => {
          if(res.data.code==200){
            //新文件
            _this.start=(_this.fileShardIndex-1)*_this.fileShardSize;
            _this.end =Math.min(file.size,_this.start+_this.fileShardSize);
            _this.fileShard=file.slice(_this.start,_this.end);//从文件中获取当前分片数据
          }else if(res.data.code==220){
            _this.fileShardIndex=res.data.ShardIndex;
            //有上传未完成的
            _this.start=(_this.fileShardIndex-1)*_this.fileShardSize;
            _this.end =Math.min(file.size,_this.start+_this.fileShardSize);
            _this.fileShard=file.slice(_this.start,_this.end);//从文件中获取当前分片数据
          }else if (res.data.code==240){
            //急速上传
            var fileId= res.data.id
            _this.percentage=100
            _this.switchC=true
            console.log(this.percentage)
            // this.$message({
            //   message: '极速上传成功',
            //   type: 'success'
            // })
            _this.finishUpload(fileId)
            return false;
          }
          //读取base64str
          let fileReader = new FileReader();
          //异步读取本地文件分片并转化为base64字符串
          fileReader.readAsDataURL(_this.fileShard);
          //本地异步读取成功，进行上传
          fileReader.onload=function (e){
            let  base64str=e.target.result;
            params.append('base64', base64str)
            params.append('fileShardIndex', _this.fileShardIndex)
            if(_this.switchC==false){
                _this.uploadFile(params)
            }
          }
        }).catch((error)=>{
          this.$message.error('上传错误')
          this.isfinish = true;
        })

      },
      finishUpload(fileId){
          var _this=this
            //进行保存提醒
            _this.uploadMsg = _this.$message({
                                duration:0,
                                message: "请稍等，正在保存...",
                                type: "warning"
                              });
            var param = new URLSearchParams()
            param.append('fileId', fileId)
            directoryFinishUpload(param).then(res => {
              if(res.data.code==100){
                  let uploadFileName= res.data.data.fileName
                  let uploadFilePath= res.data.data.filePath
                  _this.zipList = []
                  // _this.zipList = []
                  if(uploadFileName != null && uploadFileName.length > 0){
                    var fileObj = { name: uploadFileName , filePath:uploadFilePath}
                    _this.zipList.push(fileObj)
                    // var file = {name: uploadFileName, url: uploadFilePath}
                    // _this.zipList.push(file)
                  }
                  //关闭消息提醒
                  _this.uploadMsg.close()

                  //上传完成提示
                  _this.$message({
                    duration:2000,
                    message: '上传已完成',
                    type: 'success'
                  })
                _this.showUploadProcess=false
                // _this.showUploadBtn=true
              }
            })

      },
      handleBeforeRemove(){
      if(this.zipList!=null&&this.zipList.length>0){
          // var filePath= this.zipList[0].filePath;
            var _this=this
                  this.$confirm('确认删除文件？', '删除',{
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'warning'
                }).then(() => {
                  _this.showUploadBtn=true
                  this.isfinish = true;
                  _this.zipList=[];
                }).catch((error)=>{
                  handleAlert('info','取消删除')
                  return false;
                })
        }
        return false;
      },
  },
  mounted () {
    this.imageurl = sysServerUrl
    this.dataList()
    this.getTrainList()
  }
}
</script>
<style>
  .fileImg {
    width: 100%;
    height: 100%;
  }
</style>
