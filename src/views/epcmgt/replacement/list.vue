<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" ref="formInline" label-width="70px" :model="formInline" class="demo-form-inline">
        <el-form-item label="配件编码" prop="originalCode" >
          <el-input v-model="formInline.originalCode" placeholder="请输入配件编码"></el-input>
        </el-form-item>
        <el-form-item :label-width="formLabelWidth">
          <el-button type="primary" @click="onSubmit" icon="el-icon-search">搜索</el-button>
          <el-button plain @click="reset('formInline')">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="tableDetail">
      <div class="tableHandle" >
        <el-button type="text" size="min" icon="bulkImport-icon" @click="batchUpload()">
          批量上传
        </el-button>
        <el-button type="text" icon="el-icon-download" @click="downDataClick()">下载数据</el-button>
      </div>
        <el-table
          style="width:100%"
          border
          stripe
          ref="table"
          highlight-current-row
          :max-height="maximumHeight"
          :data="resultList"
          :header-cell-style="{
            'text-align': 'center',
            'background-color': 'var(--other-color)',
          }"
          @header-dragend="changeColWidth"
        >
        <el-table-column label="序号" type="index" width="60"  align="center"></el-table-column>
        <el-table-column label="旧配件编码" prop="originalCode" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column label="旧配件名称" prop="originalName" min-width="110" show-overflow-tooltip></el-table-column>
        <el-table-column label="新配件编码" prop="alternateCode" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column label="新配件名称" prop="alternateName" min-width="110" show-overflow-tooltip></el-table-column>
        <el-table-column label="替换日期" prop="alternateTime" width="100" align="center"></el-table-column>
        <el-table-column label="备注" prop="remark" min-width="150" show-overflow-tooltip></el-table-column>
        <!-- <el-table-column label="备注" prop="remark" min-width="200" >
          <template slot-scope="scope">
            <el-input v-model.trim="scope.row.remark" type="textarea" placeholder="请输入备注" rows="2"
              @change="updateNotes(scope.row,$event)" show-word-limit maxlength="500" :disabled="!hasPerm('menuAsimss5A4B_103')">
            </el-input>
          </template>
        </el-table-column> -->
          <el-table-column label="添加日期" prop="created_time" width="160" align="center">
            <template slot-scope="{row}">
              <div>
                {{ row.createdTime | conversion("yyyy-MM-dd HH:mm:ss") }}
              </div>
            </template>
          </el-table-column>
        <el-table-column label="操作" fixed="right" width="110" align="center">
          <template slot-scope="{row}">
            <el-button type="text" v-if="hasPerm('menuAsimss5A4B_103')" size="small" @click="handelUpdate(row)">编辑</el-button>
             <el-button type="text" v-if="hasPerm('menuAsimss5A4B_102')" size="small" @click="handelDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total>0" :total="total" :page.sync="currentPage" :limit.sync="pagesize" @pagination="dataList"/>

    </div>

    <el-dialog v-dialogDrag lock-scroll title="编辑" :visible.sync="dialogEditFormVisible" v-if="dialogEditFormVisible" :close-on-click-modal="false">
      <el-form :label-width="formLabelWidth" ref='replacement' :rules="replacement" :model="replacement" label-position="center" :validate-on-rule-change="false">
          <el-form-item label="旧配件编码" prop="originalCode">
            <el-input v-model="replacement.originalCode" disabled readonly></el-input>
          </el-form-item>
          <el-form-item label="旧配件名称" prop="originalName">
            <el-input v-model="replacement.originalName" placeholder="请输入旧配件名称" show-word-limit maxlength="60"></el-input>
          </el-form-item>
          <el-form-item label="新配件编码" prop="alternateCode">
            <el-input v-model="replacement.alternateCode" disabled readonly></el-input>
          </el-form-item>
          <el-form-item label="新配件名称" prop="alternateName">
            <el-input v-model="replacement.alternateName" placeholder="请输入新配件名称" show-word-limit maxlength="60"></el-input>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input  type="textarea" v-model="replacement.remark" placeholder="请输入备注" show-word-limit maxlength="500"></el-input>
          </el-form-item>

          <div class="submitArea">
            <el-button type="primary" @click="editClick">
              立即提交
            </el-button>
            <el-button @click="dialogEditFormVisible = false">
              取消
            </el-button>
          </div>
      </el-form>
    </el-dialog>

    <el-dialog v-dialogDrag width="700px !important" title="导出替换关系" :visible.sync="dialogTimeSlotVisible"
               destroy-on-close v-if="dialogTimeSlotVisible">
      <el-form ref="alloter" :label-width="formLabelWidth"  :validate-on-rule-change="false">
        <el-form-item >
          <span slot="label">日期范围
            <el-tooltip content="按添加日期筛选" placement="top">
              <i class="el-icon-question"></i>
            </el-tooltip>
          </span>

          <el-date-picker :editable="false" prop="begintime" align="center" value-format="yyyy-MM-dd" type="date" placeholder="添加日期的开始日期" v-model="startTimeSlot" :picker-options="pickerBeginTime"></el-date-picker>
          <span class="line">至</span>
          <el-date-picker :editable="false" prop="endtime" align="center" value-format="yyyy-MM-dd" type="date" placeholder="添加日期的结束日期" v-model="endTimeSlot" :picker-options="pickerEndTime"></el-date-picker>
        </el-form-item>
        <el-form-item>
          <span style="color: #ED252D;font-size: 12px;margin: 0px 6px;">
                提示：若不选择日期，则导出全部
          </span>
        </el-form-item>
        <div class="submitArea">
          <el-button type="primary" @click="exportTimeSlotClick()">
            导出
          </el-button>
          <el-button @click="dialogTimeSlotVisible = false">
            取消
          </el-button>
        </div>
      </el-form>
    </el-dialog>

    <!-- 批量上传 -->
    <el-dialog v-dialogDrag title="批量上传" :visible.sync="dialogImportFormVisible" :close-on-click-modal="false">
      <ol class="upload-ol">
        <li>
          <div>下载模板</div>
          <div>导入文件必须符合模板格式，请<a style="cursor:pointer" @click="downTemplateClick()">下载模板</a>。</div>
          <div style="color: #ED252D;font-size: 12px;margin: 0px 6px;">注：上传文件仅为批量修改替换关系的备注，不会新增替换关系</div>
        </li>
        <li class="uplaodImage">
          <div>上传文件</div>
          <el-form ref="uploadForm" class="demo-form-inline">
            <el-form-item>
              <el-upload
                class="upload-demo"
                drag
                ref="elUpload"
                action="#"
                :show-file-list="false"
                :on-exceed="handleExceed"
                multiple
                :limit="1"
                :before-upload="onBeforeUpload"
                :http-request="uploadFile"
                v-show="showUploadBtn"
                :file-list="fileList"
                :before-remove="handleBeforeRemove"
                :on-remove="handleRemove"
              >
                <div class="el-upload_text">
                  <p>点击上传</p>
                </div>
              </el-upload>
            </el-form-item>
          </el-form>
        </li>
      </ol>
    </el-dialog>

  </div>
</template>
<script>
import {cmsServerUrl, handleAlert, sysServerUrl, tableHeight, tableMaxHeight} from '@/assets/js/common.js'
import Pagination from '@/components/Pagination'
import {
  replacementList,
  replacementAdd,
  replacementEdit,
  replacementDel,
  replacementTemplate,
  replacementImportExcel,
  replacementExport,
  replacementUpdated,
  downSvg
} from '@/api/epcmgt.js';
export default {
  name: 'epcmgtreplacementlist',
  components: { Pagination },
  data () {
    return {

      memberForm:{
        auditUser:"",
        developmentUser:"",
        drawingUser:"",
      },

      formInline: {
        originalCode: '',
        alternateCode: '',
        alternateType: '',
        end: null,
        start: null,
      },
      temp: {
        id: '',
        cnName: '',
        code: '',
        sort: 1,
      },

      directionList:[
        { name: 'NN'},
        { name: 'NY'},
        { name: 'YY'},
      ],
      fileList:[],
      deleteList: [],
      dialogFormVisible: false,
      dialogStatus: '',
      editStatus:false,
      textMap: {
        edit: '编辑主组',
        add: '新增主组',
      },
      resultList: [],

      pagesize: 20,
      currentPage: 1,
      total: 0,
      formLabelWidth: '100px',
      showUploadProcess:false,
      percentage: 0,
      scheduleTitle: '上传进度',

      pickerBeginTime: {
        disabledDate: (time) => {
          return this.endTimeSlot != null ? time.getTime() > new Date(this.endTimeSlot) : false //只能选结束日期之前的日期
          //返回---结束时间是否有值？   可选时间小于结束时间   ：  任意时间都可选
        }
      },
      pickerEndTime: {
        disabledDate: (time) => {
          return this.startTimeSlot != null ? time.getTime() < new Date(this.startTimeSlot) : false //只能选开始时间之后的日期
          //返回---开始时间是否有值？   可选时间大于开始时间   ：  任意时间都可选
        }
      },
      maximumHeight: 0,

      // 编辑
      dialogEditFormVisible: false,
      replacement:{
        id: '',
        originalCode: '',
        originalName: '',
        alternateCode: '',
        alternateName: '',
        remark: '',
      },
      dialogTimeSlotVisible: false,
      startTimeSlot:'',
      endTimeSlot:'',

      showUploadBtn:false,
      dialogImportFormVisible: false,

    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth(){
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    // 分页查询数据
    dataList () {
      this.$loading.show();
      var params = new URLSearchParams()
      params.append('page', this.currentPage)
      params.append('limit', this.pagesize)
      params.append('originalCode', this.formInline.originalCode)
      replacementList(params).then(res => {
        if (res.data.code == 100) {
          this.total = res.data.total    // 总条数
          this.resultList = res.data.data   // 数据
        } else {
          handleAlert('error', res.data.msg)
        }
        this.tableHeightArea()
        this.$loading.hide();
      })
    },


    // 编辑弹窗
    handelUpdate(row){
      this.replacement.id = row.id;
      this.replacement.originalName = row.originalName;
      this.replacement.originalCode = row.originalCode;
      this.replacement.alternateName = row.alternateName;
      this.replacement.alternateCode = row.alternateCode;
      this.replacement.remark = row.remark;
      this.dialogEditFormVisible = true;
    },
    // 确认编辑
    editClick(){
      this.$loading.show()
      var params = new URLSearchParams()
      params.append('id', this.replacement.id)
      params.append('originalName', this.replacement.originalName)
      params.append('alternateName', this.replacement.alternateName)
      let remark = this.replacement.remark
      remark = remark && remark.length > 0 ? remark : "";
      params.append('remark', remark)
      replacementUpdated(params).then(res => {
        if(res.data.code === 100){
          handleAlert('success', "操作成功")
          this.dialogEditFormVisible = false;
          this.dataList()
        }else{
          handleAlert('error', res.data.msg)
        }
        this.$loading.hide()
      }).catch(e =>{
        this.$loading.hide()
        handleAlert('error', "系统繁忙，请稍后再试")
      })
    },

    // 删除
    handelDelete (row) {

      var _this=this
      this.$confirm('确定删除【' + row.originalName + '】的替换件信息?', '删除替换件', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var params = new URLSearchParams()
        params.append("id", row.id)
        replacementDel(params).then(res => {
          if (res.data.code === 100) {
            handleAlert('success','删除成功')
            if(this.resultList!=null&&this.resultList.length==1){
              this.currentPage =this.currentPage-1
            }
            this.dataList()
          }else{
            handleAlert('error','删除失败')
          }
        })
      }).catch((error)=>{
        handleAlert('info','取消删除')
      })
    },


    // 搜索
    keyDown(e) {
      if (e.keyCode === 13) {
        this.onSubmit()
      }
    },
    onSubmit () {
      this.currentPage=1
      this.dataList();
    },
    reset(){
      this.formInline.originalCode = '';
      this.formInline.alternateCode = '';
      this.formInline.alternateType = '';
      this.formInline.end = null;
      this.formInline.start = null;
      this.onSubmit();
    },

    // 下载模板
    downDataClick(){
      console.log("下载数据")
      this.dialogTimeSlotVisible = true;
      this.startTimeSlot = "";
      this.endTimeSlot = "";
    },
    // 下载模板
    downTemplateClick () {
      replacementTemplate().then(res => {
        if (!res.data) {
          return
        }
        var name = "替换关系备注导入模板.xlsx";
        var blob = new Blob([res.data]);
        var url = window.URL.createObjectURL(blob);
        var aLink = document.createElement("a");
        aLink.style.display = "none";
        aLink.href = url;
        aLink.setAttribute("download", name);
        document.body.appendChild(aLink);
        aLink.click();
        document.body.removeChild(aLink); //下载完成移除元素
        window.URL.revokeObjectURL(url); //释放掉blob对象
      })
    },

    exportTimeSlotClick(){
      var params = new URLSearchParams();
      params.append('startTime', this.startTimeSlot);
      params.append('endTime', this.endTimeSlot);
      this.$loading.show()
      replacementExport(params).then(res => {
        console.log(",,,", res)
        // loading.close();
        if (res.data.code == 100) {

          let path = sysServerUrl + res.data.data
          let name = path.substring(path.lastIndexOf("/") + 1)
          name = name.substring(0, name.lastIndexOf("_")) + ".xlsx"
          handleAlert("info", "打包已完成，下载中...")
          downSvg(path).then(resc => {
            if (!resc.data) {
              this.$loading.hide()
              handleAlert("error", "下载失败")
              return
            }
            var blob = new Blob([resc.data]);
            var url = window.URL.createObjectURL(blob);
            var aLink = document.createElement("a");
            aLink.style.display = "none";
            aLink.href = url;
            aLink.setAttribute("download", name);
            document.body.appendChild(aLink);
            aLink.click();
            document.body.removeChild(aLink); //下载完成移除元素
            window.URL.revokeObjectURL(url); //释放掉blob对象
            this.$loading.hide()
          }).catch(e => {
            console.log("10", e);
            this.$loading.hide()
            handleAlert("error", "系统异常，请稍后再试")
          })
        } else {
          this.$loading.hide()
          handleAlert("error", "数据打包错误; " + res.data.msg)
        }
      }).catch(e => {
        this.$loading.hide()
        console.log("20", e);
        handleAlert("error", "系统异常，请稍后再试")
      })
    },

    onBeforeUpload(file) {
      this.showUploadProcess=false;
      this.percentage= 0;
      var fileExt =file.name.substring(file.name.lastIndexOf('.')+1).toLowerCase();
      var text=""
      const docExt = fileExt === 'xls'
      const docxExt = fileExt === 'xlsx'
      const isLimit = file.size / 1024 / 1024 < 2
      if(!docxExt && !docExt) {
        text="上传文件只能是xlsx格式!";
        handleAlert('warning',text)
        return false;
      }
      if (!isLimit) {
        text="上传文件大小不能超过 2MB!";
        handleAlert('warning',text)
        return false;
      }
      this.showUploadProcess=true;
      this.percentage=10;
      this.scheduleTitle = '上传进度';
      return true;
    },

    handleExceed(files, fileList) {
      handleAlert('warning', `当前限制选择 1 个文件，本次选择了 ${files.length} 个文件`)
    },
    // 批量上传
    uploadFile (param) {
      var _this= this;
      var formData = new FormData();
      formData.append('file', param.file);
      // this.percentage=100;
      // this.scheduleTitle = '正在解析';
      _this.$loading.show()
      replacementImportExcel(formData).then(res => {
        if (res.data.code === 100) {
          // _this.percentage=100;
          _this.dialogImportFormVisible = false
          _this.dataList()
        }
        _this.$alert(res.data.msg, '信息提示', {dangerouslyUseHTMLString: true})
        _this.showUploadProcess=false;
        // this.scheduleTitle = '上传成功';
        _this.fileList = []
        _this.$loading.hide()
      }).catch(e => {
        _this.showUploadProcess=false;
        _this.fileList = []
        _this.$alert('系统出现异常，导入失败', '信息提示', {dangerouslyUseHTMLString: true})
        _this.$loading.hide()
      })
    },
    handleRemove(file, fileList) {
      console.log("移出文件");
      this.fileList = [];
      this.showUploadBtn = true
    },
    handleBeforeRemove() {
      if (this.fileList != null && this.fileList.length > 0) {
        const _this = this;
        this.$confirm('确认删除文件？', '删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          _this.fileList = [];
          _this.showUploadBtn = true
        }).catch((error) => {
          handleAlert('info', '取消删除')
          return false;
        })
      }
      return false;
    },
    // 批量上传
    batchUpload() {
      // 打开上传弹窗
      this.fileList = [];
      this.showUploadBtn = true;
      this.dialogImportFormVisible = true;
    },


    // 编辑备注
    updateNotes(row, remark){
      var params = new URLSearchParams()
      params.append('id', row.id)
      params.append('remark', remark)
      replacementEdit(params).then(res => {
      })
    },
    maximumArea() {
      setTimeout(() => {
        tableHeight()
        setTimeout(() => {
          this.maximumHeight = tableMaxHeight;
        }, 50)
      })
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    }

  },
  mounted () {
    this.tableHeightArea()
    this.dataList()
  },
  activated() {
    window.addEventListener('keydown', this.keyDown);
  },
  deactivated() {
    window.removeEventListener('keydown', this.keyDown, false);
  },
}
</script>

<style>
.uplaodImage .upload-demo {
  margin-top: 5px;
}

.uplaodImage .el-upload {
  width: 100%;
}

.uplaodImage .el-upload_text {
  margin-top: 12%;
}

.uplaodImage .el-upload_text svg {
  width: 35px;
  height: 35px;
  color: var(--theme-color);
}

.uplaodImage .el-upload_text p {
  margin-top: -8px;
  color: #999;
  line-height: 1.5;
  font-size: 16px;
}

.uplaodImage .el-upload-dragger {
  width: 100% !important;
  height: 160px;
  background: var(--cell-bgColor);
}

.uplaodImage .el-upload__tip {
  margin-top: 5px;
  line-height: 1.5;
}

.upload-ol {
  list-style: decimal;
  margin-left: 10px;
}

.upload-ol > li {
  margin: 10px 0 35px 5px;
}

.upload-ol > li > div {
  margin: 5px;
}

.upload-ol > li > div:first-child {
  font-weight: bold;
}

.upload-ol a {
  text-decoration: underline;
  color: #409EFF;
}
</style>
