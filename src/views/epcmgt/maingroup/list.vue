<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" ref="formInline" :label-width="formLabelWidth" :model="formInline" class="demo-form-inline">
        <el-form-item label="主组" prop="cnName">
          <el-input v-model.trim="formInline.cnName" placeholder="请输入主组名称"></el-input>
        </el-form-item>
        <el-form-item label="主组编码" prop="code">
          <el-input v-model.trim="formInline.code" placeholder="请输入主组编码"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button v-if="hasPerm('menuAsimss5A1B_104')" type="primary" @click="onSubmit" icon="el-icon-search">搜索</el-button>
          <el-button v-if="hasPerm('menuAsimss5A1B_104')" plain @click="reset('formInline')">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="tableDetail">
      <div class="tableHandle" v-if="hasPerm('menuAsimss5A1B_101')">
        <el-button type="text" icon="el-icon-plus" @click="handelAdd()">新增</el-button>
        <!-- <el-button type="text" v-if="hasPerm('menuAsimss5A1B_102')" icon="el-icon-delete" @click="batchDelClick()">批量删除</el-button> -->
      </div>
      <!-- 分页查询 -->
      <el-table
        style="width:100%"
        border
        stripe
        highlight-current-row
        :data="resultList"
        :header-cell-style="{
          'text-align': 'center',
          'background-color': 'var(--other-color)',
        }"
      >
        <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
        <el-table-column label="主组" prop="cnName" min-width="150"></el-table-column>
        <el-table-column label="主组编码" prop="code" min-width="150"></el-table-column>
         <el-table-column label="排序" prop="sort" width="100" align="center"></el-table-column>
        <el-table-column label="操作" fixed="right" width="230">
          <template slot-scope="{row}">
            <el-button type="text" v-if="hasPerm('menuAsimss5A1B_103')" size="small" @click="handelEdit(row)">编辑</el-button>
            <el-button type="text" v-if="hasPerm('menuAsimss5A1B_102')" size="small" @click="handelDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total>0" :total="total" :page.sync="currentPage" :limit.sync="pagesize" @pagination="dataList"/>
      <el-dialog v-dialogDrag :width="dialogStatus == 'member' ? '450px !important' : ''" :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
        <!-- 新增和编辑弹窗 -->
        <el-form v-if="dialogStatus === 'add' || dialogStatus === 'edit' "  :label-width="formLabelWidth" ref='temp' :model="temp" :rules="rules"  label-position="center" :validate-on-rule-change="false">
          <el-form-item label="名称" prop="cnName">
            <el-input v-model.trim="temp.cnName" placeholder="请输入主组名称"   show-word-limit maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="编码" prop="code">
            <el-input v-model.trim="temp.code" placeholder="请输入主组编码"   show-word-limit maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="排序" prop="sort">
              <el-input type="number"  :min="1" :max="9999"  @input="e => temp.sort=parserNumber(e,1,9999)"  v-model="temp.sort" :value="temp.sort" placeholder="请输入排序号"></el-input>
           </el-form-item>
          <div class="submitArea">
            <el-button type="primary" @click="dialogStatus === 'add' ? addClick('temp') : updateData('temp')">
              立即提交
            </el-button>
            <el-button @click="resetForm('temp')">
              重置
            </el-button>
          </div>
        </el-form>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { handleAlert } from '@/assets/js/common.js'
import Pagination from '@/components/Pagination'
import { groupList, groupAdd, groupEdit, groupDel } from '@/api/epcmgt.js';
export default {
  name: 'epcmgtmaingrouplist',
  components: { Pagination },
  data () {
    return {

      memberForm:{
        auditUser:"",
        developmentUser:"",
        drawingUser:"",
      },

      formInline: {
        cnName: '',
        code: '',
      },
      temp: {
        id: '',
        cnName: '',
        code: '',
        sort: 1,
      },

      deleteList: [],

      dialogFormVisible: false,
      dialogStatus: '',
      editStatus:false,
      textMap: {
        edit: '编辑主组',
        add: '新增主组',
      },
      resultList: [],

      pagesize: 20,
      currentPage: 1,
      total: 0,
      formLabelWidth: '100px',
      rules: {
        cnName: [{ required: true, message: '名称不能为空', trigger: ['blur', 'change'] }],
        code: [{ required: true, message: '编码不能为空', trigger:['blur', 'change'] }],
      },
    }
  },
  methods: {
    // 分页查询数据
    dataList () {
      var params = {
        page: this.currentPage,   // 当前页
        limit: this.pagesize,     // 每页显示的条数
        cnName: this.formInline.cnName,
        code: this.formInline.code,
      }
      groupList(params).then(res => {
        this.total = res.data.total    // 总条数
        this.resultList = res.data.data   // 数据
      })
    },

    campareSameArray(arr1,arr2){
      let ret=false
      if(arr1&&arr1.length>0&&arr2&&arr2.length>0){
        if(arr1.sort().toString()==arr2.sort().toString()){
          ret=true
        }
      }
      return ret
    },

    // 搜索
    onSubmit () {
      this.currentPage=1
      this.dataList();
    },

    resetTemp () {
      this.temp = {
        id: '',
        cnName: '',
        code: '',
        sort: 1,
      }
      this.$nextTick(function() {
        this.$refs.temp.clearValidate();
      })
    },

    resetForm (temp) {
      if(this.dialogStatus == 'edit'){
        this.temp.id = '',
        this.temp.cnName = '',
        this.temp.code = '',
        this.temp.sort = 1,
        this.$nextTick(function() {
          this.$refs.temp.clearValidate();
        })
      } else {
        this.resetTemp()
      }
    },
    // 增加
    handelAdd () {
      var _this = this
      _this.dialogFormVisible = true
      _this.dialogStatus = 'add'
      _this.editStatus = false
      setTimeout(() => {
        _this.resetTemp()
      })
    },
    addClick (temp) {

      this.$refs[temp].validate((valid) => {
        if(valid){
          var params = new URLSearchParams()
          params.append('cnName', this.temp.cnName)
          params.append('code', this.temp.code)
          params.append('sort', this.temp.sort)

          groupAdd(params).then(res => {
            if (res.data.code === 100) {
              handleAlert('success',res.data.msg)
              this.dataList()
              this.dialogFormVisible = false
            } else {
              handleAlert('error',res.data.msg)
            }
          })
        }else{
          handleAlert('error','请完善主组信息')
        }
      })

    },

    // 编辑
    handelEdit (row) {
      var _this=this
      _this.dialogFormVisible = true
      _this.resetTemp()
      _this.temp = Object.assign({}, row)
      if(this.temp.riskStatus == null){
        this.temp.riskStatus = ""
      }

      _this.dialogStatus = 'edit'
      _this.editStatus=true
    },
    updateData (temp) {
      this.$refs[temp].validate((valid) => {
        if(valid){
          var params = new URLSearchParams()
          params.append("id", this.temp.id)
          params.append('cnName', this.temp.cnName)
          params.append('code', this.temp.code)
          params.append('sort', this.temp.sort)

          groupEdit(params).then(res => {
            if (res.data.code === 100) {
              handleAlert('success','保存成功')
              this.dataList()
              this.dialogFormVisible = false
            } else {
              handleAlert('error',res.data.msg)
            }
          }).catch(err => {
            if (err !== null && err !== '' && err.responseText !== null) {
              handleAlert('error','提交失败,请重试')
            }
          })
        } else {
          handleAlert('error','请完善主组信息')
        }
      })
    },

    // 删除
    handelDelete (row) {

      var _this=this
      this.$confirm('确定删除【' + row.cnName + '】的主组信息?', '删除项目', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var params = new URLSearchParams()
        params.append("id", row.id)
        groupDel(params).then(res => {
          if (res.data.code === 100) {
            handleAlert('success','删除成功')
            if(this.resultList!=null&&this.resultList.length==1){
              this.currentPage =this.currentPage-1
            }
            this.dataList()
          }else{
            handleAlert('error','删除失败')
          }
        })
      }).catch((error)=>{
        handleAlert('info','取消删除')
      })
    },


    // 重置
    reset (formInline) {
      if (this.$refs[formInline].resetFields() !== undefined) {
        this.$refs[formInline].resetFields()
      }
      this.formInline.cnName = ''
      this.formInline.code = ''
      this.currentPage = 1
      this.dataList()
    },
  },
  mounted () {
    this.dataList()
  }
}
</script>
