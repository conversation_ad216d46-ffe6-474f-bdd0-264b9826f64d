<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" ref="formInline" :model="formInline" class="demo-form-inline">
        <el-form-item label="车系" prop="carTrain">
          <el-input v-model.trim="formInline.carTrain" placeholder="请输入车系"></el-input>
        </el-form-item>
        <el-form-item label="车型编码" prop="code">
          <el-input v-model.trim="formInline.code" placeholder="请输入车型编号"></el-input>
        </el-form-item>
        <el-form-item label="中文名称" prop="nameCh">
          <el-input v-model.trim="formInline.nameCh" placeholder="请输入中文名称"></el-input>
        </el-form-item>
        <el-form-item label="车型年款" prop="year">
          <el-input v-model.trim="formInline.year" placeholder="请输入车型年款"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit" icon="el-icon-search">搜索</el-button>
          <el-button plain @click="reset('formInline')">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <el-table
        style="width:100%;"
        border
        stripe
        highlight-current-row
        :data="resultList"
        :header-cell-style="{
          'text-align': 'center',
          'background-color': 'var(--other-color)',
        }"
      >
        <el-table-column label="车系" prop="trainName" min-width="150"></el-table-column>
        <el-table-column label="车型年款" prop="year" width="100"></el-table-column>
        <el-table-column label="车型编码" prop="code" min-width="150"></el-table-column>
        <el-table-column label="中文名称" prop="nameCh" min-width="150"></el-table-column>
        <el-table-column label="英文名称" prop="nameEn" min-width="150"></el-table-column>
        <el-table-column label="图片" prop="image" width="100">
          <template slot-scope="{row}">
            <span v-if="row.image == null || row.image === ''">无</span>
            <el-button v-if="row.image !== null && row.image !== ''" type="text" size="small" @click="check(row)">查看
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="存在数据" prop="existsData" width="200">
          <template slot-scope="{row}">
            <span v-if="row.existsData" style="color:#009933">是</span>
            <span v-if="!row.existsData" style="color:#c30000">否</span>
          </template>
        </el-table-column>
        <!--        <el-table-column label="排序" prop="sort" width="100"></el-table-column>-->
        <el-table-column label="操作" fixed="right" width="260">
          <template slot-scope="{row}">
            <el-upload
              class="upload-demo inline-block"
              ref="celUpload"
              action="#"
              :show-file-list="false"
              :file-list="fileList"
              :before-upload="onBeforeUpload"
              accept=".zip,.pdf"
              :http-request="file=>uploadPackage(file,row)"
            >
              <el-button type="text" v-if="hasPerm('menuAsimss6A1B_107')" size="small">
                上传数据包
              </el-button>
            </el-upload>
            <el-button type="text" v-if="hasPerm('menuAsimss6A1B_102')" size="small" @click="clearData(row)">
              清除数据
            </el-button>
            <!--            <el-button type="text" v-if="hasPerm('menuAsimss6A1B_108')" size="small" @click="downPackage(row)">
                          下载数据包
                        </el-button>-->
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total>0" :total="total" :page.sync="currentPage" :limit.sync="pagesize"
                  @pagination="dataList"/>
      <el-dialog v-dialogDrag :width="dialogStatus == 'config' ? '700px !important' : ''" :title="textMap[dialogStatus]"
                 :visible.sync="dialogFormVisible" v-if="isDialog">

      </el-dialog>
    </div>
  </div>
</template>
<script>
import {handleAlert, sysServerUrl} from '@/assets/js/common.js'
import Pagination from '@/components/Pagination'
import {modelAdd, modelData,} from '@/api/sysmgt.js'
import {importPackage, clearPackageData, downloadPackage, modelWithPackage} from '@/api/maintenance.js'

export default {
  name: 'maintenance_data_list',
  components: {Pagination},
  data() {
    return {
      formInline: {
        carTrain: '',
        code: '',
        nameCh: '',
        year: ''
      },
      dataForm: {
        id: '',
        trainId: '',
        trainName: '',
        file: '',
        year: '',
        code: '',
        nameCh: '',
        nameEn: '',
        alias: '',
        modelType: '',
        marketTime: '',
        createdTime: '',
        createdUser: '',
        sort: 1,
        image: ''
      },
      // 默认选中值
      sltTrainId: '',
      trainList: [],
      trainCode: '',
      // 数据默认字段
      defaultProps: {
        parent: 'pid',   // 父级唯一标识
        value: 'id',          // 唯一标识
        label: 'nameCh',       // 标签显示
        children: 'children', // 子级
      },
      uploadUrl: '',
      imgList: [],
      isFlag: true,
      isDialog: false,
      fileList: [],
      urlImg: '',
      dialogFormVisible: false,
      formLabelWidth: '100px',
      dialogStatus: '',
      textMap: {
        edit: '编辑车型',
        add: '新增车型',
        detail: '详情信息',
        config: '车型配置',
        check: '车型图片'
      },
      resultList: [],
      pagesize: 20,
      currentPage: 1,
      total: 0,
      modelId: '',
      modelCfgList: [],
      rules: {
        trainId: [{required: true, message: '车系不能为空', trigger: ['blur', 'change']}],
        year: [{required: true, message: '车型年款不能为空', trigger: ['blur', 'change']}],
        code: [{required: true, message: '车型编码不能为空', trigger: ['blur', 'change']}],
        nameCh: [{required: true, message: '中文名称不能为空', trigger: ['blur', 'change']}]
      },
    }
  },
  methods: {
    uploadPackage(file, row) {
      var _this = this
      _this.$loading.show()
      var formData = new FormData();
      formData.append('file', file.file);
      formData.append('modelId', row.id);
      formData.append('language', 'zh');
      importPackage(formData).then(res => {
        if (res.data.code === 100) {
          handleAlert('success', '批量导入成功')
          this.dataList()
        } else {
          _this.$alert(res.data.msg, '信息提示', {dangerouslyUseHTMLString: true})
        }
         _this.$loading.hide()
      }).catch(function (error) {
        _this.fileList = []
          .$alert('系统出现异常，导入失败', '信息提示', {dangerouslyUseHTMLString: true})
        _this.$loading.hide()
      })
    },
    // 数据
    dataList() {
      var params = {
        page: this.currentPage,
        limit: this.pagesize,
        trainName: this.formInline.carTrain,
        modelCodePart: this.formInline.code,
        nameCh: this.formInline.nameCh,
        year: this.formInline.year
      }
      modelWithPackage(params).then(res => {
        this.total = res.data.total
        this.resultList = res.data.data
      })
    },
    // 搜索
    onSubmit() {
      this.currentPage = 1
      this.dataList()
    },
    resetTemp() {
      this.dataForm = {
        id: '',
        trainId: '',
        trainName: '',
        file: '',
        year: '',
        code: '',
        nameCh: '',
        nameEn: '',
        alias: '',
        modelType: '',
        marketTime: '',
        createdTime: '',
        createdUser: '',
        sort: 1,
        image: ''
      }
      this.$nextTick(function () {
        this.$refs.dataForm.clearValidate();
      })
    },
    // 附件上传
    onBeforeUpload(file) {
      console.log(file.name.match(/\.[^.]*$/g))
      let match = file.name.match(/\.[^.]*$/g)[0];
      if (match.toLowerCase() !== '.zip' && match.toLowerCase() !== '.pdf') {
        handleAlert('warning', "上传的文件只能是 zip 或者 PDF 格式!")
        return false;
      }
      if (file.size / 1024 / 1024/1024 > 1) {
        handleAlert('warning', "上传的文件大小不能超过 1GB!")
        return false;
      }
      return true;
    },

  resetForm() {
      this.resetTemp()
    },
    // 查看
    check(row) {
      this.isDialog = true
      this.dialogStatus = 'check'
      this.dialogFormVisible = true
      this.urlImg = sysServerUrl + 'sys/upload/display?filePath=' + row.image
    },
    // 删除
    clearData(row) {
      this.$confirm('确定清除【' + row.code + '】的维修数据信息?', '清除数据', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$loading.show()
        let formData = new FormData();
        formData.append('modelId', row.id);
        formData.append('language', 'zh');
        clearPackageData(formData).then(res => {
          if (res.data.code === 100) {
            handleAlert('success', '删除成功')
            if (this.resultList != null && this.resultList.length === 1) {
              this.currentPage = this.currentPage - 1
            }
            this.dataList()
          } else {
            handleAlert('error', res.data.msg)
          }
          this.$loading.hdie()
        })
      }).catch((error) => {
        this.$loading.hdie()
        handleAlert('info', '取消删除')
      })
    },
    reset(formInline) {
      if (this.$refs[formInline].resetFields() !== undefined) {
        this.$tefs[formInline].resetFields()
      }
      this.currentPage = 1
      this.dataList()
    },
  },
  mounted() {
    this.dataList()
  }
}
</script>
<style>
.el-dialog .el-table .el-input__inner {
  height: 28px;
  line-height: 28px;
  padding: 0 8px;
}

.el-dialog .el-table .el-input {
  width: 100% !important;
  margin: 2px 0;
}



.imgShow {
  width: 150px;
}
</style>
