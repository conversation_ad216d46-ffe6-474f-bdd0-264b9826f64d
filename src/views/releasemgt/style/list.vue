<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" ref="formInline" :model="formInline" class="demo-form-inline">
        <el-form-item label="模板名称" prop="styleName">
          <el-input v-model.trim="formInline.styleName" placeholder="请输入模板名称"></el-input>
        </el-form-item>
        <el-form-item label="有效状态" prop="validatatus">
          <el-select v-model="formInline.validstatus" clearable>
            <el-option label="启用" value="1"></el-option>
            <el-option label="禁用" value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search" icon="el-icon-search">查询</el-button>
          <el-button plain @click="reset('formInline')">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <div class="tableHandle" v-if="hasPerm('menuAsimss4A1B_101')">
        <el-button type="text" icon="el-icon-plus" @click="add()">新增</el-button>
      </div>
      <el-table
        style="width:100%"
        border
        stripe
        highlight-current-row
        :data="resultList"
        :header-cell-style="{
          'text-align': 'center',
          'background-color': 'var(--other-color)',
        }"
      >
        <el-table-column label="名称" prop="name" min-width="150"></el-table-column>
        <el-table-column label="有效状态" prop="useFlag" width="100px">
          <template slot-scope="{row}">
            <span v-if="row.useFlag === 1" style="color:#009933">启用</span>
            <span v-if="row.useFlag === 0 " style="color:#c30000">禁用</span>
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" min-width="150"></el-table-column>
        <el-table-column label="更新时间" prop="updatedTime" width="140px">
          <template slot-scope="{row}">
            <div>
              {{ row.updatedTime | conversion("yyyy-MM-dd") }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="更新人" prop="updatedUserName" width="140px"></el-table-column>
        <el-table-column label="操作" fixed="right" width="240px">
          <template slot-scope="{row}">
            <el-button v-if="hasPerm('menuAsimss4A1B_103')" type="text" size="small" @click="edit(row)">编辑</el-button>
            <el-button v-if="hasPerm('menuAsimss4A1B_102')" type="text" size="small" @click="del(row)">删除</el-button>
            <el-button v-if="hasPerm('menuAsimss4A1B_108')" type="text" size="small" @click="down(row)">下载</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total>0" :total="total" :page.sync="currentPage" :limit.sync="pagesize" @pagination="dataList"/>
      <el-dialog v-dialogDrag :title="textMap[dialogStatus]"  :visible.sync="dialogFormVisible">
        <el-form :model="styleInfo" :label-width="formLabelWidth" ref='styleInfo' label-position="center" :rules="styleRules" >
          <el-form-item label="名称" prop="name">
            <el-input v-model.trim="styleInfo.name" placeholder="请输入样式名称" show-word-limit maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="模板:" prop="zipPath">
            <el-upload
              v-model="styleInfo.zipPath"
              class="upload-demo"
              style="max-width: 379px;"
              :action="uploadUrl"
              :headers="importHeader"
              :on-success="handleOnSuccess"
              :on-remove="handleOnRemove"
              :before-remove="beforeOnRemove"
              :before-upload="beforeAvatarUpload"
              :on-exceed="handleOnExceed"
               multiple
              :limit="1"
              :file-list="fileList"
              accept=".ZIP, .zip"
            >
              <el-button size="min" icon="el-icon-upload" type="primary">点击上传</el-button>
            </el-upload>
          </el-form-item>
          <el-form-item label="有效状态:" prop="useFlag">
            <el-radio-group v-model="styleInfo.useFlag">
              <el-radio :label="1">启用</el-radio>
              <el-radio :label="0">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="备注:" prop="remark">
            <el-input v-model.trim="styleInfo.remark" placeholder="请输入备注信息" show-word-limit maxlength="100"></el-input>
          </el-form-item>
          <div class="submitArea">
            <el-button type="primary" @click="onSubmit()" >
              立即提交
            </el-button>
            <el-button @click="resetForm()">重置</el-button>
          </div>
        </el-form>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { sysServerUrl,cmsServerUrl, handleAlert } from '@/assets/js/common.js'
import Pagination from '@/components/Pagination'
import { styleData, styleAdd, styleEdit, styleDel, styleDownload } from '@/api/releasemgt.js'
export default {
  name: 'releasemgtstylelist',
  components: { Pagination },
  data () {
    return {
      name: '更多',
      formInline: {
        styleName: '',
        validstatus: ''
      },
      textMap: {
        add: '新增模板',
        edit: '编辑模板'
      },
      styleInfo: {
        id: 0,
        name: '',
        zipPath: '',
        useFlag: '',
        remark: ''
      },
      uploadUrl: '',
      isFlag: true,
      fileList: [],
      styleRules: {
        name: [{ required: true, message: '名称不能为空', trigger: ['blur', 'change'] }],
        zipPath: [{ required: true, message: '样式文件不能为空', trigger: ['blur', 'change'] }],
        useFlag: [{ required: true, message: '是否有效状态不能为空', trigger: ['blur', 'change'] }]
      },
      dialogFormVisible: false,
      dialogStatus: '',
      formLabelWidth: '100px',
      resultList: [],
      pagesize: 20,
      currentPage: 1,
      total: 0
    }
  },
  computed: {
    importHeader: function () {
      return { Authorization: sessionStorage.token };
    }
  },
  methods: {
    onSubmit () {
      if(this.styleInfo.name == ''){
        handleAlert('error','名称不能为空')
        return  false
      }
      if(this.styleInfo.zipPath==''){
        handleAlert('error','样式不能为空，请上传样式文件')
        return  false
      }
      if(this.styleInfo.useFlag==''){
        handleAlert('error','有效状态不能为空')
        return  false
      }
      var params = new URLSearchParams()
      params.append('id', this.styleInfo.id)
      params.append('name', this.styleInfo.name)
      params.append('zipPath',this.styleInfo.zipPath)
      params.append('useFlag', this.styleInfo.useFlag)
      params.append('remark', this.styleInfo.remark)
      if( this.dialogStatus === 'add'){
        //新增样式
        styleAdd(params).then(res => {
           if(res.data.code==100){
            handleAlert('success','添加样式成功')
            this.dialogFormVisible=false
            this.dataList()
          }else{
            handleAlert('success',res.data.msg)
          }
        }).catch(function(error){
          handleAlert('error','添加样式失败')
        })
      }else if( this.dialogStatus === 'edit'){
        //更新样式
        styleEdit(params).then(res => {
          if(res.data.code==100){
            handleAlert('success','修改样式成功')
            this.dialogFormVisible=false
            this.dataList()
          }
        }).catch(function(error){
          handleAlert('error','修改样式失败')
        })
      }
    },
    handleOnSuccess (res, obj) {
      this.styleInfo.zipPath=res.data.fileUrl
      this.fileList=[]
      var file = {name: res.data.fileName, url: res.data.fileUrl}
      this.fileList.push(file)
      this.isFlag = true;
    },
    beforeOnRemove(file, fileList) {
      if(this.isFlag){
        return this.$confirm(`确定移除选择文件？`, '删除',{type: 'warning'});
      }
    },
    handleOnRemove(file,fileList){
      this.isFlag = true;
      this.styleInfo.zipPath=""
    },
    beforeAvatarUpload (file) {
      var fileName = file.name.substring(file.name.lastIndexOf(".")+1).toLowerCase()
      const extension = fileName === 'zip'
      const isLt2M = file.size / 1024 / 1024 < 10
      if (!extension ) {
        handleAlert('warning','上传样式模板只能是 zip 格式!')
        this.isFlag = false;
        return false;
      }
      if (!isLt2M) {
        handleAlert('warning','上传样式模板大小不能超过 10 MB!')
        this.isFlag = false;
        return false;
      }
    },
    handleOnExceed (files, fileList) {
      handleAlert('warning',`当前限制选择1个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
    },
    resetTemp(){
      this.fileList=[]
      this.styleInfo = {
        id: 0,
        name: '',
        zipPath: '',
        useFlag: '',
        remark: ''
      }
      this.$nextTick(function() {
        this.$refs.styleInfo.clearValidate();
      })
    },
    add () {
      this.resetTemp()
      this.dialogStatus = 'add'
      this.dialogFormVisible = true
    },
    resetForm() {
      this.resetTemp();
    },
    edit (row) {
      this.resetTemp()
      this.dialogStatus = 'edit'
      this.styleInfo.id=row.id
      this.styleInfo.name=row.name
      this.styleInfo.zipPath=row.zipPath
      this.styleInfo.useFlag=row.useFlag
      this.styleInfo.remark=row.remark
      this.fileList=[]
      let fileName=row.zipPath
      let index =fileName.lastIndexOf('\\')
      if(index!=-1){
        fileName=fileName.substring(index+1)
      }
      var file = {name: fileName, url: row.zipPath}
      this.fileList.push(file)
      this.dialogFormVisible = true
    },
    del (row) {
      this.$confirm('确定删除【'+ row.name + '】的样式信息?', '删除手册样式', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        styleDel(row.id).then(res => {
          if (res.data.code === 100) {
            handleAlert('success','删除成功')
            if(this.resultList!=null&&this.resultList.length==1){
              this.currentPage =this.currentPage-1
            }
            this.dataList()
          }else{
            handleAlert('error',res.data.msg)
          }
        })
      }).catch((error) => {
        handleAlert('info','取消删除')
      })
    },
    // 获取数据
    dataList () {
      var params = {
        page: this.currentPage,
        limit: this.pagesize,
        name: this.formInline.styleName,// 样式名称
        useFlag: this.formInline.validstatus// 有效状态
      }
      styleData(params).then(res => {
        this.total = res.data.total
        this.resultList = res.data.data
      })
    },
    // 搜索
    search(){
      this.currentPage = 1
      this.dataList()
    },
    // 下载
    down(row){
      var params ='?id='+ row.id;
      styleDownload(params).then(res => {
        if(res.data.size == 0){
          handleAlert('warning','还没有该样式哦！')
          return
        }
        var name = row.name + "_样式模板.zip";
        var blob = new Blob([res.data], { type: 'application/zip' });
        var url = window.URL.createObjectURL(blob);
        var aLink = document.createElement("a");
        aLink.style.display = "none";
        aLink.href = url;
        aLink.setAttribute("download", name);
        document.body.appendChild(aLink);
        aLink.click();

        document.body.removeChild(aLink); //下载完成移除元素
        window.URL.revokeObjectURL(url); //释放掉blob对象
      })
    },
    reset (formInline) {
      if (this.$refs[formInline].resetFields() !== undefined) {
        this.$refs[formInline].resetFields()
      }
      this.formInline.validstatus = ""
      this.currentPage = 1
      this.dataList()
    },
  },
  mounted () {
    this.uploadUrl = sysServerUrl + 'sys/upload/attach?flag=temp'
    this.dataList()
  }
}
</script>
<style>
  .el-button-group .el-button--primary:first-child{
    display: none;
  }
  .el-button-group .el-button--primary:last-child{
    border-radius: 5px;
  }
  .el-dropdown .el-dropdown__caret-button::before {
    content: '更多';
    position: relative;
    display:inline-block;
    width:auto;
    top: 0px;
    bottom: 0px;
    left: 0;
    background: transparent;
  }


</style>
