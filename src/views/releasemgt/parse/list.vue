<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" :label-width="formLabelWidth" ref="formInline" :model="formInline" class="demo-form-inline">
        <el-form-item label="规则名称" prop="queryParseName">
          <el-input v-model.trim="formInline.queryParseName" placeholder="请输入规则名称"></el-input>
        </el-form-item>
        <el-form-item label="手册类型" prop="queryManualType">
          <el-select v-model.trim="formInline.queryManualType" clearable filterable>
            <el-option v-for="(item, index) in manualTypeList" :key="index" :label="item.name" :value="item.code"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="样式" prop='queryStyle'>
          <el-select v-model="formInline.queryStyle" clearable filterable>
            <el-option v-for="(item, index) in manualStyleList" :key="index" :value="item.id" :label="item.name"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit" icon="el-icon-search">查询</el-button>
          <el-button plain @click="reset('formInline')">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <div class="tableHandle" v-if="hasPerm('menuAsimss4A3B_101')">
        <el-button type="text" icon="el-icon-plus" @click="headelAdd()">新增</el-button>
      </div>
      <el-table
        style="width:100%"
        border
        stripe
        highlight-current-row
        :data="resultList"
        :header-cell-style="{
          'text-align': 'center',
          'background-color': 'var(--other-color)',
        }"
      >
        <el-table-column label="手册类型" prop="manualTypeName" min-width="150"></el-table-column>
        <el-table-column label="规则名称" prop="name" min-width="150"></el-table-column>
        <el-table-column label="规则编码" prop="code" min-width="150"></el-table-column>
        <el-table-column label="样式模板" prop="styleName" min-width="150"></el-table-column>
        <el-table-column label="是否默认" prop="isDefault" width="100">
          <template slot-scope="{row}">
            <span v-if="row.isDefault === 1" style="color:#009933">是</span>
            <span v-if="row.isDefault !== 1" style="color:#c30000">否</span>
          </template>
        </el-table-column>
        <el-table-column label="有效状态" prop="userFlag" width="100">
          <template slot-scope="{row}">
            <span v-if="row.useFlag === 1" style="color:#009933">启用</span>
            <span v-if="row.useFlag !== 1" style="color:#c30000">禁用</span>
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" min-width="150"></el-table-column>
        <el-table-column label="更新时间" prop="updatedTime" width="140">
          <template slot-scope="{row}">
            <div>
              {{ row.updatedTime | conversion("yyyy-MM-dd") }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="更新人" prop="updatedUserName" width="140"></el-table-column>
        <el-table-column label="操作" fixed="right" width="200">
          <template slot-scope="{row}">
            <el-button v-if="hasPerm('menuAsimss4A3B_104')" type="text" size="small" @click="headelCheck(row)">查看</el-button>
            <el-button v-if="hasPerm('menuAsimss4A3B_103')" type="text" size="small" @click="headelEdit(row)">编辑</el-button>
            <el-button v-if="hasPerm('menuAsimss4A3B_102')" type="text" size="small" @click="headelDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total>0" :total="total" :page.sync="currentPage" :limit.sync="pagesize" @pagination="dataList"/>
      <el-dialog v-dialogDrag :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
        <el-form v-if="dialogStatus === 'add' || dialogStatus === 'edit'" :label-width="formLabelWidth" :model="temp" ref="temp" label-position="center" :rules="rules">
          <el-form-item label="规则名称" prop="name">
            <el-input v-model.trim="temp.name" placeholder="请输入规则名称" show-word-limit maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="规则编码" prop="code">
            <el-input v-model.trim="temp.code" placeholder="请输入规则编码" show-word-limit maxlength="100"></el-input>
          </el-form-item>
          <el-form-item label="手册类型" prop="manualType">
            <el-select v-model="temp.manualType" clearable filterable>
              <el-option v-for="(item, index) in manualTypeList" :key="index" :label="item.name" :value="item.code"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="样式" prop="styleId">
            <el-select v-model="temp.styleId" clearable filterable>
              <el-option v-for="(item, index) in manualStyleList" :key="index" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="是否默认" prop="isDefault">
            <el-select v-model="temp.isDefault">
              <el-option v-for="(item, index) of defaultList" :key="index" :label="item.name" :value="item.code"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model.trim="temp.remark" placeholder="请输入备注" show-word-limit maxlength="100"></el-input>
          </el-form-item>
          <div class="submitArea">
            <el-button type="primary" @click="dialogStatus === 'add' ? addClick('temp') : editClick('temp')">立即提交</el-button>
            <el-button @click="resetForm('temp')">重置</el-button>
          </div>
        </el-form>
        <el-form v-if="dialogStatus === 'detail'" :label-width="formLabelWidth" :model="temp" ref="temp" label-position="center" :rules="rules">
          <el-form-item label="规则名称" prop="name">
            <el-input v-model="temp.name" readonly></el-input>
          </el-form-item>
          <el-form-item label="规则编码" prop="code">
            <el-input v-model="temp.code" readonly></el-input>
          </el-form-item>
          <el-form-item label="手册类型" prop="manualType">
            <el-input v-model="temp.manualType" readonly></el-input>
          </el-form-item>
          <el-form-item label="样式" prop="styleId">
            <el-input v-model="temp.styleName" readonly></el-input>
          </el-form-item>
          <el-form-item label="是否默认" prop="isDefault">
            <el-input v-if="temp.isDefault == 1" value="是" readonly></el-input>
            <el-input v-else value="否" readonly></el-input>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="temp.remark" readonly></el-input>
          </el-form-item>
        </el-form>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { sysServerUrl, cmsServerUrl, handleAlert } from '@/assets/js/common.js'
import Pagination from '@/components/Pagination'
import { parseData, parseManualType, parseManualStyle, parseUser,
 parseProject, parseAdd, parseEdit, parseDel } from '@/api/releasemgt.js'
export default {
  name: 'releasemgtparselist',
  components: { Pagination },
  data () {
    return {
      formInline: {
        queryParseName: '',
        queryManualType: '',
        queryStyle: ''
      },
      temp: {
        id: '',
        projectId: '',
        name: '',
        code: '',
        manualType: '',
        styleId: '',
        isDefault: '',
        remark: '',
        cnName: '',
        principal: '',
        manualStatus: '',
        riskStatus: ''
      },
      riskList: [
        { name: '无风险', code: 1 },
        { name: '低风险', code: 2 },
        { name: '中风险', code: 3 },
        { name: '高风险', code: 4 }
      ],
      manualList: [
        { name: '进行中', code: 1 },
        { name: '暂停中', code: 2 },
        { name: '已验收', code: 3 }
      ],
      defaultList: [
        { name: '是', code: 1 },
        { name: '否', code: 0 }
      ],
      time: {
        deadlineTime: '',
        firstDraftTime: '',
        finalDraftTime: ''
      },
      manualTypeList: [],
      manualStyleList: [],
      resultList: [],
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        detail: '解析规则明细',
        edit: '编辑解析规则',
        add: '新增解析规则'
      },
      formLabelWidth: '100px',
      pagesize: 20,
      currentPage: 1,
      total: 0,
      rules: {
        name: [{ required: true, message: '请输入规则名称', trigger: ['blur', 'change'] }],
        code: [{ required: true, message: '请输入规则编码', trigger: ['blur', 'change'] }],
        manualType: [{ required: true, message: '请选择手册类型', trigger: ['blur', 'change'] }],
        styleId: [{ required: true, message: '请选择样式', trigger: ['blur', 'change'] }],
        isDefault: [{ required: true, message: '请选择是否默认', trigger: ['blur', 'change'] }]
      }
    }
  },
  methods: {
    // 获取手册类型列表
    getManualTypeList () {
      parseManualType().then(res => {
        if (res.data.code === 100) {
          this.manualTypeList = res.data.data
        }
      })
    },
    // 获取手册样式列表
    getManualStyleList () {
      parseManualStyle().then(res => {
        if (res.data.code === 100) {
          this.manualStyleList = res.data.data
        }
      })
    },
    dataList () {
      var params = {
        page: this.currentPage,
        limit: this.pagesize,
        name: this.formInline.queryParseName,
        manualType: this.formInline.queryManualType,
        styleId: this.formInline.queryStyle   // 样式
      }
      parseData(params).then(res => {
        this.total = res.data.total
        this.resultList = res.data.data
      })
    },
    // 搜索
    onSubmit () {
      this.currentPage = 1
      this.dataList()
    },
    // 重置
    reset (formInline) {
      if (this.$refs[formInline].resetFields() !== undefined) {
        this.$refs[formInline].resetFields()
      }
      this.currentPage = 1
      this.dataList()
    },
    resetTemp () {
      this.temp = {
        name: '',
        code: '',
        manualType: '',
        styleId: '',
        isDefault: '',
        remark: ''
      }
      this.$nextTick(function() {
        this.$refs.temp.clearValidate();
      })
    },
    // 新增
    headelAdd () {
      this.dialogFormVisible = true
      this.resetTemp()
      this.dialogStatus = 'add'
    },
    addClick (temp) {
      this.$refs[temp].validate((valid) => {
        if (valid) {
          var params = new URLSearchParams()
          params.append('name', this.temp.name)
          params.append('code', this.temp.code)
          params.append('manualType', this.temp.manualType)
          params.append('styleId', this.temp.styleId)
          params.append('isDefault', this.temp.isDefault)
          params.append('remark', this.temp.remark)
          parseAdd(params).then(res=>{
            if (res.data.code === 100) {
              handleAlert('success',res.data.msg)
              this.dataList()
              this.dialogFormVisible = false
            } else {
              handleAlert('error',res.data.msg)
            }
          }).catch(err => {
            if (err !== null && err !== '' && err.responseText !== null) {
              handleAlert('error','提交失败,请重试')
            }
          })
        } else {
          handleAlert('error','请完善信息')
        }
      })
    },
    resetForm (temp) {
      if (this.$refs[temp].resetFields() !== undefined) {
        this.$refs[temp].resetFields()
      }
    },
    // 查看
    headelCheck (row) {
      this.resetTemp()
      this.temp = Object.assign({}, row)
      this.dialogFormVisible = true
      this.dialogStatus = 'detail'
    },
    // 编辑
    headelEdit (row) {
      this.resetTemp()
      this.temp = Object.assign({}, row)
      this.dialogFormVisible = true
      this.dialogStatus = 'edit'
    },
    editClick (temp) {
      this.$refs[temp].validate((valid) => {
        if (valid) {
          var params = new URLSearchParams()
          params.append('id', this.temp.id)
          params.append('name', this.temp.name)
          params.append('code', this.temp.code)
          params.append('manualType', this.temp.manualType)
          params.append('styleId', this.temp.styleId)
          params.append('isDefault', this.temp.isDefault)
          params.append('remark', this.temp.remark)
          parseEdit(params).then(res => {
            if (res.data.code === 100) {
              handleAlert('success',res.data.msg)
              this.dataList()
              this.dialogFormVisible = false
            } else {
              handleAlert('error',res.data.msg)
            }
          })
        } else {
          handleAlert('error','请完善信息')
        }
      })
    },
    // 删除
    headelDelete (row) {
      this.$confirm('确定删除【'+ row.name +'】的解析规则?', '删除解析规则', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(res => {
        parseDel(row.id).then(res => {
          if (res.data.code === 100) {
            handleAlert('success','删除成功！')
            if(this.resultList!=null&&this.resultList.length==1){
              this.currentPage =this.currentPage-1
            }
            this.dataList()
          }else{
            handleAlert('error','删除失败')
          }
        })
      }).catch((error) => {
        handleAlert('info','取消删除')
      })
    },
  },
  mounted () {
    this.getManualTypeList()
    this.getManualStyleList()
    this.dataList()
  }
}
</script>
