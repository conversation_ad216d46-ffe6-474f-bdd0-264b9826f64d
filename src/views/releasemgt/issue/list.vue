<template>
  <div class="layoutContainer">
    <div class="infoDetail">
      <el-row>
        <el-col :span="5" class="leftData">
          <div>
            <div class="topButton">
              <span style="height:42px;line-height:42px;margin-left: 10px;">手册项目</span>
            </div>
            <div class="scrollClass">
              <el-scrollbar>
                <el-tree ref="tree" node-key="id" :data="listdata" :props="defaultProps" :default-expanded-keys="treeExpandIdList" @node-click="handleNodeClick" highlight-current>
                  <span slot-scope="{ node, data }" class="custom-tree-node" @mouseenter="mouseenter(data)" @mouseleave="mouseleave(data)">
                    <span>{{data.nameCh}}</span>
                    <span v-show="data.isCurrent" class="attribute" @click="attributeClick(data)">
                      <i class="el-icon-more" style="transform: rotate(90deg);" title="属性"></i>
                    </span>
                  </span>
                </el-tree>
              </el-scrollbar>
            </div>
          </div>
        </el-col>
        <el-col :span="19" class="fromRight manualManage">
          <div class="rightTitle" v-if="hasPerm('menuAsimss4A2B_101')">
            <el-button type="text" icon="el-icon-plus" @click="handelAdd()">新增</el-button>
          </div>
          <el-table
            style="width:100%"
            :data="resultList"
            stripe
            highlight-current-row
            :header-cell-style="{
              'text-align': 'center',
              'background-color': 'var(--other-color)',
            }"
          >
            <el-table-column label="手册分类" prop="manualTypeName" align="center"></el-table-column>
            <el-table-column label="输出类型" prop="outputType" align="center"></el-table-column>
            <el-table-column label="更新人" prop="updatedUserName" align="center"></el-table-column>
            <el-table-column label="是否组合手册" prop="isComposite" align="center">
              <template slot-scope="{row}">
                <span v-if="row.isComposite === 1">是</span>
                <span v-if="row.isComposite === 0">否</span>
              </template>
            </el-table-column>
            <el-table-column label="备注" prop="remark" align="center"></el-table-column>
            <el-table-column label="操作" fixed="right" width="180" align="left">
              <template slot-scope="{row}"  style="text-align:center">
                <el-button type="text" v-if="hasPerm('menuAsimss4A2B_109')" size="small" @click="headelSetUp(row)">目录</el-button>
                <el-button type="text" v-if="hasPerm('menuAsimss4A2B_118')" size="small" @click="headelIssue(row)">发布</el-button>
                <el-button type="text" v-if="row.outputType == 'pdf' && hasPerm('menuAsimss4A2B_102')" size="small" @click="headelSet(row)">设置</el-button>
                <el-button type="text" v-if="hasPerm('menuAsimss4A2B_102')" size="small" @click="headelDel(row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination v-show="total>15" :total="total" :page.sync="currentPage" :limit.sync="pagesize" @pagination="pagingEvent"/>
        </el-col>
      </el-row>
    </div>
    <el-dialog v-dialogDrag :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
      <el-form v-if="dialogStatus == 'attr'" ref='trainTemp' :model="trainTemp">
        <el-form-item label="主机厂" prop="brand" :label-width="formLabelWidth">
          <el-input v-model="trainTemp.brand" readonly></el-input>
        </el-form-item>
        <el-form-item label="项目名称" prop="nameCh" :label-width="formLabelWidth">
          <el-input v-model="trainTemp.nameCh" readonly></el-input>
        </el-form-item>
        <el-form-item label="项目状态" prop="projectStatus" :label-width="formLabelWidth">
          <el-input v-if="trainTemp.projectStatus == '1'" value="进行中" readonly></el-input>
          <el-input v-if="trainTemp.projectStatus == '2'" value="暂停中" readonly></el-input>
          <el-input v-if="trainTemp.projectStatus == '3'" value="已验收" readonly></el-input>
        </el-form-item>
        <el-form-item label="车型" prop="trainCode" :label-width="formLabelWidth">
          <el-input v-model="trainTemp.trainCode" readonly></el-input>
        </el-form-item>
        <el-form-item label="年款" prop="trainYear" :label-width="formLabelWidth">
          <el-input v-model="trainTemp.trainYear" readonly></el-input>
        </el-form-item>
      </el-form>
      <el-form v-if="dialogStatus == 'add'" ref="addTemp" :label-width="formLabelWidth" :model="addTemp" :rules="rules">
        <el-form-item label="手册类型" prop="manualType">
          <el-select v-model="addTemp.manualType" clearable filterable>
            <el-option
              v-for="(item, index) in manualTypeList"
              :key="index"
              :value="item.code"
              :label="item.name"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="语种" prop="language">
          <el-select v-model="addTemp.language" clearable filterable>
            <el-option v-for="(item, index) of languageList" :key="index" :label="item.name" :value="item.code"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="输出类型" prop="outputType">
          <el-select v-model="addTemp.outputType" clearable>
            <el-option label="html" value="html"></el-option>
            <el-option label="pdf" value="pdf"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="组合手册" prop="isComposite">
          <el-radio-group v-model="addTemp.isComposite">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model.trim="addTemp.remark" placeholder="请输入备注信息" show-word-limit maxlength="100"></el-input>
        </el-form-item>
        <div class="submitArea">
          <el-button type="primary" @click="addClick('addTemp')">立即提交</el-button>
          <el-button @click="resetForm()"> 重置 </el-button>
        </div>
      </el-form>
      <el-form v-if="dialogStatus == 'set'" ref="setTemp" :label-width="formLabelWidth" :model="setTemp" :rules="setRules">
        <el-form-item label="封面" prop="coverPage">
          <el-upload
            class="upload-demo"
            :action="uploadUrl"
            :headers="importHeader"
            :on-success="handleOnSuccess"
            :on-remove="handleOnRemove"
            :before-remove="beforeOnRemove"
            :before-upload="beforeAvatarUpload"
            :on-exceed="handleOnExceed"
            :limit="1"
            :file-list="coverList"
            accept=".JPG, .PNG, .JPEG,.jpg, .png, .jpeg"
            list-type="picture"
          >
            <el-button size="min" icon="el-icon-upload" type="primary">选择图片</el-button>
          </el-upload>
        </el-form-item>
        <el-form-item label="封底" prop="backCoverPage">
          <el-upload
            class="upload-demo"
            :action="uploadUrl"
            :headers="importHeader"
            :on-success="handleTwoOnSuccess"
            :on-remove="handleTwoOnRemove"
            :before-remove="beforeOnRemove"
            :before-upload="beforeAvatarUpload"
            :on-exceed="handleOnExceed"
            :limit="1"
            accept=".JPG, .PNG, .JPEG,.jpg, .png, .jpeg"
            :file-list="backCoverList"
            list-type="picture"
          >
            <el-button size="min" icon="el-icon-upload" type="primary">选择图片</el-button>
          </el-upload>
        </el-form-item>
        <div class="submitArea">
          <el-button type="primary" @click="addSubmit('setTemp') ">
            立即提交
          </el-button>
          <el-button @click="dialogFormVisible = false">
            取消
          </el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import { sysServerUrl, addTabs, contentSize, handleAlert, expandTree } from '@/assets/js/common.js'
import { catalogueList, manualType } from '@/api/cmsmgt.js'
import { releaseBasicList, releaseBasicAdd, releaseBasicDel, releaseBasicSet } from '@/api/releasemgt.js'
import Pagination from '@/components/Pagination'

export default {
  name: 'releasemgtissuelist',
  components: { Pagination },
  data () {
    return {
      setId:"",
      uploadUrl: '',
      coverList:[],
      backCoverList: [],
      defaultProps: {
        children: 'children',
        label: 'nameCh',
      },
      trainTemp: {
        id:"",
        frimId: "",
        brand: "",
        nameCh: "",
        projectStatus:'',
        trainCode:"",
        trainYear: "",
      },
      addTemp:{
        manualType:"",
        isComposite:"",
        language:'chinese',
        outputType:"",
        remark:""
      },
      languageList:[],
      manualList: [
        { name: '进行中', code: 1 },
        { name: '暂停中', code: 2 },
        { name: '已验收', code: 3 }
      ],
      riskList: [
        { name: '无风险', code: 1 },
        { name: '低风险', code: 2 },
        { name: '中风险', code: 3 },
        { name: '高风险', code: 4 }
      ],
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        attr: '项目属性',
        add: '新增项目发布',
        set: '设置封面封底',
      },
      parjectId:"",
      parjectName:"",
      pagesize: 15,
      currentPage: 1,
      total: 0,
      principalList: [],
      resultList: [],
      listdata: [],
      treeExpandIdList:[],
      manualTypeList: [],
      formLabelWidth: '100px',
      rules: {
        manualType: [{ required: true, message: "手册类型不能为空", tigger: ['blur', 'change'] }],
        language: [{ required: true, message: '语种不能为空', trigger: ['blur', 'change'] }],
        isComposite: [{ required: true, message: "请选择组合手册", trigger:  ['blur', 'change'] }],
        outputType: [{ required: true, message: "输出类型不能为空", trigger: ['blur', 'change'] }],
      },
      setTemp: {
        coverPage:"",
        backCoverPage:"",
      },
      setRules: {
        coverPage: [{ required: true, message: "封面不能为空", tigger: ['blur', 'change'] }],
        backCoverPage: [{ required: true, message: "封底不能为空", trigger:  ['blur', 'change'] }],
      }
    }
  },
  computed: {
    importHeader: function () {
      return { Authorization: sessionStorage.token };
    }
  },
  methods: {
    // 数据
    mouseenter(data) {
      data.isCurrent = true
    },
    mouseleave(data) {
      data.isCurrent = false
    },
    attributeClick(data){
      event.stopPropagation()
      this.trainDetail(data)
      this.dialogFormVisible = true
      this.dialogStatus = 'attr'
      this.$nextTick(function() {
        this.$refs.trainTemp.clearValidate();
      })
    },
    trainDetail(data){
      this.trainTemp.id = data.id
      this.trainTemp.frimId= data.frimId
      this.trainTemp.brand= data.brand
      this.trainTemp.nameCh= data.nameCh
      this.trainTemp.projectStatus= data.projectStatus
      this.trainTemp.trainCode= data.trainCode
      this.trainTemp.trainYear= data.trainYear
    },
    // 目录
    dataList () {
      catalogueList().then(res => {
        this.listdata = res.data.data
        this.expandStatus(this.listdata)
      })
    },
    // 获取语种列表
    getlanguageType(){
      this.languageList = JSON.parse(sessionStorage.getItem('language'))
    },
    expandStatus(data){
      var nodeExpand = expandTree(data)
      this.treeExpandIdList.push(nodeExpand.id)
      this.handleNodeClick(nodeExpand)
      this.$nextTick(() => {
        this.$refs.tree.setCurrentKey(nodeExpand.id);
      });
    },
    pagingEvent(){
      this.manualDetail(this.parjectId)
    },
    // 右侧内容
    manualDetail(id){
      this.parjectId = id;
      var params = {
        projectId: id,
        page: this.currentPage, // 当前页
        limit: this.pagesize, // 每页显示的条数
      };
      releaseBasicList(params).then(res => {
        this.total = res.data.total
        this.resultList = res.data.data
      })
    },
    handleNodeClick (data) {
      this.manualDetail(data.id);
      this.projectId = data.id
      this.parjectName = data.name
      if (data.firmId) {
        $(".manualManage .rightTitle").show()
      } else {
        $(".manualManage .rightTitle").hide()
      }
    },
    // 获取手册分类
    getManualList() {
      manualType().then((res) => {
        if (res !== null && res.data.code === 100) {
          this.manualTypeList = res.data.data;
        }
      });
    },
    resetTemp() {
      this.addTemp={
        manualType:"",
        isComposite:"",
        outputType:"",
        language:'chinese',
        remark:""
      }
      this.$nextTick(function() {
        this.$refs.addTemp.clearValidate();
      })
    },
    // 新增
    handelAdd(){
      this.dialogFormVisible = true
      this.resetTemp();
      this.dialogStatus = "add"
    },
    addClick(temp){
      this.$refs[temp].validate((valid) => {
        if (valid) {
          var params = new URLSearchParams();
          params.append("projectId", this.projectId);
          params.append("language", this.addTemp.language);
          params.append("manualType", this.addTemp.manualType);
          params.append("isComposite", this.addTemp.isComposite);
          params.append("outputType", this.addTemp.outputType);
          params.append("remark", this.addTemp.remark);
          releaseBasicAdd(params).then((res) => {
            if(res.data.code === 100){
              handleAlert('success',res.data.msg)
              this.manualDetail(this.parjectId)
              this.dialogFormVisible = false;
            } else {
              handleAlert('error',res.data.msg)
            }
          }).catch((err) => {
            if (err !== null && err !== "" && err.responseText !== null) {
              handleAlert('error',"提交失败,请重试")
            }
          })
        } else {
          handleAlert('error',"请完善信息")
        }
      })
    },
    resetForm(){
      this.resetTemp()
    },
    // 发布  目录
    headelSetUp(row){
      let title = `${this.parjectName}(${row.manualTypeName})目录`
      this.$router.push({ name: 'releaseCatalog', params: { basicId:row.id,projectId:row.projectId , manualType:row.manualType, outputType:row.outputType, isComposite:row.isComposite,  } })
      addTabs(this.$route.path, title);
    },
    headelIssue (row) {
      let title =`${this.parjectName}(${row.manualTypeName})发布`
      sessionStorage.setItem("issuePush", title)
      this.$router.push({ name: 'release', params: { id: row.id, projectId:row.projectId , outputType:row.outputType } })
      addTabs(this.$route.path, title);
    },
    // 设置
    headelSet(row){
      console.log(row)
      this.setId = row.id
      this.coverList = []
      this.backCoverList = []
      this.setTemp.coverPage = ''
      this.setTemp.backCoverPage = ''
      this.dialogFormVisible = true
      this.dialogStatus = "set"
      this.setTemp = Object.assign({}, row)
      if(this.setTemp.coverPage != null&&this.setTemp.coverPage.length>0){
        var cover = {url: sysServerUrl + 'sys/upload/display?filePath='+ this.setTemp.coverPage}
        this.coverList.push(cover)
      }
      if(this.setTemp.backCoverPage != null&&this.setTemp.backCoverPage.length>0){
        var backCover = {url: sysServerUrl + 'sys/upload/display?filePath=' + this.setTemp.backCoverPage}
        this.backCoverList.push(backCover)
      }
    },
    addSubmit(setTemp){
      if(this.setTemp.coverPage==''){
        handleAlert('error','请选择封面')
        return false
      }
      if(this.setTemp.backCoverPage==''){
        handleAlert('error','请选择封底')
        return false
      }
      console.log(this.setTemp.coverPage)
      console.log(this.setTemp.backCoverPage)

      var params = new URLSearchParams()
      params.append('id',this.setId)
      params.append('coverPage', this.setTemp.coverPage)
      params.append('backCoverPage', this.setTemp.backCoverPage)
      releaseBasicSet(params).then(res => {
        if(res.data.code === 100){
          handleAlert('success','保存成功')
          this.dialogFormVisible = false
        }else{
          handleAlert('error',res.data.msg)
        }
      }).catch(err => {
        if (err !== null && err !== '' && err.responseText !== null) {
          handleAlert('error','提交失败,请重试')
        }
      })
    },
    // 封面
    handleOnSuccess (res, obj) {
      this.setTemp.coverPage = res.data.fileUrl
      this.coverList = []
      if(this.setTemp.coverPage != null && this.setTemp.coverPage.length > 0){
        var clickName = { url: sysServerUrl + 'sys/upload/display?filePath=' + this.setTemp.coverPage }
        this.coverList.push(clickName)
      }
    },
    handleOnRemove(file,fileList){
      if(fileList.length == '0'){
        this.coverPage=[]
        this.setTemp.coverPage = ""
      }
    },
    // 封底
    handleTwoOnSuccess (res, obj) {
      this.setTemp.backCoverPage = res.data.fileUrl
      this.backCoverList = []
      if(this.setTemp.backCoverPage != null && this.setTemp.backCoverPage.length > 0){
        var clickName = { url: sysServerUrl + 'sys/upload/display?filePath=' + this.setTemp.backCoverPage }
        this.backCoverList.push(clickName)
      }
    },
    handleTwoOnRemove(file,fileList){
      if(fileList.length == '0'){
        this.backCoverPage=[]
        this.setTemp.backCoverPage = ""
      }
    },
    // 移除图片
    beforeOnRemove(file) {
      return this.$confirm(`确定移除选择文件？`, '删除',{type: 'warning'});
    },
    beforeAvatarUpload (file) {
      var fileName = file.name.substring(file.name.lastIndexOf(".")+1).toLowerCase()
      const extension = fileName === 'png'
      const extension2 = fileName === 'jpg'
      const extension3 = fileName === 'jpeg'
      const extension4 = fileName === 'gif'
      const isLt2M = file.size / 1024 / 1024 < 5
      if (!extension && !extension2 && !extension3 && !extension4) {
        handleAlert('warning','上传图片只能是 png、jpg、jpeg、gif格式!')
        return false
      }
      if (!isLt2M) {
        handleAlert('warning','上传图片大小不能超过 5MB!')
        return false
      }
    },
    handleOnExceed (files, fileList) {
      handleAlert('warning',`当前限制选择1张图片，本次选择了 ${files.length} 张图片，共选择了 ${files.length + fileList.length} 张图片`)
    },
    // 删除
    headelDel(row){
      this.$confirm("确定删除【"+row.manualTypeName+"】的相关信息?", "删除项目发布", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        releaseBasicDel(row.id).then(res => {
          if(res.data.code == 100){
            handleAlert('success','删除成功')
            if(this.resultList!=null&&this.resultList.length==1){
              this.currentPage =this.currentPage-1
            }
            this.manualDetail(row.projectId);
          } else{
            handleAlert('error',res.data.msg)
          }
        })
      }).catch((error) => {
        handleAlert('info','取消删除')
      });
    },
  },
  mounted () {
    this.uploadUrl=sysServerUrl + 'sys/upload/attach?flag=manualImage'
    this.dataList()
    this.getManualList();
    this.getlanguageType();
    contentSize();
  }
}
</script>
<style>
  .infoDetail .manualManage .rightTitle {
    display: none
  }
  .fromRight.manualManage .pagination-container{
    border: none
  }
</style>
