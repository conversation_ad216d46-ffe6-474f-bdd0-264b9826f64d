<template>
  <div class="layoutContainer">
    <div class="infoDetail">
      <el-row>
        <el-col :span="5" class="leftData">
          <div>
            <div class="topButton">
              <span style="height: 42px; line-height: 42px; margin-left: 10px">手册项目</span>
            </div>
            <div class="scrollClass">
              <el-scrollbar>
                <el-tree
                  ref="tree"
                  :data="listdata"
                  node-key="id"
                  :props="defaultProps"
                  :default-expanded-keys="treeExpandIdList"
                  @node-click="handleNodeClick"
                >
                  <span
                    slot-scope="{ data }"
                    class="custom-tree-node"
                    @mouseenter="mouseenter(data)"
                    @mouseleave="mouseleave(data)"
                  >
                    <span>{{ data.nameCh }}</span>
                    <span
                      v-show="data.isCurrent"
                      class="attribute"
                      @click="attributeClick(data)"
                    >
                      <i
                        class="el-icon-more"
                        style="transform: rotate(90deg)"
                        title="属性"
                      ></i>
                    </span>
                  </span>
                </el-tree>
              </el-scrollbar>
            </div>
          </div>
        </el-col>
        <el-col :span="19" class="fromRight manualManage">
          <div class="rightTitle" v-if="hasPerm('menuAsimss4A5B_101')">
            <el-button type="text" icon="el-icon-plus" @click="handelAdd()">新增</el-button>
          </div>
          <el-table
            style="width: 100%"
            :data="resultList"
            stripe
            highlight-current-row
            :header-cell-style="{
              'text-align': 'center',
              'background-color': 'var(--other-color)',
            }"
            :cell-style="{ 'text-align': 'center' }"
          >
          <el-table-column
              label="手册分类"
              prop="manualTypeName"
            ></el-table-column>
            <el-table-column label="推送环境" prop="pushEnvName"></el-table-column>
            <el-table-column label="推送方式" prop="pushMethod">
              <template slot-scope="{row}">
                <span v-if="row.pushMethod == 1">本机复制</span>
                <span v-if="row.pushMethod == 2">ftp上传</span>
                <span v-if="row.pushMethod == 3">http上传</span>
              </template>
            </el-table-column>
            <el-table-column label="推送参数" prop="pushParam">
              <template slot-scope="{ row }">
                <el-button v-if="row.pushMethod !== 1 && hasPerm('menuAsimss4A5B_103')" type="text" size="small" @click="handelParameter(row)">设置参数</el-button>
              </template>
            </el-table-column>
            <el-table-column label="展示Url" prop="displayUrl"></el-table-column>
            <el-table-column label="资源路径" prop="resourcePath"></el-table-column>
            <el-table-column label="操作" fixed="right" width="150">
              <template slot-scope="{ row }">
                <el-button v-if="hasPerm('menuAsimss4A5B_103')" type="text" size="small" @click="headelEdit(row)">编辑</el-button>
                <el-button v-if="hasPerm('menuAsimss4A5B_102')" type="text" size="small" @click="headelDel(row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
    </div>
    <el-dialog v-dialogDrag :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
      <el-form
        v-if="dialogStatus === 'add' || dialogStatus === 'edit'"
        ref="temp"
        :model="temp"
        :rules="rules"
        label-position="center"
        :validate-on-rule-change="false"
        :label-width="formLabelWidth"
      >
        <el-form-item
          label="手册类型"
          prop="manualType"

        >
          <el-select v-model="temp.manualType" @change="onChangeManualType" clearable filterable>
            <el-option
              v-for="(item, index) in manualTypeList"
              :key="index"
              :value="item.code"
              :label="item.name"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="语种" prop="language">
          <el-select v-model="temp.language" clearable filterable>
            <el-option v-for="(item, index) of languageList" :key="index" :label="item.name" :value="item.code"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="推送环境" prop="pushEnv">
          <el-select v-model="temp.pushEnv" clearable filterable>
            <el-option v-for="(item, index) in pushEvnList" :key="index" :value="item.code" :label="item.name"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="推送方式" prop="pushMethod">
          <el-select v-model="temp.pushMethod" clearable filterable>
            <el-option value="1" label="本机复制"></el-option>
            <el-option value="2" label="ftp上传"></el-option>
            <el-option value="3" label="http上传"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="展示Url" prop="displayUrl">
          <el-input v-model.trim="temp.displayUrl" placeholder="请输入展示Url" show-word-limit maxlength="100"></el-input>
        </el-form-item>
        <el-form-item label="资源路径" prop="resourcePath">
          <el-input v-model.trim="temp.resourcePath" placeholder="请输入资源路径" show-word-limit maxlength="100"></el-input>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model.trim="temp.remark" placeholder="请输入备注" show-word-limit maxlength="100"></el-input>
        </el-form-item>
        <div class="submitArea">
          <el-button type="primary" @click="dialogStatus === 'edit' ? editClick('temp') : addClick('temp')">
            立即提交
          </el-button>
          <el-button @click="resetForm()"> 重置 </el-button>
        </div>
      </el-form>
      <el-form v-if="dialogStatus == 'attr'" :label-width="formLabelWidth" ref='trainTemp' :model="trainTemp">
        <el-form-item label="主机厂" prop="brand">
          <el-input v-model="trainTemp.brand" readonly></el-input>
        </el-form-item>
        <el-form-item label="项目名称" prop="nameCh">
          <el-input v-model="trainTemp.nameCh" readonly></el-input>
        </el-form-item>
        <el-form-item label="项目状态" prop="projectStatus">
          <el-input v-if="trainTemp.projectStatus == '1'" value="进行中" readonly></el-input>
          <el-input v-if="trainTemp.projectStatus == '2'" value="暂停中" readonly></el-input>
          <el-input v-if="trainTemp.projectStatus == '3'" value="已验收" readonly></el-input>
        </el-form-item>
        <el-form-item label="车型" prop="trainCode">
          <el-input v-model="trainTemp.trainCode" readonly></el-input>
        </el-form-item>
        <el-form-item label="年款" prop="trainYear">
          <el-input v-model="trainTemp.trainYear" readonly></el-input>
        </el-form-item>
      </el-form>
      <el-form v-if="dialogStatus == 'ftpVal'" :label-width="formLabelWidth" ref="setTemp" :model="setTemp">
        <el-form-item label="主机" prop="ftpHost">
          <el-input v-model.trim="setTemp.ftpHost" placeholder="请输入主机" show-word-limit maxlength="50"></el-input>
        </el-form-item>
        <el-form-item label="端口" prop="ftpPort">
          <el-input v-model.trim="setTemp.ftpPort" placeholder="请输入端口" show-word-limit maxlength="50"></el-input>
        </el-form-item>
        <el-form-item label="用户名" prop="ftpUserName">
          <el-input v-model.trim="setTemp.ftpUserName" placeholder="请输入用户名" show-word-limit maxlength="50"></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="ftpPassword">
          <el-input v-model.trim="setTemp.ftpPassword" placeholder="请输入密码" show-word-limit maxlength="50"></el-input>
        </el-form-item>
        <div class="submitArea">
          <el-button type="primary" @click="ftpClick('temp')">
            立即提交
          </el-button>
          <el-button @click="resetSetForm()"> 重置 </el-button>
        </div>
      </el-form>
      <el-form v-if="dialogStatus == 'httpVal'" :label-width="formLabelWidth" ref="setTemp" :model="setTemp">
        <el-form-item label="服务路径" prop="httpHost">
          <el-input v-model.trim="setTemp.httpHost" placeholder="请输入服务路径" show-word-limit maxlength="50"></el-input>
        </el-form-item>
        <div class="submitArea">
          <el-button type="primary" @click="httpClick('temp')">
            立即提交
          </el-button>
          <el-button @click="resetSetForm()"> 重置 </el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import { contentSize, tabPath, handleAlert, expandTree } from "@/assets/js/common.js";
import { catalogueList } from "@/api/cmsmgt.js";
import { pushEvnList, pushSetData, pushSetUpdate, pushSetAdd, pushSetDel } from "@/api/releasemgt.js";
import { manualType } from '@/api/cmsmgt.js'
export default {
  name: "releasemgtpushSetlist",
  data() {
    return {
      defaultProps: {
        children: "children",
        label: "nameCh",
      },
      trainTemp: {
        id: "",
        frimId: "",
        brand: "",
        nameCh: "",
        projectStatus: "",
        trainCode: "",
        trainYear: "",
      },
      setTemp:{
        ftpHost:"",
        ftpPort:"",
        ftpUserName:"",
        ftpPassword:"",
        httpHost:"",
      },
      temp: {
        createdUser:"",
        displayUrl:"",
        id:"",
        manualType: "",
        language:'chinese',
        projectId:"",
        pushEnv:"",
        pushEnvName:"",
        pushMethod:"",
        pushParam:"",
        remark:"",
        resourcePath:"",
        updatedUser:"",
        updatedUserName:"",
      },
      dialogFormVisible: false,
      dialogStatus: "",
      textMap: {
        edit: "编辑推送设置",
        add: "新增推送设置",
        attr: "项目属性",
        ftpVal: "设置ftp参数",
        httpVal: "设置http参数",
      },
      manualTypeList: [],
      languageList: [],
      pushEvnList: [],
      resultList: [],
      treeExpandIdList:[],
      listdata: [],
      formLabelWidth: "100px",
      rules: {
        manualType: [{ required: true, message: '手册类型不能为空', trigger: ['blur', 'change'] }],
        language: [{ required: true, message: '语种不能为空', trigger: ['blur', 'change'] }],
        pushEnv: [{ required: true, message: '推送环境不能为空', trigger: ['blur', 'change'] }],
        pushMethod: [{ required: true, message: "推送方式不能为空", tigger: ['blur', 'change'] }],
      },
    };
  },
  methods: {
    // 数据
    mouseenter(data) {
      data.isCurrent = true;
    },
    mouseleave(data) {
      data.isCurrent = false;
    },
    attributeClick(data) {
      event.stopPropagation()
      this.trainDetail(data)
      this.dialogFormVisible = true;
      this.dialogStatus = "attr";
      this.$nextTick(function() {
        this.$refs.trainTemp.clearValidate();
      })
    },
    trainDetail(data){
      this.trainTemp.id = data.id
      this.trainTemp.frimId= data.frimId
      this.trainTemp.brand= data.brand
      this.trainTemp.nameCh= data.nameCh
      this.trainTemp.projectStatus= data.projectStatus
      this.trainTemp.trainCode= data.trainCode
      this.trainTemp.trainYear= data.trainYear
    },
    // 目录
    dataList() {
      catalogueList().then((res) => {
        this.listdata = res.data.data;
        this.expandStatus(this.listdata)
      });
    },
    expandStatus(data){
      var nodeExpand = expandTree(data)
      this.treeExpandIdList.push(nodeExpand.id)
      this.handleNodeClick(nodeExpand)
      this.$nextTick(() => {
        this.$refs.tree.setCurrentKey(nodeExpand.id);
      });
    },
    // 右侧内容
    manualDetail(id) {
      var params = {
        projectId	: id,
      };
      pushSetData(params).then(res => {
        this.total = res.data.total;
        this.resultList = res.data.data;
      })
    },
    // 获取推送环境
    getPushEvnList () {
      pushEvnList().then(res => {
        if(res.data.code === 100){
          this.pushEvnList = res.data.data;
        }
      })
    },
    handleNodeClick(data) {
      this.manualDetail(data.id);
      this.projectId = data.id;
      if (data.firmId) {
        $(".manualManage .rightTitle").show()
      } else {
        $(".manualManage .rightTitle").hide()
      }
    },
    resetTemp() {
      this.temp= {
        createdUser:"",
        displayUrl:"",
        id:"",
        manualType: "",
        language:'chinese',
        projectId:"",
        pushEnv:"",
        pushEnvName:"",
        pushMethod:"",
        pushParam:"",
        remark:"",
        resourcePath:"",
        updatedUser:"",
        updatedUserName:"",
      }
      this.$nextTick(function() {
        this.$refs.temp.clearValidate();
      })
    },
    resrtSetTemp(){
      this.setTemp={
        ftpHost:"",
        ftpPort:"",
        ftpUserName:"",
        ftpPassword:"",
        httpHost:"",
      }
      this.$nextTick(function() {
        this.$refs.setTemp.clearValidate();
      })
    },
    // 设置参数
    handelParameter(row){
      this.dialogFormVisible = true;
      this.resrtSetTemp()
      this.temp = Object.assign({}, row);
      if(row.pushMethod == 2){
        this.dialogStatus = "ftpVal";
        var ftpDetail = JSON.parse(row.pushParam);
        if(ftpDetail !== null){
          this.setTemp.ftpHost = ftpDetail.host
          this.setTemp.ftpPort = ftpDetail.port
          this.setTemp.ftpUserName = ftpDetail.username
          this.setTemp.ftpPassword = ftpDetail.password
        }
      }else if(row.pushMethod == 3){
        this.dialogStatus = "httpVal";
        var httpDetail = JSON.parse(row.pushParam);
        if(httpDetail !== null){
          this.setTemp.httpHost = httpDetail.host
        }
      }
    },
    ftpClick(){
      var pushVal = {
        host: this.setTemp.ftpHost,
        port: this.setTemp.ftpPort,
        username: this.setTemp.ftpUserName,
        password: this.setTemp.ftpPassword
      }
      var params = new URLSearchParams();
      params.append("id", this.temp.id);
      params.append("pushEnv", this.temp.pushEnv);
      params.append("pushMethod", this.temp.pushMethod);
      params.append("pushParam", JSON.stringify(pushVal));
      pushSetUpdate(params).then((res) => {
        if (res.data.code === 100) {
          handleAlert('success',res.data.msg)
          this.manualDetail(this.projectId)
          this.dialogFormVisible = false;
        } else {
          handleAlert('error',res.data.msg)
        }
      }).catch((err) => {
        if (err !== null && err !== "" && err.responseText !== null) {
          handleAlert('error',"提交失败,请重试")
        }
      });
    },
    httpClick(){
      var pushVal = {
        host: this.setTemp.httpHost,
      }
      var params = new URLSearchParams();
      params.append("id", this.temp.id);
      params.append("pushEnv", this.temp.pushEnv);
      params.append("pushMethod", this.temp.pushMethod);
      params.append("pushParam", JSON.stringify(pushVal));
      pushSetUpdate(params).then((res) => {
        if (res.data.code === 100) {
          handleAlert('success',res.data.msg)
          this.manualDetail(this.projectId)
          this.dialogFormVisible = false;
        } else {
          handleAlert('error',res.data.msg)
        }
      }).catch((err) => {
        if (err !== null && err !== "" && err.responseText !== null) {
          handleAlert('error',"提交失败,请重试")
        }
      });
    },
    resetSetForm(){
      this.resrtSetTemp()
    },
    // 获取手册分类
    getManualList () {
      manualType().then(res => {
        if (res !== null && res.data.code === 100) {
          this.manualTypeList = res.data.data
        }
      })
    },
    // 获取语种列表
    getlanguageType(){
      this.languageList = JSON.parse(sessionStorage.getItem('language'))
    },
    // 新增
    handelAdd() {
      this.dialogFormVisible = true;
      this.resetTemp();
      this.dialogStatus = "add";
    },
    addClick(temp) {
      this.$refs[temp].validate((valid) => {
        if (valid) {
          var params = new URLSearchParams();
          params.append("manualType", this.temp.manualType);
          params.append("language", this.temp.language);
          params.append("projectId", this.projectId);
          params.append("pushEnv", this.temp.pushEnv);
          params.append("pushMethod", this.temp.pushMethod);
          params.append("remark", this.temp.remark);
          params.append("resourcePath", this.temp.resourcePath);
          params.append("displayUrl", this.temp.displayUrl);
          pushSetAdd(params).then((res) => {
            if (res.data.code === 100) {
              handleAlert('success',res.data.msg)
              this.manualDetail(this.projectId)
              this.dialogFormVisible = false;
            } else {
              handleAlert('error',res.data.msg)
            }
          }).catch((err) => {
            if (err !== null && err !== "" && err.responseText !== null) {
              handleAlert('error',"提交失败,请重试")
            }
          });
        } else {
          handleAlert('error',"请完善信息")
        }
      });
    },
    resetForm() {
      this.resetTemp();
    },
    // 编辑
    headelEdit(row) {
      this.dialogFormVisible = true;
      this.resetTemp();
      this.dialogStatus = 'edit';
      this.temp = Object.assign({}, row);
      this.temp.pushMethod = String(this.temp.pushMethod)
    },
    editClick(temp) {
      this.$refs[temp].validate((valid) => {
        if (valid) {
          var params = new URLSearchParams();
          params.append("id", this.temp.id);
          params.append("pushEnv", this.temp.pushEnv);
          params.append("pushMethod", this.temp.pushMethod);
          params.append("remark", this.temp.remark);
          params.append("resourcePath", this.temp.resourcePath);
          params.append("displayUrl", this.temp.displayUrl);
          pushSetUpdate(params).then((res) => {
            if (res.data.code === 100) {
              handleAlert('success',res.data.msg)
              this.manualDetail(this.projectId)
              this.dialogFormVisible = false;
            } else {
              handleAlert('error',res.data.msg)
            }
          }).catch((err) => {
            if (err !== null && err !== "" && err.responseText !== null) {
              handleAlert('error',"提交失败,请重试")
            }
          });
        } else {
          handleAlert('error',"请完善信息")
        }
      });
    },
    // 删除
    headelDel(row) {
      this.$confirm("确定删除当前推送配置?", "删除推送配置", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then((res) => {
          pushSetDel(row.id).then((res) => {
            if (res.data.code === 100) {
              handleAlert('success',"删除成功")
              this.manualDetail(this.projectId)
            }else{
               handleAlert('error',"删除失败")
            }
          });
        }).catch((error) => {
          handleAlert('info','取消删除')
        });
    },
  },
  mounted() {
    this.dataList();
    this.getPushEvnList();
    this.getlanguageType();
    this.getManualList();
    contentSize();
  },
};
</script>
<style>
.infoDetail .manualManage .rightTitle {
  display: none;
}

</style>
