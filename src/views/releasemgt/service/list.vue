<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" ref="searchForm" :label-width="formLabelWidth" :model="searchForm" class="demo-form-inline">
        <el-form-item label="品牌" prop="brandId" >
          <el-select v-model="searchForm.brandId" placeholder="请选择年品牌" @change="getModelList" clearable filterable>
            <el-option v-for="(item,index) in brandList" :key="index" :label="item.brandName" :value="item.brandId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="车型" prop="modelId" >
          <el-select v-model="searchForm.modelId" placeholder="请选择年车型" clearable filterable>
            <el-option v-for="(item,index) in modelList" :key="index" :label="item.modelName" :value="item.modelId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search()" icon="el-icon-search">搜索</el-button>
          <el-button plain @click="resetSearch('searchForm')">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <div class="tableHandle" v-if="hasPerm('menuAsimss4A6B_101')">
        <el-button type="text" icon="el-icon-plus"  @click="addClick()">新增</el-button>
      </div>
      <!-- 列表内容 -->
      <el-table
        style="width:100%"
        border
        stripe
        highlight-current-row
        :data="resultList"
        :header-cell-style="{
          'text-align': 'center',
          'background-color': 'var(--other-color)',
        }"
      >
        <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
        <el-table-column label="品牌" prop="brandName" min-width="100" ></el-table-column>
        <el-table-column label="车型" prop="modelName" min-width="80" ></el-table-column>
        <el-table-column label="排序" prop="sort" min-width="100" align="center"></el-table-column>

        <el-table-column label="状态" prop="status" min-width="80">
          <template slot-scope="{row}">
            <span v-if="row.status === '1'">未推送</span>
            <span v-if="row.status === '2'" style="color:#009933">成功</span>
            <span v-if="row.status === '3'" style="color:#F9332F">失败</span>
          </template>
        </el-table-column>
        <el-table-column label="推送时间" prop="pushTime" min-width="140">
          <template slot-scope="{row}">
            <div>
              {{ row.pushTime | conversion("yyyy-MM-dd") }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column label="操作" fixed="right" min-width="200">
           <template slot-scope="{row}">
            <!-- 可以推送的状态： -->
            <el-button v-if="hasPerm('menuAsimss4A6B_101')" type="text" size="small" @click="headerDetail(row)">推送</el-button>
            <!-- 可以开启的状态：关闭且推送成功 -->
            <el-button v-if="row.isClose == 1 && row.status == '2' && hasPerm('menuAsimss4A6B_103')" type="text" size="small" @click="editStatus(row, 0)">开启</el-button>
            <!-- 可以关闭的状态：开启且推送成功 -->
            <el-button v-if="row.status == '2' && row.isClose == 0 && hasPerm('menuAsimss4A6B_103')" type="text" size="small" @click="editStatus(row, 1)">关闭</el-button>
            <!-- 可以编辑的状态：关闭 -->
            <el-button v-if="row.isClose == 1 && hasPerm('menuAsimss4A6B_103')" type="text" size="small" @click="editClick(row)">编辑</el-button>
            <!-- 可以删除的状态：关闭  -->
            <el-button v-if="row.isClose == 1 && hasPerm('menuAsimss4A6B_102')" type="text" size="small" @click="del(row)">删除</el-button>
            <el-button v-if="hasPerm('menuAsimss4A6B_104')" type="text" size="small" @click="dow(row)">下载数据包</el-button>
           </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total>0" :total="total" :page.sync="currentPage" :limit.sync="pagesize" @pagination="dataList"/>

        <!-- 添加 -->
        <el-dialog v-dialogDrag title="添加"  width="600px !important" :visible.sync="dialogaddFormVisible" :close-on-click-modal="false">
          <el-form ref='temp' :label-width="formLabelWidth" :model="temp" label-position="center" :validate-on-rule-change="false">
            <el-form-item label="品牌" prop="brandId">
              <el-select v-model="temp.brandId" placeholder="请选择年车型" @change="getModelList" clearable filterable>
                <el-option v-for="(item,index) in brandList" :key="index" :label="item.brandName" :value="item.brandId"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="车型" prop="modelId">
              <el-select v-model="temp.modelId" placeholder="请选择年车型" @change="getModel" clearable filterable>
                  <el-option v-for="(item,index) in modelList" :key="index" :label="item.modelName" :value="item.modelId"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="排序" prop="sort">
              <el-input type="number" :min="1" :max="9999"  @input="e => temp.sort=parserNumber(e,1,9999)"  v-model.trim="temp.sort"></el-input>
            </el-form-item>
            <el-form-item label="备注" prop="remark">
              <el-input v-model.trim="temp.remark" placeholder="请输入备注" show-word-limit maxlength="100"></el-input>
            </el-form-item>
            <!-- 文件上传 -->
          <el-form-item>
            <el-upload
                  class="upload-demo inline-block"
                  ref="elUpload"
                  action="#"
                  :show-file-list="false"
                  multiple
                  :limit="1"
                  :before-upload="onBeforeUpload"
                >
                <el-button type="primary" v-show="showUploadBtn">选择文件</el-button>
                <div slot="tip" class="el-upload__tip" v-if="isfinish">只能上传zip文件，且文件以【车型名称】命名</div>
                <el-progress style="width:350px;margin-top:50px;margin-left:10px" readonly="readonly" v-show="showUploadProcess" color="green" type="line"  :text-inside="true" :stroke-width="20" :percentage="percentage"></el-progress>
                </el-upload>
                 <el-upload
                  ref="elUploadResult"
                  action="#"
                  :show-file-list="true"
                  :file-list="zipList"
                  :limit="1"
                  :before-remove="handleBeforeRemove"

                >
               </el-upload>
          </el-form-item>
          <div class="submitArea">
            <el-button type="primary" @click="add()" >提交</el-button>
            <!-- <el-button plain @click="resetAdd()">取消</el-button> -->
          </div>
          </el-form>
        </el-dialog>

        <!-- 编辑手册 -->
        <el-dialog v-dialogDrag title="编辑"  width="600px !important" :visible.sync="dialogeditFormVisible" :close-on-click-modal="false">
          <el-form :model="temp" :label-width="formLabelWidth" label-position="center" :validate-on-rule-change="false">
            <el-form-item label="品牌" prop="brandName">
              <el-input v-model.trim="temp.brandName"  readonly="readonly" ></el-input>
            </el-form-item>
            <el-form-item label="车型" prop="modelName">
              <el-input v-model.trim="temp.modelName" readonly="readonly" ></el-input>
            </el-form-item>
            <el-form-item label="排序" prop="sort">
              <el-input type="number" :min="1" :max="9999"  @input="e => temp.sort=parserNumber(e,1,9999)"  v-model="temp.sort" placeholder="请输入排序"></el-input>
            </el-form-item>
            <el-form-item label="备注" prop="remark">
              <el-input v-model.trim="temp.remark" placeholder="请输入数据包描述" show-word-limit maxlength="100"></el-input>
            </el-form-item>

              <!-- 文件上传 -->
            <el-form-item>
              <el-upload
                    class="upload-demo inline-block"
                    ref="elUpload"
                    action="#"
                    :show-file-list="false"
                    multiple
                    :limit="1"
                    :before-upload="onBeforeUpload"
                  >
                  <el-button type="primary" v-show="showUploadBtn">替换数据包</el-button>
                  <div slot="tip" class="el-upload__tip" v-if="isfinish">只能上传zip文件，且文件以【车型名称】命名</div>
                  <el-progress style="width:350px;margin-top:50px;margin-left:10px" readonly="readonly" v-show="showUploadProcess" color="green" type="line"  :text-inside="true" :stroke-width="20" :percentage="percentage"></el-progress>
                  </el-upload>
                  <el-upload
                    ref="elUploadResult"
                    action="#"
                    :show-file-list="true"
                    :file-list="zipList"
                    :limit="1"
                    :before-remove="handleBeforeRemove"

                  >
                </el-upload>
            </el-form-item>
            <div class="submitArea">
              <el-button type="primary" @click="edit()" >提交</el-button>
              <el-button plain @click="dialogeditFormVisible = false">取消</el-button>
            </div>
          </el-form>
        </el-dialog>
    </div>
  </div>
</template>
<script>
import { sysServerUrl, addTabs, handleAlert } from '@/assets/js/common.js'
import Pagination from '@/components/Pagination'
import { serviceModel, serviceData, serviceAdd, serviceEdit, serviceDel, basicFinishUpload } from '@/api/releasemgt.js'
import {  procSplitFile, checkUploadProgress } from '@/api/sysmgt.js'

import SparkMD5 from 'spark-md5'

/** 计算文件md5值
 * @param file 文件
 * @param chunkSize 分片大小
 * @returns Promise
 */
 function getmd5(file, chunkSize) {
    return new Promise((resolve, reject) => {
        let blobSlice = File.prototype.slice || File.prototype.mozSlice || File.prototype.webkitSlice;
        let chunks = Math.ceil(file.size / chunkSize);
        let currentChunk = 0;
        let spark = new SparkMD5.ArrayBuffer();
        let fileReader = new FileReader();
        fileReader.onload = function(e) {
            spark.append(e.target.result);
            currentChunk++;
            if (currentChunk < chunks) {
                loadNext();
            } else {
                let md5 = spark.end();
                resolve(md5);
                //  console.log(md5);
            }
        };
        fileReader.onerror = function(e) {
            reject(e);
        };
        function loadNext() {
            let start = currentChunk * chunkSize;
            let end = start + chunkSize;
            if (end > file.size){
                end = file.size;
            }
            fileReader.readAsArrayBuffer(blobSlice.call(file, start, end));
        }
        loadNext();
    });
}



export default {
  name: 'releasemgtservicelist',
  components: { Pagination },
  data () {
    return {

      formLabelWidth: '80px',

      // 搜索表单
      searchForm:{
        brandId: '',  // 品牌
        modelId: '',  // 车型
      },

      // 当前页
      currentPage: 1,
      // 每页显示的条数
      pagesize: 10,
      // 总条数
      total: 0,

      // 车型名称
      modelName: '',

      // 获取的数据集
      resultList: [],
      // 车型结果集
      modelList: [],
      // 品牌结果集
      brandList: [],

      dialogaddFormVisible: false,  // 添加弹窗
      dialogeditFormVisible: false,  // 编辑弹窗

      // 添加的表单
      temp: {
        modelName: '',
        brandName: '',
        modelId: '',  // 车型
        brandId: '',  // 品牌
        zipPath: '', // 手册路径
        remark: '',  // 手册说明
        sort: 1,   // 排序
      },
      isfinish: true,
      // 分片上传
      fileList: [],
      zipList:[],
      //切片文件
      fileShard:{},
      //当前文件
      curFile:{},
        //文件分割的开始位置
      start: 0,
      //文件分割的结束位置
      end: 0,
      //文件大小
      fileSize: 0,
      fileKey: '',
      fileShardIndex: 0,
      fileShardTotal: 0,
      fileShardSize: 0,
      switchC: false,
      percentage: 0,
      showUploadBtn:false,
      showUploadProcess:false,
      flagType: 'temp/servicePush',
    }
  },
  methods: {
    // 数据
    dataList(){
      var params = new URLSearchParams()
      params.append('page', this.currentPage)
      params.append('limit', this.pagesize)
      params.append('brandId', this.searchForm.brandId)
      params.append('modelId', this.searchForm.modelId)
      serviceData(params).then(res => {
        this.total = res.data.total    // 总条数
        this.resultList = res.data.data   // 数据
        // console.log("--", this.resultList);
      }).catch(err => {
        handleAlert('error','系统开小差了...')
      })
    },

    // 搜索
    search(){
      this.currentPage = 1;
      this.dataList();
    },

    // 重置搜索表单
    resetSearch(){
      this.searchForm.brandId = '';

      this.searchForm.modelId = '';

      this.currentPage = 1;
      this.dataList();
    },

    // 重置添加表单
    resetAdd(){
      this.temp = {
        brandName: '',
        modelName: '',
        modelId: '',  // 车型
        brandId: '',  // 品牌
        zipPath: '', // 手册路径
        remark: '',  // 手册说明
        sort: 1,   // 排序
      };
      if (this.$refs.temp) {
        this.$nextTick(function() {
          this.$refs.temp.clearValidate();
        })
      };
      this.isfinish = true;
      this.fileList = [];
      this.zipList = [];
      this.showUploadBtn = true;
      this.showUploadProcess = false;
      this.percentage = 0;
    },

    // 获取品牌
    getBrandTrainList(){
      serviceModel().then(res => {
        this.brandList = res.data.data
        // console.log("---", this.brandList);
      }).catch(err => {
        handleAlert('error','系统开小差了...')
      })
    },

    // 获取车型
    getModelList(){
      this.searchForm.modelId = ''
      this.temp.modelId = ''

      let brandId = this.dialogaddFormVisible ? this.temp.brandId : this.searchForm.brandId
      // console.log("===", brandId);
      if (!brandId || brandId == '') {
        return;
      }
      let _this = this;
      this.brandList.forEach(item => {
        if (item.brandId == brandId) {
          _this.modelList = item.modelList;
        }
      })
    },

    getModel(modelId){
      // console.log("---=", modelId);
      this.temp.modelName = ''
      if (!modelId || modelId == '') {
        return;
      }
      let _this = this;
      this.modelList.forEach(item => {
        if (item.modelId == modelId) {
          // console.log("-=-=--=:", item);
          _this.temp.modelName = item.modelName;
        }
      })

      // console.log("--=-=-", _this.temp.modelName);
    },

    // 添加-显示弹窗
    addClick(){
      this.resetAdd();
      this.dialogaddFormVisible = true;
    },

    // 添加
    add(){
      if(!this.zipList || this.zipList.length <= 0){
        handleAlert('error','请上传数据包')
        return false;
      }

      var params = new URLSearchParams()
      params.append('modelId', this.temp.modelId)
      params.append('brandId', this.temp.brandId)
      params.append('modelYear', this.temp.modelYear)
      params.append('sort', this.temp.sort)
      params.append('zipPath', this.zipList[0].filePath)
      params.append('remark', this.temp.remark)
      serviceAdd(params).then(res => {
        if (res.data.code == 100) {
          handleAlert('success','成功')
          this.dataList();
          this.dialogaddFormVisible = false;
        }else{
          handleAlert('error',res.data.msg)
        }
      }).catch(err => {
        handleAlert('error','系统开小差了...')
      })
    },

    // 编辑
    editClick(row){
      this.resetAdd();
      this.temp.brandId = row.brandId
      this.temp.modelId = row.modelId
      this.temp.brandName = row.brandName
      this.temp.modelName = row.modelName
      this.temp.sort = row.sort
      this.temp.id = row.id
      this.temp.remark = row.remark
      this.dialogeditFormVisible = true
    },

    edit(){
      var params = new URLSearchParams()
      params.append("id", this.temp.id)
      if(this.zipList && this.zipList.length > 0){
        params.append('zipPath', this.zipList[0].filePath)
      }
      params.append('sort', this.temp.sort)
      params.append('remark', !this.temp.remark ? "" : this.temp.remark)
      serviceEdit(params).then(res => {
        if (res.data.code == 100) {
          handleAlert('success','成功')
          this.dataList();
          this.dialogeditFormVisible = false;
        }else{
          handleAlert('error',res.data.msg)
        }
      }).catch(err => {
        handleAlert('error','系统开小差了...')
      })
    },

    // 修改状态
    editStatus(row, type){
      var params = new URLSearchParams()
      params.append("id", row.id)
      params.append("isClose", type)
      params.append('sort', row.sort)
      params.append('remark', !row.remark ? "" : row.remark)
      serviceEdit(params).then(res => {
        if (res.data.code == 100) {
          // handleAlert('success','成功')
          this.dataList();
          // this.dialogeditFormVisible = false;
        }else{
          handleAlert('error',res.data.msg)
        }
      }).catch(err => {
        handleAlert('error','系统开小差了...')
      })
    },


    // 删除
    del(row){
      this.$confirm('确定删除【' + row.modelName + '】吗?', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        serviceDel(row.id).then(res => {
          if (res.data.code === 100) {
            handleAlert('success','删除成功')
            this.dataList();
          }else{
            handleAlert('error','删除失败，' + res.data.msg)
          }
        })
      }).catch((error)=>{
        handleAlert('info','取消删除')
      })
    },

    dow(row){
      window.open(row.zipPath, '_blank');
    },

    // 详情
    headerDetail(row){
      let title = row.brandName + "  丨  " +row.modelName
      this.$router.push({ name: 'servicepushList', params:{id:row.id}})
      addTabs(this.$route.path, title);
    },


    // 上传前的校验
    onBeforeUpload(file){
      let text=""
      if(!this.temp.modelId || this.temp.modelId == ''){
        text="请先选择车型";
        this.$message.error(text)
        return false;
      }
      // 判断文件名是否是手册名称命名 的
      let name = file.name.substring(0, file.name.lastIndexOf('.'))
      if (name != this.temp.modelName) {
        handleAlert('error','文件应以车型名称【'+ this.temp.modelName +'】命名')
        return false
      }
      this.flagType='temp/serverPush'

      // 获取文件后缀
      var fileExt =file.name.substring(file.name.lastIndexOf('.')+1).toLowerCase();
      // 文件后缀是否是 zip
      const zipExt = fileExt === 'zip'
      // 文件大小不能超过1G
      const isLimit = file.size / 1024 / 1024 < 1024
      if(!zipExt) {
        text="上传文件只能是 zip 格式!";
        this.$message.error(text)
        return false;
      }
      if (!isLimit) {
        text="上传文件大小不能超过 1GB!";
        this.$message.error(text)
        return false;
      }
      this.fileShardSize = 1*1024 * 1024; //每片文件大小
      this.isfinish = false;
      //点击后隐藏上传按钮 ，防止重复点击
      this.showUploadBtn=false
      this.showUploadProcess=true
      this.percentage=1
      var _this=this
      getmd5(file,_this.fileShardSize).then(e =>{
              _this.switchC=false;
              _this.fileShardIndex=1;//分片索引
              _this.curFile=file;
              _this.fileKey=e;
              _this.fileSize=file.size;
              _this.fileShardTotal=Math.ceil(file.size/_this.fileShardSize);//分片总数
              var fileFullName=file.name;
              _this.fileSuffix = fileFullName.substr(fileFullName.lastIndexOf('.') + 1);
              _this.fileName = fileFullName.substr(0, fileFullName.lastIndexOf('.'));

              //上传参数
              var params =  new FormData()
              params.append('fileName', _this.fileName)
              params.append('fileShardTotal', _this.fileShardTotal)
              params.append('fileKey', _this.fileKey)
              params.append('fileSuffix', _this.fileSuffix)
              params.append('fileShardSize', _this.fileShardSize)
              params.append('fileSize', _this.fileSize)
              params.append('fileFlag', _this.flagType)

              _this.updateProgress(file,params)

          })
    },
    // 批量上传
    uploadFile (formData) {
      var _this=this
      // 上传
      procSplitFile(formData).then(res => {
        if(res.data.code==200){
          //上传分片完成
          if(res.data.shardIndex<_this.fileShardTotal){
            _this.fileShardIndex=_this.fileShardIndex+1;
            _this.start=(_this.fileShardIndex-1)*_this.fileShardSize;
            _this.end =Math.min(_this.curFile.size,_this.start+_this.fileShardSize);
            _this.fileSize=_this.curFile.size;
            var params =  new FormData()
            params.append('fileName', _this.fileName)
            params.append('fileShardTotal', _this.fileShardTotal)
            params.append('fileKey', _this.fileKey)
            params.append('fileSuffix', _this.fileSuffix)
            params.append('fileShardSize', _this.fileShardSize)
            params.append('fileSize', _this.fileSize)
            params.append('fileFlag', _this.flagType)
            params.append('fileShardIndex', _this.fileShardIndex)
            var fileShardtem=_this.curFile.slice(_this.start,_this.end);//从文件中获取当前分片数据
            let fileReader = new FileReader();
            //异步读取本地文件分片数据并转化为base64字符串
            fileReader.readAsDataURL(fileShardtem);
            //本地异步读取成功后，进行上传
            fileReader.onload = function (e) {
              let  base64str = e.target.result;
              params.append('base64', base64str)
              _this.uploadFile(params)
            }
            let perentNum=Math.ceil(this.fileShardIndex * 100 / this.fileShardTotal)
            if(perentNum>100){
              this.percentage=100
            }else{
              this.percentage=perentNum
            }
          }
        }else if(res.data.code==100){
          var fileId= res.data.id
          //上传完成
          _this.percentage=100
          _this.switchC=true

          _this.finishUpload(fileId)
        }
        console.log(this.percentage)
      }).catch((error)=>{
        if(error.response){
        console.log(error.response.data)
        console.log(error.response.status)
        console.log(error.response.headers)
        }else{
          console.log(error.message)
        }
      })

    },
    updateProgress(file,params){
      var _this= this
      var param = new URLSearchParams()
      param.append('shardKey', _this.fileKey)
      // 批量上传
      checkUploadProgress(param).then(res => {
        if(res.data.code==200){
          //新文件
          _this.start=(_this.fileShardIndex-1)*_this.fileShardSize;
          _this.end =Math.min(file.size,_this.start+_this.fileShardSize);
          _this.fileShard=file.slice(_this.start,_this.end);//从文件中获取当前分片数据
        }else if(res.data.code==220){
          _this.fileShardIndex=res.data.ShardIndex;
          //有上传未完成的
          _this.start=(_this.fileShardIndex-1)*_this.fileShardSize;
          _this.end =Math.min(file.size,_this.start+_this.fileShardSize);
          _this.fileShard=file.slice(_this.start,_this.end);//从文件中获取当前分片数据
        }else if (res.data.code==240){
          //急速上传
          var fileId= res.data.id
          _this.percentage=100
          _this.switchC=true
          console.log(this.percentage)
          _this.finishUpload(fileId)
          return false;
        }
        //读取base64str
        let fileReader = new FileReader();
        //异步读取本地文件分片并转化为base64字符串
        fileReader.readAsDataURL(_this.fileShard);
        //本地异步读取成功，进行上传
        fileReader.onload=function (e){
          let  base64str=e.target.result;
          params.append('base64', base64str)
          params.append('fileShardIndex', _this.fileShardIndex)
          if(_this.switchC==false){
              _this.uploadFile(params)
          }
        }
      }).catch((error)=>{
        this.$message.error('上传错误')
      })

    },
    // 上传完成
    finishUpload(fileId){
         var _this=this
           //进行保存提醒
          _this.uploadMsg = _this.$message({
                              duration:0,
                              message: "请稍等，正在保存...",
                              type: "warning"
                            });
          var param = new URLSearchParams()
          param.append('fileId', fileId)
          basicFinishUpload(param).then(res => {
            if(res.data.code==100){
                let uploadFileName= res.data.data.fileName
                let uploadFilePath= res.data.data.filePath
                _this.zipList = []
                if(uploadFileName != null && uploadFileName.length > 0){
                  var fileObj = { name: uploadFileName , filePath:uploadFilePath}
                  _this.zipList.push(fileObj)
                }
                //关闭消息提醒
                _this.uploadMsg.close()

                //上传完成提示
                _this.$message({
                  duration:2000,
                  message: '上传已完成',
                  type: 'success'
                })
              _this.showUploadProcess=false
              _this.showUploadBtn=false
            }
          })

    },
    // 文件移除
    handleBeforeRemove(){
     if(this.zipList!=null&&this.zipList.length>0){
        // var filePath= this.zipList[0].filePath;
          var _this=this
                this.$confirm('确认删除文件？', '删除',{
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => {
                _this.zipList=[];
                _this.showUploadBtn=true;
                _this.isfinish = true;
              }).catch((error)=>{
                handleAlert('info','取消删除')
                 return false;
              })
      }
      return false;
    },
  },
  mounted () {
    this.dataList();
    this.getBrandTrainList();
  }
}
</script>
