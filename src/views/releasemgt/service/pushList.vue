<template>
  <div class="layoutContainer">
    <div class="secondFloat">
    </div>
    <div class="tableDetail">
      <div class="tableHandle" v-if="hasPerm('menuAsimss4A6B_101')">
        <el-button type="text" icon="el-icon-plus" @click="addData()">新增</el-button>
      </div>

      <el-table
        style="width:100%"
        border
        stripe
        highlight-current-row
        :data="resultList"
        :header-cell-style="{
          'text-align': 'center',
          'background-color': 'var(--other-color)',
        }"
      >
        <el-table-column label="推送地址" prop="pushPath" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column label="推送状态" prop="status" min-width="80" align="center">
          <template slot-scope="{row}">
            <span v-if="row.status === 1" style="color:#009933">推送成功</span>
            <span v-if="row.status === 0" style="color:#c30000">推送失败</span>
          </template>
        </el-table-column>
        <el-table-column label="失败原因" prop="reason" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column label="推送人" prop="pushUserName" min-width="80"></el-table-column>
        <el-table-column label="推送时间" prop="createdTime" min-width="80" align="center">
          <template slot-scope="{row}">
            <div>
              {{ row.createdTime | conversion("yyyy-MM-dd") }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" min-width="100" show-overflow-tooltip></el-table-column>
      </el-table>
      <pagination v-show="total>0" :total="total" :page.sync="currentPage" :limit.sync="pagesize" @pagination="dataList"/>

      <el-dialog v-dialogDrag :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" :close-on-click-modal="false">
        <el-form v-if="dialogStatus === 'add'" :label-width="formLabelWidth" :model="temp" ref="temp" label-position="center">
          <el-form-item label="推送地址" prop="pushPath">
            <el-input v-model.trim="temp.pushPath" placeholder="请输入推送地址" show-word-limit maxlength="150"></el-input>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model.trim="temp.remark" placeholder="请输入备注" show-word-limit maxlength="100"></el-input>
          </el-form-item>
          <div class="submitArea">
            <el-button type="primary" @click="addClick('temp')">立即提交</el-button>
          </div>
        </el-form>
      </el-dialog>
      <el-dialog v-dialogDrag title="正在推送" v-if="showUploadProcess" :visible="showUploadProcess" lock-scroll  :close-on-click-modal="false"  :close-on-press-escape="false"  :show-close="false" :destroy-on-close="true">
        <el-progress style="width:530px;margin-top:50px;margin-left:10px" color="green" type="line"  :text-inside="true" :stroke-width="20" :percentage="percentage" :disabled="true"></el-progress>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { handleAlert } from '@/assets/js/common.js'
import Pagination from '@/components/Pagination'
import { servicePush, servicePushList } from '@/api/releasemgt.js'
export default {
  name: 'servicepushList',
  components: { Pagination },
  data () {
    return {
      basicId: '',
      temp: {
        pushPath:"",
        remark: "",
      },
      dialogStatus: '',
      dialogFormVisible: false,
      textMap: {
        add: '新增推送'
      },
      resultList: [],
      pagesize: 10,
      currentPage: 1,
      total: 0,
      formLabelWidth: '80px',

      showUploadProcess: false,
      percentage: 0,

    }
  },
  methods: {
    // 数据
    dataList () {
      var params = {
        basicId:this.basicId,
        limit:this.pagesize,
        page:this.currentPage,

      }
      servicePushList(params).then(res =>{
        this.total = res.data.total
        this.resultList = res.data.data
      })
    },

    resetTemp(){
      this.temp.pushPath = ''
      this.temp.remark = ''
    },

    // 新增
    addData () {
      this.dialogStatus = 'add'
      this.dialogFormVisible = true
      this.resetTemp()
    },
    addClick (temp) {
      if (!this.temp.pushPath || this.temp.pushPath == '') {
        handleAlert('error','请输入要推送的地址')
        return false;
      }
      this.showUploadProcess=true;
      this.percentage=37;
      setTimeout(()=>{
        this.percentage=62;
      }, 1000)
      setTimeout(()=>{
        this.percentage=83;
      }, 2000)
      setTimeout(()=>{
        this.percentage=96;
      }, 3000)
      var params = new URLSearchParams()
      params.append('pushPath', this.temp.pushPath)
      params.append('remark', this.temp.remark)
      params.append('basicId', this.basicId)
      servicePush(params).then(res => {
        this.percentage=100;

        if (res.data.code === 100) {
          handleAlert('success','提交成功')
        } else {
          handleAlert('error','新增失败, ' + res.data.msg)
        }
        this.dataList()
        this.showUploadProcess=false;
        this.dialogFormVisible = false

      }).catch((err) => {
        if (err !== null && err !== "" && err.responseText !== null) {
          handleAlert('error',"新增失败,请重试")
          this.showUploadProcess=false;
        }
      });
    },
  },
  mounted () {
    this.basicId = this.$route.params.id
    this.dataList()
  }
}
</script>

