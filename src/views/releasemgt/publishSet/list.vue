<template>
  <div class="layoutContainer">
    <div class="infoDetail">
      <el-row>
        <el-col :span="5" class="leftData">
          <div>
            <div class="topButton">
              <span style="height: 42px; line-height: 42px; margin-left: 10px">手册项目</span>
            </div>
            <div class="scrollClass">
              <el-scrollbar>
                <el-tree
                  ref="tree"
                  node-key="id"
                  :data="listdata"
                  :props="defaultProps"
                  :default-expanded-keys="treeExpandIdList"
                  @node-click="handleNodeClick"
                >
                  <span
                    slot-scope="{ data }"
                    class="custom-tree-node"
                    @mouseenter="mouseenter(data)"
                    @mouseleave="mouseleave(data)"
                  >
                    <span>{{ data.nameCh }}</span>
                    <span
                      v-show="data.isCurrent"
                      class="attribute"
                      @click="attributeClick(data)"
                    >
                      <i
                        class="el-icon-more"
                        style="transform: rotate(90deg)"
                        title="属性"
                      ></i>
                    </span>
                  </span>
                </el-tree>
              </el-scrollbar>
            </div>
          </div>
        </el-col>
        <el-col :span="19" class="fromRight manualManage">
          <div class="rightTitle" v-if="hasPerm('menuAsimss4A4B_101')">
            <el-button type="text" icon="el-icon-plus" @click="handelAdd()">新增</el-button>
          </div>
          <el-table
            style="width: 100%"
            :data="resultList"
            stripe
            highlight-current-row
            :header-cell-style="{
              'text-align': 'center',
              'background-color': 'var(--other-color)',
            }"
            :cell-style="{ 'text-align': 'center' }"
          >
          <el-table-column
              label="手册分类"
              prop="manualTypeName"
            ></el-table-column>
          <el-table-column label="发布名称" prop="releaseName"></el-table-column>

            <el-table-column label="解析规则" prop="parseName"></el-table-column>
            <el-table-column label="前提条件" prop="preconditionStr"></el-table-column>
            <el-table-column label="发布状态" prop="status" width="100">
              <template slot-scope="{ row }">
                <span v-if="row.status === 1">未开始</span>
                <span v-if="row.status === 2">发布中</span>
                <span v-if="row.status === 3" style="color:#009933">发布完成</span>
                <span v-if="row.status === 4" style="color:#c30000">发布失败</span>
              </template>
            </el-table-column>
            <el-table-column label="发布结果" prop="result"></el-table-column>
            <el-table-column label="更新人" prop="updatedUserName"></el-table-column>
            <el-table-column label="更新时间" prop="updatedTime">
              <template slot-scope="{ row }">
                <div>
                  {{ row.updatedTime | conversion("yyyy-MM-dd") }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" fixed="right" width="150">
              <template slot-scope="{ row }">
                <el-button v-if="hasPerm('menuAsimss4A4B_103')" type="text" size="small" @click="headelEdit(row)">编辑</el-button>
                <el-button v-if="hasPerm('menuAsimss4A4B_102')" type="text" size="small" @click="headelDel(row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination v-show="total>15" :total="total" :page.sync="currentPage" :limit.sync="pagesize" @pagination="pagingEvent"/>
        </el-col>
      </el-row>
    </div>
    <el-dialog v-dialogDrag :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
      <el-form
        v-if="dialogStatus === 'add' || dialogStatus === 'edit'"
        ref="temp"
        :model="temp"
        :rules="rules"
        label-position="center"
        :validate-on-rule-change="false"
        :label-width="formLabelWidth"
      >
        <el-form-item
          label="手册类型"
          prop="manualType"
        >
          <el-select v-model="temp.manualType" @change="onChangeManualType" clearable filterable>
            <el-option
              v-for="(item, index) in manualTypeList"
              :key="index"
              :value="item.code"
              :label="item.name"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="语种" prop="language">
          <el-select v-model="temp.language" clearable filterable>
            <el-option v-for="(item, index) of languageList" :key="index" :label="item.name" :value="item.code"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="发布名称" prop="releaseName">
          <el-input v-model.trim="temp.releaseName" placeholder="请输入发布名称"  show-word-limit maxlength="50"></el-input>
        </el-form-item>
        <el-form-item
          label="解析规则"
          prop="parseId"
        >
          <el-select v-model="temp.parseId" @focus="getInvestInfo()" clearable filterable>
            <el-option
              v-for="(item, index) in manualParseList"
              :key="index"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="输出类型" prop="outputType">
          <el-select v-model="temp.outputType" clearable @change="onChangeOutputType">
            <el-option label="html" value="html"></el-option>
            <el-option label="pdf" value="pdf"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="前提条件"
          prop="preconditiongArr"
        >
          <el-checkbox-group v-model="preconditiongArr">
            <el-checkbox v-for="(item, index) in preconditionList" :key="index" :label="item.id">
              {{ item.releaseName }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <div class="submitArea">
          <el-button type="primary" @click="dialogStatus === 'edit' ? editClick('temp') : addClick('temp')">
            立即提交
          </el-button>
          <el-button @click="resetForm()"> 重置 </el-button>
        </div>
      </el-form>
      <el-form v-if="dialogStatus == 'attr'" :label-width="formLabelWidth" ref='trainTemp' :model="trainTemp">
        <el-form-item label="主机厂" prop="brand">
          <el-input v-model="trainTemp.brand" readonly></el-input>
        </el-form-item>
        <el-form-item label="项目名称" prop="nameCh">
          <el-input v-model="trainTemp.nameCh" readonly></el-input>
        </el-form-item>
        <el-form-item label="项目状态" prop="projectStatus">
          <el-input v-if="trainTemp.projectStatus == '1'" value="进行中" readonly></el-input>
          <el-input v-if="trainTemp.projectStatus == '2'" value="暂停中" readonly></el-input>
          <el-input v-if="trainTemp.projectStatus == '3'" value="已验收" readonly></el-input>
        </el-form-item>
        <el-form-item label="车型" prop="trainCode">
          <el-input v-model="trainTemp.trainCode" readonly></el-input>
        </el-form-item>
        <el-form-item label="年款" prop="trainYear">
          <el-input v-model="trainTemp.trainYear" readonly></el-input>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import { contentSize, tabPath, handleAlert, expandTree } from "@/assets/js/common.js";
import { catalogueList, manualType } from "@/api/cmsmgt.js";
import {
  configData,
  configParse,
  configCondition,
  configAdd,
  configEdit,
  configDel,
  releaseData,
} from "@/api/releasemgt.js";
import Pagination from '@/components/Pagination'
export default {
  name: "releasemgtpublishSetlist",
  components: { Pagination },
  data() {
    return {
      defaultProps: {
        children: "children",
        label: "nameCh",
      },
      trainTemp: {
        id: "",
        frimId: "",
        brand: "",
        nameCh: "",
        projectStatus: "",
        trainCode: "",
        trainYear: "",
      },
      parjectId:"",
      preconditiongArr: [],
      bizTypeVal:"",
      temp: {
        id: "",
        bizId: "",
        bizType: "",
        manualType: "",
        releaseName: "",
        language:'chinese',
        outputType: "",
        manualTypeName: "",
        parseId: "",
        parseCode: "",
        parsePrecondition: "",
        status: "",
        issueResult: "",
        remark: "",
      },
      dialogFormVisible: false,
      dialogStatus: "",
      textMap: {
        edit: "编辑发布设置",
        add: "新增发布设置",
        attr: "项目属性",
      },
      resultList: [],
      listdata: [],
      languageList:[],
      manualParseList: [],
      preconditionList: [],
      treeExpandIdList: [],
      manualTypeList: [],
      pagesize: 15,
      currentPage: 1,
      total: 0,
      formLabelWidth: "100px",
      rules: {
        manualType: [{ required: true, message: "手册类型不能为空", tigger: ['blur', 'change'] },],
        language: [{ required: true, message: '语种不能为空', trigger: ['blur', 'change'] }],
        releaseName: [{ required: true, message: "发布名称不能为空", trigger: ['blur', 'change'] },],
        parseId: [{ required: true, message: "解析规则不能为空", trigger: ['blur', 'change'] },],
        outputType: [{ required: true, message: "输出类型不能为空", trigger: ['blur', 'change'] },],
      },
    };
  },
  methods: {
    // 数据
    mouseenter(data) {
      data.isCurrent = true;
    },
    mouseleave(data) {
      data.isCurrent = false;
    },
    attributeClick(data) {
      event.stopPropagation()
      this.trainDetail(data)
      this.dialogFormVisible = true;
      this.dialogStatus = "attr";
      this.$nextTick(function() {
        this.$refs.trainTemp.clearValidate();
      })
    },
    trainDetail(data){
      this.trainTemp.id = data.id
      this.trainTemp.frimId= data.frimId
      this.trainTemp.brand= data.brand
      this.trainTemp.nameCh= data.nameCh
      this.trainTemp.projectStatus= data.projectStatus
      this.trainTemp.trainCode= data.trainCode
      this.trainTemp.trainYear= data.trainYear
    },
    // 获取语种列表
    getlanguageType(){
      this.languageList = JSON.parse(sessionStorage.getItem('language'))
    },
    // 目录
    dataList() {
      catalogueList().then((res) => {
        this.listdata = res.data.data;
        this.expandStatus(this.listdata)
      });
    },
    expandStatus(data){
      var nodeExpand = expandTree(data)
      this.treeExpandIdList.push(nodeExpand.id)
      this.handleNodeClick(nodeExpand)
      this.$nextTick(() => {
        this.$refs.tree.setCurrentKey(nodeExpand.id);
      });
    },
    pagingEvent(){
      this.manualDetail(this.parjectId)
    },
    // 右侧内容
    manualDetail(id) {
      this.parjectId = id;
      var params = {
        bizId: id,
        page: this.currentPage, // 当前页
        limit: this.pagesize, // 每页显示的条数
      };
      configData(params).then((res) => {
        this.total = res.data.total;
        this.resultList = res.data.data;
      });
    },
    handleNodeClick(data) {
      this.manualDetail(data.id);
      this.parjectId = data.id
      $(".manualManage .rightTitle").show()
      if (data.firmId) {
        this.bizTypeVal = "project";
      } else {
        this.bizTypeVal = "firm";
      }
    },
    onChangeManualType(value) {
      this.temp.parseCode = ""
      this.temp.parseId=""
      if (value != null && value != "") {
        this.getManualParseList(value);
      }
    },
    getInvestInfo(){
      if(this.temp.manualType == ''){
        handleAlert('warning',"手册类型不能为空")
      }
    },
    onChangeOutputType(){
      this.preconditionList = [];
      if (this.temp.outputType != null && this.temp.outputType != "") {
        this.getPreconditionList(this.temp);
      }
    },
    // 获取手册分类
    getManualList() {
      manualType().then((res) => {
        if (res !== null && res.data.code === 100) {
          this.manualTypeList = res.data.data;
        }
      });
    },
    resetTemp() {
      sessionStorage.removeItem("codeVal")
      this.temp={
        bizId:"",
        bizType:"",
        manualType: "",
        releaseName: "",
        language:'chinese',
        parseCode:"",
        outputType: "",
        parsePrecondition :"",
        parseId:"",
      }
      this.preconditiongArr= [];
      this.$nextTick(function() {
        this.$refs.temp.clearValidate();
      })
    },
    // 新增
    handelAdd() {
      this.dialogFormVisible = true;
      this.resetTemp();
      this.dialogStatus = "add";
      this.manualParseList = [];
      this.preconditionList = [];
    },
    addClick(temp) {
      this.$refs[temp].validate((valid) => {
        if (valid) {
          var params = new URLSearchParams();
          params.append("bizId", this.parjectId);
          params.append("bizType", this.bizTypeVal);
          params.append("manualType", this.temp.manualType);
          params.append("language", this.temp.language);
          params.append("releaseName", this.temp.releaseName);
          let curPrecondition = "";
          if (this.preconditiongArr != null &&this.preconditiongArr.length > 0) {
            curPrecondition = this.preconditiongArr.join();
          }
          params.append("parsePrecondition", curPrecondition);
          params.append("parseId", this.temp.parseId)
          params.append("outputType", this.temp.outputType);
          configAdd(params).then((res) => {
            if (res.data.code === 100) {
              handleAlert('success',res.data.msg)
              this.manualDetail(this.parjectId)
              this.dialogFormVisible = false;
            } else {
              handleAlert('error',res.data.msg)
            }
          }).catch((err) => {
            if (err !== null && err !== "" && err.responseText !== null) {
              handleAlert('error',"提交失败,请重试")
            }
          });
        } else {
          handleAlert('error',"请完善信息")
        }
      });
    },
    resetForm() {
      this.resetTemp();
    },
    // 编辑
    headelEdit(row) {
      this.manualParseList = [];
      this.preconditionList = [];
      this.getManualParseList(row.manualType);
      this.getPreconditionList(row);
      setTimeout(() => {
        this.dialogFormVisible = true;
        this.dialogStatus = 'edit';
        this.resetTemp();
        this.temp = Object.assign({}, row);
        this.temp.parseCode = ""
        sessionStorage.setItem("codeVal", row.parseCode)
      })
      this.preconditiongArr =[]
      if (row.parsePrecondition != null && row.parsePrecondition != "") {
        let curArr = row.parsePrecondition.split(",");
        if(curArr != null && curArr.length>0){
          for (let i = 0; i < curArr.length; i++) {
            this.preconditiongArr.push(parseInt(curArr[i]));
          }
        }
      }
    },
    editClick(temp) {
      this.$refs[temp].validate((valid) => {
        if (valid) {
          var params = new URLSearchParams();
          params.append("id", this.temp.id);
          params.append("bizId", this.parjectId);
          params.append("bizType",  this.bizTypeVal);
          params.append("manualType", this.temp.manualType);
          params.append("language", this.temp.language);
          params.append("releaseName", this.temp.releaseName);
          let curPrecondition = "";
          if (this.preconditiongArr != null && this.preconditiongArr.length > 0) {
            curPrecondition = this.preconditiongArr.join();
          }
          params.append("parsePrecondition", curPrecondition);
          params.append("parseId", this.temp.parseId)
          // params.append("parseCode", this.temp.parseCode);
          params.append("outputType", this.temp.outputType);
          configEdit(params).then((res) => {
            if (res.data.code === 100) {
              handleAlert('success',res.data.msg)
              this.manualDetail(this.parjectId)
              this.dialogFormVisible = false;
            } else {
              handleAlert('error',res.data.msg)
            }
          }).catch((err) => {
            if (err !== null && err !== "" && err.responseText !== null) {
              handleAlert('error',"提交失败,请重试")
            }
          });
        } else {
          handleAlert('error',"请完善信息")
        }
      });
      sessionStorage.removeItem("codeVal")
    },
    // 删除
    headelDel(row) {
      this.currentPage = 1
      var params = {
        page: this.currentPage,
        limit: this.pagesize,
        basicId:this.projectId,
        releaseName: row.releaseName
      }
      releaseData(params).then(res => {
        if(res.data.code == '100'){
          if(res.data.data.length == '0'){
            this.$confirm("确定删除【"+row.manualTypeName+"】的相关信息?", "删除发布配置", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            }).then((res) => {
              configDel(row.id).then((res) => {
                if (res.data.code === 100) {
                  handleAlert('success',"删除成功")
                  if(this.resultList!=null&&this.resultList.length==1){
                    this.currentPage =this.currentPage-1
                  }
                  this.manualDetail(this.parjectId)
                }else{
                  handleAlert('error',"删除失败")
                }
              })
            }).catch((error) => {
              handleAlert('info','取消删除')
            });
          }else{
            handleAlert('error','发布设置已使用不可删除')
          }
        }
      })
    },
    // 手册解析规则列表
    getManualParseList(manualType) {
      var params = {
        userFlag: 1,
        manualType: manualType,
      };
      configParse(params).then((res) => {
        if (res.data.code === 100) {
          this.manualParseList = res.data.data
          if(sessionStorage.getItem("codeVal") !== null){
            this.temp.parseCode = sessionStorage.getItem("codeVal")
          }
        }else{
          this.manualParseList = []
        }
      });
    },
    //获取前提条件列表
    getPreconditionList(row) {
      this.preconditionList=[]
      var params = {
        bizId: this.parjectId,
        outputType: row.outputType,
        bizType: this.bizTypeVal,
        manualType: row.manualType
      };
      configCondition(params).then((res) => {
        if (res.data.code === 100) {
          var configList = res.data.data;
          if(configList != null) {
            for (let i = 0; i < configList.length; i++) {
              let curManualType = configList[i].manualType;
              if (
                curManualType != manualType &&
                curManualType != null &&
                curManualType != ""
              ) {
                this.preconditionList.push(configList[i]);
              }
            }
          }
        }
      });
    },
  },
  mounted() {
    this.dataList();
    this.getManualList();
    this.getlanguageType()
    contentSize();
  },
};
</script>
<style>
.infoDetail .manualManage .rightTitle {
  display: none
}
</style>
