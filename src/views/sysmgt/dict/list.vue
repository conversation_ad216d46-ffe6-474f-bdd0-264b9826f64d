<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" ref="formInline" label-width="90px" :model="formInline" class="demo-form-inline">
        <el-form-item label="类型" prop="type">
          <el-select v-model="formInline.type" clearable filterable>
            <el-option v-for="(item, index) of typeList" :key="index" :label="item" :value="item"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="名称(或编码)" prop="name">
          <el-input v-model.trim="formInline.name" placeholder="请输入名称(或编码)"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit" icon="el-icon-search">查询</el-button>
          <el-button plain @click="reset('formInline')">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <div class="tableHandle" v-if="hasPerm('menuAsimss1A1B_101')">
        <el-button type="text" icon="el-icon-plus" @click="addData()">新增</el-button>
      </div>
      <el-table
        style="width:100%"
        border
        stripe
        ref="table"
        highlight-current-row
        :max-height="maximumHeight"
        :header-cell-style="{
          'text-align': 'center',
          'background-color': 'var(--other-color)',
        }"
        :data="resultList"
        @header-dragend="changeColWidth"
      >
        <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="类型" prop="classify" min-width="150" maxlength="50">
          <template slot-scope="{row}">
            <span>{{row.classify}}</span>
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip label="编码" prop="code" min-width="150" maxlength="50">
          <template slot-scope="{row}">
            <span>{{row.code}}</span>
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip label="名称" prop="name" min-width="150">
          <template slot-scope="{row}">
            <span>{{row.name}}</span>
          </template>
        </el-table-column>
        <el-table-column label="排序" prop="sort" width="100" align="center">
          <template slot-scope="{row}">
            <span>{{row.sort}}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" class="state" prop="useFlag"  width="100" align="center">
          <template slot-scope="{row}">
            <span v-if="row.useFlag === '1'" style="color:#009933">生效</span>
            <span v-if="row.useFlag === '0'" style="color:#c30000">失效</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right"  width="200">
          <template slot-scope="{row}">
            <el-button v-if="hasPerm('menuAsimss1A1B_103')" @click="handleEdit(row)" type="text" size="small">编辑</el-button>
            <el-button v-if="row.useFlag === '0' && hasPerm('menuAsimss1A1B_102')" @click="handleDelete(row, row.id)" type="text" size="small">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total>0" :total="total" :page.sync="currentPage" :limit.sync="pagesize" @pagination="dataList"/>
      <el-dialog v-dialogDrag :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" :close-on-click-modal="false">
        <el-form :model="temp" ref="temp" :label-width="formLabelWidth" label-position="center" :rules="rules">
          <el-form-item label="类型" prop="classify">
            <el-input v-show="classifyStatus === 'add'" v-model.trim="temp.classify" style="width: 62% !important;" placeholder="请输入类型[只能输入字母]" oninput="value=value.replace(/[^A-Za-z]+/g,'')"  show-word-limit maxlength="40"></el-input>
            <el-select  v-show="dialogStatus === 'addpage' && classifyStatus !== 'add'" clearable filterable ref="select" @focus="focus" @visible-change="visibleChange" v-model="temp.classify" style="width:60%">
              <el-option v-for="(item, index) of typeList"  :key="index" :label="item" :value="item" ></el-option>
            </el-select>
            <el-input v-show="dialogStatus === 'update' && classifyStatus !== 'add'" :disabled="true" filterable  v-model="temp.classify" oninput="value=value.replace(/[^[A-Za-z]+$]/g,'')"></el-input>
            <el-button  v-show="dialogStatus === 'addpage' && classifyStatus !== 'add'" style="padding: 9px 15px;margin: 0 10px !important" type="primary" @click="addClassify">
              添加类型
            </el-button>
            <el-button  v-show="classifyStatus === 'add'" style="padding: 9px 15px;;margin: 0 10px 0 50px !important" type="primary" @click="changeClassify">
              选择类型
            </el-button>
          </el-form-item>
          <el-form-item label="编码" prop="code">
            <el-input v-model.trim="temp.code" placeholder="请输入编码[只能输入字母、数字、下划线、短横线]" oninput="value=value.replace(/[^\w+-$]/g,'')"  show-word-limit maxlength="30"></el-input>
          </el-form-item>
          <el-form-item label="名称" prop="name">
            <el-input v-model.trim="temp.name" placeholder="请输入名称" show-word-limit maxlength="45"></el-input>
          </el-form-item>
          <el-form-item label="排序" prop="sort">
            <el-input type="number" v-model.trim="temp.sort" :min="1" :max="9999"  @input="e => temp.sort=parserNumber(e,1,9999)"></el-input>
          </el-form-item>
          <el-form-item label="生效状态" prop="useFlag">
            <el-switch v-model="whether"></el-switch>
          </el-form-item>
          <div class="submitArea">
            <el-button type="primary" @click="dialogStatus === 'addpage' ? addClick('temp') : updateData('temp')">
              立即提交
            </el-button>
            <el-button @click="resetForm('temp')">
              重置
            </el-button>
          </div>
        </el-form>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import { dictData, dictTypeList, dictAdd, dictEdit, dictDel } from '@/api/sysmgt.js'
import { handleAlert, tableHeight, tableMaxHeight } from '@/assets/js/common.js'
export default {
  name: 'sysmgt_dict_list',
  components: { Pagination },
  data () {
    return {
      formInline: {
        name: '',
        type: ''
      },
      dialogFormVisible: false,
      dialogStatus: '',
      classifyStatus: '',
      textMap: {
        update: '编辑字典',
        addpage: '新增字典'
      },
      typeList: [],
      temp: {
        id: '',
        classify: '',
        code: '',
        name: '',
        sort: 1,
        useFlag: ''
      },
      resultList: [],
      pagesize: 20,
      currentPage: 1,
      total: 0,
      whether: false,
      inputFlag: null,
      formLabelWidth: '100px',
      rules: {
        classify: [{ required: true, message: '请输入类型', trigger: ['blur', 'change'] }],
        code: [{ required: true, message: '请输入编码', trigger: ['blur', 'change'] }],
        name: [{ required: true, message: '请输入名称', trigger: ['blur', 'change'] }],
        sort: [{ required: true, message: '请输入排序', trigger: ['blur', 'change'] }]
      },
      maximumHeight: 0,
    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth(){
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    // 数据
    dataList () {
      this.$loading.show();
      var params = new URLSearchParams()
      params.append('page', this.currentPage)
      params.append('limit', this.pagesize)
      params.append('name', this.formInline.name)
      params.append('classify', this.formInline.type)
      dictData(params).then(res=>{
        if (res.data.code == 100) {
          this.total = res.data.total
          this.resultList = res.data.data
        } else {
          handleAlert('error',res.data.msg)
        }
        this.$loading.hide();
        this.tableHeightArea();
      })
    },
    // 类型数据
    getDictList () {
      var _this = this
      dictTypeList().then(res => {
        _this.typeList = res.data.data
      })
    },
    refreshData () {
      this.dataList()
      this.getDictList()
    },
    focus(){
      if(this.inputFlag){
        this.$refs.select.blur()
      }
    },
    visibleChange(flag){
      setTimeout(() =>{
        this.inputFlag=flag
      },0)
    },
    // 搜索
    keyDown(e) {
      if (e.keyCode === 13) {
        this.onSubmit()
      }
    },
    onSubmit () {
      this.currentPage=1
      this.dataList()
    },
    // 搜索重置
    reset (formInline) {
      if (this.$refs[formInline].resetFields() !== undefined) {
        this.$refs[formInline].resetFields()
      }
      this.currentPage = 1
      this.refreshData()
    },
    resetTemp () {
      this.temp = {
        classify: '',
        code: '',
        name: '',
        sort: 1,
        useFlag: 1
      }
      this.whetherStates()
      this.$nextTick(function() {
        this.$refs.temp.clearValidate();
      })
    },
    whetherStates () {
      if (this.temp.useFlag === 0 ) {
        this.whether = false
      } else {
        this.whether = true
      }
    },
    useFlagState() {
      if (this.whether === false) {
        this.temp.useFlag = 0
      } else {
        this.temp.useFlag = 1
      }
    },
    // 增加
    addData () {
      this.resetTemp()
      this.dialogStatus = 'addpage'
      this.classifyStatus=''
      this.dialogFormVisible = true
    },
    // 增加分类
    addClassify () {
      this.classifyStatus = 'add'
      this.temp.classify=""
    },
    changeClassify() {
      this.classifyStatus=''
      this.temp.classify=""
    },
    addClick (temp) {
      this.$refs[temp].validate((valid) => {
        if (valid) {
          this.useFlagState()
          this.$loading.show();
          var params = new URLSearchParams()
          params.append('classify', this.temp.classify)
          params.append('code', this.temp.code)
          params.append('name', this.temp.name)
          params.append('useFlag', Number(this.temp.useFlag))
          params.append('sort', this.temp.sort)
          // var params = {
          //   'classify': this.temp.classify,
          //   'code': this.temp.code,
          //   'name': this.temp.name,
          //   'useFlag': Number(this.temp.useFlag),
          //   'sort': this.temp.sort
          // }
          dictAdd(params).then(res=>{
            if (res.data.code === 100) {
              handleAlert('success','保存成功')
              this.refreshData()
              this.dialogFormVisible = false
            } else {
              handleAlert('error',res.data.msg)
            }
            this.$loading.hide();
          }).catch(err => {
            this.$loading.hide();
            if(err !== null && err !=='' && err.responseText !== null){
              handleAlert('error','提交失败,请重试')
            }
          })
        }else{
          handleAlert('error','请完善信息')
          this.$loading.hide();
        }
      })
    },
    // 编辑
    handleEdit (row) {
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.resetTemp()
      this.temp = Object.assign({}, row)
      this.classifyStatus=''
      this.whetherStates()
      this.whether = row.useFlag == 1
    },
    updateData (temp) {
      this.$refs[temp].validate((valid) => {
        if (valid) {
          this.useFlagState()
          this.$loading.show();
          var params = new URLSearchParams()
          params.append('id', this.temp.id)
          params.append('classify', this.temp.classify)
          params.append('code', this.temp.code)
          params.append('name', this.temp.name)
          params.append('useFlag', Number(this.temp.useFlag))
          params.append('sort', this.temp.sort)
          // var params = {
          //   id: this.temp.id,
          //   classify: this.temp.classify,
          //   code: this.temp.code,
          //   name: this.temp.name,
          //   sort: this.temp.sort,
          //   useFlag: this.temp.useFlag
          // }
          dictEdit(params).then(res=>{
            if(res.data.code === 100){
              handleAlert('success','保存成功')
              this.refreshData()
              this.dialogFormVisible = false
            }else{
              handleAlert('error',res.data.msg)
            }
            this.$loading.hide();
          })
        } else {
          handleAlert('error','请完善信息')
          this.$loading.hide();
        }
      })
    },
    // 重置
    resetForm (temp) {
      if(this.dialogStatus=='addpage'){
        this.resetTemp()
      }else{
        this.temp.code=''
        this.temp.name=''
        this.temp.sort=1
        this.useFlag=1
        this.whetherStates()
        this.$nextTick(function() {
          this.$refs.temp.clearValidate();
        })
      }
    },
    // 删除
    handleDelete (row, id) {
      this.$confirm('确定删除【' + row.name + '】的相关信息?', '删除数据', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        dictDel(id).then(res =>{
          if (res.data.code === 100) {
            handleAlert('success','删除成功')
            if(this.resultList!=null&&this.resultList.length==1){
              this.currentPage =this.currentPage-1
            }
            this.refreshData()
          }else{
            handleAlert('error','删除失败')
          }
        })
      }).catch((error) => {
        handleAlert('info','取消删除')
      })
    },
    maximumArea() {
      setTimeout(() => {
        tableHeight()
        setTimeout(() => {
          this.maximumHeight = tableMaxHeight;
        }, 50)
      })
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    }
  },
  mounted () {
    var _this = this
    _this.tableHeightArea()
    _this.dataList()
    _this.getDictList()
    window.addEventListener('keydown', _this.keyDown)
  },
  destroyed() {
    window.removeEventListener('keydown', this.keyDown, false)
  },
}
</script>

