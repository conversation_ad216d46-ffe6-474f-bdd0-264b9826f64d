<template>
  <div class="layoutContainer addNoticeContent">
    <el-form :model="temp" ref="temp" :label-width="formLabelWidth" label-position="center" :rules="rules" >
      <el-row>
        <el-col :span="20">
          <el-form-item label="标题" prop="title">
            <el-input v-model.trim="temp.title" placeholder="请输入公告标题" show-word-limit maxlength="100"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :xs="20" :sm="15" :md="10" :lg="6" :xl="5">
          <el-form-item label="状态" prop="status">
            <el-select v-model="temp.status" clearable filterable>
              <el-option v-for="(item, index) of statusList" :key="index" :label="item.name" :value="item.code"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xs="20" :sm="15" :md="10" :lg="6" :xl="5">
          <el-form-item label="类型" prop="tpye">
            <el-select v-model="temp.type" clearable filterable>
              <el-option v-for="(item, index) of typeList" :key="index" :label="item.name" :value="item.code"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xs="20" :sm="15" :md="10" :lg="6" :xl="5">
          <el-form-item label="是否置顶" prop="isTop">
            <el-select v-model="temp.isTop" clearable filterable>
              <el-option v-for="(item, index) of topRecmd" :key="index" :label="item.name" :value="item.code"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="20">
          <el-form-item label="适用国家" prop="country">
            <template slot="label">
              <span>适用国家
                <el-tooltip class="item" effect="dark" placement="right">
                  <i class="el-icon-question" style="font-size: 16px; vertical-align: middle;"></i>
                  <div slot="content">
                    <p>不选择视为适用于所有国家</p>
                  </div>
                </el-tooltip>
              </span>
            </template>
            <el-select v-model="temp.country" multiple clearable filterable @change="getChangeTarget">
              <el-option v-for="(item, index) of userCountryList" :key="index" :label="item.name" :value="item.code">
                <span class="checkbox"></span>
                <span class="label-name-box" style="margin-left: 8px;">{{ item.name }}</span>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="是否推荐" prop="isRecmd">
            <el-select v-model="temp.isRecmd" clearable filterable>
              <el-option v-for="(item, index) of topRecmd" :key="index" :label="item.name" :value="item.code"></el-option>
            </el-select>
          </el-form-item>
        </el-col> -->
      </el-row>
      <el-row>
        <el-col :span="20">
          <el-form-item label="适用对象" prop="target">
            <template slot="label">
              <span>适用对象
                <el-tooltip class="item" effect="dark" placement="right">
                  <i class="el-icon-question" style="font-size: 16px; vertical-align: middle;"></i>
                  <div slot="content">
                    <p>不选择视为适用于国家下的所有服务店</p>
                  </div>
                </el-tooltip>
              </span>
            </template>
            <el-button type="primary" @click="suitTarget()">{{ targetName }}</el-button>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="20">
          <el-form-item label="内容" prop="content">
            <TinymceEditor ref="noticeEditor" :imgUploadUrl="uploadUrl" v-model="temp.content"/>
          </el-form-item>
        </el-col>
      </el-row>
      <div class="submitArea">
        <el-button type="primary" @click="addClick('temp')">保存</el-button>
        <el-button v-if="this.noticeId == 'add'" @click="resetForm('temp')">重置</el-button>
      </div>
    </el-form>

    <el-dialog v-dialogDrag lock-scroll title="适用对象" :visible.sync="dialogeditTargetVisible" v-if="dialogeditTargetVisible" :close-on-click-modal="false">
        <div style="height: 500px; width: 100%; overflow-y:auto;">
          <el-input placeholder="输入关键字进行过滤" v-model="filterText"></el-input>
          <el-tree
            :data="targetList"
            show-checkbox
            node-key="onlyId"
            ref="targetTree"
            :default-expand-all="false"
            :check-on-click-node="false"
            :default-checked-keys="checkedTarget"
            :filter-node-method="filterNode"
            :props="defaultTargetProps">
          </el-tree>
        </div>
        <div style="width: 100%; display: flex;  justify-content: center;  align-items: center;">
          <el-button type="primary" @click="getCheckedKeys()">确定</el-button>
          <el-button @click="dialogeditTargetVisible=false">取消</el-button>
        </div>
      </el-dialog>

  </div>
</template>
<script>
import { sysServerUrl, cmsServerUrl, addTabs, removeTabs, handleAlert } from '@/assets/js/common.js'
import TinymceEditor from  "@/components/TinymceEditor/TinymceEditor.vue"
import { noticeTypeList, noticeAdd, noticeEdit, userCountryData, noticeInfo, noticTarget, noticEmpower } from '@/api/sysmgt.js'
export default {
  name: 'addNotice',
  components: { TinymceEditor },
  data () {
    return {
      init: {
        // 组件
        plugins: ' lists image colorpicker textcolor wordcount contextmenu autoresize',
        // 工具栏
        toolbar: 'bold italic underline strikethrough | fontsizeselect | forecolor backcolor | alignleft aligncenter alignright alignjustify | bullist numlist | outdent indent  | undo redo | link unlink image  | removeformat ',
        branding: false,
        max_width: 800,
        min_height: 300
      },
      temp: {
        id: '',
        title: '',
        content: '',
        status: 1,
        type: 'notice',
        isTop: 0,
        isRecmd: 0,
        country: ['cn']
      },
      userCountryList: [],
      statusList: [
        { name: '草稿', code: 1 },
        { name: '发布', code: 2 },
        // { name: '关闭', code: 3 }
      ],
      topRecmd: [
        {name: '是', code: 1},
        {name: '否', code: 0},
      ],
      formLabelWidth: '100px',
      uploadUrl: '', // 文件上传地址
      rules: {
        title: [{ required: true, message: '公告标题不能为空', tigger: ['blur', 'change'] }]
      },
      typeList: [],
      noticeId: "",
      targetName: '未选择',

      filterText: '',
      // 可选的
      targetList: [],
      // 已选的
      checkedKeys: [],
      checkedTarget: [],
      dialogeditTargetVisible: false,
      defaultTargetProps:{
        children: 'children',
        label: 'name'
      }

    }
  },
  methods: {
    // 公告类型
    getTypeList () {
      noticeTypeList().then(res => {
        if (res !== null && res.data.code === 100) {
          this.typeList = res.data.data
        }
      })
    },
    // 获取国家
    getUserCountryList(){
      userCountryData().then(res=>{
        this.userCountryList = res.data.data
      })
    },
    addClick (temp) {
      this.$refs[temp].validate((valid) => {
        if (valid) {
          let activeEditor = tinymce.activeEditor;
          let editBody = activeEditor.getBody();
          activeEditor.selection.select(editBody);
          let text = activeEditor.selection.getContent( { 'format' : 'text' } );
          if (text.trim() == '' && (this.temp.content).indexOf("img") ==-1) {
            handleAlert('error','系统公告内容为空')
            return false;
          }
          this.$loading.show();
          var params = new URLSearchParams()
          params.append('title', this.temp.title)
          params.append('content', this.temp.content)
          params.append('status', this.temp.status)
          if (this.temp.country && this.temp.country.length>0) {
            params.append('sltCountry', this.temp.country.toString())
          }
          if (this.checkedTarget && this.checkedTarget.length>0) {
            params.append('targetStr', this.checkedTarget.toString())
          }
          params.append('type', this.temp.type)
          params.append('isTop', this.temp.isTop)
          params.append('isRecmd', this.temp.isRecmd)
          if (this.noticeId == "add") {
            noticeAdd(params).then(res => {
              if (res.data.code === 100) {
                handleAlert('success',res.data.msg)
                removeTabs(this.$route);
                this.$router.push('/sysmgt/notice/list');
                setTimeout(() => {
                  addTabs(this.$route.path, "系统公告");
                })
              } else {
                handleAlert('error',res.data.msg)
              }
              this.$loading.hide();
            }).catch(err => {
              this.$loading.hide();
              if (err !== null && err !== '' && err.responseText !== null) {
                handleAlert('error','提交失败,请重试')
              }
            })
          }else{
            params.append('id', this.temp.id)
            noticeEdit(params).then(res => {
              if (res.data.code === 100) {
                handleAlert('success',res.data.msg)
                removeTabs(this.$route);
                this.$router.push('/sysmgt/notice/list')
                setTimeout(() => {
                  addTabs(this.$route.path, "系统公告");
                })
              } else {
                handleAlert('error',res.data.msg)
              }
              this.$loading.hide();
            }).catch(err => {
              this.$loading.hide();
              if (err !== null && err !== '' && err.responseText !== null) {
                handleAlert('error','提交失败,请重试')
              }
            })
          }
          if(this.temp.status == "2") {
            window.noticeUnread();
          }
        } else {
          handleAlert('warning','请完善信息')
          this.$loading.hide();
        }
      })
    },
    getNoticeInfo () {
      noticeInfo(this.noticeId).then(res=>{
        if(res.data.code == 100){
          var data = res.data.data;
          this.temp.id = data.id
          this.temp.title = data.title
          this.temp.content = data.content
          this.temp.status = data.status
          this.temp.type = data.type
          this.temp.isTop = data.isTop
          this.temp.isRecmd = data.isRecmd
          this.temp.country = data.countryList
          this.$refs.noticeEditor.setValue(data.content)
        }else{
          handleAlert('error', '内容已删除')
          removeTabs(this.$route);
          this.$router.push('/sysmgt/notice/list')
          setTimeout(() => {
            addTabs(this.$route.path, "系统公告");
          })
        }
      })
    },
    resetTemp(){
      this.temp= {
        id: '',
        title: '',
        content: '',
        status: 1,
        type: 'notice',
        isTop: 0,
        isRecmd: 0,
        country: ['cn']
      },
      this.checkedTarget = []
      this.targetName = '未选择'
      this.$nextTick(function() {
        this.$refs.temp.clearValidate();
        this.$refs.noticeEditor.setValue("")
      })
    },
    // 重置
    resetForm (temp) {
      this.resetTemp()
    },

    // 适用对象
    suitTarget(){
      this.filterText = ''
      this.dialogeditTargetVisible = true
      this.getNoticTarget()
    },
    getChangeTarget(){
      this.targetName = '未选择'
      this.checkedTarget = []
      this.getNoticTarget()
    },
    filterNode(value, data, node) {
      if (!value) return true;
      let names = this.getParents(node,node.data.name,'name');
      return names.indexOf(value) !== -1;
    },
    getParents(node,name,key){
        if(node.parent && node.parent.data[key]){
            name+= node.parent.data[key]
            return this.getParents(node.parent,name,key)
        }
        return name
    },

    getCheckedKeys() {
      let list = this.$refs.targetTree.getCheckedKeys()
      this.dialogeditTargetVisible = false
      if(list && list.length > 0){
        this.targetName = '已选择'
      }else{
        this.targetName = '未选择'
      }
      let lst = []
      list.forEach(item => {
          if(item && item.endsWith("people")){
            lst.push(item)
          }
        })
      this.checkedTarget = lst

    },
    getNoticTarget(){
      var params = new URLSearchParams()
      params.append('countryStr', this.temp.country.toString())
      noticTarget(params).then(res => {
        this.targetList = res.data.data
      })
    },
    getEmpowerUser(){
      noticEmpower(this.noticeId).then(res => {
        this.checkedKeys = res.data.data
        let list = []
        this.checkedKeys.forEach(item => {
          list.push(item + "people")
        })
        this.checkedTarget = list
        if (this.checkedTarget && this.checkedTarget.length > 0) {
          this.targetName = '已选择'
        }else{
          this.targetName = '未选择'
        }
      })
    },
    initialization() {
      this.getTypeList()
      this.getUserCountryList()
      this.getNoticTarget()
      this.uploadUrl = sysServerUrl + 'sys/upload/attach?flag=sysNotice'
      this.noticeId = this.$route.params.id;
      if (this.noticeId != "add") {
        this.getNoticeInfo()
        this.getEmpowerUser()
      } else {
        this.resetTemp()
      }
    },
  },
  mounted () {
    this.initialization()
  },
  watch: {
    filterText(val) {
      this.$refs.targetTree.filter(val);
    },
    $route(to) {
      if (to.name == 'addNotice') {
        this.initialization()
      }
    }
  }
}
</script>
<style>
  .layoutContainer.addNoticeContent {
    overflow-y: auto;
  }
  .tox-pop--top {
    display: none;
    visibility: hidden;
    opacity: 0;
  }
  .addNoticeContent .el-select {
    width: 100%
  }
</style>
