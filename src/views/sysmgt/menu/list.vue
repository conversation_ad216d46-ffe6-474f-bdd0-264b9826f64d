<template>
  <div class="layoutContainer">
    <div class="tableDetail">
      <div class="tableHandle" v-if="hasPerm('menuAsimss1A4B_101')">
        <el-button type="text" @click="addRootMenu()" icon="el-icon-plus">新增一级菜单</el-button>

        <!-- 2023-12-08 权限按钮配置 -->
        <el-button type="text" @click="addPermission()" icon="el-icon-look_permission">权限按钮配置</el-button>
      </div>
      <el-table
        style="width:100%"
        border
        ref="table"
        highlight-current-row
        :data="resultList"
        :max-height="maximumHeight"
        default-expand-all
        row-key="id"
        :header-cell-style="{
          'text-align': 'center',
          'background-color': 'var(--other-color)',
        }"
        :tree-props="{children:'children',hasChildren:'hasChildren'}"
        :row-style="rowStyle"
        @header-dragend="changeColWidth"
      >
      <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
        <el-table-column label="菜单结构" prop="name" min-width="150" show-overflow-tooltip></el-table-column>
        <el-table-column label="编码" prop="code" min-width="130" show-overflow-tooltip></el-table-column>
        <el-table-column label="父级编码" prop="pcode" min-width="130" show-overflow-tooltip></el-table-column>
        <el-table-column label="地址" prop="url" min-width="150" show-overflow-tooltip></el-table-column>
        <el-table-column label="类型" prop="type" width="100">
          <template slot-scope="{row}">
            <span v-if="row.type == '1'">系统</span>
            <span v-if="row.type == '2'">主菜单</span>
            <span v-if="row.type == '3'">二级菜单</span>
          </template>
        </el-table-column>
        <el-table-column label="排序" prop="sort" width="100" align="center"></el-table-column>
        <el-table-column label="备注" prop="memo" min-width="150" show-overflow-tooltip></el-table-column>
        <el-table-column label="状态" prop="userFlag" width="100" align="center">
          <template slot-scope="{row}">
            <span v-if="row.useFlag === 1" style="color:#009933"> 生效</span>
            <span v-if="row.useFlag === 0" style="color:#c30000"> 失效</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="260" fixed="right" style="border:1px solid">
          <template slot-scope="{row}">
            <el-button type="text" v-if="hasPerm('menuAsimss1A4B_104')" size="small" @click="handelDetail(row)">查看
            </el-button>
            <el-button type="text" v-if="hasPerm('menuAsimss1A4B_103')" size="small" @click="handelEdit(row)">编辑
            </el-button>
            <el-button type="text" v-if="hasPerm('menuAsimss1A4B_102')" size="small" @click="handelDelete(row)">删除
            </el-button>
            <el-button type="text" size="small" @click="addSubMenu(row)"
                       v-if="row.type == '2' && hasPerm('menuAsimss1A4B_101')">添加子项
            </el-button>
            <el-button type="text" size="small" @click="assignButton(row)"
                       v-if="row.type == '3' && hasPerm('menuAsimss1A4B_106')">分配按钮
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-dialog v-dialogDrag :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" :before-close="handleClose" :close-on-click-modal="false">
        <!-- 新增 编辑 添加子项-->
        <el-form v-if="dialogStatus === 'add' || dialogStatus === 'edit' || dialogStatus === 'child'" :model="temp"
          :label-width="formLabelWidth"  ref="temp" :rules="rules" label-position="center">
          <el-form-item label="菜单名" prop="name">
            <el-input v-model.trim="temp.name" show-word-limit maxlength="20" placeholder="请输入菜单名"></el-input>
          </el-form-item>
          <el-form-item label="完整编码" prop="code">
            <el-input v-model.trim="temp.code" :disabled="true" show-word-limit maxlength="20" :placeholder="menuCode"></el-input>
          </el-form-item>
          <el-form-item label="编码后缀" prop="suffix">
            <el-input v-model.trim="temp.suffix" show-word-limit maxlength="10" :disabled="dialogStatus === 'edit'" placeholder="请输入编码后缀"></el-input>
          </el-form-item>
          <el-form-item label="URL地址" prop="url">
            <el-input v-model.trim="temp.url" show-word-limit maxlength="100" placeholder="请输入URL地址"></el-input>
          </el-form-item>
          <el-form-item label="图标：" prop="icon">
            <el-upload
              class="upload-demo"
              action="#"
              :http-request="uploadAttach"
              :on-success="handleOnSuccess"
              :on-remove="handleOnRemove"
              :before-remove="beforeOnRemove"
              :before-upload="beforeAvatarUpload"
              :on-exceed="handleOnExceed"
              multiple
              :limit="1"
              :file-list="imgList"
              accept=".JPG, .PNG, .JPEG,.jpg, .png, .jpeg"
              list-type="picture"
            >
              <el-button size="min" icon="el-icon-upload" type="primary">选择图片</el-button>
            </el-upload>
          </el-form-item>
          <el-form-item label="排序" prop="sort">
            <el-input type="number" :min="1" :max="9999" @input="e => temp.sort=parserNumber(e,1,9999)" v-model.trim="temp.sort"></el-input>
          </el-form-item>
          <el-form-item label="备注" prop="memo">
            <el-input v-model.trim="temp.memo" show-word-limit maxlength="50" placeholder="请输入备注"></el-input>
          </el-form-item>
          <el-form-item label="生效状态" prop="useFlag">
            <el-switch v-model="whether"></el-switch>
          </el-form-item>
          <div class="submitArea">
            <el-button type="primary" @click="dialogStatus === 'edit' ? updateSubmit('temp') : addSubmit('temp') ">
              立即提交
            </el-button>
            <el-button @click="dialogFormVisible = false">
              取消
            </el-button>
          </div>
        </el-form>
        <!-- 查看 -->
        <el-form v-if="dialogStatus === 'detail'" :label-width="formLabelWidth" :rules="rules" :model="temp" ref="temp" label-position="center">
          <el-form-item label="编码" prop="code">
            <el-input v-model="temp.code" readonly></el-input>
          </el-form-item>
          <el-form-item label="父级编码" prop="pcode">
            <el-input v-model="temp.pcode" readonly></el-input>
          </el-form-item>
          <el-form-item label="菜单名" prop="name">
            <el-input v-model="temp.name" readonly></el-input>
          </el-form-item>
          <el-form-item label="菜单地址" prop="url">
            <el-input v-model="temp.url" readonly></el-input>
          </el-form-item>
          <el-form-item label="类型" prop="type">
            <el-input v-if="temp.type === 1" readonly value="系统"></el-input>
            <el-input v-else-if="temp.type === 2" readonly value="一级菜单"></el-input>
            <el-input v-else value="二级菜单" readonly></el-input>
          </el-form-item>
          <el-form-item label="备注" prop="memo">
            <el-input v-model="temp.memo" readonly></el-input>
          </el-form-item>
          <el-form-item label="排序" prop="sort">
            <el-input type="number" v-model="temp.sort" readonly></el-input>
          </el-form-item>
          <el-form-item label="生效状态" prop="useFlag">
            <el-switch v-model="whether" disabled></el-switch>
          </el-form-item>
        </el-form>
        <!-- 分配按钮 -->
        <el-form v-if="dialogStatus === 'button'" prop="permissions" ref="alloter" :model="alloter">
          <el-checkbox-group v-model="alloter.permissionsCode">
            <el-checkbox border v-for="(item, index) in permissionList" :key="index" :label="item.code">
              {{ item.name }}
            </el-checkbox>
          </el-checkbox-group>
          <div class="submitArea">
            <el-button type="primary" @click="submitForm('alloter')">
              确定
            </el-button>
            <el-button @click="dialogFormVisible = false">
              取消
            </el-button>
          </div>
        </el-form>
      </el-dialog>


      <!-- 权限按钮操作 -->
      <el-dialog v-dialogDrag title="权限按钮配置" width="650px !important" :visible.sync="dialogPermissionVisible" :before-close="handleClose" :close-on-click-modal="false">
          <div style="width: 100%; max-height: 600px;" >
              <div class="tableHandle">
                <el-button type="text" icon="el-icon-plus" @click="addPermissionConfig">新增</el-button>
              </div>
              <el-table style="width:800px" class="permissionArea"  height="500px" border :data="permissionResulList"
                  :header-cell-style="{
                  'text-align': 'center',
                  'background-color': 'var(--other-color)',
                }"
                ref="tablePermission">
                <el-table-column label="名称" min-width="100" >
                  <template slot-scope="{row}">
                    <div>
                      <el-input v-model.trim="row.name" oninput="value=value.replace(/^\s+|\s+$/g,'')" show-word-limit maxlength="20"  placeholder="请输入按钮名称"></el-input>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="编码" min-width="100" >
                  <template slot-scope="{row}">
                    <div>
                      <el-input v-if="inputCode(row)" v-model.trim="row.code" oninput="value=value.replace(/^\s+|\s+$/g,'')"  show-word-limit maxlength="10" :value="row.code" placeholder="请输入按钮编码"></el-input>
                      <span v-else>{{ row.code }}</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="80" align="center">
                  <template slot-scope="{row}">
                    <el-button type="text" size="small" @click="handelSaveConfig(row)">保存</el-button>
                    <el-button type="text" size="small" @click="handelDelConfig(row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
          </div>
      </el-dialog>

    </div>
  </div>
</template>
<script>
import { sysServerUrl, handleAlert, tableHeight, tableMaxHeight } from '@/assets/js/common.js'
import {
  menuData,
  menuAssignList,
  menuAdd,
  menuEdit,
  menuCheck,
  menuDel,
  menuAssignInfo,
  menuAssignUpdate,
  importAttach,
  permissionList,
  operatePermission,
  delPermission,
} from '@/api/sysmgt.js'

export default {
  name: 'sysmgt_menu_list',
  data() {
    return {
      dialogImageUrl: '',
      dialogVisible: false,
      temp: {
        id: 0,
        name: '',
        code: '',
        pcode: '',
        suffix: '',
        url: '',
        icon: '',
        sort: 1,
        type: 2,
        memo: '',
        createUser: '',
        createTime: '',
        updateUser: '',
        updateTiem: '',
        useFlag: 1
      },
      menuCode: '',
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        edit: '编辑菜单',
        detail: '菜单明细',
        add: '新增菜单',
        child: '新增子项菜单',
        button: '分配按钮'
      },
      alloter: {
        menuCode: '',
        permissionsCode: []
      },
      isFlag: true,
      whether: false,
      formLabelWidth: '100px',
      resultList: [],
      permissionList: [],
      imgList: [],
      pagesize: 10,
      currentPage: 1,
      rules: {
        name: [{required: true, message: '菜单名不能为空', trigger: ['blur', 'change']}],
        suffix: [{required: true, message: '编码后缀不能为空', trigger: ['blur', 'change']}]
      },
      maximumHeight: 0,

      // ==== 权限按钮配置
      dialogPermissionVisible: false,
      permissionResulList: [],
      permission:{
        id: '',
        code: '',
        name: '',
      },
    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth(){
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    rowStyle({ row, rowIndex }) {
      let styleJson = {
        "background": "var(--cell-bgColor)",
      }
      if (row.pcode != "menuAsimss") {
        if(row.children.length == 0 || row.children == null) {
          return styleJson;
        } else {
          return {};
        }
      } else {
        return {};
      }
    },
    handleClose(done) {
      if (window.getSelection().toString()) {
        window.getSelection().removeAllRanges();
      } else {
        done();
      }
    },
    // 数据
    dataList() {
      this.$loading.show();
      menuData().then(res => {
        if (res.data.code === 100) {
          this.resultList = res.data.data
        }
        this.$loading.hide()
        this.tableHeightArea()
      })
    },
    // 按钮
    getPermissionList() {
      menuAssignList().then(res => {
        if (res.data.code === 100) {
          this.permissionList = res.data.data
        }
      })
    },
    resetTemp() {
      this.temp = {
        id: 0,
        name: '',
        code: '',
        suffix: '',
        pcode: '',
        url: '',
        icon: '',
        sort: 1,
        memo: '',
        type: 2,
        useFlag: 1
      }
      this.imgList = []
      this.whether = false
      this.whetherStates()
      this.$nextTick(function () {
        this.$refs.temp.clearValidate();
      })
    },
    whetherStates() {
      if (this.temp.useFlag === 0) {
        this.whether = false
      } else {
        this.whether = true
      }
    },
    useFlagStates() {
      if (this.whether === false) {
        this.temp.useFlag = 0
      } else {
        this.temp.useFlag = 1
      }
    },
    // 增加一级菜单
    addRootMenu() {
      this.dialogStatus = 'add'
      this.dialogFormVisible = true
      this.resetTemp()
      this.temp.code = 'menuAsimss'
      this.temp.pcode = "menuAsimss"
      this.imgList = []
      this.temp.suffix = ""
    },
    // 添加子项
    addSubMenu(row) {
      this.dialogStatus = 'child'
      this.dialogFormVisible = true
      this.resetTemp()
      this.temp.code = row.code
      this.temp.pcode = row.code
      this.imgList = []
      this.temp.useFlag = row.useFlag
      this.temp.type = 3
      this.temp.suffix = ""
      this.whetherStates()
    },
    // 添加
    addSubmit(temp) {
      this.useFlagStates()
      this.$refs[temp].validate((valid) => {
        if (valid) {
          var curCode = this.temp.pcode + this.temp.suffix
          this.$loading.show();
          var params = new URLSearchParams()
          params.append('name', this.temp.name)
          params.append('code', curCode)
          params.append('url', this.temp.url)
          params.append('icon', this.temp.icon)
          params.append('sort', this.temp.sort)
          params.append('memo', this.temp.memo)
          params.append('type', this.temp.type)
          params.append('useFlag', this.temp.useFlag)
          params.append('pcode', this.temp.pcode)
          menuAdd(params).then(res => {
            if (res.data.code === 100) {
              handleAlert('success', '保存成功')
              this.dataList()
              this.dialogFormVisible = false
            } else {
              handleAlert('error', res.data.msg)
            }
            this.$loading.hide();
          }).catch(err => {
            this.$loading.hide();
            if (err !== null && err !== '' && err.responseText !== null) {
              handleAlert('error', '提交失败,请重试')
            }
          })
        } else {
          handleAlert('error', '请完善菜单信息')
          this.$loading.hide();
        }
      })
    },
    // 编辑
    handelEdit(row) {
      this.dialogStatus = 'edit'
      this.dialogFormVisible = true
      this.resetTemp()
      row.suffix = row.code.substring(row.pcode.length)
      this.temp = Object.assign({}, row)
      this.imgList = []
      if (this.temp.icon != null && this.temp.icon.length > 0) {
        var img = {url: sysServerUrl + 'sys/upload/display?filePath=' + this.temp.icon}
        this.imgList.push(img)
      }
      this.whetherStates()
    },
    // 编辑
    updateSubmit(temp) {
      this.useFlagStates()
      this.$refs[temp].validate((valid) => {
        if (valid) {
          var curCode = this.temp.pcode + this.temp.suffix
          this.$loading.show()
          var params = new URLSearchParams()
          params.append('id', this.temp.id)
          params.append('name', this.temp.name)
          params.append('code', curCode)
          params.append('url', this.temp.url)
          params.append('icon', this.temp.icon)
          params.append('sort', this.temp.sort)
          params.append('memo', this.temp.memo)
          params.append('type', this.temp.type)
          params.append('useFlag', this.temp.useFlag)
          params.append('pcode', this.temp.pcode)
          menuEdit(params).then(res => {
            if (res.data.code === 100) {
              handleAlert('success', '保存成功')
              this.dataList()
              this.dialogFormVisible = false
            } else {
              handleAlert('error', res.data.msg)
            }
            this.$loading.hide()
          }).catch(err => {
            this.$loading.hide()
            if (err !== null && err !== '' && err.responseText !== null) {
              handleAlert('error', '提交失败,请重试')
            }
          })
        } else {
          handleAlert('error', '请完善菜单信息')
          this.$loading.hide()
        }
      })
    },
    handleOnSuccess(res, obj) {
      this.temp.icon = res.data.fileUrl
      this.imgList = []
      if (this.temp.icon != null && this.temp.icon.length > 0) {
        var img = {url: sysServerUrl + 'sys/upload/display?filePath=' + this.temp.icon}
        this.imgList.push(img)
      }
    },
    beforeOnRemove(file, fileList) {
      if (this.isFlag) {
        return this.$confirm(`确定移除选择文件？`, '删除图片', {type: 'warning'});
      }

    },
    handleOnRemove(file, fileList) {
      if (fileList.length == '0') {
        this.imgList = []
        this.temp.icon = ""
      }
    },
    beforeAvatarUpload(file) {
      this.isFlag = true;
      var fileName = file.name.substring(file.name.lastIndexOf(".") + 1).toLowerCase()
      const extension = fileName === 'png'
      const extension2 = fileName === 'jpg'
      const extension3 = fileName === 'jpeg'
      const extension4 = fileName === 'gif'
      const isLt2M = file.size / 1024 / 1024 < 5
      if (!extension && !extension2 && !extension3 && !extension4) {
        handleAlert('warning', '上传图片只能是 png、jpg、jpeg、gif格式!')
        this.isFlag = false;
        return false
      }
      if (!isLt2M) {
        handleAlert('warning', '上传图片大小不能超过 5MB!')
        this.isFlag = false;
        return false
      }
    },
    uploadAttach(param){
      var _this= this
			var formData = new FormData();
			formData.append('file', param.file);
      formData.append('flag', "menuIcon");
      importAttach(formData).then(res => {
        if (res.data.code === 100) {
          _this.temp.icon = res.data.data.fileUrl
          _this.imgList = []
          if (_this.temp.icon != null && _this.temp.icon.length > 0) {
            var img = {url: sysServerUrl + 'sys/upload/display?filePath=' + this.temp.icon}
            _this.imgList.push(img)
          }
        }
      }).catch(function(error){
      })
    },
    handleOnExceed(files, fileList) {
      handleAlert('warning', `当前限制选择1张图片，本次选择了 ${files.length} 张图片，共选择了 ${files.length + fileList.length} 张图片`)
    },
    // 查看
    handelDetail(row) {
      this.resetTemp()
      var params = "?id=" + row.id
      menuCheck(params).then(res => {
        this.temp = Object.assign({}, res.data.data.menu)
        this.temp.createUser = res.data.data.createUser
        this.temp.updateUser = res.data.data.updateUser
        this.temp.createTime = res.data.data.createTime
        this.temp.updateTime = res.data.data.updateTime
        this.whetherStates()
      })
      this.dialogStatus = 'detail'
      this.dialogFormVisible = true
    },
    // 删除
    handelDelete(row) {
      this.$confirm('确定删除【' + row.name + '】的相关信息?', '删除菜单', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var params = '?id=' + row.id
        menuDel(params).then(res => {
          if (res.data.code === 100) {
            handleAlert('success', '删除成功')
            this.dataList()
          } else {
            handleAlert('error', res.data.msg)
          }
        })
      }).catch((error) => {
        handleAlert('info', '取消删除')
      })
    },
    // 分配按钮
    assignButton(row) {
      this.alloter.menuCode = row.code
      this.alloter.permissionsCode = []
      this.dialogStatus = 'button'
      this.dialogFormVisible = true
      var params = '?menuCode=' + row.code
      menuAssignInfo(params).then(res => {
        var butLength = res.data.data.length
        if (butLength > 0) {
          for (var i = 0; i < butLength; i++) {
            this.alloter.permissionsCode.push(res.data.data[i].permissionCode)
          }
        }
      })
    },
    submitForm(alloter) {
      var list = []
      this.alloter.permissionsCode.forEach(function (item) {
        list.push(item)
      })
      this.$loading.show()
      var params = new URLSearchParams()
      params.append('menuCode', this.alloter.menuCode)
      for (var i = 0; i < list.length; i++) {
        params.append('permissions', list[i])
      }
      menuAssignUpdate(params).then(res => {
        if (res.data.code === 100) {
          handleAlert('success', '分配成功')
          this.dataList()
        }
        this.$loading.hide()
      })
      this.dialogFormVisible = false
    },
    maximumArea() {
      setTimeout(() => {
        tableHeight()
        setTimeout(() => {
          this.maximumHeight = tableMaxHeight;
        }, 50)
      })
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    },

    // ======= 2023-12-08 权限按钮配置
    addPermission(){
      this.dialogPermissionVisible = true;
      // 按钮列表
      permissionList().then(res => {
        this.permissionResulList = res.data.data
      })
    },
    inputCode(row){
      return row.code.length <= 0 || typeof(row.id) === 'string';
    },
    // 添加按钮
    addPermissionConfig(){
      let _this = this
      let id = "add_" + new Date().getTime();
      _this.permission = {
        id: id,
        code: '',
        name: '',
      };
      _this.permissionResulList.push(_this.permission)
      // 将滚动条移动到 最后一行
      setTimeout(() => {
        _this.$refs.tablePermission.bodyWrapper.scrollTop = _this.$refs.tablePermission.bodyWrapper.scrollHeight;
      })

    },
    // 保存
    handelSaveConfig(row){
      var params = new URLSearchParams()
      this.$loading.show()
      if (typeof(row.id) !== 'string') {
        params.append('id', row.id)
      }
      params.append('name', row.name)
      params.append('code', row.code)
      operatePermission(params).then(res => {
        if(res.data.code == 100){
          handleAlert('success', "保存成功");
          row.id = res.data.data.id
          this.getPermissionList();
        }else{
          handleAlert('error', res.data.msg);
        }
        this.$loading.hide()
      })
    },
    handelDelConfig(row){
      this.$confirm('确定要删除按钮【'+row.code+'】吗？', '删除按钮', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        dangerouslyUseHTMLString:true,
        type: 'warning'
      }).then(() => {
        let _this = this
        let len = _this.permissionResulList.length
        if (typeof(row.id) !== 'string') {
          var params = new URLSearchParams()
          params.append('code', row.code)
          delPermission(params).then(res => {
            if(res.data.code == 100){
              handleAlert('success', "删除成功");
              for (let i = 0; i < len; i++) {
                const item = _this.permissionResulList[i];
                if(item.id === row.id){
                  _this.permissionResulList.splice(i, 1);
                  break
                }
              }
            }else{
              handleAlert('error', res.data.msg);
            }
          })
        }else{
          for (let i = 0; i < len; i++) {
            const item = _this.permissionResulList[i];
            if(item.id === row.id){
              _this.permissionResulList.splice(i, 1);
              break
            }
          }
        }

        this.getPermissionList();
      })
    }


  },
  mounted() {
    this.tableHeightArea();
    this.dataList();
    this.getPermissionList();
  },
}
</script>
<style>
.permissionArea .el-input .el-input__count,
.permissionArea .el-input .el-input__count .el-input__count-inner {
  width: 38px;
}
.permissionArea .el-input__inner {
  height: 30px;
  line-height: 30px;
  padding: 0 5px;
}
.cell > .el-input > .el-input__inner {
  /* height: 25px;
    line-height: 25px;
    margin: 6px;
    width: 200px; */
}
</style>

<style>
.el-icon-look_permission{
    background: url('~@/assets/image/permission.png') center no-repeat;
   /* background-size: cover;*/
}
.el-icon-look_permission{
    font-size: 17px;
}
.el-icon-look_permission:before{
    content: "\e611";
}

</style>
