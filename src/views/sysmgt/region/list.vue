<template>
  <div class="layoutContainer">
    <div class="infoDetail">
      <el-row>
        <el-col :span="8" class="leftData">
          <div>
            <div class="topButton" v-if="hasPerm('menuAsimss1Aregion_101') || hasPerm('menuAsimss1Aregion_102') || hasPerm('menuAsimss1Aregion_107')">
              <el-button type="text" v-if="hasPerm('menuAsimss1Aregion_101')" icon="el-icon-plus" @click="addRoot">新增根节点</el-button>
              <el-button type="text" v-if="hasPerm('menuAsimss1Aregion_101')" icon="el-icon-plus" @click="addSub">新增子节点</el-button>
              <!-- <el-upload
                class="upload-demo inline-block"
                ref="elUpload"
                action="#"
                :show-file-list="false"
                multiple
                :limit="1"
                :file-list="fileList"
                :before-upload="onBeforeUpload"
                :http-request="uploadFile"
                :on-change="onUploadChange"
              >
                <el-button type="text" v-if="hasPerm('menuAsimss1Aregion_107')" size="min" icon="bulkImport-icon">批量上传</el-button>
              </el-upload>
              <el-button type="text" v-if="hasPerm('menuAsimss1Aregion_107')" icon="el-icon-download" @click="downTemplateClick()">下载模板</el-button> -->
              <el-dropdown v-if="hasPerm('menuAsimss1Aregion_107')">
                <el-button  type="text" size="small" style="font-size: 14px;" icon="el-icon-upload2">导入数据</el-button>&nbsp;&nbsp;&nbsp;
                <el-dropdown-menu slot="dropdown" style="margin-top: -3px">
                  <el-dropdown-item >
                    <el-upload
                    class="upload-demo inline-block"
                    ref="elUpload"
                    action="#"
                    :show-file-list="false"
                    multiple
                    :limit="1"
                    :file-list="fileList"
                    :before-upload="onBeforeUpload"
                    :http-request="(param) => uploadFile(param, '0')"
                    :on-change="onUploadChange"
                    >导入大洲数据</el-upload>
                  </el-dropdown-item>
                  <el-dropdown-item >
                    <el-upload
                        class="upload-demo inline-block"
                        ref="elUpload"
                        action="#"
                        :show-file-list="false"
                        multiple
                        :limit="1"
                        :file-list="fileList"
                        :before-upload="onBeforeUpload"
                        :http-request="(param) => uploadFile(param, '1')"
                        :on-change="onUploadChange"
                      >
                      导入国家数据
                    </el-upload>
                  </el-dropdown-item>
                  <el-dropdown-item >
                    <el-upload
                        class="upload-demo inline-block"
                        ref="elUpload"
                        action="#"
                        :show-file-list="false"
                        multiple
                        :limit="1"
                        :file-list="fileList"
                        :before-upload="onBeforeUpload"
                        :http-request="(param) => uploadFile(param, '2')"
                        :on-change="onUploadChange"
                      >
                      导入区域数据
                    </el-upload>
                  </el-dropdown-item>
                  <el-dropdown-item >
                    <el-upload
                        class="upload-demo inline-block"
                        ref="elUpload"
                        action="#"
                        :show-file-list="false"
                        multiple
                        :limit="1"
                        :file-list="fileList"
                        :before-upload="onBeforeUpload"
                        :http-request="(param) => uploadFile(param, '3')"
                        :on-change="onUploadChange"
                      >
                      导入省份数据
                    </el-upload>
                  </el-dropdown-item>
                  <el-dropdown-item >
                    <el-upload
                        class="upload-demo inline-block"
                        ref="elUpload"
                        action="#"
                        :show-file-list="false"
                        multiple
                        :limit="1"
                        :file-list="fileList"
                        :before-upload="onBeforeUpload"
                        :http-request="(param) => uploadFile(param, '4')"
                        :on-change="onUploadChange"
                      >
                      导入城市数据
                    </el-upload>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>

              <!-- <el-button type="text" v-if="hasPerm('menuAsimss1Aregion_107')" icon="el-icon-download" @click="downTemplateClick()">下载模板</el-button> -->
              <el-dropdown v-if="hasPerm('menuAsimss1Aregion_107')" placement="bottom">
                <el-button  type="text" size="small" style="font-size: 14px;" icon="el-icon-download">下载模板</el-button>&nbsp;&nbsp;&nbsp;
                <el-dropdown-menu slot="dropdown" style="margin-top: -3px">
                  <el-dropdown-item  @click.native="downTemplateClick('大洲导入模板.xlsx')">下载大洲模板</el-dropdown-item>
                  <el-dropdown-item  @click.native="downTemplateClick('国家导入模板.xlsx')">下载国家模板</el-dropdown-item>
                  <el-dropdown-item  @click.native="downTemplateClick('区域导入模板.xlsx')">下载区域模板</el-dropdown-item>
                  <el-dropdown-item  @click.native="downTemplateClick('省份导入模板.xlsx')">下载省份模板</el-dropdown-item>
                  <el-dropdown-item  @click.native="downTemplateClick('城市导入模板.xlsx')">下载城市模板</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <el-button type="text" v-if="hasPerm('menuAsimss1Aregion_102')" icon="el-icon-delete" @click="del">删除</el-button>
            </div>
            <div class="scrollClass">
              <el-scrollbar>
                <el-tree ref="tree" node-key="id" :default-expanded-keys="nodeKeyList" :data="listdata" :props="defaultProps" @node-click="handleNodeClick"></el-tree>
              </el-scrollbar>
            </div>
          </div>
        </el-col>
        <el-col :span="16" class="fromRight">
          <div class="formTitle" v-html="editTitle"></div>
          <el-form ref="form" :model="form" :rules="rules" :label-width="formLabelWidth" :validate-on-rule-change="false">
            <el-form-item class="bgc" label="节点类型" prop="typeName">
              <el-input class="formCode" :disabled="true"  v-model.trim='form.typeName'></el-input>
            </el-form-item>

            <el-form-item class="bgc" label="区域代码" prop="code">
              <el-input class="formCode" :disabled="butType !=='addNodeBut'" placeholder="请输入区域代码" oninput="value=value.replace(/[^\w_-]/ig,'')" v-model.trim='form.code' show-word-limit maxlength="20"></el-input>
            </el-form-item>

            <el-form-item label="区域名称" prop="name">
              <el-input v-model.trim="form.name" placeholder="请输入区域名称" show-word-limit maxlength="50"></el-input>
            </el-form-item>

            <el-form-item label="排序" prop="sort">
              <el-input type="number" placeholder="请输入排序"  :min="1" :max="9999"  @input="e => form.sort=parserNumber(e,1,9999)" v-model.trim="form.sort"></el-input>
            </el-form-item>
            <!-- <el-form-item label="生效状态" prop="whether" id="state_whether">
              <el-switch v-model="form.whether"></el-switch>
            </el-form-item> -->
            <el-form-item class="butArea">
              <el-button v-show="butType!=='addNodeBut'" type="primary" @click="preserve()">保存</el-button>
              <el-button v-show="butType==='addNodeBut'" type="primary" @click="preserve()">确定</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>


    </div>
  </div>
</template>
<script>
import { sysServerUrl, contentSize, handleAlert } from '@/assets/js/common.js'
import { regionData, regionType, regionAdd, regionEdit, regionDel, regionImportInfo, regionTemplate } from '@/api/sysmgt.js'
export default {
  name: 'sysmgt_region_list',
  data () {
    return {
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      nodeKeyList:[],
      editTitle: '当前信息',
      rules: {
        code: [],
        name: []
      },

      formLabelWidth: '100px',
      butType: '',
      listdata: [],
      typeList: [],
      fileList: [],

      form:{
        id: '',
        pid: '',
        typeName: '',
        type: '',
        code: '',
        name: '',
        sort: 1,
        whether: true,

      }
    }
  },
  methods: {
    // 获取结构树
    dataList(){
      regionData().then(res => {
        this.listdata = res.data.data
      })
    },
    // 获取类型
    getTypeList(){
      regionType().then(res => {
        this.typeList = res.data.data
      })
    },
    // 显示详情，点击获取当前车系信息
    handleNodeClick (data) {
      let _this = this;
      _this.form.code = data.code;
      _this.form.name = data.name;
      _this.form.id = data.id;
      _this.form.whether = data.inUse == 1;
      _this.form.pid = data.pid;
      _this.form.sort = data.sort;
      _this.form.type = data.type;
      for (let i = 0; i < _this.typeList.length; i++) {
        const item = _this.typeList[i];
        if (item.code == data.type) {
          _this.form.typeName = item.name;
          break
        }
      }
    },
    resetting(){
      this.form = {
        id: '',
        pid: '',
        typeName: '',
        type: '',
        code: '',
        name: '',
        sort: 1,
        whether: true,

      }
    },
    // 增加根节点
    addRoot(){
      this.butType='addNodeBut'
      this.editTitle='新增根节点'
      this.form.typeName=this.typeList[0].name
      this.form.type=this.typeList[0].code
      this.form.pid='0'
      this.form.id = ''
      this.form.code = ''
      this.form.name = ''
      this.form.sort = 1
      this.form.whether = true
    },
    // 新增子节点
    addSub(){
      if(this.form.id == ''){
        handleAlert('warning','请先选中父级节点')
        return false
      }
      if(this.form.type == 4){
        handleAlert('warning','选中的父级节点不能为 城市 节点')
        return false
      }
      let typeObj;
      for (let i = 0; i < this.typeList.length; i++) {
        const item = this.typeList[i];
        if (item.code == this.form.type) {
          typeObj = this.typeList[i+1]
          break
        }
      }
      this.butType='addNodeBut'
      this.editTitle='当前选中父级节点：'+ this.form.name
      let pid = this.form.id
      this.form.whether = true;
      this.form.pid = pid;
      this.form.sort = 1;
      this.form.type = typeObj.code;
      this.form.typeName = typeObj.name;
      this.form.id = '';
      this.form.code = '';
      this.form.name = '';
    },
    // 删除
    del(){
      var _this=this
      if(this.form.id==''){
        handleAlert('warning','请先选中要删除的节点')
        return false
      }else{
        _this.$confirm('确定删除【' +  _this.form.name + '】区域?', '删除区域节点', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          //删除节点
          _this.deleteNode(_this.form.id)
          this.resetting()
        }).catch((error)=>{
          // handleAlert('info','取消删除')
        })
      }
    },
    deleteNode(id){
      regionDel(id).then(res => {
        if (res.data.code == 100) {
          handleAlert('success','删除成功')
          this.dataList()
        }else{
          handleAlert('error',res.data.msg)
        }
      }).catch(function(error){
        handleAlert('error','系统异常，请稍后再试')
      })
    },
    // 保存
    preserve(){
      if (!this.form.code) {
        handleAlert('error', '代码不能为空');
        return false;
      }
      if(!this.form.name){
        handleAlert('error', '名称不能为空');
        return false;
      }
      this.nodeKeyList = []
      this.$loading.show();
      var params = new URLSearchParams()
      params.append('id', this.form.id)
      params.append('pid', this.form.pid)
      params.append('type',this.form.type)
      params.append('inUse', this.form.whether ? 1 : 0)
      params.append('sort', this.form.sort)
      params.append('name', this.form.name)
      params.append('code',this.form.code)
      if(this.form.id!=null && this.form.id!='' && this.form.id!='null' && this.form.id != undefined && this.form.id != 'undefined'){
        this.updateNode(params)
        this.nodeKeyList.push(this.form.id)
      } else {
        this.addNode(params)
        this.nodeKeyList.push(this.form.pid)
      }
    },
    // 编辑
    updateNode(params){
      regionEdit(params).then(res => {
        if (res.data.code == 100) {
          handleAlert('success','编辑成功')
          this.dataList()
        }else{
          handleAlert('error',res.data.msg)
        }
        this.$loading.hide();
      }).catch(function(error){
        handleAlert('error','系统异常，请稍后再试')
        this.$loading.hide();
      })
    },
    // 新增节点
    addNode(params){
      var _this=this
      regionAdd(params).then(res => {
        if(res.data.code==100){
          handleAlert('success','添加成功')
          _this.dataList()
          this.butType=''
          this.editTitle='当前信息'
        }else{
          handleAlert('error',res.data.msg)
        }
        this.$loading.hide();
      }).catch(function(error){
        handleAlert('error','系统异常，请稍后再试')
        this.$loading.hide();
      })
    },

    // 下载模板
    downTemplateClick (name) {
      regionTemplate(name).then(res => {
        if(!res.data){
          return
        }
        var blob = new Blob([res.data]);
        var url = window.URL.createObjectURL(blob);
        var aLink = document.createElement("a");
        aLink.style.display = "none";
        aLink.href = url;
        aLink.setAttribute("download", name);
        document.body.appendChild(aLink);
        aLink.click();
        document.body.removeChild(aLink); //下载完成移除元素
        window.URL.revokeObjectURL(url); //释放掉blob对象
      })
    },
    // 附件上传
    onBeforeUpload(file) {
      var fileExt =file.name.substring(file.name.lastIndexOf('.')+1).toLowerCase();
      var text=""
      const docExt = fileExt === 'xls'
      const docxExt = fileExt === 'xlsx'
      const isLimit = file.size / 1024 / 1024 < 10
      if(!docxExt) {
        text="上传文件只能是xlsx格式!";
        handleAlert('warning',text)
        return false;
      }
      if (!isLimit) {
        text="上传文件大小不能超过 10MB!";
        handleAlert('warning',text)
        return false;
      }
      return true;
    },
    onUploadChange(file){
      //this.files.push(file.raw)

    },
    // 批量上传
    uploadFile (param, type) {
      var _this= this
			var formData = new FormData();
			formData.append('file', param.file);
      formData.append('type', type);
      regionImportInfo(formData).then(res => {
        if (res.data.code === 100) {
          if (res.data.data) {
            _this.$alert("警告：" + res.data.data,'导入成功',{dangerouslyUseHTMLString:true})
          }else{
            handleAlert('success','批量导入成功')
          }

          _this.dataList()
        }else{
          _this.$alert(res.data.msg,'导入失败',{dangerouslyUseHTMLString:true})
        }
        _this.infoList = []
      }).catch(function(error){
        _this.infoList = []
        _this.$alert('系统出现异常，导入失败','信息提示',{dangerouslyUseHTMLString:true})
      })
		},
    handlesuccess (file, fileList) {
    },
    handleRemove (file, fileList) {
    },
    handlePreview (file) {

    },
    handleExceed (files, fileList) {
      handleAlert('warning',`当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
    },
    beforeRemove (file, fileList) {
      return this.$confirm(`确定移除选择文件？`, '删除',{type: 'warning'});
    },
    uploadFileError(err, file, fileList) {
      handleAlert('error','导入失败')
      this.$refs.upload.clearFiles();
    },

  },
  mounted () {
    this.dataList()
    this.getTypeList()
    contentSize()
  },
}
</script>
<style>
  .bgc .el-input.is-disabled .el-input__inner {
    background-color: #F4F4F5;
  }
  #state_whether .el-switch {
    height: 38px !important;
    line-height: 32px !important;
  }
</style>
