<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" ref="formInline" label-width="80px" :model="formInline" class="demo-form-inline">
        <el-form-item label="服务店名称" prop="realName">
          <el-input v-model.trim="formInline.realName" placeholder="请输入服务店名称"></el-input>
        </el-form-item>
        <el-form-item label="登录账户" prop="loginName">
          <el-input v-model.trim="formInline.loginName" placeholder="请输入登录账号"></el-input>
        </el-form-item>
        <!-- <el-form-item label="默认角色" prop="userrole">
          <el-select v-model="formInline.userrole" clearable filterable>
            <el-option v-for="(item,index) in userRoleList" :key="index" :label="item.name" :value="item.code"></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="所属国家" prop="userCountry">
          <el-select v-model="formInline.userCountry" clearable filterable>
            <el-option v-for="(item, index) in userCountryList" :key="index" :label="item.name"
              :value="item.code"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit" icon="el-icon-search">搜索</el-button>
          <el-button plain @click="reset('formInline')">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <div class="tableHandle">
        <el-button type="text" v-if="hasPerm('menuAsimss1AdealerMgt_101')" icon="el-icon-plus" @click="addClcik()">新增</el-button>
        <!-- <el-button type="text" v-if="hasPerm('menuAsimss1A2B_102')" icon="el-icon-delete"
          @click="batchDelClick()">批量删除</el-button> -->
        <el-upload class="upload-demo inline-block" ref="elUpload" action="#"
          :show-file-list="false" multiple :limit="1" :file-list="fileList" :before-upload="onBeforeUpload"
          :http-request="uploadFile" :on-change="onUploadChange">
          <el-button type="text" v-if="hasPerm('menuAsimss1AdealerMgt_107')" size="min" icon="bulkImport-icon">批量上传</el-button>
        </el-upload>
        <el-button type="text" v-if="hasPerm('menuAsimss1AdealerMgt_108')" icon="bulkDown-icon"
          @click="batchExport()">批量下载</el-button>
        <el-button type="text" icon="el-icon-download"
          @click="downTemplateClick()">下载模板</el-button>
      </div>
      <!--
        lazy :load="load"
      -->
      <el-table
        ref="multipleTable"
        row-key="id"
        style="width:100%"
        border
        highlight-current-row
        :max-height="maximumHeight"
        :data="resultList"
        default-expand-all
        :header-cell-style="{
          'text-align': 'center',
          'background-color': 'var(--other-color)',
        }"
        :row-style="rowStyle"
        @header-dragend="changeColWidth"
        @selection-change="handleSelectionChange"
        @select-all="selectAll"
        :tree-props="{ children: 'secondUserList', hasChildren: 'hasChildren' }"
      >
        <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
        <!-- <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column> -->
        <el-table-column label="服务店名称" prop="realName"  min-width="200" show-overflow-tooltip></el-table-column>
        <el-table-column label="登录账户" prop="username"  min-width="100" show-overflow-tooltip></el-table-column>
        <!-- <el-table-column label="默认角色" prop="defaultRoleName" min-width="100" show-overflow-tooltip></el-table-column> -->
        <el-table-column label="所属国家" prop="defaultCountryName" align="center"  min-width="100" ></el-table-column>
        <!-- <el-table-column label="所属部门" prop="deptName" min-width="100" show-overflow-tooltip></el-table-column> -->
        <el-table-column label="联系人" prop="contacts" min-width="100" show-overflow-tooltip></el-table-column>
        <!-- <el-table-column label="性别" prop="sex" width="70">
          <template slot-scope="{row}">
            <span v-if="row.sex === 0">未知</span>
            <span v-if="row.sex === 1">男</span>
            <span v-if="row.sex === 2">女</span>
          </template>
        </el-table-column> -->
        <el-table-column label="手机号" prop="mobile" width="150" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="邮箱" prop="email" width="150" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="地址" prop="address" min-width="160" show-overflow-tooltip></el-table-column>
        <!-- <el-table-column label="类型" prop="userType" width="60" align="center">
          <template slot-scope="{row}">
            <span v-if="row.userType === 2">前台</span>
            <span v-if="row.userType === 1">后台</span>
          </template>
        </el-table-column> -->
        <el-table-column label="状态" prop="useFlag" width="70" align="center">
          <template slot-scope="{row}">
            <span v-if="row.useFlag === 1" style="color:#009933">生效</span>
            <span v-if="row.useFlag === 0" style="color:#c30000">失效</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="360">
          <template slot-scope="{row}">
            <el-button v-if="row.pid == 0 && hasPerm('menuAsimss1AdealerMgt_101') && false" type="text" size="small"
              @click="addClcik(row)">新增二级</el-button>

              <el-button type="text" v-if="hasPerm('menuAsimss1AdealerMgt_101') && row.useFlag==1" size="small"
            @click="editUseFlag(row, 0)">停用</el-button>
            <el-button type="text" v-if="hasPerm('menuAsimss1AdealerMgt_101') && row.useFlag==0" size="small"
            @click="editUseFlag(row, 1)">启用</el-button>

              <el-button v-if="row.pid == 0 && hasPerm('menuAsimss1AdealerMgt_101')" type="text" size="small"
              @click="addSubAccount(row)">批量新增二级</el-button>
            <!-- <el-button v-if="hasPerm('menuAsimss1A2B_105')" type="text" size="small" @click="distriRole(row)">分配角色</el-button> -->
            <el-button v-if="hasPerm('menuAsimss1AdealerMgt_113')" type="text" size="small"
              @click="distriTrain(row)">分配车型</el-button>
            <el-button v-if="hasPerm('menuAsimss1AdealerMgt_113') && row.userType == 1" type="text" size="small"
              @click="distriCountry(row)">分配国家</el-button>
            <el-button v-if="hasPerm('menuAsimss1AdealerMgt_103')" type="text" size="small"
              @click="initPsw(row)">重置密码</el-button>
            <el-button v-if="hasPerm('menuAsimss1AdealerMgt_103')" type="text" size="small" @click="editopen(row)">编辑</el-button>
            <el-button type="text" v-if="hasPerm('menuAsimss1AdealerMgt_102')" size="small"
              @click="delectClick(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="currentPage" :limit.sync="pagesize"
        @pagination="pageLimitList" />
      <!-- 新增，编辑 -->
      <el-dialog v-dialogDrag lock-scroll :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
        <!-- 新增，编辑 -->
        <el-form v-if="dialogStatus === 'edit' || dialogStatus === 'add'" ref='temp' :rules="fromTemp" :model="temp"
          label-position="center" :validate-on-rule-change="false" :label-width="formLabelWidth">
          <el-form-item label="登录账户" prop="username">
            <el-input v-if="dialogStatus === 'add'" v-model.trim="temp.username" placeholder="请输入账号" show-word-limit
              maxlength="20"></el-input>
            <el-input v-if="dialogStatus === 'edit' && temp.username !== ''" v-model.trim="temp.username" show-word-limit
              maxlength="20" :disabled="true"></el-input>
            <el-input v-if="dialogStatus === 'edit' && temp.username === ''" v-model.trim="temp.username" show-word-limit
              maxlength="20"></el-input>
          </el-form-item>
          <el-form-item label="服务店名称" prop="realName">
            <el-input v-model.trim="temp.realName" placeholder="请输入服务店名称" show-word-limit maxlength="50"></el-input>
          </el-form-item>
          <!-- <el-form-item label="默认角色" prop="defaultRoleCode">
            <el-select v-model="temp.defaultRoleCode" clearable filterable>
              <el-option v-for="(item, index) in userRoleList" :key="index" :label="item.name"
                :value="item.code"></el-option>
            </el-select>
          </el-form-item> -->

          <!-- 2023-08-01 添加默认国家 -->
          <el-form-item label="所属国家" prop="defaultCountryCode">
            <el-select v-model="temp.defaultCountryCode" clearable filterable @change="getCity">
              <el-option v-for="(item, index) in userCountryList" :key="index" :label="item.name"
                :value="item.code"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="所属城市" prop="cityId">
            <select-tree ref="modelSelectTree"
              :options="cityList"
              v-model.trim="temp.cityId"
              :props="defaultProps"
              :show_checkbox="false"
              :expand_on_click_node="true"
              :check_on_click_node="false"
              placeholder="请选择城市" />

          </el-form-item>
          <el-form-item label="联系人" prop="contacts">
            <el-input v-model.trim="temp.contacts" show-word-limit maxlength="20" placeholder="请输入联系人"></el-input>
          </el-form-item>
          <el-form-item label="性别" prop="sex">
            <el-radio-group v-model="temp.sex">
              <el-radio :label="1">男</el-radio>
              <el-radio :label="2">女</el-radio>
              <el-radio :label="0">未知</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="手机号" prop="mobile">
            <el-input v-model.trim="temp.mobile" maxlength="11" placeholder="请输入手机号"></el-input>
          </el-form-item>
          <el-form-item label="邮箱" prop="email">
            <el-input v-model.trim="temp.email" placeholder="请输入邮箱"></el-input>
          </el-form-item>
          <el-form-item label="地址" prop="address">
            <el-input rows="2" v-model.trim="temp.address" show-word-limit maxlength="100" placeholder="请输入地址"></el-input>
          </el-form-item>
          <el-form-item label="授权价格查看" prop="grantPrice">
            <el-switch v-model="temp.grantPrice"></el-switch>
          </el-form-item>
          <!-- <el-form-item label="类型" prop="userType">
           <el-radio-group v-model="temp.userType" @change="changeUserType">
            <el-radio :label="2">前台</el-radio>
            <el-radio :label="1">后台</el-radio>
          </el-radio-group>
          </el-form-item> -->
          <el-form-item label="生效状态" prop="useFlag">
            <el-switch :disabled="temp.useFlagDisable" v-model="temp.useFlag"></el-switch>
          </el-form-item>
          <div class="submitArea">
            <el-button type="primary" @click="dialogStatus === 'edit' ? editClick('temp') : addClick('temp')">
              立即提交
            </el-button>
            <el-button @click="resetForm('temp')">
              重置
            </el-button>
          </div>
        </el-form>
        <!-- 分配角色 -->
        <!-- <el-form :label-width="formLabelWidth" v-if="dialogStatus === 'assignRole'" ref="alloter" :model="sltUserRole" :validate-on-rule-change="false">
          <el-form-item label="选择角色">
            <el-checkbox-group v-model="sltUserRole.roleCodeArr">
              <el-checkbox v-for=" (item, cindex) in userRoleList" :key="cindex"
                :label="item.code">{{ item.name }}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="默认角色" prop="defaultRoleCode">
            <el-radio-group v-model="sltUserRole.defaultRoleCode">
              <el-radio v-for="(item, cindex) in userRoleList" :key="cindex" :label="item.code">{{ item.name }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="updateUserRole()">
              立即提交
            </el-button>
            <el-button @click="resetCode()">
              重置
            </el-button>
          </el-form-item>
        </el-form> -->
        <!-- 分配国家 -->
        <el-form v-if="dialogStatus === 'assignCountry'" ref="alloter" :model="sltUserCountry"
          :validate-on-rule-change="false" :label-width="formLabelWidth">
          <!-- <el-form-item label="负责国家">
            <el-checkbox-group v-model="sltUserCountry.countryArr">
              <el-checkbox v-for=" (item, cindex) in userCountryList" :key="cindex"  :label="item.code" >{{item.name}}</el-checkbox>
            </el-checkbox-group>
          </el-form-item> -->
          <el-form-item label="所在国家" prop="defaultCountry">
            <el-radio-group v-model="sltUserCountry.defaultCountry">
              <el-radio v-for="(item, cindex) in userCountryList" :key="cindex"
                :label="item.code">{{ item.name }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <div class="submitArea">
            <el-button type="primary" @click="updateUserCountry()">
              立即提交
            </el-button>
            <el-button @click="resetCode()">
              重置
            </el-button>
          </div>
        </el-form>
      </el-dialog>
      <!-- 分配车型 -->
      <el-dialog v-dialogDrag lock-scroll title="分配车型" :visible.sync="dialogTrainTreeVisible" v-if="dialogTrainTreeVisible">
        <div style="height: 300px; width: 100%; overflow-y:auto;">
          <el-input placeholder="输入关键字进行过滤" v-model="filterText"></el-input>
          <el-tree :data="showTrainTree" show-checkbox node-key="id" ref="trainTree" :default-expand-all="true"
            :default-checked-keys="checkedTrain" :filter-node-method="filterNode" :props="defaultTrainProps">
          </el-tree>
        </div>
        <div class="submitArea">
          <el-button type="primary" @click="getCheckedKeys()">立即提交</el-button>
          <el-button @click="dialogTrainTreeVisible = false">取消</el-button>
        </div>
        <!-- <div style="width: 100%; display: flex;  justify-content: center;  align-items: center;">
          <el-button type="primary" @click="getCheckedKeys()">立即提交</el-button>
          <el-button @click="dialogTrainTreeVisible = false">取消</el-button>
        </div> -->
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { sysServerUrl, handleAlert, tableHeight, tableMaxHeight } from '@/assets/js/common.js'
import Pagination from '@/components/Pagination'
import SelectTree from '@/components/TreeView/SelectTree.vue'
import {
  dealerData,
  dealerChildData,
  userRoleData,
  userAdd,
  userEdit,
  userDel,
  dealerBatchDown,
  downDealerTemplate,
  userBatchDel,
  assignRole,
  updateAssignRole,
  userPasswordReset,
  departmentData,
  userCompleteCountryData,
  getUserCountry,
  updatedCountry,
  getUserBrandTrainTree,
  getUserTrain,
  getUserTrainByUserId,
  getUserInfoId,
  updateUserTrain,
  getCountryTree,
  dealerBatchSubAccount,
} from '@/api/sysmgt.js'
export default {
  name: 'sysmgt_dealer_list',
  components: { Pagination, SelectTree },
  data() {
    return {
      checkAll:false,
      resolveMap:new Map,
      formInline: {
        realName: '',
        loginName: '',
        userrole: '',
        userCountry: ''
      },
      temp: {
        id: '',
        realName: '',
        username: '',
        defaultRoleName: '',
        defaultRoleCode: 'generalUser',
        defaultCountryCode: '',
        defaultCountryName: '',
        deptId: '',
        deptName: '',
        contacts: '',
        sex: '',
        mobile: '',
        email: '',
        address: '',
        userType: 2,
        useFlag: true,
        grantPrice : true,
        useFlagDisable:false,
        cityId: '',
        cityName: '',
      },
      perms: [],
      sltUserRole: {
        userName: '',
        defaultRoleCode: '',
        roleCodeArr: []
      },
      sltUserCountry: {
        userName: '',
        defaultCountry: '',
        countryArr: []
      },
      dialogFormVisible: false,
      formLabelWidth: '100px',
      resultList: [],
      dialogStatus: '',
      textMap: {
        edit: '用户编辑',
        add: '新增用户',
        restPwd: '重置密码',
        assignRole: '分配角色',
        assignCountry: '分配国家',
      },
      defaultProps: {
        parent: 'pid',
        value: 'id',
        label: 'name',
        children: 'children',
        disabled: function (val) {
          if (val.children == null) {
            return false
          } else {
            return true
          }
        }
      },
      deleteList: [],
      pagesize: 20,
      currentPage: 1,
      total: 0,
      userRoleList: [],
      userCountryList: [],
      fileList: [],
      uploadFileList: [],
      fromTemp: {
        username: [{ required: true, message: '账号不能为空', trigger: ['blur', 'change'] }],
        realName: [{ required: true, message: '服务店名称不能为空', trigger: ['blur', 'change'] }],
        // defaultRoleCode: [{ required: true, message: '默认角色不能为空', trigger: ['blur', 'change'] }],
        // mobile: [{ pattern: /^1\d{10}$/, message: '请输入正确的手机格式', trigger: ['blur', 'change'] }],
        // email: [{ type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }],
        defaultCountryCode: [{ required: true, message: '所属国家不能为空', trigger: ['blur', 'change'] }],
        cityId: [{ required: true, message: '所属城市不能为空', trigger: ['blur', 'change']}]
        // deptId: [{ required: true, message: '所属部门不能为空', trigger: ['blur', 'change']}]
      },
      cityList: [],

      // 车型和人员的关联
      filterText: '',
      trainUser: '',
      userTrainTree: [],
      showTrainTree:[],
      checkedTrain: [],
      parentTrainIds:[],
      dialogTrainTreeVisible: false,
      defaultTrainProps: {
        children: 'children',
        label: 'name'
      },
      maximumHeight: 0,
    }
  },
  watch: {
    filterText(val) {
      this.$refs.trainTree.filter(val);
    },
  },
  methods: {
    rowStyle({ row, rowIndex }) {
      let styleJson = {
        "background": "var(--cell-bgColor)",
      }
      if (row.pid != 0) {
        if (row.children == null || row.children.length > 0) {
          return styleJson;
        } else {
          return {};
        }
      } else {
        return {};
      }
    },
    // 监听表格列宽变化
    changeColWidth(){
      this.$nextTick(() => {
        this.$refs.multipleTable.doLayout();
      })
    },
    pageLimitList(){
      this.dataList()
    },
    // 列表数据
    dataList(pid) {
      this.$loading.show();
      var params = new URLSearchParams()
      params.append('page', this.currentPage)
      params.append('limit', this.pagesize)
      params.append('realName', this.formInline.realName)
      params.append('username', this.formInline.loginName)
      params.append('defaultRoleCode', 'generalUser')
      params.append('defaultCountryCode', this.formInline.userCountry)
      params.append("userType",2)
      //如果需要懒加载，把下面的注释打开即可
      //   if(pid && pid !=0 && this.resolveMap.has(pid+'')){
      //     this.$set( this.$refs.multipleTable.store.states.lazyTreeNodeMap,pid,[ ])
      //          var { tree, treeNode, resolve } =  this.resolveMap.get(pid+'')
      //          this.load(tree,treeNode,resolve)
      //     }else{
      //   dealerData(params).then(res => {
      //     this.total = res.data.total
      //     this.resultList = res.data.data
      //   })
      // }
      dealerData(params).then(res => {
        if (res.data.code == 100) {
          this.total = res.data.total
          this.resultList = res.data.data
        } else {
          handleAlert("error", res.data.msg)
        }
        this.tableHeightArea()
        this.$loading.hide();
      })
    },
    // 搜索
    keyDown(e) {
      if (e.keyCode === 13) {
        this.onSubmit()
      }
    },
    onSubmit() {
      this.currentPage = 1
      this.dataList()
    },
    // 角色信息
    getUserRoleList() {
      userRoleData().then(res => {
        this.userRoleList = res.data.data
      })
    },
    // 获取国家
    getUserCountryList() {
      userCompleteCountryData().then(res => {
        this.userCountryList = res.data.data
        console.log("guojia", this.userCountryList);
      })
    },
    // 所属城市
    getCity() {
      this.$refs.modelSelectTree.initSelected('','')
      if (!this.temp.defaultCountryCode) {
        this.cityList = []
      }else{
        this.cityList = []
        this.userCountryList.forEach(o => {
          if(o.code == this.temp.defaultCountryCode){
            this.cityList.push(o)
          }
        })
      }

    },
    resetTemp() {
      this.temp = {
        id: '',
        realName: '',
        username: '',
        defaultRoleName: '',
        defaultRoleCode: 'generalUser',
        defaultCountryCode: '',
        deptId: '',
        deptName: '',
        contacts: '',
        sex: '',
        mobile: '',
        email: '',
        address: '',
        userType: 2,
        useFlag: true,
        grantPrice:true,
        useFlagDisable: false,
        cityName: ''
      }
      this.$nextTick(function () {
        this.$refs.temp.clearValidate();
      })
      setTimeout(() => {
        this.$refs.modelSelectTree.initSelected('','')
      });
    },
    changeUserType(val) {
      this.temp.userType = val
    },
    // 新增
    addClcik(row) {
      var _this = this
      _this.dialogStatus = 'add'
      _this.dialogFormVisible = true
      setTimeout(() => {
        _this.$refs.modelSelectTree.initSelected('','')
        _this.resetTemp()
        if(row){
        this.temp.pid = row.id
        if(row.useFlag == 0){
          this.temp.useFlagDisable = true
        }
      }
      })
    },
    getCurrentNode(node) {
      if (node != null) {
        this.$refs['temp'].validateField('cityId')
      }
    },
    addClick(temp) {
      this.$refs[temp].validate((valid) => {
        if (valid) {
          this.$loading.show();
          var params = new URLSearchParams()
          params.append('realName', this.temp.realName)
          params.append('username', this.temp.username)
          params.append('defaultRoleCode', 'generalUser')
          params.append('defaultCountryCode', this.temp.defaultCountryCode)
          if (this.temp.cityId && this.temp.cityId != 'null' && this.temp.cityId != 'undefined') {
            params.append('cityId', this.temp.cityId)
          }
          params.append('email', this.temp.email)
          params.append('address', this.temp.address)
          params.append('contacts', this.temp.contacts)
          params.append('mobile', this.temp.mobile)
          params.append('userType', 2)
          params.append('sex', this.temp.sex)
          params.append("grantPrice",this.temp.grantPrice?1:0)
          params.append('useFlag', this.temp.useFlag?1:0)
          if(this.temp.pid){
            params.append("pid",this.temp.pid)
          }
          userAdd(params).then(res => {
            if (res.data.code === 100) {
              handleAlert("success", res.data.msg)
              this.dataList(this.temp.pid)
              this.dialogFormVisible = false
            } else {
              handleAlert("error", res.data.msg)
            }
            this.$loading.hide();
          })
        } else {
          handleAlert("error", '请完善用户信息')
          this.$loading.hide();
        }
      })
    },
    resetForm(temp) {
      if (this.dialogStatus == 'add') {
        var tempPid = this.temp.pid
        this.resetTemp()
        this.temp.pid = tempPid
      } else {
        this.temp.deptId = ''
        this.temp.contacts = ''
        this.temp.sex = 0,
          this.temp.mobile = ''
        this.temp.email = ''
        this.temp.address = ''
        this.temp.userType = 2
        this.temp.useFlag = 1
        this.$nextTick(function () {
          this.$refs.temp.clearValidate();
        })
      }
    },
    reset(formInline) {
      if (this.$refs[formInline].resetFields() !== undefined) {
        this.$refs[formInline].resetFields()
      }
      this.temp.grantPrice = true
      this.temp.useFlag = true
      this.currentPage = 1
      this.dataList()
    },
    selectAll(){
        this.checkAll = !this.checkAll;
        this.handSelectAll(this.resultList,this.checkAll)
    },
    handSelectAll(tableData,checkAll){
      tableData.forEach((row)=>{
        this.$refs.multipleTable.toggleRowSelection(row, checkAll);
        if(row.children!=undefined){
          this.handSelectAll(row.children,checkAll)
        }
      })
    },
    // 批量删除
    handleSelectionChange(val) {
      this.deleteList = val
    },
    batchDelClick() {
      var list = []
      if(this.deleteList.length == 0){
        handleAlert("error","未选中任何用户")
        return
      }
      this.deleteList.forEach(function (item) {
        list.push(item.id)
      })
      var params = new URLSearchParams()
      params.append('ids', JSON.stringify(list))
      this.$confirm('确定批量删除选中的行', '批量删除用户', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        userBatchDel(params).then(res => {
          if (res.data.code === 100) {
            handleAlert('success', res.data.msg)
            this.dataList()
          } else {
            handleAlert('error', res.data.msg)
          }
        })
      }).catch((error) => {
        handleAlert('info', '取消删除')
      })
    },
    // 附件上传
    onBeforeUpload(file) {
      var fileExt = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase();
      var text = ""
      const docExt = fileExt === 'xls'
      const docxExt = fileExt === 'xlsx'
      const isLimit = file.size / 1024 / 1024 < 2
      if (!docxExt) {
        text = "上传文件只能是xlsx格式!";
        handleAlert('warning', text)
        return false;
      }
      if (!isLimit) {
        text = "上传文件大小不能超过 2MB!";
        handleAlert('warning', text)
        return false;
      }
      return true;
    },
    onUploadChange(file) {
      //this.files.push(file.raw)

    },
    // 批量上传
    uploadFile(param) {
      var _this = this
      var formData = new FormData();
      formData.append('file', param.file);
      _this.$axios({
        method: 'post',
        url: sysServerUrl + 'sys/dealer/batchImport',
        data: formData
      }).then(res => {
        if (res.data.code === 100) {
          handleAlert('success', '批量导入成功')
          this.dataList()
        } else {
          _this.$alert(res.data.msg, '信息提示', { dangerouslyUseHTMLString: true })
        }
        _this.fileList = []
      }).catch(function (error) {
        _this.fileList = []
        _this.$alert('系统出现异常，导入失败', '信息提示', { dangerouslyUseHTMLString: true })
      })
    },
    handlesuccess(file, fileList) {
      this.form.image = file.data.fileName
    },
    handleRemove(file, fileList) {

    },
    handlePreview(file) {

    },
    handleExceed(files, fileList) {
      handleAlert('warning', `当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
    },
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除选择文件？`, '删除', { type: 'warning' });
    },
    uploadFileError(err, file, fileList) {
      handleAlert('error', '导入失败')
      this.$refs.upload.clearFiles();
    },
    // 批量下载
    batchExport() {
      var params = new URLSearchParams()
      params.append('realName', this.formInline.realName)
      params.append('username', this.formInline.loginName)
      params.append('defaultRoleCode', 'generalUser')
      params.append('defaultCountryCode', this.formInline.userCountry)
      params.append("userType",2)
      dealerBatchDown(params).then(res => {
        if (!res.data) {
          return
        }
        var name = "服务店信息列表.xlsx";
        var blob = new Blob([res.data]);
        var url = window.URL.createObjectURL(blob);
        var aLink = document.createElement("a");
        aLink.style.display = "none";
        aLink.href = url;
        aLink.setAttribute("download", name);
        document.body.appendChild(aLink);
        aLink.click();
        document.body.removeChild(aLink); //下载完成移除元素
        window.URL.revokeObjectURL(url); //释放掉blob对象
      })
    },
    // 下载模板
    downTemplateClick() {
      var params = '';
      downDealerTemplate(params).then(res => {
        if (!res.data) {
          return
        }
        var name = "服务店导入模板.xlsx";
        var blob = new Blob([res.data]);
        var url = window.URL.createObjectURL(blob);
        var aLink = document.createElement("a");
        aLink.style.display = "none";
        aLink.href = url;
        aLink.setAttribute("download", name);
        document.body.appendChild(aLink);
        aLink.click();
        document.body.removeChild(aLink); //下载完成移除元素
        window.URL.revokeObjectURL(url); //释放掉blob对象
      })
    },
    // 分配角色
    distriRole(row) {
      if (this.$refs.temp) {
        this.$nextTick(function () {
          this.$refs.temp.clearValidate();
        })
      }

      this.dialogStatus = 'assignRole'
      this.dialogFormVisible = true
      var params = '?id=' + row.id
      assignRole(params).then(res => {
        this.sltUserRole.defaultRoleCode = res.data.data.defaultRoleCode
        this.sltUserRole.userName = res.data.data.username;
        this.sltUserRole.roleCodeArr = res.data.data.roleList
        var roleCodeArr = res.data.data.roleList
        //获取当前用户选择的角色
        if (roleCodeArr != null && roleCodeArr.length > 0) {
          if (this.userRoleList.length > 0) {
            for (var i = 0; i < this.userRoleList.length; i++) {
              var curRoleCode = this.userRoleList[i].code
              if (roleCodeArr.indexOf(curRoleCode) > -1) {
                if (this.sltUserRole.roleCodeArr.indexOf(curRoleCode) == -1) {
                  this.sltUserRole.roleCodeArr.push(curRoleCode)
                }
              }
            }
          }
        }
      })

    },

    // 分配车型
    distriTrain(row) {
      this.filterText = ''
    if(this.userTrainTree == null || this.userTrainTree == undefined || this.userTrainTree.length == 0){
            handleAlert("error","系统尚无任何车型信息，请先创建车型信息！")
            return
          }
      // 获取用户已经关联的车型
      this.trainUser = row.username
      var params = new URLSearchParams()
      params.append('username', row.username)
      this.checkedTrain = []
      this.parentTrainIds = []
      if(row.pid && row.pid !=0){
        var parentTrainParam = new URLSearchParams()
        parentTrainParam.append('userId', row.pid)
        getUserTrainByUserId(parentTrainParam).then((res)=>{
          if(res.data.code = 100){
              let parentTrains = res.data.data
              for (let i = 0; i < parentTrains.length; i++) {
               this.parentTrainIds.push(parentTrains[i].trainId);
               }
               this.showTrainTree = JSON.parse(JSON.stringify(this.userTrainTree))
              this.handTreeDisable(this.showTrainTree)
              getUserTrain(params).then(res2 => {
            if (res2.data.code == 100) {
              let list = res2.data.data
              for (let i = 0; i < list.length; i++) {
               this.checkedTrain.push(list[i].trainId);
               }
          this.dialogTrainTreeVisible = true
          }
        })
        }
        })
      }else{
        this.showTrainTree = this.userTrainTree
      getUserTrain(params).then(res => {
        if (res.data.code == 100) {
          let list = res.data.data
          for (let i = 0; i < list.length; i++) {
            this.checkedTrain.push(list[i].trainId);
          }
          this.dialogTrainTreeVisible = true
        }
      })
    }
    },
    /**
     * 处理车型树禁用信息
     * @param {*} tree
     */
    handTreeDisable(tree){
        tree.forEach(f=>{
          if(f.children){
            this.handTreeDisable(f.children)
          }else{
            f.disabled = !this.parentTrainIds.includes(f.id)
          }
        })
    },
    // 获取选中
    getCheckedKeys() {
      let list = this.$refs.trainTree.getCheckedKeys()
      if (!list || list.length <= 0) {
        handleAlert("error", "请选择车型")
        return false
      }
      this.$loading.show()
      var params = new URLSearchParams()
      params.append('username', this.trainUser)
      params.append('trainIds', list.toString())
      updateUserTrain(params).then(res => {
        if (res.data.code == 100) {
          handleAlert("success", "成功")
          this.dialogTrainTreeVisible = false
        } else {
          handleAlert("error", "分配失败，" + res.data.msg)
        }
        this.$loading.hide()
      }).catch(e => {
        handleAlert("error", "系统开小差，请稍后再试")
        this.$loading.hide()
      })
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },

    // 分配国家
    distriCountry(row) {
      // this.$nextTick(function() {
      //   this.$refs.temp.clearValidate();
      // })
      this.sltUserCountry.countryArr = []
      this.sltUserCountry.defaultCountry = row.defaultCountryCode
      this.sltUserCountry.userName = row.username

      this.dialogStatus = 'assignCountry'
      this.dialogFormVisible = true
      getUserCountry(row.username).then(res => {
        let countryList = []
        res.data.data.forEach(item => {
          if (item.isDefault == 0) {
            countryList.push(item.countryCode)
          }
        })

        if (countryList && countryList.length > 0) {
          for (var i = 0; i < this.userCountryList.length; i++) {
            var curRoleCode = this.userCountryList[i].code
            if (countryList.indexOf(curRoleCode) > -1) {
              if (this.sltUserCountry.countryArr.indexOf(curRoleCode) == -1) {
                this.sltUserCountry.countryArr.push(curRoleCode)
              }
            }
          }
        }



      })
    },

    updateUserCountry() {
      var defaultCountry = this.sltUserCountry.defaultCountry
      var countryArr = this.sltUserCountry.countryArr
      if (defaultCountry == '') {
        handleAlert('error', '请选择所属国家')
        return false
      }
      let sltCountry = ''
      if (countryArr && countryArr.length > 0) {
        sltCountry = countryArr.toString()
      }
      this.$loading.show();
      var params = new FormData()
      params.append('username', this.sltUserCountry.userName)
      params.append('sltCountry', sltCountry)
      params.append('defaultCountry', defaultCountry)
      updatedCountry(params).then(res => {
        if (res.data.code === 100) {
          handleAlert('success', '分配成功')
          this.dataList()
          this.dialogFormVisible = false
        }
        this.$loading.hide();
      }).catch(function (error) {
        handleAlert('error', '分配失败')
        this.$loading.hide();
      })
    },

    updateUserRole() {
      var roleCode = this.sltUserRole.defaultRoleCode
      var sltRoleCodeArr = this.sltUserRole.roleCodeArr
      if (sltRoleCodeArr.length == 0) {
        handleAlert('error', '请选择角色')
        return false
      }
      if (roleCode == '') {
        handleAlert('error', '请选择默认角色')
        return false
      }
      var params = new FormData()
      params.append('userName', this.sltUserRole.userName)
      params.append('code', sltRoleCodeArr)
      params.append('defultCode', roleCode)
      updateAssignRole(params).then(res => {
        if (res.data.code === 100) {
          handleAlert('success', '分配成功')
          this.dataList()
          this.dialogFormVisible = false
        }
      }).catch(function (error) {
        handleAlert('error', '分配失败')
      })
    },
    resetCode() {
      this.sltUserRole = {
        userName: '',
        defaultRoleCode: 'generalUser',
        roleCodeArr: []
      }
    },
    // 密码重置
    initPsw(row) {
      this.$confirm('确定重置【' + row.username + '】的密码吗?', '重置密码', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var params = '?id=' + row.id
        userPasswordReset(params).then(res => {
          if (res.data.code === 100) {
            handleAlert('success', res.data.msg)
          } else if (res.data.code === 101) {
            handleAlert('error', res.data.msg)
          } else {
            handleAlert('error', res.data.msg)
          }
        })
      }).catch((error) => {
        handleAlert('info', '取消重置')
      })
    },
    // 编辑
    editopen(row) {
      var _this = this
      var searchParam =   new URLSearchParams()
      searchParam.append("id",row.id)
      getUserInfoId(searchParam).then((res)=>{
        if(res.data.code == 100){
          _this.dialogStatus = 'edit'
          _this.dialogFormVisible = true
          _this.resetTemp()
          var data = res.data.data
          _this.temp = Object.assign({}, data)
          if(row.pid && row.pid !=0){
            var tempData = _this.$refs.multipleTable.store.states.data

            for(let parent of tempData){
              if(parent.id == row.pid){
                if(parent.useFlag == 0){
                _this.temp.useFlagDisable = true
              }
              break
              }
            }
          }
          this.temp.grantPrice = data.grantPrice == 1
          this.temp.useFlag = data.useFlag == 1
          if (data === null || data.sex === null) {
            _this.temp.sex = 0
          } else {
            _this.temp.sex = data.sex
          }
        }else{
          handleAlert('error',res.data.msg)
        }
        setTimeout(() => {
          _this.getCity()
          _this.$refs.modelSelectTree.initSelected(_this.temp.cityName, _this.temp.cityId)
        })
      })



      // _this.temp.deptId = String(row.deptId)
      // setTimeout(function(){
      //   if(row.deptName){
      //     _this.$refs.modelSelectTree.initSelected(row.deptName.split(' ')[1], _this.temp.deptId)
      //   }

      // });

    },

    // 启用 停用
    editUseFlag(row, type){
      // console.log("row", row);
      var params = new URLSearchParams()
      params.append('id', row.id)
      params.append('realName', row.realName)
      params.append('username', row.username)
      params.append('useFlag', type)
      params.append('userType', 2)
      if (row.cityId && row.cityId != 'null' && row.cityId != 'undefined') {
        params.append('cityId', row.cityId)
      }
      params.append('defaultCountryCode', row.defaultCountryCode)
      params.append('defaultRoleCode', 'generalUser')
      userEdit(params).then(res => {
        if (res.data.code === 100) {
          handleAlert('success', '操作成功')
          this.dataList(this.temp.pid)
          // this.dialogFormVisible = false
        } else {
          handleAlert('error', res.data.msg)
        }
      }).catch(e => {

      })
    },

    // 提交编辑
    editClick(temp) {
      this.$refs[temp].validate((valid) => {
        if (valid) {
          this.$loading.show()
          var params = new URLSearchParams()
          params.append('id', this.temp.id)
          params.append('realName', this.temp.realName)
          params.append('username', this.temp.username)
          params.append('defaultRoleCode', 'generalUser')
          if (this.temp.cityId && this.temp.cityId != 'null' && this.temp.cityId != 'undefined') {
            params.append('cityId', this.temp.cityId)
          }

          params.append('contacts', this.temp.contacts)
          params.append('sex', this.temp.sex)
          params.append('mobile', this.temp.mobile)
          params.append('email', this.temp.email)
          params.append('address', this.temp.address)
          params.append('userType', 2)
          params.append('useFlag', this.temp.useFlag?1:0)
          params.append('defaultCountryCode', this.temp.defaultCountryCode)
          params.append("grantPrice",this.temp.grantPrice?1:0)
          userEdit(params).then(res => {
            if (res.data.code === 100) {
              handleAlert('success', '保存成功')
              this.dataList(this.temp.pid)
              this.dialogFormVisible = false
            } else {
              handleAlert('error', res.data.msg)
            }
            this.$loading.hide()
          })
        } else {
          handleAlert('error', '请完善用户信息')
          this.$loading.hide()
        }
      })
    },
    // 删除
    delectClick(row) {
      this.$confirm('确定删除【' + row.realName + '】的相关信息?', '删除用户', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        userDel(row.id).then(res => {
          if (res.data.code === 100) {
            handleAlert('success', '删除成功')
            if (this.resultList != null && this.resultList.length == 1) {
              this.currentPage = this.currentPage > 1 ? this.currentPage - 1 : this.currentPage
            }
            this.dataList(row.pid)
          } else {
            handleAlert('error', res.data.msg)
          }
        })
      }).catch((error) => {
        handleAlert('info', '取消删除')
      })
    },

    // 获取 品牌-车型 结构树
    getUserTrainTree() {
      getUserBrandTrainTree().then(res => {
        this.userTrainTree = res.data.data
      })
    },
    load(tree, treeNode, resolve) {
      this.resolveMap.set(tree.id+"",{tree,treeNode,resolve})
      var params = new URLSearchParams()
      params.append('pid', tree.id)

      // var params = {
      //   page: this.currentPage,
      //   limit: this.pagesize,
      //   realName: this.formInline.realName,
      //   username: this.formInline.loginName,
      //   defaultRoleCode: this.formInline.userrole,
      //   defaultCountryCode: this.formInline.userCountry,
      // }
      dealerChildData(params).then(res => {
        if (res.data.code === 100) {
        resolve(res.data.data)
        tree.children = res.data.data
        }else{
          handleAlert('error', '请求失败，请稍后重试')
        }
      })
    },
    maximumArea() {
      setTimeout(() => {
        tableHeight()
        setTimeout(() => {
          this.maximumHeight = tableMaxHeight;
        }, 50)
      })
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    },


    addSubAccount(row){
      this.$prompt('请输入添加的个数', '批量添加【'+row.username+'】的二级账户', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputValue:4,
          inputPattern: /^[1-9]\d*$/,
          inputErrorMessage: '请输入大于0的正整数'
        }).then(({ value }) => {
          var params = new URLSearchParams()
          params.append('username', row.username)
          params.append('number', value)
          dealerBatchSubAccount(params).then(res => {
            if(res.data.code == 100){
              handleAlert("success", "操作成功")
              this.dataList()
            }else{
              handleAlert("error", res.data.msg)
            }
          }).catch(e => {
            handleAlert("error", "系统繁忙，请稍后再试")
          })
        }).catch(() => {

        });

    }


  },
  mounted() {
    this.tableHeightArea()
    let tt = this.$store.state.perms.indexOf('menuAsimss1A2B_105')
    this.dataList()
    this.getUserRoleList()
    this.getUserCountryList()
    this.getUserTrainTree()
    window.addEventListener('keydown', this.keyDown)
  },
  destroyed() {
    window.removeEventListener('keydown', this.keyDown, false)
  },
}
</script>
