<template>
  <div class="layoutContainer">
    <div class="infoDetail">
      <el-row>
        <el-col :span="7" class="leftData">
          <div>
            <div class="topButton" v-if="hasPerm('menuAsimss1A6B_101') || hasPerm('menuAsimss1A6B_102')">
              <el-button type="text" v-if="hasPerm('menuAsimss1A6B_101')" icon="el-icon-plus" @click="handelAddDept">新增部门</el-button>
              <el-button type="text" v-if="hasPerm('menuAsimss1A6B_101') && isDept" icon="el-icon-plus" @click="handelAddGroup">新增小组</el-button>
              <el-button type="text" v-if="hasPerm('menuAsimss1A6B_102')" icon="el-icon-delete" @click="handelDel">删除</el-button>
            </div>
            <div class="scrollClass">
              <el-scrollbar>
                <el-tree ref="tree" node-key="id" :default-expanded-keys="nodeKeyList" :data="listdata" :props="defaultProps" @node-click="handleNodeClick"></el-tree>
              </el-scrollbar>
            </div>
          </div>
        </el-col>
        <el-col :span="17" class="fromRight">
          <div class="formTitle" v-html="editTitle"></div>
          <el-form ref="form" :label-width="formLabelWidth" :model="form" :rules="rules">
            <el-form-item label="名称" prop="name">
              <el-input placeholder="请输入名称" v-model.trim='form.name' show-word-limit maxlength="50"></el-input>
            </el-form-item>
            <el-form-item label="类型" prop="type">
              <el-select v-model="form.type" :disabled="true">
                <el-option label="部门" value="dept"></el-option>
                <el-option label="小组" value="group"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="排序" prop="sort">
              <el-input type="number" placeholder="请输入排序"  :min="1" :max="9999"  @input="e => form.sort=parserNumber(e,1,9999)" v-model.trim="form.sort"></el-input>
            </el-form-item>
            <el-form-item label="备注" prop="remark">
              <el-input placeholder="请输入备注" v-model.trim='form.remark' show-word-limit maxlength="100"></el-input>
            </el-form-item>
            <el-form-item class="butArea">
             <el-button v-show="butType===''"  v-if="hasPerm('menuAsimss1A6B_101') || hasPerm('menuAsimss1A6B_103')" type="primary" @click="preserve('form')">保存</el-button>
              <el-button v-show="butType==='addNodeBut'" type="primary" @click="determine('form')">确定</el-button>
              <!-- <el-button v-show="butType==='addNodeBut'" @click="resetForm()">重置</el-button> -->
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>
import { contentSize, handleAlert } from '@/assets/js/common.js'
import { departmentData, departmentType, departmentTeam, departmentAdd, departmentEdit, departmentDel } from '@/api/sysmgt.js'

export default {
  name: 'sysmgt_department_list',
  data(){
    return{
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      parentId:"",
      nodeKeyList:[],
      listdata: [],
      addType: '',
      butType: '',
      formLabelWidth: '100px',
      editTitle: '当前信息',
      isDept: false,  // 2023-02-08 当点击部门时，出现 新增小组
      form:{
        id:"",
        pid:"",
        type:"",
        name:"",
        remark:"",
        sort:1,
        children:[],
      },
      rules: {
        name: [{ required: true, message: '名称不能为空', trigger: ['blur', 'change'] }],
      },
    }
  },
  methods:{
    // 数据
    dataList(){
      departmentData().then(res => {
        this.listdata = res.data.data
        this.$nextTick(() => {
          this.$refs.tree.setCurrentKey(this.form.id);
        });
      })
    },
    resetTemp(){
      this.form = {
        id:"",
        pid:"",
        type:"",
        name:"",
        remark:"",
        sort:1,
        children:[],
      }
      this.$nextTick(function() {
        this.$refs.form.clearValidate();
      })
    },
    // 当前信息
    assignment(data){
      this.butType = ''
      this.editTitle = "当前目录节点信息"
      this.form.id = data.id
      this.parentId = data.id
      this.form.pid = data.pid
      this.form.type = data.type
      this.form.name = data.name
      this.form.sort = data.sort
      this.form.remark = data.remark
      this.form.children = data.children
    },
    handleNodeClick (data){
      this.resetTemp()
      this.assignment(data)
      // 当点击部门时，显示新增小组
      this.isDept = data.type == 'dept'
    },
    // 新增部门
    handelAddDept(){
      this.resetTemp()
      this.addType="dept"
      this.butType='addNodeBut'
      this.editTitle= '添加部门节点'
      this.form.type="dept"
    },
    // 新增小组
    handelAddGroup(){
      if(this.form.pid === ''){
        handleAlert('warning', '请先选中父级节点')
        return false
      }
      this.addType="group"
      this.resetTemp()
      this.butType='addNodeBut'
      this.editTitle= '添加小组节点'
      this.form.type="group"
    },
    //确定
    determine(form){
      this.nodeKeyList = []
      this.$refs[form].validate((valid) => {
        if(valid){
          this.$loading.show();
          var params = new URLSearchParams()
          if(this.addType == 'dept'){
            params.append("pid", 0)
          }else{
            params.append("pid", this.parentId)
          }
          params.append("name", this.form.name)
          params.append("remark", this.form.remark)
          params.append("sort", this.form.sort)
          departmentAdd(params).then(res => {
            if(res.data.code==100){
              handleAlert('success','新增成功')
              this.nodeKeyList.push(this.parentId)
              this.resetTemp()
              this.butType = ''
              this.editTitle = "当前目录节点信息"
              this.dataList()
            }else{
              handleAlert('error',res.data.msg)
            }
            this.$loading.hide();
          }).catch(function(error){
            handleAlert('error','新增节点信息失败')
            this.$loading.hide();
          })
        }
      })
    },
    // 保存
    preserve(form){
      this.nodeKeyList = []
      this.$refs[form].validate((valid) => {
        if(valid){
          this.$loading.show();
          var params = new URLSearchParams()
          params.append("id", this.form.id)
          params.append("pid", this.form.pid)
          params.append("type", this.form.type)
          params.append("name", this.form.name)
          params.append("remark", this.form.remark)
          params.append("sort", this.form.sort)
          departmentEdit(params).then(res => {
            if(res.data.code==100){
              handleAlert('success','保存成功')
              this.nodeKeyList.push(this.form.id)
              this.butType = ''
              this.editTitle = "当前目录节点信息"
              this.dataList()
            }else{
              handleAlert('error',res.data.msg)
            }
            this.$loading.hide();
          }).catch(function(error){
            handleAlert('error','保存节点信息失败')
            this.$loading.hide();
          })
        }
      })
    },
    // 重置
    resetForm(){
      this.resetTemp()
    },
    // 删除
    handelDel(){

      if(this.form.id == null || this.form.id.length<=0){
        handleAlert('warning','请先选中要删除的节点')
        return false
      }
      this.nodeKeyList = []
      if(this.form.children !== null && this.form.children.length>0){
        handleAlert('error','有子节点无法删除')
        return
      }
      var id = this.form.id
      this.$confirm('确定删除【'+this.form.name+'】的目录节点信息?','删除目录节点',{
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        departmentDel(id).then(res => {
          if(res.data.code == 100){
            handleAlert('success', '删除成功')
            this.nodeKeyList.push(this.form.pid)
            this.resetTemp()
            this.butType = ''
            this.editTitle = "当前目录节点信息"
            this.dataList()
          }else{
            handleAlert('error', res.data.msg)
          }
        })
      }).catch((error) => {
        handleAlert('info','取消删除')
      })
    },
  },
  mounted(){
    this.dataList()
    contentSize()
  },
}
</script>
<style>
</style>
