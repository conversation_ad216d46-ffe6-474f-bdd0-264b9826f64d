<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" ref="formInline" label-width="70px" :model="formInline" class="demo-form-inline">
        <el-form-item label="姓名" prop="realName">
          <el-input v-model.trim="formInline.realName" placeholder="请输入姓名"></el-input>
        </el-form-item>
        <el-form-item label="登录账户" prop="loginName">
          <el-input v-model.trim="formInline.loginName" placeholder="请输入登录账号"></el-input>
        </el-form-item>
        <el-form-item label="角色" prop="userrole">
          <el-select v-model="formInline.userrole" clearable filterable>
            <el-option v-for="(item,index) in userRoleList" :key="index" :label="item.name" :value="item.code"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属国家" prop="userCountry">
          <el-select v-model="formInline.userCountry" clearable filterable>
            <el-option v-for="(item,index) in userCountryList" :key="index" :label="item.name" :value="item.code"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit" icon="el-icon-search">搜索</el-button>
          <el-button plain @click="reset('formInline')">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <div class="tableHandle"
        v-if="hasPerm('menuAsimss1A2B_101') || hasPerm('menuAsimss1A2B_102') || hasPerm('menuAsimss1A2B_107') || hasPerm('menuAsimss1A2B_108')"
      >
        <el-button type="text" v-if="hasPerm('menuAsimss1A2B_101')" icon="el-icon-plus" @click="addClcik()">新增</el-button>
        <!-- <el-button type="text" v-if="hasPerm('menuAsimss1A2B_102')" icon="el-icon-delete" @click="batchDelClick()">批量删除</el-button> -->
        <el-upload
          class="upload-demo inline-block"
          ref="elUpload"
          action="#"
          :show-file-list="false"
          multiple
          :limit="1"
          :file-list="fileList"
          :before-upload="onBeforeUpload"
          :http-request="uploadFile"
          :on-change="onUploadChange"
        >
          <el-button type="text" v-if="hasPerm('menuAsimss1A2B_107') && userInfo.defaultRoleCode != 'generalUser'" size="min" icon="bulkImport-icon">批量上传</el-button>
        </el-upload>
        <el-button type="text" v-if="hasPerm('menuAsimss1A2B_108')" icon="bulkDown-icon" @click="batchExport()">批量下载</el-button>
        <el-button type="text" v-if="hasPerm('menuAsimss1A2B_108')  && userInfo.defaultRoleCode != 'generalUser'" icon="el-icon-download" @click="downTemplateClick()">下载模板</el-button>
      </div>
      <el-table
        style="width:100%"
        border
        stripe
        ref="table"
        highlight-current-row
        :max-height="maximumHeight"
        :data="resultList"
        :header-cell-style="{
          'text-align': 'center',
          'background-color': 'var(--other-color)',
        }"
        @header-dragend="changeColWidth"
        @selection-change="handleSelectionChange"
      >
        <!-- <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column> -->
        <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
        <el-table-column label="姓名" prop="realName" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column label="登录账户" prop="username" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column label="默认角色" prop="defaultRoleName" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column label="所属国家" prop="defaultCountryName" min-width="100" align="center" show-overflow-tooltip></el-table-column>
        <el-table-column label="所属部门" prop="deptName" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column label="联系人" prop="contacts" min-width="100" show-overflow-tooltip></el-table-column>
        <!-- <el-table-column label="性别" prop="sex" width="70">
          <template slot-scope="{row}">
            <span v-if="row.sex === 0">未知</span>
            <span v-if="row.sex === 1">男</span>
            <span v-if="row.sex === 2">女</span>
          </template>
        </el-table-column> -->
        <el-table-column label="手机号" prop="mobile"  align="center" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column label="邮箱" prop="email" min-width="150" show-overflow-tooltip></el-table-column>
        <el-table-column label="地址" prop="address" min-width="150" show-overflow-tooltip></el-table-column>
        <!-- <el-table-column label="类型" prop="userType" width="60" align="center">
          <template slot-scope="{row}">
            <span v-if="row.userType === 2">前台</span>
            <span v-if="row.userType === 1">后台</span>
          </template>
        </el-table-column> -->
        <el-table-column label="状态" prop="useFlag" width="60" align="center">
          <template slot-scope="{row}">
            <span v-if="row.useFlag === 1" style="color:#009933">生效</span>
            <span v-if="row.useFlag === 0" style="color:#c30000">失效</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="350">
          <template slot-scope="{row}">
            <el-button v-if="hasPerm('menuAsimss1A2B_105')" type="text" size="small" @click="distriRole(row)">分配角色</el-button>
            <el-button v-if="hasPerm('menuAsimss1A2B_105')" type="text" size="small" @click="distriTrain(row)">分配车型</el-button>
            <el-button v-if="hasPerm('menuAsimss1A2B_105') && row.userType==1" type="text" size="small" @click="distriCountry(row)">分配国家</el-button>
            <el-button v-if="hasPerm('menuAsimss1A2B_103')" type="text" size="small" @click="initPsw(row)">重置密码</el-button>
            <el-button v-if="hasPerm('menuAsimss1A2B_103')"  type="text" size="small" @click="editopen(row)">编辑</el-button>
            <el-button type="text" v-if="hasPerm('menuAsimss1A2B_102')" size="small" @click="delectClick(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total>0" :total="total" :page.sync="currentPage" :limit.sync="pagesize" @pagination="dataList"/>
      <!-- 新增，编辑 -->
      <el-dialog v-dialogDrag lock-scroll :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" :close-on-click-modal="false">
        <!-- 新增，编辑 -->
        <el-form v-if="dialogStatus === 'edit' || dialogStatus === 'add'" :label-width="formLabelWidth" ref='temp' :rules="fromTemp" :model="temp" label-position="center" :validate-on-rule-change="false">
          <el-form-item label="登录账户" prop="username">
            <el-input v-if="dialogStatus === 'add' " v-model.trim="temp.username" placeholder="请输入账号" show-word-limit maxlength="20"></el-input>
            <el-input v-if="dialogStatus === 'edit' && temp.username !== ''"  placeholder="请输入账号" v-model.trim="temp.username" show-word-limit maxlength="20" :disabled="true" ></el-input>
            <el-input v-if="dialogStatus === 'edit' && temp.username === ''" placeholder="请输入账号" v-model.trim="temp.username" show-word-limit maxlength="20"></el-input>
          </el-form-item>
          <el-form-item label="姓名" prop="realName">
            <el-input v-model.trim="temp.realName" placeholder="请输入姓名" show-word-limit maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="默认角色" prop="defaultRoleCode">
            <el-select v-model="temp.defaultRoleCode" clearable filterable>
              <el-option v-for="(item,index) in userRoleList" :key="index" :label="item.name" :value="item.code"></el-option>
            </el-select>
          </el-form-item>

          <!-- 2023-08-01 添加默认国家 -->
          <el-form-item label="所属国家" prop="defaultCountryCode">
            <el-select v-model="temp.defaultCountryCode" clearable filterable>
              <el-option v-for="(item,index) in userCountryList" :key="index" :label="item.name" :value="item.code"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="所属部门" prop="deptId">
            <select-tree ref="modelSelectTree"
              :options="departmentList"
              v-model.trim="temp.deptId"
              :props="defaultProps"
              @getCurrentNode="getCurrentNode"
              placeholder="请选择部门" />
          </el-form-item>
          <el-form-item label="联系人" prop="contacts">
            <el-input v-model.trim="temp.contacts" show-word-limit maxlength="20"  placeholder="请输入联系人"></el-input>
          </el-form-item>
          <el-form-item label="性别" prop="sex">
            <el-radio-group  v-model="temp.sex">
              <el-radio :label="1">男</el-radio>
              <el-radio :label="2">女</el-radio>
              <el-radio :label="0">未知</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="手机号" prop="mobile">
            <el-input v-model.trim="temp.mobile" maxlength="11" placeholder="请输入手机号"></el-input>
          </el-form-item>
          <el-form-item label="邮箱" prop="email">
            <el-input v-model.trim="temp.email"  placeholder="请输入邮箱"></el-input>
          </el-form-item>
          <el-form-item label="地址" prop="address">
            <el-input rows="2" v-model.trim="temp.address"  show-word-limit maxlength="100" placeholder="请输入地址"></el-input>
          </el-form-item>
          <!-- <el-form-item label="类型" prop="userType">
           <el-radio-group v-model="temp.userType" @change="changeUserType">
            <el-radio :label="2">前台</el-radio>
            <el-radio :label="1">后台</el-radio>
          </el-radio-group>
          </el-form-item> -->
          <el-form-item label="生效状态" prop="useFlag">
           <el-switch v-model="whether"></el-switch>
          </el-form-item>
          <div class="submitArea">
            <el-button type="primary" @click="dialogStatus === 'edit' ? editClick('temp') : addClick('temp')">
              立即提交
            </el-button>
            <el-button @click="resetForm('temp')">
              重置
            </el-button>
          </div>
        </el-form>
        <!-- 分配角色 -->
        <el-form v-if="dialogStatus === 'assignRole'" ref="alloter" :label-width="formLabelWidth" :model="sltUserRole" :validate-on-rule-change="false">
          <el-form-item label="默认角色" prop="defaultRoleCode">
            <el-radio-group v-model="sltUserRole.defaultRoleCode">
              <el-radio v-for="(item, cindex) in userRoleList" :key="cindex"  :label="item.code" >{{item.name}}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="选择角色">
            <el-checkbox-group v-model="sltUserRole.roleCodeArr">
              <el-checkbox v-for=" (item, cindex) in userRoleList" :key="cindex"  :label="item.code" >{{item.name}}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <div class="submitArea">
            <el-button type="primary" @click="updateUserRole()">
              立即提交
            </el-button>
            <el-button @click="resetCode()">
              重置
            </el-button>
          </div>
        </el-form>
        <!-- 分配国家 -->
        <el-form v-if="dialogStatus === 'assignCountry'" :label-width="formLabelWidth" ref="alloter" :model="sltUserCountry" :validate-on-rule-change="false">
          <!-- <el-form-item label="所属国家" prop="defaultCountry">
            <el-radio-group v-model="sltUserCountry.defaultCountry">
              <el-radio v-for="(item, cindex) in userCountryList" :key="cindex"  :label="item.code" >{{item.name}}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="授权国家">
            <el-checkbox-group v-model="sltUserCountry.countryArr">
              <el-checkbox v-for=" (item, cindex) in userCountryList" :key="cindex"  :label="item.code" >{{item.name}}</el-checkbox>
            </el-checkbox-group>
          </el-form-item> -->
          <el-form-item label="授权国家">
            <el-select v-model="sltUserCountry.countryArr" multiple filterable placeholder="请授权">
              <el-option
                v-for="(item, cindex) in userCountryList"
                :key="cindex"
                :label="item.name"
                :value="item.code">
                <span class="checkbox"></span>
                <span class="label-name-box" style="margin-left: 8px;">{{ item.name }}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <div class="submitArea">
            <el-button type="primary" @click="updateUserCountry()">
              立即提交
            </el-button>
            <el-button @click="resetCode()">
              重置
            </el-button>
          </div>
        </el-form>
      </el-dialog>

      <!-- 分配车型 -->
      <el-dialog v-dialogDrag lock-scroll title="分配车型" :visible.sync="dialogTrainTreeVisible" v-if="dialogTrainTreeVisible" :close-on-click-modal="false">
        <div style="height: 300px; width: 100%; overflow-y:auto;">
          <el-input placeholder="输入关键字进行过滤" v-model="filterText"></el-input>
          <el-tree
            :data="userTrainTree"
            show-checkbox
            node-key="id"
            ref="trainTree"
            :default-expand-all="true"
            :check-on-click-node="true"
            :default-checked-keys="checkedTrain"
            :filter-node-method="filterNode"
            :props="defaultTrainProps">
          </el-tree>
        </div>
        <div style="width: 100%; display: flex;  justify-content: center;  align-items: center;">
          <el-button type="primary" @click="getCheckedKeys()">立即提交</el-button>
          <el-button @click="dialogTrainTreeVisible=false">取消</el-button>
        </div>
      </el-dialog>

    </div>
  </div>
</template>
<script>
import { sysServerUrl, handleAlert, tableHeight, tableMaxHeight } from '@/assets/js/common.js'
import Pagination from '@/components/Pagination'
import SelectTree from '@/components/TreeView/SelectTree.vue'
import {
  userData,
  userDataList,
  assignRoleData,
  userAdd,
  userEdit,
  userDel,
  userDown,
  downTemplate,
  userBatchDel,
  assignRole,
  updateAssignRole,
  userPasswordReset,
  departmentData,
  userCountryData,
  getUserCountry,
  updatedCountry,
  getUserBrandTrainTree,
  getUserTrain,
  updateUserTrain,
  getUserInfo,
} from '@/api/sysmgt.js'
export default {
  name: 'sysmgt_user_list',
  components: { Pagination, SelectTree },
  data () {
    return {
      formInline: {
        realName: '',
        loginName: '',
        userrole: '',
        userCountry: ''
      },
      temp: {
        id: '',
        realName: '',
        username: '',
        defaultRoleName: '',
        defaultRoleCode: '',
        defaultCountryCode: '',
        defaultCountryName: '',
        deptId:'',
        deptName:'',
        contacts: '',
        sex: '',
        mobile: '',
        email: '',
        address: '',
        userType: 1,
        useFlag: 1
      },
      perms:[],
      sltUserRole: {
        userName: '',
        defaultRoleCode: '',
        roleCodeArr: []
      },
      sltUserCountry: {
        userName: '',
        defaultCountry: '',
        countryArr: []
      },
      dialogFormVisible: false,
      formLabelWidth: '100px',
      resultList: [],
      dialogStatus: '',
      textMap: {
        edit: '用户编辑',
        add: '新增用户',
        restPwd: '重置密码',
        assignRole: '分配角色',
        assignCountry: '分配国家',
      },
      defaultProps: {
        parent: 'pid',
        value: 'id',
        label: 'name',
        children: 'children',
        disabled:function(val){
          if(val.children == null){
            return false
          }else {
            return true
          }
        }
      },
      deleteList: [],
      aboriginalRow: {},
      userInfo:{},
      pagesize: 20,
      currentPage: 1,
      total: 0,
      whether: false,
      userRoleList: [],
      userCountryList: [],
      fileList: [],
      uploadFileList: [],
      fromTemp: {
        username: [{ required: true, message: '账号不能为空', trigger: ['blur', 'change'] }],
        realName: [{ required: true, message: '姓名不能为空', trigger: ['blur', 'change'] }],
        defaultRoleCode: [{ required: true, message: '默认角色不能为空', trigger: ['blur', 'change'] }],
        mobile: [{ pattern: /^1\d{10}$/, message: '请输入正确的手机格式', trigger: ['blur', 'change'] }],
        email: [{ type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }],
        defaultCountryCode: [{ required: true, message: '默认国家不能为空', trigger: ['blur', 'change']}]
        // deptId: [{ required: true, message: '所属部门不能为空', trigger: ['blur', 'change']}]
      },
      departmentList: [],

      // 车型和人员的关联
      filterText: '',
      trainUser: '',
      userTrainTree: [],
      checkedTrain: [],
      dialogTrainTreeVisible: false,
      defaultTrainProps: {
        children: 'children',
        label: 'name'
      },
      maximumHeight: 0,
    }
  },
  watch: {
    filterText(val) {
      this.$refs.trainTree.filter(val);
    },
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth(){
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    // 列表数据
    dataList () {
      this.$loading.show();
      var params = new URLSearchParams()
      params.append('page', this.currentPage)
      params.append('limit', this.pagesize)
      params.append('realName', this.formInline.realName)
      params.append('username', this.formInline.loginName)
      params.append('defaultRoleCode', this.formInline.userrole)
      params.append('defaultCountryCode', this.formInline.userCountry)
      userDataList(params).then(res=>{
        if (res.data.code == 100) {
          this.total = res.data.total
          this.resultList = res.data.data
        } else {
          handleAlert("error", res.data.msg)
        }
        this.tableHeightArea()
        this.$loading.hide();
      })
    },
    // 搜索
    keyDown(e) {
      if (e.keyCode === 13) {
        this.onSubmit()
      }
    },
    onSubmit () {
      this.currentPage=1
      this.dataList()
    },
    // 角色信息
    getUserRoleList () {
      assignRoleData().then(res=>{
        this.userRoleList = res.data.data
      })
    },
    // 获取国家
    getUserCountryList(){
      userCountryData().then(res=>{
        this.userCountryList = res.data.data
      })
    },
    // 所属部门
    getDepartment(){
      departmentData().then(res => {
        if(res.data.data){
          this.departmentList = res.data.data
        }else{
          this.departmentList = []
        }
      })
    },
    resetTemp () {
      this.temp = {
        id: '',
        realName: '',
        username: '',
        defaultRoleName: '',
        defaultRoleCode: '',
        defaultCountryCode: '',
        deptId: '',
        deptName:'',
        contacts: '',
        sex: '',
        mobile: '',
        email: '',
        address: '',
        userType: 1,
        useFlag: 1
      }
      this.whetherState()
      this.$nextTick(function() {
        this.$refs.temp.clearValidate();
      })
    },
    whetherState(){
      if (this.temp.useFlag === 0) {
        this.whether = false
      } else {
        this.whether = true
      }
    },
    selectState () {
      if (this.whether === false) {
        this.temp.useFlag = 0
      } else {
        this.temp.useFlag = 1
      }
    },
    changeUserType(val){
      this.temp.userType=val
    },
    // 新增
    addClcik () {
      var _this = this
      _this.dialogStatus = 'add'
      _this.dialogFormVisible = true
      setTimeout(() => {
        _this.$refs.modelSelectTree.initSelected('','')
        _this.resetTemp()
      })
    },
    getCurrentNode(node){
      if(node != null){
        this.$refs['temp'].validateField('deptId')
      }
    },
    addClick (temp) {
      this.$refs[temp].validate((valid) => {
        if (valid) {
          this.selectState()
          this.$loading.show()
          var params = new URLSearchParams()
          params.append('realName', this.temp.realName)
          params.append('username', this.temp.username)
          params.append('defaultRoleCode', this.temp.defaultRoleCode)
          params.append('defaultCountryCode', this.temp.defaultCountryCode)
          if (this.temp.deptId && this.temp.deptId != 'null' && this.temp.deptId != 'undefined') {
            params.append('deptId', this.temp.deptId)
          }
          params.append('email', this.temp.email)
          params.append('address', this.temp.address)
          params.append('contacts', this.temp.contacts)
          params.append('mobile', this.temp.mobile)
          params.append('useFlag', this.temp.useFlag)
          params.append('userType', 1)
          params.append('sex', this.temp.sex)
          userAdd(params).then(res => {
            if(res.data.code === 100){
              handleAlert("success", res.data.msg)
              this.dataList()
              this.dialogFormVisible = false
            }else{
              handleAlert("error", res.data.msg)
            }
            this.$loading.hide()
          })
        } else {
          handleAlert("error", '请完善用户信息')
          this.$loading.hide()
        }
      })
    },
    resetForm (temp) {
      if(this.dialogStatus == 'add'){
        this.resetTemp()
      } else {
        // this.temp.deptId=''
        // this.temp.contacts= ''
        // this.temp.sex= 0,
        // this.temp.mobile= ''
        // this.temp.email= ''
        // this.temp.address= ''
        // this.temp.userType= 1
        // this.temp.useFlag= 1
        // this.whetherState()
        // this.$nextTick(function() {
        //   this.$refs.temp.clearValidate();
        // })
        let _this = this
        _this.temp = Object.assign({}, _this.aboriginalRow)
        if (row.useFlag === 0) {
          _this.whether = false
        } else {
          _this.whether = true
        }
        if (row === null || row.sex === null) {
          _this.temp.sex = 0
        } else {
          _this.temp.sex = row.sex
        }
        if(row.deptId){
            _this.temp.deptId = String(row.deptId)
          let depN = row.deptName.split(' ')[1]
          if (!depN || depN.trim().length <= 0) {
            depN = row.deptName
          }
          setTimeout(function(){
            if(row.deptName){
              _this.$refs.modelSelectTree.initSelected(depN, _this.temp.deptId)
            }else{
              _this.$refs.modelSelectTree.initSelected('', '')
            }

          });
        }
      }
    },
    reset (formInline) {
      if (this.$refs[formInline].resetFields() !== undefined) {
        this.$refs[formInline].resetFields()
      }
      this.currentPage = 1
      this.dataList()
    },
    // 批量删除
    handleSelectionChange (val) {
      this.deleteList = val
    },
    // batchDelClick () {
    //   var list = []
    //   this.deleteList.forEach(function (item) {
    //     list.push(item.id)
    //   })
    //   var params = new URLSearchParams()
    //   params.append('ids', JSON.stringify(list))
    //   this.$confirm('确定批量删除选中的行', '批量删除用户', {
    //     confirmButtonText: '确定',
    //     cancelButtonText: '取消',
    //     type: 'warning'
    //   }).then(() => {
    //     // userBatchDel(params).then(res=>{
    //     //   if (res.data.code === 100) {
    //     //     handleAlert('success',res.data.msg)
    //     //     this.dataList()
    //     //   }else{
    //     //     handleAlert('error',res.data.msg)
    //     //   }
    //     // })
    //   }).catch((error) => {
    //     handleAlert('info','取消删除')
    //   })
    // },
    // 附件上传
    onBeforeUpload(file) {
      var fileExt =file.name.substring(file.name.lastIndexOf('.')+1).toLowerCase();
      var text=""
      const docExt = fileExt === 'xls'
      const docxExt = fileExt === 'xlsx'
      const isLimit = file.size / 1024 / 1024 < 2
      if(!docxExt) {
        text="上传文件只能是xlsx格式!";
        handleAlert('warning',text)
        return false;
      }
      if (!isLimit) {
        text="上传文件大小不能超过 2MB!";
        handleAlert('warning',text)
        return false;
      }
      return true;
    },
    onUploadChange(file){
      //this.files.push(file.raw)

    },
    // 批量上传
    uploadFile (param) {
      var _this= this
			var formData = new FormData();
			formData.append('file', param.file);
      formData.append('type', "1");
      _this.$axios({
        method: 'post',
        url: sysServerUrl + 'sys/user/batchImport',
        data: formData
      }).then(res => {
        if (res.data.code === 100) {
          handleAlert('success','批量导入成功')
          this.dataList()
        }else{
          _this.$alert(res.data.msg,'信息提示',{dangerouslyUseHTMLString:true})
        }
        _this.fileList = []
      }).catch(function(error){
        _this.fileList = []
        _this.$alert('系统出现异常，导入失败','信息提示',{dangerouslyUseHTMLString:true})
      })
		},
    handlesuccess (file, fileList) {
      this.form.image = file.data.fileName
    },
    handleRemove (file, fileList) {

    },
    handlePreview (file) {

    },
    handleExceed (files, fileList) {
      handleAlert('warning',`当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
    },
    beforeRemove (file, fileList) {
      return this.$confirm(`确定移除选择文件？`, '删除',{type: 'warning'});
    },
    uploadFileError(err, file, fileList) {
      handleAlert('error','导入失败')
      this.$refs.upload.clearFiles();
    },
    // 批量下载
    batchExport () {
      var params = new URLSearchParams()
      params.append('realName', this.formInline.realName)
      params.append('username', this.formInline.loginName)
      params.append('defaultRoleCode', this.formInline.userrole)
      params.append('defaultCountryCode', this.formInline.userCountry)
      userDown(params).then(res => {
        if(!res.data){
          return
        }
        var name = "用户信息列表.xlsx";
        var blob = new Blob([res.data]);
        var url = window.URL.createObjectURL(blob);
        var aLink = document.createElement("a");
        aLink.style.display = "none";
        aLink.href = url;
        aLink.setAttribute("download", name);
        document.body.appendChild(aLink);
        aLink.click();
        document.body.removeChild(aLink); //下载完成移除元素
        window.URL.revokeObjectURL(url); //释放掉blob对象
      })
     },
    // 下载模板
    downTemplateClick () {
      var params ='';
      downTemplate(params).then(res => {
        if(!res.data){
          return
        }
        var name = "用户导入模板.xlsx";
        var blob = new Blob([res.data]);
        var url = window.URL.createObjectURL(blob);
        var aLink = document.createElement("a");
        aLink.style.display = "none";
        aLink.href = url;
        aLink.setAttribute("download", name);
        document.body.appendChild(aLink);
        aLink.click();
        document.body.removeChild(aLink); //下载完成移除元素
        window.URL.revokeObjectURL(url); //释放掉blob对象
      })
    },
    // 分配角色
    distriRole (row) {
      if(this.$refs.temp){
        this.$nextTick(function() {
          this.$refs.temp.clearValidate();
        })
      }
      this.dialogStatus = 'assignRole'
      this.dialogFormVisible = true
      var params ='?id='+ row.id
      assignRole(params).then(res => {
        this.sltUserRole.defaultRoleCode = res.data.data.defaultRoleCode
        this.sltUserRole.userName =res.data.data.username;
        this.sltUserRole.roleCodeArr = res.data.data.roleList
        var roleCodeArr= res.data.data.roleList
        //获取当前用户选择的角色
        if(roleCodeArr!=null&&roleCodeArr.length>0){
          if(this.userRoleList.length>0){
            for(var i=0;i<this.userRoleList.length;i++){
              var curRoleCode= this.userRoleList[i].code
              if(roleCodeArr.indexOf(curRoleCode) > -1){
                if(this.sltUserRole.roleCodeArr.indexOf(curRoleCode)==-1){
                  this.sltUserRole.roleCodeArr.push(curRoleCode)
                }
              }
            }
          }
        }
      })

    },

    // 分配车型
    distriTrain(row){
      this.filterText = ''
      // 获取用户已经关联的车型
      this.trainUser = row.username
      var params = new URLSearchParams()
      params.append('username', row.username)
      this.checkedTrain = []
      getUserTrain(params).then(res => {
        if (res.data.code == 100) {
          let list = res.data.data
          for (let i = 0; i < list.length; i++) {
            this.checkedTrain.push(list[i].trainId);
          }
          this.dialogTrainTreeVisible = true
        }
      })
    },
    // 获取选中
    getCheckedKeys() {
      let list = this.$refs.trainTree.getCheckedKeys()
      if(!list || list.length <= 0){
        handleAlert("error", "请选择车型")
        return false
      }
      this.$loading.show()
      var params = new URLSearchParams()
      params.append('username', this.trainUser)
      params.append('trainIds', list.toString())
      updateUserTrain(params).then(res => {
        if (res.data.code == 100) {
          handleAlert("success", "成功")
          this.dialogTrainTreeVisible = false
        }else{
          handleAlert("error", "分配失败，" + res.data.msg)
        }
        this.$loading.hide()
      }).catch(e => {
        handleAlert("error", "系统开小差，请稍后再试")
        this.$loading.hide()
      })
    },
    // filterNode(value, data) {
    //     if (!value) return true;
    //     return data.name.indexOf(value) !== -1;
    //   },

    filterNode(value, data, node) {
      if (!value) return true;
      let names = this.getParents(node,node.data.name,'name');
      return names.indexOf(value) !== -1;
    },
    getParents(node,name,key){
        if(node.parent && node.parent.data[key]){
            name+= node.parent.data[key]
            return this.getParents(node.parent,name,key)
        }
        return name
    },

    // 分配国家
    distriCountry(row){
      // this.$nextTick(function() {
      //   this.$refs.temp.clearValidate();
      // })
      this.sltUserCountry.countryArr = []
      this.sltUserCountry.defaultCountry = row.defaultCountryCode
      this.sltUserCountry.userName = row.username

      this.dialogStatus = 'assignCountry'
      this.dialogFormVisible = true
      getUserCountry(row.username).then(res => {
        let countryList = []
        res.data.data.forEach(item => {
          if (item.isDefault == 0) {
            countryList.push(item.countryCode)
          }
        })

        if (countryList && countryList.length>0) {
          for(var i=0;i<this.userCountryList.length;i++){
              var curRoleCode= this.userCountryList[i].code
              if(countryList.indexOf(curRoleCode) > -1){
                if(this.sltUserCountry.countryArr.indexOf(curRoleCode)==-1){
                  this.sltUserCountry.countryArr.push(curRoleCode)
                }
              }
          }
        }



      })
    },

    updateUserCountry(){
      var defaultCountry= this.sltUserCountry.defaultCountry
      var countryArr=this.sltUserCountry.countryArr
      //if(defaultCountry ==''){
      //  handleAlert('error','请选择默认国家')
      // return false
      //}
      let sltCountry = ''
      if (countryArr && countryArr.length>0) {
        sltCountry = countryArr.toString()
      }
      this.$loading.show();
      var params = new FormData()
			params.append('username', this.sltUserCountry.userName)
      params.append('sltCountry', sltCountry)
      params.append('defaultCountry', defaultCountry)
      updatedCountry(params).then(res=>{
        if(res.data.code === 100){
          handleAlert('success','分配成功')
          this.dataList()
          this.dialogFormVisible = false
        }
        this.$loading.hide();
      }).catch(function(error){
        handleAlert('error','分配失败')
        this.$loading.hide();
      })
    },

    updateUserRole () {
      var roleCode= this.sltUserRole.defaultRoleCode
      var sltRoleCodeArr=this.sltUserRole.roleCodeArr
      if(sltRoleCodeArr.length==0){
        handleAlert('error','请选择角色')
        return false
      }
      if(roleCode==''){
        handleAlert('error','请选择默认角色')
        return false
      }
      this.$loading.show()
      var params = new FormData()
			params.append('userName', this.sltUserRole.userName)
      params.append('code', sltRoleCodeArr)
      params.append('defultCode', roleCode)
      updateAssignRole(params).then(res=>{
        if(res.data.code === 100){
          handleAlert('success','分配成功')
          this.dataList()
          this.dialogFormVisible = false
        }
        this.$loading.hide()
      }).catch(function(error){
        handleAlert('error','分配失败')
        this.$loading.hide()
      })
    },
    resetCode () {
      this.sltUserRole={
        userName: '',
        defaultRoleCode: '',
        roleCodeArr: []
      }
    },
    // 密码重置
    initPsw (row) {
      this.$confirm('确定重置【' + row.username + '】的密码吗?', '重置密码', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
          var params = '?id=' + row.id
          userPasswordReset(params).then(res => {
            if(res.data.code === 100){
              handleAlert('success',res.data.msg)
            }else if(res.data.code === 101){
              handleAlert('error',res.data.msg)
            }else {
              handleAlert('error',res.data.msg)
            }
          })
      }).catch((error) => {
        handleAlert('info','取消重置')
      })
    },
    // 编辑
    editopen (row) {
      var _this = this

      _this.aboriginalRow = row;
      _this.dialogStatus = 'edit'
      _this.dialogFormVisible = true
      _this.resetTemp()
      _this.temp = Object.assign({}, row)
      if (row.useFlag === 0) {
        _this.whether = false
      } else {
        _this.whether = true
      }
      if (row === null || row.sex === null) {
        _this.temp.sex = 0
      } else {
        _this.temp.sex = row.sex
      }
      if(row.deptId){
        _this.temp.deptId = String(row.deptId)
        let depN = row.deptName.split(' ')[1]
        if (!depN || depN.trim().length <= 0) {
          depN = row.deptName
        }
        setTimeout(function(){
          if(row.deptName){
            _this.$refs.modelSelectTree.initSelected(depN, _this.temp.deptId)
          }else{
            _this.$refs.modelSelectTree.initSelected('', '')
          }

        });
      }else{
        setTimeout(function(){
          _this.$refs.modelSelectTree.initSelected('', '')
        })
      }


    },
    // 提交编辑
    editClick (temp) {
      this.$refs[temp].validate((valid) => {
        if (valid) {
          this.selectState()
          this.$loading.show()
          var params = new URLSearchParams()
          params.append('id', this.temp.id)
          params.append('realName', this.temp.realName)
          params.append('username', this.temp.username)
          params.append('defaultRoleCode', this.temp.defaultRoleCode)
          if (this.temp.deptId && this.temp.deptId != 'null' && this.temp.deptId != 'undefined') {
            params.append('deptId', this.temp.deptId)
          }else{
            params.append('deptId', '')
          }

          params.append('contacts', this.temp.contacts)
          params.append('sex', this.temp.sex)
          if(this.temp.mobile != null && this.temp.mobile != undefined){
            params.append('mobile', this.temp.mobile)
          }
          if(this.temp.email != null && this.temp.email != undefined){
            params.append('email', this.temp.email)
          }

          params.append('address', this.temp.address)
          // params.append('userType', this.temp.userType)
          params.append('useFlag', this.temp.useFlag)
          params.append('defaultCountryCode', this.temp.defaultCountryCode)

          userEdit(params).then(res => {
            if(res.data.code === 100){
              handleAlert('success','保存成功')
              this.dataList()
              this.dialogFormVisible = false
            } else {
              handleAlert('error',res.data.msg)
            }
            this.$loading.hide()
          })
        } else {
          handleAlert('error','请完善用户信息')
          this.$loading.hide()
        }
      })
    },
    // 删除
    delectClick (row) {
      this.$confirm('确定删除【' + row.realName + '】的相关信息?', '删除用户', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        userDel(row.id).then(res=>{
          if(res.data.code === 100){
            handleAlert('success','删除成功')
            if(this.resultList!=null&&this.resultList.length==1){
              this.currentPage =this.currentPage-1
            }
            this.dataList()
          }else{
            handleAlert('error','删除失败')
          }
        })
      }).catch((error) => {
        handleAlert('info','取消删除')
      })
    },

    // 获取 品牌-车型 结构树
    getUserTrainTree(){
      getUserBrandTrainTree().then(res => {
          this.userTrainTree = res.data.data
      })
    },

    // 用户信息
    userData () {
      getUserInfo().then(res => {
        this.userInfo = res.data.data
      })
    },
    maximumArea() {
      setTimeout(() => {
        tableHeight()
        setTimeout(() => {
          this.maximumHeight = tableMaxHeight;
        }, 50)
      })
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    }
  },
  mounted () {
    this.tableHeightArea()
    let tt= this.$store.state.perms.indexOf('menuAsimss1A2B_105')
    this.dataList()
    this.getUserRoleList()
    this.getDepartment()
    this.getUserCountryList()
    this.getUserTrainTree()
    this.userData()
    window.addEventListener('keydown', this.keyDown);
  },
  destroyed() {
    window.removeEventListener('keydown', this.keyDown, false)
  },
}
</script>
