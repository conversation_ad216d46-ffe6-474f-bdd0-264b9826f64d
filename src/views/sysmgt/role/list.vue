<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" ref="formInline" label-width="65px" :model="formInline" class="demo-form-inline">
        <el-form-item label="角色名" prop="rolename">
          <el-input v-model.trim="formInline.rolename" placeholder="请输入角色名"></el-input>
        </el-form-item>
        <el-form-item label="编码" prop="code">
          <el-input v-model.trim="formInline.code" placeholder="请输入编码"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit" icon="el-icon-search">搜索</el-button>
          <el-button plain @click="reset('formInline')">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <div class="tableHandle" v-if="hasPerm('menuAsimss1A3B_101')">
        <el-button type="text" icon="el-icon-plus"  @click="addHandle()">新增</el-button>
      </div>
      <el-table
        style="width:100%"
        border
        stripe
        ref="table"
        highlight-current-row
        :max-height="maximumHeight"
        :data="resultList"
        :header-cell-style="{
          'text-align': 'center',
          'background-color': 'var(--other-color)',
        }"
        @header-dragend="changeColWidth"
      >
        <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="角色名" prop="name" min-width="150"></el-table-column>
        <el-table-column show-overflow-tooltip label="编码" prop="code" min-width="150"></el-table-column>
        <el-table-column label="层级" prop="level" width="100" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="备注" prop="memo" min-width="150"></el-table-column>
        <el-table-column label="状态" prop="useFlag" width="100" align="center">
          <template slot-scope="{row}">
            <span v-if="row.useFlag === '1'" style="color:#009933">生效</span>
            <span v-if="row.useFlag === '0'" style="color:#c30000">失效</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="160">
          <template slot-scope="{row}">
            <el-button type="text" v-if="hasPerm('menuAsimss1A3B_103')" size="small" @click="handleEdit(row)">编辑</el-button>
            <el-button type="text" v-if="hasPerm('menuAsimss1A3B_102')" size="small" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total>0" :total="total" :page.sync="currentPage" :limit.sync="pagesize" @pagination="dataList"/>
      <el-dialog v-dialogDrag :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" :close-on-click-modal="false">
        <el-form ref='dataForm' :label-width="formLabelWidth" :model="dataForm" :rules="fromrules" label-position="center">
          <el-form-item label="角色名" prop="name">
            <el-input v-model.trim="dataForm.name" show-word-limit maxlength="30" placeholder="请输入角色名"></el-input>
          </el-form-item>
          <el-form-item label="编码" prop="code">
            <el-input v-model.trim="dataForm.code" show-word-limit maxlength="20" oninput="value=value.replace(/[^\w+$]/g,'')" placeholder="请输入编码"></el-input>
          </el-form-item>
          <el-form-item label="备注" prop="memo">
            <el-input v-model.trim="dataForm.memo" show-word-limit maxlength="100" placeholder="请输入备注"></el-input>
          </el-form-item>
          <el-form-item label="层级" prop="level">
            <el-input type="number" :min="1" :max="99"  @input="e => dataForm.level=parserNumber(e,1,99)" v-model.trim="dataForm.level" placeholder="请输入等级"></el-input>
            <span>数值越小，等级越高</span>
          </el-form-item>
          <el-form-item label="生效状态" prop="useFlag">
            <el-switch v-model="whether"></el-switch>
          </el-form-item>
          <div class="submitArea">
            <el-button type="primary" @click="dialogStatus === 'add' ? addClick('dataForm') : editClick('dataForm')">
              立即提交
            </el-button>
            <el-button @click="resetForm('dataForm')">
              重置
            </el-button>
          </div>
        </el-form>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { handleAlert, tableHeight, tableMaxHeight } from '@/assets/js/common.js'
import Pagination from '@/components/Pagination'
import { userData, roleData, roleAdd, roleEdit, roleDel } from '@/api/sysmgt.js'
export default {
  name: 'sysmgt_role_list',
  components: { Pagination },
  data () {
    return {
      formInline: {
        rolename: '',
        code: ''
      },
      dataForm: {
        name: '',
        code: '',
        level: 1,
        memo: '',
        useFlag: '1'
      },
      dialogFormVisible: false,
      formLabelWidth: '100px',
      dialogStatus: '',
      textMap: {
        edit: '编辑角色',
        add: '新增角色'
      },
      whether: false,
      resultList: [],
      pagesize: 20,
      currentPage: 1,
      total: 0,
      fromrules: {
        name: [{ required: true, message: '角色名不能为空', trigger: ['blur', 'change'] }],
        code: [{ required: true, message: '编号不能为空', trigger: ['blur', 'change'] }]
      },
      maximumHeight: 0,
    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth(){
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    // 数据
    dataList () {
      this.$loading.show();
      var params = new URLSearchParams()
      params.append('page', this.currentPage)
      params.append('limit', this.pagesize)
      params.append('name', this.formInline.rolename)
      params.append('code', this.formInline.code)
      roleData(params).then(res => {
        if (res.data.code == 100) {
          this.total = res.data.total
          this.resultList = res.data.data
        } else {
          handleAlert('error',res.data.msg)
        }
        this.$loading.hide();
        this.tableHeightArea()
      })
    },
    // 搜索
    keyDown(e) {
      if (e.keyCode === 13) {
        this.onSubmit()
      }
    },
    onSubmit () {
      this.currentPage=1
      this.dataList()
    },
    // 重置
    resetForm (dataForm) {
      this.resetTemp()
    },
    reset (formInline) {
      if (this.$refs[formInline].resetFields() !== undefined) {
        this.$refs[formInline].resetFields()
      }
      this.currentPage = 1
      this.dataList()
    },
    resetTemp () {
      this.dataForm = {
        id: '',
        name: '',
        code: '',
        level: 1,
        memo: '',
        useFlag: 1
      }
      this.whetherState();
      this.$nextTick(function() {
        this.$refs.dataForm.clearValidate();
      })
    },
    // 新增
    addHandle () {
      this.resetTemp()
      this.dialogStatus = 'add'
      this.dialogFormVisible = true
    },
    whetherState(){
      if (this.dataForm.useFlag == 0 ) {
        this.whether = false
      } else {
        this.whether = true
      }
    },
    useFlagState () {
      if (this.whether === false) {
        this.dataForm.useFlag = 0
      } else {
        this.dataForm.useFlag = 1
      }
    },
    addClick (dataForm) {
      this.$refs[dataForm].validate((valid) => {
        if (valid) {
          this.useFlagState()
          this.$loading.show()
          var params = new URLSearchParams()
          params.append('name', this.dataForm.name)
          params.append('code', this.dataForm.code)
          params.append('level', this.dataForm.level)
          params.append('memo', this.dataForm.memo)
          params.append('useFlag', this.dataForm.useFlag)
          roleAdd(params).then(res => {
            if(res.data.code === 100){
              handleAlert('success',res.data.msg)
              this.dataList()
              this.dialogFormVisible = false
            }else{
              handleAlert('error',res.data.msg)
            }
            this.$loading.hide()
          })
        } else {
          handleAlert('error','请完善角色信息')
          this.$loading.hide()
        }
      })
    },
    // 编辑
    handleEdit (row) {
      this.dialogStatus = 'edit'
      this.dialogFormVisible = true
      this.resetTemp()
      this.dataForm = Object.assign({}, row)
      this.whetherState()
    },
    editClick (dataForm) {
      this.$refs[dataForm].validate((valid) => {
        if (valid) {
          this.useFlagState()
          this.$loading.show()
          var params = new URLSearchParams()
          params.append('id', this.dataForm.id)
          params.append('name', this.dataForm.name)
          params.append('code', this.dataForm.code)
          params.append('level', this.dataForm.level)
          params.append('memo', this.dataForm.memo)
          params.append('useFlag', this.dataForm.useFlag)
          roleEdit(params).then(res => {
            if(res.data.code === 100){
              handleAlert('success',res.data.msg)
              this.dataList()
              this.dialogFormVisible = false
            }else {
              if(res.data.code === 404){
                handleAlert('error','系统出现异常，更新失败')
              }else{
                handleAlert('error',res.data.msg)
              }
            }
            this.$loading.hide()
          })
        } else {
          handleAlert('error','请完善角色信息')
          this.$loading.hide()
        }
      })
    },
    // 删除
    handleDelete (row) {
      var _this= this
      this.currentPage=1
      var params = new URLSearchParams()
      params.append('page', this.currentPage)
      params.append('limit', this.pagesize)
      params.append('defaultRoleCode', row.code)
      this.$confirm('确定刪除【' + row.name + '】的相关信息?', '删除角色', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.roledelClick(row.id);
      }).catch((error) => {
        handleAlert('info','取消删除')
      })
    },
    roledelClick(id) {
      roleDel(id).then(res => {
        if(res.data.code === 100){
          handleAlert('success','删除成功!')
          if(this.resultList!=null&&this.resultList.length==1){
            this.currentPage =this.currentPage-1
          }
          this.dataList()
        }else{
          handleAlert('error','删除失败: ' + res.data.msg)
        }
      })
    },
    maximumArea() {
      setTimeout(() => {
        tableHeight()
        setTimeout(() => {
          this.maximumHeight = tableMaxHeight;
        }, 50)
      })
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    }
  },
  mounted () {
    this.tableHeightArea()
    this.dataList()
    window.addEventListener('keydown', this.keyDown)
  },
  destroyed() {
    window.removeEventListener('keydown', this.keyDown, false)
  },
}
</script>
