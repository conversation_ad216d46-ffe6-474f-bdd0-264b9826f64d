<!--suppress ALL -->
<template>
  <div class="layoutContainer">
    <el-tabs class="partsListManagement" v-model="activeName" @tab-click="handleClick">
      <el-dialog v-dialogDrag width="700px !important" title="添加物料代码国家对照" :visible.sync="materialCountry.form.visible" :close-on-click-modal="false">
        <el-form :rules="materialCountry.form.rules" ref="addMaterialCountry" :label-width="form.formLabelWidth" :model="materialCountry.form.data">
          <el-form-item label="物料代码" prop="materialCode">
            <el-input type="text" v-model.trim="materialCountry.form.data.materialCode" placeholder="请输入物料代码" show-word-limit maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="品牌名称" prop="epcBrandId">
            <el-select v-model="materialCountry.form.data.epcBrandId" placeholder="请选择品牌名称" clearable filterable @change="selectBrand">
              <el-option
                v-for="item in materialCountry.epcBrandList"
                :key="item.id"
                :label="item.nameCh"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="型号名称" prop="epcModelId">
            <el-select v-model="materialCountry.form.data.epcModelId" placeholder="请选择型号名称" filterable clearable @change="selectModel">
              <el-option-group
                v-for="(model,group) in materialCountry.epcCarModelMap"
                :key="group"
                :label="group">
                <el-option
                  v-for="item in model"
                  :key="item.id"
                  :label="item.nameCh"
                  :value="item.id">
                </el-option>
              </el-option-group>
            </el-select>
          </el-form-item>
          <el-form-item label="配置名称" prop="configId">
            <el-select v-model="materialCountry.form.data.configId" placeholder="请选择配置名称" filterable clearable>
              <el-option-group
                v-for="(model,group) in materialCountry.epcConfigMap"
                :key="group"
                :label="group">
                <el-option
                  v-for="item in model"
                  :key="item.id"
                  :label="item.nameCh"
                  :value="item.id">
                </el-option>
              </el-option-group>
            </el-select>
          </el-form-item>
          <div class="submitArea">
            <el-button type="primary" @click="submit('addMaterialCountry')">
              确认
            </el-button>
            <el-button @click="cancel()">
              取消
            </el-button>
          </div>
        </el-form>
      </el-dialog>

      <el-dialog v-dialogDrag width="700px !important" :title="form.title" :visible.sync="form.visible" :close-on-click-modal="false">
        <el-form :rules="form.rules" ref="addOrEditForm" :label-width="form.formLabelWidth" :model="form.data">
          <el-form-item v-for="(item,index) in form.entity" :label="item.lable" :prop="item.prop" :key="index">
            <el-input :disabled="item.readonly||(item.prop.endsWith('ode') && form.type === 'edit')" v-show="!item.hide" :type="item.type" v-model.trim="form.data[item.prop]" :placeholder="item.placeholder" show-word-limit maxlength="50" min="1" max="999"></el-input>
          </el-form-item>
          <div class="submitArea">
            <el-button type="primary" @click="submit('addOrEditForm')">
              确认
            </el-button>
            <el-button @click="cancel()">
              取消
            </el-button>
          </div>
        </el-form>
      </el-dialog>

      <el-tab-pane name="electrophoretic">
        <span slot="label" class="tab-title">
          电泳件清单
        </span>
        <div class="secondFloat">
          <el-form :inline="true" ref="electrophoretic" label-width="80px" :model="electrophoretic.searchForm" class="demo-form-inline">
            <el-form-item label="配件编码" prop="code">
              <el-input v-model.trim="electrophoretic.searchForm.code" placeholder="请输入配件编码"></el-input>
            </el-form-item>
            <el-form-item label="配件名称" prop="name">
              <el-input v-model.trim="electrophoretic.searchForm.name" placeholder="请输入配件名称"></el-input>
            </el-form-item>
            <el-form-item label="电泳件编码" prop="destCode">
              <el-input v-model.trim="electrophoretic.searchForm.destCode" placeholder="请输入电泳件编码"></el-input>
            </el-form-item>
            <el-form-item label="电泳件名称" prop="destName">
              <el-input v-model.trim="electrophoretic.searchForm.destName" placeholder="请输入电泳件名称"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="search('electrophoretic')" icon="el-icon-search">搜索</el-button>
              <el-button plain @click="reset('electrophoretic')">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="tableDetail">
          <div class="tableHandle">
            <el-button type="text" icon="el-icon-plus" @click="add('electrophoretic')">新增</el-button>
            <!--            <el-button type="text" icon="el-icon-delete" @click="batchDelete('electrophoretic')">批量删除</el-button>-->
            <el-upload
              class="upload-demo inline-block"
              ref="celUpload"
              action="#"
              :show-file-list="false"
              :before-upload="onBeforeUpload"
              accept=".xlsx"
              :http-request="file => importTableData(file,'electrophoretic')"
            >
              <el-button type="text" icon="bulkImport-icon">批量上传</el-button>
            </el-upload>
            <el-button type="text" icon="bulkDown-icon" @click="exportData('electrophoretic')">批量下载</el-button>
            <el-button type="text" icon="el-icon-download" @click="downloadTemplate('电泳件清单上传模板.xlsx')">
              下载模板
            </el-button>
          </div>
          <el-table
            style="width:100%;"
            border
            stripe
            ref="electTable"
            highlight-current-row
            :max-height="maximumHeight"
            :data="electrophoretic.tableData"
            :header-cell-style="{
              'text-align': 'center',
              'background-color': 'var(--other-color)',
            }"
            @header-dragend="changeColWidth"
            @selection-change="selection => handleSelectionChange(selection,'electrophoretic')"
          >
            <!-- <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>-->
            <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="配件编码" prop="code" min-width="150"></el-table-column>
            <el-table-column show-overflow-tooltip label="配件名称" prop="name" width="300"></el-table-column>
            <el-table-column show-overflow-tooltip label="电泳件编码" prop="destCode" min-width="150"></el-table-column>
            <el-table-column show-overflow-tooltip label="电泳件名称" prop="destName" min-width="300"></el-table-column>
            <!--            <el-table-column show-overflow-tooltip label="备注" prop="remark" min-width="150"></el-table-column>-->
            <el-table-column label="状态" prop="inUse" min-width="60" fixed="right" align="center">
              <template v-slot="{row}">
                <span style="color: green;" v-if="row.inUse">生效</span>
                <span style="color: red;" v-else>失效</span>
              </template>
            </el-table-column>
            <el-table-column label="更新人员" prop="updatedByName" min-width="150" align="center"></el-table-column>
            <el-table-column label="更新日期" prop="updatedTime" min-width="150" align="center"></el-table-column>
            <!--            <el-table-column show-overflow-tooltip label="创建日期" prop="createTime" fixed="right" min-width="150" align="center"></el-table-column>-->
            <el-table-column label="操作" fixed="right" width="150">
              <template v-slot="{row}">
                <div v-if="row.inUse">
                  <el-button type="text" size="small" @click="toggleStatus(row.id,false,'electrophoretic')">
                    停用
                  </el-button>
                  <el-button type="text" size="small" @click="editRow('electrophoretic',row)">
                    编辑
                  </el-button>
                </div>
                <div v-else>
                  <el-button type="text" size="small" @click="toggleStatus(row.id,true,'electrophoretic')">
                    启用
                  </el-button>
                  <el-button type="text" size="small" @click="deleteRow(row.id,'electrophoretic')">
                    删除
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <pagination v-show="electrophoretic.total > 0" :total="electrophoretic.total" :page.sync="electrophoretic.currentPage" :limit.sync="electrophoretic.pageSize" @pagination="getTableData('electrophoretic')"/>
        </div>

      </el-tab-pane>
      <el-tab-pane name="split">
        <span slot="label" class="tab-title">
          拆分件清单
        </span>
        <div class="secondFloat">
          <el-form :inline="true" ref="split" label-width="80px" :model="split.searchForm" class="demo-form-inline">
            <el-form-item label="总成件编码" prop="code">
              <el-input v-model.trim="split.searchForm.code" placeholder="请输入总成件编码"></el-input>
            </el-form-item>
            <el-form-item label="总成件名称" prop="name">
              <el-input v-model.trim="split.searchForm.name" placeholder="请输入总成件名称"></el-input>
            </el-form-item>
            <el-form-item label="总成件状态" prop="inUse">
              <el-select v-model="split.searchForm.inUse" placeholder="请选择状态" clearable>
                <el-option label="生效" value="1"></el-option>
                <el-option label="失效" value="0"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="search('split')" icon="el-icon-search">搜索</el-button>
              <el-button plain @click="reset('split')">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="tableDetail">
          <div class="tableHandle">
            <el-button type="text" icon="el-icon-plus" @click="add('split')">新增</el-button>
            <el-upload
              class="upload-demo inline-block"
              ref="celUpload"
              action="#"
              :show-file-list="false"
              :before-upload="onBeforeUpload"
              accept=".xlsx"
              :http-request="file => importTableData(file,'split')"
            >
              <el-button type="text" icon="bulkImport-icon">批量上传</el-button>
            </el-upload>
            <el-button type="text" icon="bulkDown-icon" @click="exportData('split')">批量下载</el-button>
            <el-button type="text" icon="el-icon-download" @click="downloadTemplate('拆分件清单上传模板.xlsx')">
              下载模板
            </el-button>
          </div>
          <el-table
            style="width:100%;"
            border
            ref="splitTable"
            highlight-current-row
            :max-height="maximumHeight"
            :data="split.tableData"
            :header-cell-style="{
              'text-align': 'center',
              'background-color': 'var(--other-color)',
            }"
            row-key="id"
            default-expand-all
            :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
            :row-style="rowStyle"
            @header-dragend="changeColWidth"
            @selection-change="selection => handleSelectionChange(selection,'split')"
          >
            <!--            <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>-->
            <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="配件编码" prop="code" min-width="150"></el-table-column>
            <el-table-column show-overflow-tooltip label="配件名称" prop="name" width="300"></el-table-column>
            <el-table-column show-overflow-tooltip label="单车用量" prop="amount" min-width="150" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="备注" prop="remark" min-width="150"></el-table-column>
            <el-table-column label="分类" prop="inUse" min-width="60" fixed="right" align="center">
              <template v-slot="{row}">
                <span style="color: green;" v-if="row.children">总成件</span>
                <span v-else>拆分件</span>
              </template>
            </el-table-column>
            <el-table-column label="状态" prop="inUse" min-width="60" fixed="right" align="center">
              <template v-slot="{row}">
                <span style="color: green;" v-if="row.inUse">生效</span>
                <span style="color: red;" v-else>失效</span>
              </template>
            </el-table-column>
            <el-table-column label="更新人员" prop="updatedByName" min-width="150" align="center"></el-table-column>
            <el-table-column label="更新日期" prop="updatedTime" min-width="150" align="center"></el-table-column>
            <!--            <el-table-column show-overflow-tooltip label="状态" prop="inUse" fixed="right" min-width="100"></el-table-column>-->
            <!--            <el-table-column show-overflow-tooltip label="创建日期" prop="createTime" fixed="right" min-width="150"  align="center"></el-table-column>-->
            <el-table-column label="操作" fixed="right" width="200">
              <template v-slot="{row}">
                <div v-if="row.inUse">
                  <el-button type="text" size="small" @click="toggleStatus(row.id,false,'split')">
                    停用
                  </el-button>
                  <el-button v-if="row.children" type="text" size="small" @click="add('splitSub',row)">
                    添加拆分件
                  </el-button>
                  <el-button type="text" size="small" @click="editRow('split',row)">
                    编辑
                  </el-button>
                </div>
                <div v-else>
                  <el-button type="text" size="small" @click="toggleStatus(row.id,true,'split')">
                    启用
                  </el-button>
                  <el-button type="text" size="small" @click="deleteRowAfterCheck(row,'split')">
                    删除
                  </el-button>
                </div>

              </template>
            </el-table-column>
          </el-table>
          <pagination v-show="split.total > 0" :total="split.total" :page.sync="split.currentPage" :limit.sync="split.pageSize" @pagination="getTableData('split')"/>
        </div>
      </el-tab-pane>
      <el-tab-pane name="noSupply">
        <span slot="label" class="tab-title">
          <span>不供货清单</span>
        </span>
        <div class="secondFloat">
          <el-form :inline="true" ref="noSupply" label-width="65px" :model="noSupply.searchForm" class="demo-form-inline">
            <el-form-item label="配件编码" prop="code">
              <el-input v-model.trim="noSupply.searchForm.code" placeholder="请输入配件编码"></el-input>
            </el-form-item>
            <el-form-item label="配件名称" prop="name">
              <el-input v-model.trim="noSupply.searchForm.name" placeholder="请输入配件名称"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="search('noSupply')" icon="el-icon-search">搜索</el-button>
              <el-button plain @click="reset('noSupply')">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="tableDetail">
          <div class="tableHandle">
            <el-button type="text" icon="el-icon-plus" @click="add('noSupply')">新增</el-button>
            <el-upload
              class="upload-demo inline-block"
              ref="celUpload"
              action="#"
              :show-file-list="false"
              :before-upload="onBeforeUpload"
              accept=".xlsx"
              :http-request="file => importTableData(file,'noSupply')"
            >
              <el-button type="text" icon="bulkImport-icon">批量上传</el-button>
            </el-upload>
            <el-button type="text" icon="bulkDown-icon" @click="exportData('noSupply')">批量下载</el-button>
            <el-button type="text" icon="el-icon-download" @click="downloadTemplate('不供货清单上传模板.xlsx')">
              下载模板
            </el-button>
          </div>
          <el-table
            style="width:100%;"
            border
            stripe
            ref="noSupplyTable"
            highlight-current-row
            :max-height="maximumHeight"
            :data="noSupply.tableData"
            :header-cell-style="{
              'text-align': 'center',
              'background-color': 'var(--other-color)',
            }"
            @header-dragend="changeColWidth"
            @selection-change="selection => handleSelectionChange(selection,'noSupply')"
          >
            <!--            <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>-->
            <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="配件编码" prop="code" min-width="150"></el-table-column>
            <el-table-column show-overflow-tooltip label="配件名称" prop="name" min-width="150"></el-table-column>
            <el-table-column label="状态" prop="inUse" min-width="60" fixed="right" align="center">
              <template v-slot="{row}">
                <span style="color: green;" v-if="row.inUse">生效</span>
                <span style="color: red;" v-else>失效</span>
              </template>
            </el-table-column>
            <el-table-column label="更新人员" prop="updatedByName" min-width="150" align="center"></el-table-column>
            <el-table-column label="更新日期" prop="updatedTime" min-width="150" align="center"></el-table-column>
            <!--            <el-table-column label="创建日期" prop="createTime" fixed="right" min-width="150"  align="center"></el-table-column>-->
            <el-table-column label="操作" fixed="right" width="150">
              <template v-slot="{row}">
                <div v-if="row.inUse">
                  <el-button type="text" size="small" @click="toggleStatus(row.id,false,'noSupply')">
                    停用
                  </el-button>
                  <!--                  <el-button type="text" size="small" @click="editRow('noSupply',row)">
                                      编辑
                                    </el-button>-->
                </div>
                <div v-else>
                  <el-button type="text" size="small" @click="toggleStatus(row.id,true,'noSupply')">
                    启用
                  </el-button>
                  <el-button type="text" size="small" @click="deleteRow(row.id,'noSupply')">
                    删除
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <pagination v-show="noSupply.total > 0" :total="noSupply.total" :page.sync="noSupply.currentPage" :limit.sync="noSupply.pageSize" @pagination="getTableData('noSupply')"/>
        </div>
      </el-tab-pane>
      <el-tab-pane name="materialCountry">
                <span slot="label" class="tab-title">
                 <span>物料代码车型对照清单</span>
                </span>
        <div class="secondFloat">
          <el-form :inline="true" ref="materialCountry" :model="materialCountry.searchForm" class="demo-form-inline">
            <el-form-item label="物料代码" prop="materialCode">
              <el-input v-model.trim="materialCountry.searchForm.materialCode" placeholder="请输入物料代码"></el-input>
            </el-form-item>
            <el-form-item label="品牌" prop="epcBrandId">
              <el-select v-model="materialCountry.searchForm.epcBrandId" placeholder="请选择品牌" clearable filterable @change="selectSearchBrand">
                <el-option
                  v-for="item in materialCountry.epcBrandList"
                  :key="item.id"
                  :label="item.nameCh"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="车型" prop="epcModelId">
              <el-select v-model="materialCountry.searchForm.epcModelId" placeholder="请选择车型" filterable clearable @change="selectSearchModel">
                <el-option-group
                  v-for="(model,group) in materialCountry.epcCarModelMap"
                  :key="group"
                  :label="group">
                  <el-option
                    v-for="item in model"
                    :key="item.id"
                    :label="item.nameCh"
                    :value="item.id">
                  </el-option>
                </el-option-group>
              </el-select>
            </el-form-item>
            <el-form-item label="年款" prop="epcYearId">
              <el-select v-model="materialCountry.searchForm.epcYearId" placeholder="请选择年款" filterable clearable>
                <el-option
                  v-for="item in materialCountry.yearList"
                  :key="item.id"
                  :label="item.year"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="search('materialCountry')" icon="el-icon-search">搜索</el-button>
              <el-button plain @click="reset('materialCountry')">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="tableDetail">
          <div class="tableHandle">
            <el-button type="text" icon="el-icon-plus" @click="addMaterialCountry('materialCountry')">新增</el-button>
            <el-upload
              class="upload-demo inline-block"
              ref="celUpload"
              action="#"
              :show-file-list="false"
              :before-upload="onBeforeUpload"
              accept=".xlsx"
              :http-request="file => importTableData(file,'materialCountry')"
            >
              <el-button type="text" icon="bulkImport-icon">批量上传</el-button>
            </el-upload>
            <el-button type="text" icon="bulkDown-icon" @click="exportData('materialCountry')">批量下载</el-button>
            <el-button type="text" icon="el-icon-download" @click="downloadTemplate('物料代码配置对照表上传模板.xlsx')">
              下载模板
            </el-button>
            <el-button type="text" icon="el-icon-connection" @click="matchSbomData">匹配SBOM</el-button>
          </div>
          <el-table
            style="width:100%;"
            border
            stripe
            ref="materialTable"
            highlight-current-row
            :max-height="maximumHeight"
            :data="materialCountry.tableData"
            :header-cell-style="{
              'text-align': 'center',
              'background-color': 'var(--other-color)',
            }"
            @header-dragend="changeColWidth"
            @selection-change="selection => handleSelectionChange(selection,'configMatch')"
          >
            <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="物料代码" prop="materialCode" min-width="200"></el-table-column>
            <el-table-column show-overflow-tooltip label="品牌名称" prop="epcBrandName" min-width="150"></el-table-column>
            <el-table-column show-overflow-tooltip label="车型名称" prop="epcModelName" min-width="150"></el-table-column>
            <el-table-column show-overflow-tooltip label="年款名称" prop="epcYear" min-width="150"></el-table-column>
            <el-table-column show-overflow-tooltip label="配置名称" prop="configName" min-width="150"></el-table-column>
            <el-table-column label="状态" prop="inUse" min-width="60" align="center">
              <template v-slot="{row}">
                <span style="color: green;" v-if="row.inUse">生效</span>
                <span style="color: red;" v-else>失效</span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="VIN计数" min-width="70" prop="vinCount"></el-table-column>
            <el-table-column label="更新人员" prop="updatedByName" min-width="150" align="center"></el-table-column>
            <el-table-column label="更新日期" prop="updatedTime" min-width="150" align="center"></el-table-column>
            <el-table-column label="操作" fixed="right" width="120">
              <template v-slot="{row}">
                <div v-if="row.inUse">
                  <el-button type="text" size="small" @click="toggleStatus(row.id,false,'materialCountry')">
                    停用
                  </el-button>
                </div>
                <div v-else>
                  <el-button type="text" size="small" @click="toggleStatus(row.id,true,'materialCountry')">
                    启用
                  </el-button>
                  <el-button type="text" size="small" @click="deleteRow(row.id,'materialCountry')">
                    删除
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <pagination v-show="materialCountry.total > 0" :total="materialCountry.total" :page.sync="materialCountry.currentPage" :limit.sync="materialCountry.pageSize" @pagination="getTableData('materialCountry')"/>
        </div>
      </el-tab-pane>
      <el-tab-pane v-if="hasPerm('menuAsimssAA2B_124')" name="vinToVin">
        <span slot="label" class="tab-title">
          VIN对照清单
        </span>
        <div class="secondFloat">
          <el-form :inline="true" ref="vinToVin" label-width="65px" :model="vinToVin.searchForm" class="demo-form-inline">
            <el-form-item label="销售VIN" prop="customVin">
              <el-input v-model.trim="vinToVin.searchForm.customVin" placeholder="请输入销售VIN"></el-input>
            </el-form-item>
            <el-form-item label="生产VIN" prop="sapVin">
              <el-input v-model.trim="vinToVin.searchForm.sapVin" placeholder="请输入生产VIN"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="search('vinToVin')" icon="el-icon-search">搜索</el-button>
              <el-button plain @click="reset('vinToVin')">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="tableDetail">
          <div class="tableHandle">
            <el-button type="text" icon="el-icon-plus" @click="add('vinToVin')">新增</el-button>
            <el-upload
              class="upload-demo inline-block"
              ref="celUpload"
              action="#"
              :show-file-list="false"
              :before-upload="onBeforeUpload"
              accept=".xlsx"
              :http-request="file => importTableData(file,'vinToVin')"
            >
              <el-button type="text" icon="bulkImport-icon">批量上传</el-button>
            </el-upload>
            <el-button type="text" icon="bulkDown-icon" @click="exportData('vinToVin')">批量下载</el-button>
            <el-button type="text" icon="el-icon-download" @click="downloadTemplate('VIN对照清单上传模板.xlsx')">
              下载模板
            </el-button>
          </div>
          <el-table
            style="width:100%;"
            border
            stripe
            ref="vinTable"
            highlight-current-row
            :max-height="maximumHeight"
            :data="vinToVin.tableData"
            :header-cell-style="{
              'text-align': 'center',
              'background-color': 'var(--other-color)',
            }"
            @header-dragend="changeColWidth"
            @selection-change="selection => handleSelectionChange(selection,'vinToVin')"
          >
            <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="销售VIN" prop="customVin" min-width="150" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="生产VIN" prop="sapVin" min-width="150" align="center"></el-table-column>
            <el-table-column label="状态" prop="inUse" min-width="60" fixed="right" align="center">
              <template v-slot="{row}">
                <span style="color: green;" v-if="row.inUse">生效</span>
                <span style="color: red;" v-else>失效</span>
              </template>
            </el-table-column>
            <el-table-column label="更新人员" prop="updatedByName" fixed="right" min-width="150" align="center"></el-table-column>
            <el-table-column label="更新日期" prop="updatedTime" fixed="right" min-width="150" align="center"></el-table-column>
            <el-table-column label="操作" fixed="right" width="150">
              <template v-slot="{row}">
                <div v-if="row.inUse">
                  <el-button type="text" size="small" @click="toggleStatus(row.id,false,'vinToVin')">
                    停用
                  </el-button>
                  <el-button type="text" size="small" @click="editRow('vinToVin',row)">
                    编辑
                  </el-button>
                </div>
                <div v-else>
                  <el-button type="text" size="small" @click="toggleStatus(row.id,true,'vinToVin')">
                    启用
                  </el-button>
                  <el-button type="text" size="small" @click="deleteRow(row.id,'vinToVin')">
                    删除
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <pagination v-show="vinToVin.total > 0" :total="vinToVin.total" :page.sync="vinToVin.currentPage" :limit.sync="vinToVin.pageSize" @pagination="getTableData('vinToVin')"/>
        </div>
      </el-tab-pane>
      <el-tab-pane name="color">
        <span slot="label" class="tab-title">
          颜色件清单
        </span>
        <div class="secondFloat">
          <el-form :inline="true" ref="color" label-width="65px" :model="color.searchForm" class="demo-form-inline">
            <el-form-item label="颜色编码" prop="code">
              <el-input v-model.trim="color.searchForm.code" placeholder="请输入颜色编码"></el-input>
            </el-form-item>
            <el-form-item label="颜色名称" prop="name">
              <el-input v-model.trim="color.searchForm.name" placeholder="请输入颜色名称"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="search('color')" icon="el-icon-search">搜索</el-button>
              <el-button plain @click="reset('color')">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="tableDetail">
          <div class="tableHandle">
            <el-button type="text" icon="el-icon-plus" @click="add('color')">新增</el-button>
            <el-upload
              class="upload-demo inline-block"
              ref="celUpload"
              action="#"
              :show-file-list="false"
              :before-upload="onBeforeUpload"
              accept=".xlsx"
              :http-request="file => importTableData(file,'color')"
            >
              <el-button type="text" icon="bulkImport-icon">批量上传</el-button>
            </el-upload>
            <el-button type="text" icon="bulkDown-icon" @click="exportData('color')">批量下载</el-button>
            <el-button type="text" icon="el-icon-download" @click="downloadTemplate('零部件颜色代码及名称上传模板.xlsx')">
              下载模板
            </el-button>
          </div>
          <el-table
            style="width:100%;"
            border
            stripe
            ref="colorTable"
            highlight-current-row
            :max-height="maximumHeight"
            :data="color.tableData"
            :header-cell-style="{
              'text-align': 'center',
              'background-color': 'var(--other-color)',
            }"
            @header-dragend="changeColWidth"
            @selection-change="selection => handleSelectionChange(selection,'color')"
          >
            <!--            <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>-->
            <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="颜色编码" prop="code" min-width="150" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="颜色名称" prop="name" min-width="150" align="center"></el-table-column>
            <el-table-column label="状态" prop="inUse" min-width="60" fixed="right" align="center">
              <template v-slot="{row}">
                <span style="color: green;" v-if="row.inUse">生效</span>
                <span style="color: red;" v-else>失效</span>
              </template>
            </el-table-column>
            <el-table-column label="更新人员" prop="updatedByName" fixed="right" min-width="150" align="center"></el-table-column>
            <el-table-column label="更新日期" prop="updatedTime" fixed="right" min-width="150" align="center"></el-table-column>
            <el-table-column label="操作" fixed="right" width="150">
              <template v-slot="{row}">
                <div v-if="row.inUse">
                  <el-button type="text" size="small" @click="toggleStatus(row.id,false,'color')">
                    停用
                  </el-button>
                  <el-button type="text" size="small" @click="editRow('color',row)">
                    编辑
                  </el-button>
                </div>
                <div v-else>
                  <el-button type="text" size="small" @click="toggleStatus(row.id,true,'color')">
                    启用
                  </el-button>
                  <el-button type="text" size="small" @click="deleteRow(row.id,'color')">
                    删除
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <pagination v-show="color.total > 0" :total="color.total" :page.sync="color.currentPage" :limit.sync="color.pageSize" @pagination="getTableData('color')"/>
        </div>
      </el-tab-pane>
      <el-tab-pane name="engineSplit" v-if="authorityType == true">
                <span slot="label" class="tab-title">
                 发动机拆分清单
                </span>

        <div class="secondFloat">
          <el-form :inline="true" ref="engineSplit" label-width="80px" :model="engineSplit.searchForm" class="demo-form-inline">
            <el-form-item label="总成件编码" prop="code">
              <el-input v-model.trim="engineSplit.searchForm.code" placeholder="请输入总成件编码"></el-input>
            </el-form-item>
            <el-form-item label="总成件名称" prop="name">
              <el-input v-model.trim="engineSplit.searchForm.name" placeholder="请输入总成件名称"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="search('engineSplit')" icon="el-icon-search">搜索</el-button>
              <el-button plain @click="reset('engineSplit')">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="tableDetail">
          <div class="tableHandle">
            <!--            <el-button type="text" icon="el-icon-plus" @click="add('engineSplit')">新增</el-button>-->
            <!--            <el-upload
                          class="upload-demo inline-block"
                          ref="celUpload"
                          action="#"
                          :show-file-list="false"
                          :before-upload="onBeforeUpload"
                          accept=".xlsx"
                          :http-request="file => importTableData(file,'engineSplit')"
                        >
                          <el-button type="text" icon="bulkImport-icon">批量上传</el-button>
                        </el-upload>-->
            <el-button type="text" icon="bulkDown-icon" @click="exportData('engineSplit')">批量下载</el-button>
            <!--            <el-button type="text" icon="el-icon-download" @click="downloadTemplate('拆分件清单上传模板.xlsx')">
                          下载模板
                        </el-button>-->
          </div>
          <el-table
            style="width:100%;"
            border
            highlight-current-row
            ref="engineTable"
            :max-height="maximumHeight"
            :data="engineSplit.tableData"
            :header-cell-style="{
              'text-align': 'center',
              'background-color': 'var(--other-color)',
            }"
            row-key="id"
            default-expand-all
            :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
            :row-style="rowStyle"
            @header-dragend="changeColWidth"
            @selection-change="selection => handleSelectionChange(selection,'engineSplit')"
          >
            <!--            <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>-->
            <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="配件编码" prop="code" min-width="300"></el-table-column>
            <el-table-column show-overflow-tooltip label="配件名称" prop="name" min-width="300"></el-table-column>
            <el-table-column show-overflow-tooltip label="单车用量" prop="amount" min-width="150"></el-table-column>
            <!--            <el-table-column show-overflow-tooltip label="备注" prop="remark" min-width="150"></el-table-column>-->
            <el-table-column label="分类" prop="inUse" min-width="60" fixed="right" align="center">
              <template v-slot="{row}">
                <span style="color: green;" v-if="row.children">总成件</span>
                <span v-else>拆分件</span>
              </template>
            </el-table-column>

          </el-table>
          <pagination v-show="engineSplit.total > 0" :total="engineSplit.total" :page.sync="engineSplit.currentPage" :limit.sync="engineSplit.pageSize" @pagination="getTableData('engineSplit')"/>
        </div>
      </el-tab-pane>

    </el-tabs>
  </div>
</template>
<script>
import {handleAlert, tableHeight, tableMaxHeight} from '@/assets/js/common.js'
import Pagination from '@/components/Pagination'
import {getUserInfo} from '@/api/sysmgt.js'
import {
  deleteData,
  downloadTemplate,
  exportData,
  importData,
  listBrandTree,
  listData,
  listYear,
  matchSbom,
  saveData,
  updateData
} from '@/api/sbommgt.js'

export default {
  name: 'sbomconfiglist',
  components: {Pagination},
  data() {
    return {
      activeName: 'electrophoretic',
      authorityType: "",
      electrophoretic: {
        searchForm: {
          code: '',
          name: '',
          destCode: '',
          destName: '',
        },
        pageSize: 20,
        currentPage: 1,
        total: 0,
        tableData: [],
        // confirmConfigFormVisible: false,
      },
      color: {
        searchForm: {
          code: '',
          name: '',
        },
        pageSize: 20,
        currentPage: 1,
        total: 0,
        tableData: [],
        // confirmConfigFormVisible: false,
      },
      vinToVin: {
        searchForm: {
          customVin: '',
          sapVin: '',
        },
        pageSize: 20,
        currentPage: 1,
        total: 0,
        tableData: [],
      },
      split: {
        searchForm: {
          code: '',
          name: '',
          inUse: '',
        },
        pageSize: 10,
        currentPage: 1,
        total: 0,
        tableData: [],
        // confirmConfigFormVisible: false,
      },
      noSupply: {
        searchForm: {
          code: '',
          name: '',
        },
        pageSize: 20,
        currentPage: 1,
        total: 0,
        tableData: [],
        // confirmConfigFormVisible: false,
      },
      materialCountry: {
        searchForm: {
          materialCode: '',
          configId: '',
          epcBrandId: '',
          epcModelId: '',
          epcYearId: '',
        },
        pageSize: 20,
        currentPage: 1,
        total: 0,
        tableData: [],
        languaList: [],
        epcBrandList: [],
        epcCarModelList: [],
        epcCarModelMap: {},
        configList: [],
        epcConfigMap: {},
        yearList: [],
        form: {
          title: "新增物料代码国家对照",
          visible: false,
          formLabelWidth: '150px',
          data: {
            materialCode: '',
            epcBrandId: '',
            epcModelId: '',
            configId: '',
          },
          rules: {
            materialCode: [{required: true, message: '物料代码不能为空', trigger: ['blur', 'change']}],
            epcBrandId: [{required: true, message: '品牌名称不能为空', trigger: ['blur', 'change']}],
            epcModelId: [{required: true, message: '车型名称不能为空', trigger: ['blur', 'change']}],
            configId: [{required: true, message: '配置名称不能为空', trigger: ['blur', 'change']}],
          }
        }
      },
      engineSplit: {
        searchForm: {
          code: '',
          name: '',
        },
        pageSize: 10,
        currentPage: 1,
        total: 0,
        tableData: [],
        // confirmConfigFormVisible: false,
      },
      form: {
        visible: false,
        formLabelWidth: '100px',
        data: {},
        type: "",
        electrophoretic: {
          title: '新增电泳件',
          rules: {
            code: [{required: true, message: '配件编码不能为空', trigger: ['blur', 'change']}],
            name: [{required: true, message: '配件名称不能为空', trigger: ['blur', 'change']}],
            destCode: [{required: true, message: '电泳件编码不能为空', trigger: ['blur', 'change']}],
            destName: [{required: true, message: '电泳件名称不能为空', trigger: ['blur', 'change']}],
          },
          entity: [
            {
              lable: "配件编码",
              prop: "code",
              placeholder: "请输入配件编码",

            }, {
              lable: "配件名称",
              prop: "name",
              placeholder: "请输入配件名称",

            }, {
              lable: "电泳件编码",
              prop: "destCode",
              placeholder: "请输入电泳件编码",

            }, {
              lable: "电泳件名称",
              prop: "destName",
              placeholder: "请输入电泳件名称",

            }],
        },
        split: {
          title: '新增总成件',
          rules: {
            code: [{required: true, message: '配件编码不能为空', trigger: ['blur', 'change']}],
            name: [{required: true, message: '配件名称不能为空', trigger: ['blur', 'change']}],
          },
          entity: [
            {
              lable: "配件编码",
              prop: "code",
              placeholder: "请输入配件编码",

            }, {
              lable: "配件名称",
              prop: "name",
              placeholder: "请输入配件名称",

            }, {
              lable: "单车用量",
              prop: "amount",
              type: "number",
              placeholder: "请输入单车用量",
            }, {
              lable: "备注",
              prop: "remark",
              placeholder: "请输入备注",

            }],
        },
        splitSub: {
          title: '新增拆分件',
          rules: {
            code: [{required: true, message: '配件编码不能为空', trigger: ['blur', 'change']}],
            name: [{required: true, message: '配件名称不能为空', trigger: ['blur', 'change']}],
          },
          entity: [
            {
              prop: "pid",
              hide: true,
            },
            {
              lable: "总成件编码",
              prop: "assemblyCode",
              readonly: true,
            }, {
              lable: "总成件名称",
              prop: "assemblyName",
              readonly: true,
            },
            {
              lable: "配件编码",
              prop: "code",
              placeholder: "请输入配件编码",

            }, {
              lable: "配件名称",
              prop: "name",
              placeholder: "请输入配件名称",

            }, {
              lable: "单车用量",
              prop: "amount",
              type: "number",
              placeholder: "请输入单车用量",

            }, {
              lable: "备注",
              prop: "remark",
              placeholder: "请输入备注",

            }],
        },
        noSupply: {
          title: '新增不供货配件',
          rules: {
            code: [{required: true, message: '配件编码不能为空', trigger: ['blur', 'change']}],
            name: [{required: true, message: '配件名称不能为空', trigger: ['blur', 'change']}],
          },
          entity: [
            {
              lable: "配件编码",
              prop: "code",
              placeholder: "请输入配件编码",

            }, {
              lable: "配件名称",
              prop: "name",
              placeholder: "请输入配件名称",

            }],
        },
        cngEngine: {

        },
        color: {
          title: '新增颜色件',
          rules: {
            code: [{required: true, message: '颜色件编码不能为空', trigger: ['blur', 'change']}],
            name: [{required: true, message: '颜色件名称不能为空', trigger: ['blur', 'change']}],
          },
          entity: [
            {
              lable: "颜色件编码",
              prop: "code",
              placeholder: "请输入颜色件编码",

            }, {
              lable: "颜色件名称",
              prop: "name",
              placeholder: "请输入颜色件名称",

            }],
        },
        vinToVin: {
          title: '新增VIN对照',
          rules: {
            customVin: [{required: true, message: '销售VIN不能为空', trigger: ['blur', 'change']}],
            sapVin: [{required: true, message: '生产VIN不能为空', trigger: ['blur', 'change']}],
          },
          entity: [
            {
              lable: "销售VIN",
              prop: "customVin",
              placeholder: "请输入销售VIN",

            }, {
              lable: "生产VIN",
              prop: "sapVin",
              placeholder: "请输入生产VIN",

            }],
        }
      },
      entityName: null,
      maximumHeight: 0,
    }
  },
  methods: {
    rowStyle({row, rowIndex}) {
      let styleJson = {
        "background": "var(--cell-bgColor)",
      }
      if (row.pid != 0) {
        if (row.children == null || row.children.length == 0) {
          return styleJson;
        } else {
          return {};
        }
      } else {
        return {};
      }
    },
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        if (this.$refs.electTable) {
          this.$refs.electTable.doLayout();
        }
        if (this.$refs.splitTable) {
          this.$refs.splitTable.doLayout();
        }
        if (this.$refs.noSupplyTable) {
          this.$refs.noSupplyTable.doLayout();
        }
        if (this.$refs.materialTable) {
          this.$refs.materialTable.doLayout();
        }
        if (this.$refs.cngTable) {
          this.$refs.cngTable.doLayout();
        }
        if (this.$refs.configTable) {
          this.$refs.configTable.doLayout();
        }
        if (this.$refs.vinTable) {
          this.$refs.vinTable.doLayout();
        }
        if (this.$refs.colorTable) {
          this.$refs.colorTable.doLayout();
        }
        if (this.$refs.engineTable) {
          this.$refs.engineTable.doLayout();
        }
      })
    },
    handleClick(tab, event) {
      this.getTableData(tab.name);
    },
    initParam(tableName) {
      let current = this[tableName];
      let params = {
        currentPage: current.currentPage,
        limit: current.pageSize,
      }
      for (let [key, value] of Object.entries(current.searchForm)) {
        params[key] = value.replace('[', '').replace(']', '');
      }
      return {current, params};
    },
    authorityState() {
      getUserInfo().then(res => {
        if (res.data.code == 100) {
          if (res.data.data.defaultRoleCode == "systemAdmin") {
            this.authorityType = true;
          } else {
            this.authorityType = false;
          }
        }
      })
    },
    // 数据
    getTableData(tableName) {
      this.$loading.show();
      let {current, params} = this.initParam(tableName);
      listData(params, tableName).then(res => {
        current.total = res.data.total
        current.tableData = res.data.data
        this.tableHeightArea();
        this.$loading.hide();
      })
    },
    keyDown(e) {
      if (e.keyCode === 13) {
        this.search(this.activeName);
      }
    },
    search(formName) {
      this[formName].currentPage = 1;
      this.getTableData(formName);
    },
    editRow(entityName, row) {
      this.entityName = entityName;
      for (let [key, value] of Object.entries(this.form[entityName])) {
        this.form[key] = value;
      }
      this.form.type = "edit";
      this.form.title = this.form[entityName].title.replace("新增", "更新");
      if (row && !row.children) {
        this.form.title = this.form.title.replace("总成件", "拆分件");
      }
      //设置表单数据
      let formData = {};
      let props = this.form.entity.map(e => e.prop);
      for (let [key, value] of Object.entries(row)) {
        if ('id' === key || props.includes(key)) {
          formData[key] = value;
        }
      }
      this.form.data = formData;
      this.form.visible = true;
      this.$nextTick(() => {
        this.$refs.addOrEditForm.clearValidate();
      })
    },
    add(entityName, row) {
      this.form.type = "add";
      this.entityName = entityName;
      for (let [key, value] of Object.entries(this.form[entityName])) {
        this.form[key] = value;
      }

      //重置表单数据
      this.form.data = {};
      //设置拆分件的分组数据
      if (row) {
        this.form.data.assemblyCode = row.code;
        this.form.data.assemblyName = row.name;
        this.form.data.pid = row.id;
      }
      this.form.visible = true;
      this.$nextTick(() => {
        this.$refs.addOrEditForm.clearValidate();
      })
    },
    cancel() {
      this.form.visible = false;
      this.form.cngEngine.visible = false;
      this.materialCountry.form.visible = false;
    },
    submit(formName) {
      console.log(formName)
      if (this.entityName === 'splitSub') {
        this.entityName = 'split';
      }
      this.$refs[formName].validate((valid) => {
          if (valid) {
            let params = {};
            if ('configMatch' === this.entityName || 'materialCountry' === this.entityName) {
              this.form.data = this[this.entityName].form.data;
            }
            for (let [key, value] of Object.entries(this.form.data)) {
              params[key] = value;
            }
            this.$loading.show()
            saveData(params, this.entityName).then((res) => {
              if (res.data.code === 100) {
                handleAlert('success', res.data.msg)
                this.getTableData(this.entityName)
                this.cancel();
              } else {
                handleAlert('error', res.data.msg)
              }
              this.$loading.hide()
            }).catch((err) => {
              this.$loading.hide()
              if (err !== null && err !== "" && err.responseText !== null) {
                handleAlert('error', "提交失败,请重试")
              }
            });
          } else {
            this.$loading.hide()
            handleAlert('error', "请完善信息")
          }
        }
      );
    },
    onBeforeUpload(file) {
      console.log(file)
      if (file.name.match(/\.[^.]*$/g)[0] !== '.xlsx') {
        handleAlert('warning', "上传的文件只能是 xlsx 格式!")
        return false;
      }
      if (file.size / 1024 / 1024 > 100) {
        handleAlert('warning', "上传的文件大小不能超过 100MB!")
        return false;
      }
      return true;
    },
    importTableData(file, tableName) {
      this.$loading.show()
      let formData = new FormData();
      formData.append('file', file.file);
      importData(formData, tableName).then(res => {
        if (res.data.code === 100) {
          handleAlert('success', '批量导入成功')
          this.getTableData(tableName)
        } else {
          this.$alert(res.data.msg, '信息提示', {dangerouslyUseHTMLString: true})
        }
        this.$loading.hide()
      }).catch(err => {
        this.$loading.hide()
        handleAlert('error', '请稍候重试')
      })
    },
    // 搜索
    reset(formName) {
      this.$refs[formName].resetFields();
      this[formName].currentPage = 1;
      this.getTableData(formName);
    },
    downloadTemplate(templateFilename) {
      downloadTemplate(templateFilename).then(res => {
        if (!res.data) {
          return
        }
        let blob = new Blob([res.data]);
        let url = window.URL.createObjectURL(blob);
        let aLink = document.createElement("a");
        aLink.style.display = "none";
        aLink.href = url;
        aLink.setAttribute("download", templateFilename);
        document.body.appendChild(aLink);
        aLink.click();
        document.body.removeChild(aLink); //下载完成移除元素
        window.URL.revokeObjectURL(url); //释放掉blob对象
      }).catch(err => {
        handleAlert('error', err)
      })
    },
    exportData(entityName) {
      this.$loading.show();
      let {current, params} = this.initParam(entityName);
      let postParam = new FormData();
      for (let [key, value] of Object.entries(params)) {
        postParam.append(key, value);
      }
      exportData(postParam, entityName).then(res => {
        if (!res.data) {
          handleAlert('error', '请稍候重试')
          return;
        }
        this.downloadRes(res);
      }).catch(err => {
        handleAlert('error', '请稍候重试')
        this.$loading.hide();
      })
    },
    downloadRes(res) {
      let fileName = decodeURI(res.headers['content-disposition'])
      if (fileName) {
        fileName = fileName.substring(fileName.indexOf('=') + 1)
      }
      let blob = new Blob([res.data]);
      let url = window.URL.createObjectURL(blob);
      let aLink = document.createElement("a");
      aLink.style.display = "none";
      aLink.href = url;
      aLink.setAttribute("download", fileName);
      document.body.appendChild(aLink);
      aLink.click();
      document.body.removeChild(aLink); //下载完成移除元素
      window.URL.revokeObjectURL(url); //释放掉blob对象
      this.$loading.hide();
    },
    toggleStatus(id, inUse, entityName) {
      updateData({inUse: inUse, id: id}, entityName).then(res => {
        if (res.data.code === 100) {
          handleAlert('success', '操作成功')
          this.getTableData(entityName)
        } else {
          this.$alert(res.data.msg, '信息提示', {dangerouslyUseHTMLString: true})
        }
      }).catch(err => {
        handleAlert('error', '请稍候重试')
      })
    },
    deleteRow(id, entityName) {
      this.$confirm('确定删除?', `删除数据`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteData({id: id}, entityName).then(res => {
          if (res.data.code === 100) {
            handleAlert('success', '删除成功')
            this.getTableData(entityName)
          } else {
            this.$alert(res.data.msg, '信息提示', {dangerouslyUseHTMLString: true})
          }
        }).catch(err => {
          handleAlert('error', '请稍候重试')
        })
      });
    },
    deleteRowAfterCheck(row, entityName) {
      /* if (row && row.children && row.children.length > 0) {
         handleAlert('error', '请先删除拆分件，再删除总成件')
         return;
       }*/
      this.deleteRow(row.id, entityName);
    },
    editConfigMatch(entityName, row) {
      console.log(entityName, row, this.configMatch.form.visible)
      listBrandTree().then(res => {
        this.configMatch.epcBrandList = res.data.data
      });

      this.form.type = "edit";
      // this.form.title = this.form[entityName].title.replace("新增", "更新");

      //设置表单数据
      let formData = {};
      for (let [key, value] of Object.entries(row)) {
        formData[key] = value;
      }
      this.configMatch.form.data = formData;
      this.configMatch.form.visible = true;
      this.$nextTick(() => {
        this.$refs.configMatchForm.clearValidate();
      })
    },
    addConfigMatch(entityName) {
      this.entityName = entityName;
      this[entityName].form.visible = true;
      listBrandTree().then(res => {
        this.configMatch.epcBrandList = res.data.data
      });

      this.form.type = "add";
      this.$nextTick(() => {
        this.$refs.configMatchForm.clearValidate();
      })
    },
    addMaterialCountry(entityName) {
      this.entityName = entityName;
      //重置表单数据
      // this.materialCountry.form.data = {};

      this[entityName].form.visible = true;
      this.$nextTick(() => {
        this.$refs.addMaterialCountry.clearValidate();
        this.$refs.addMaterialCountry.resetFields();
      })
      this.form.type = "add";
    },

    selectBrand(value) {
      let entity = this.materialCountry;
      entity.form.data.epcModelId = '';
      entity.form.data.configId = '';
      if (!value) {
        entity.carModelMap = {};
        entity.configMap = {}
        return;
      }
      entity.epcCarModelList = entity.epcBrandList.filter(e => e.id === value)[0].children;
      entity.epcCarModelMap = entity.epcCarModelList.reduce((group, item) => {
        let type = item.type === 1 ? "燃油车" : "新能源";
        group[type] = group[type] ?? [];
        group[type].push(item);
        return group;
      }, {});
      console.log(entity.epcCarModelMap)
    },
    selectModel(value) {
      this.materialCountry.searchForm.epcYearId = '';
      if (value) {
        listYear({"trainId": value}).then(res => {
          this.materialCountry.yearList = res.data.data;
        }).catch(err => {
          handleAlert('error', '请稍候重试')
        })
      } else {
        this.materialCountry.yearList = [];
      }

      console.log(value)
      let entity = this.materialCountry;
      // this.materialCountry.form.data.epcModelId = value;
      console.log(this.materialCountry.form.data)
      entity.form.data.configId = '';
      if (!value) {
        entity.epcConfigMap = {}
      }
      let configList = entity.epcCarModelList.filter(e => e.id === value)[0].children;
      entity.epcConfigMap = configList.reduce((group, item) => {
        let {year} = item;
        group[year] = group[year] ?? [];
        group[year].push(item);
        return group;
      }, {});
    },
    selectMaterialsBrand(value) {
      this.materialsToConfig.searchForm.epcModelId = '';
      // 没有值
      if (!value) {
        this.materialsToConfig.epcCarModelMap = {};
        this.materialsToConfig.epcConfigMap = {}
        return;
      }
      this.materialsToConfig.epcCarModelList = this.materialsToConfig.epcBrandList.filter(e => e.id === value)[0].children;
      this.materialsToConfig.epcCarModelMap = this.materialsToConfig.epcCarModelList.reduce((group, item) => {
        let type = item.type === 1 ? "燃油车" : "新能源";
        group[type] = group[type] ?? [];
        group[type].push(item);
        return group;
      }, {});
    },

    selectMaterialsModel(value) {
      if (!value) {
        this.materialsToConfig.epcConfigMap = {}
      }
      let configList = this.materialsToConfig.epcCarModelList.filter(e => e.id === value)[0].children;
      this.materialsToConfig.epcConfigMap = configList.reduce((group, item) => {
        let {year} = item;
        group[year] = group[year] ?? [];
        group[year].push(item);
        return group;
      }, {});
    },
    maximumArea() {
      setTimeout(() => {
        tableHeight()
        setTimeout(() => {
          this.maximumHeight = tableMaxHeight;
        }, 50)
      })
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    },
    addEngine(str1) {
      this.entityName = "cngEngine";
      this.form.data = this.form.cngEngine.data;
      this.form.cngEngine.visible = true;
      this.$nextTick(() => {
        this.$refs.addOrEditForm2.clearValidate();
      })
    },
    selectSearchBrand(value) {
      this.materialCountry.searchForm.epcModelId = '';
      this.materialCountry.searchForm.configId = '';
      if (!value) {
        this.materialCountry.epcCarModelMap = {};
        this.materialCountry.epcConfigMap = {}
        return;
      }
      this.materialCountry.epcCarModelList = this.materialCountry.epcBrandList.filter(e => e.id === value)[0].children;
      this.materialCountry.epcCarModelMap = this.materialCountry.epcCarModelList.reduce((group, item) => {
        let type = item.type === 1 ? "燃油车" : "新能源";
        group[type] = group[type] ?? [];
        group[type].push(item);
        return group;
      }, {});
    },
    selectSearchModel(value) {
      this.materialCountry.searchForm.epcYearId = '';
      if (value) {
        listYear({"trainId": value}).then(res => {
          this.materialCountry.yearList = res.data.data;
        }).catch(err => {
          handleAlert('error', '请稍候重试')
        })
      } else {
        this.materialCountry.yearList = [];
      }
    },
    matchSbomData() {
      this.$loading.show();
      const params = new FormData();
      // 添加搜索条件
      for (let [key, value] of Object.entries(this.materialCountry.searchForm)) {
        if (value !== '') {
          params.append(key, value);
        }
      }

      matchSbom(params).then(res => {
        if (!res.data) {
          handleAlert('error', '请稍候重试')
          return;
        }
        let fileName = decodeURI(res.headers['content-disposition'])
        if (fileName) {
          fileName = fileName.substring(fileName.indexOf('=') + 1)
        }
        let blob = new Blob([res.data]);
        let url = window.URL.createObjectURL(blob);
        let aLink = document.createElement("a");
        aLink.style.display = "none";
        aLink.href = url;
        aLink.setAttribute("download", fileName);
        document.body.appendChild(aLink);
        aLink.click();
        document.body.removeChild(aLink);
        window.URL.revokeObjectURL(url);
        this.$loading.hide();
      }).catch(err => {
        this.$loading.hide();
        handleAlert('error', '请稍候重试')
      })
    },
  },
  mounted() {
    this.tableHeightArea();
    this.getTableData('electrophoretic');
    this.authorityState()
    listBrandTree().then(res => {
      this.materialCountry.epcBrandList = res.data.data
    });
  },
  activated() {
    window.addEventListener('keydown', this.keyDown);
  },
  deactivated() {
    window.removeEventListener('keydown', this.keyDown, false);
  },
}
</script>
<style scoped>
/* tab */
.partsListManagement .el-tabs__nav-scroll {
  border-bottom: 1px solid #f1f1f1;
}
</style>
