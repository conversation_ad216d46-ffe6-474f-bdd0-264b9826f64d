<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" ref="searchForm" label-width="65px" :model="searchForm" class="demo-form-inline">
        <el-form-item label="配件编码" prop="code">
          <el-input v-model.trim="searchForm.code" placeholder="请输入配件编码"></el-input>
        </el-form-item>
        <el-form-item label="配件名称" prop="name">
          <el-input v-model.trim="searchForm.name" placeholder="请输入配件名称"></el-input>
        </el-form-item>
        <el-form-item label="VIN码" prop="vin">
          <el-input v-model.trim="searchForm.vin" placeholder="请输入VIN码"></el-input>
        </el-form-item>
        <el-form-item label="车型" prop="modelId">
          <el-select v-model="searchForm.modelId" placeholder="请选择车辆型号" filterable clearable @change="selectSearchModel">
            <el-option-group
              v-for="brand in brandList"
              :key="brand.id"
              :label="brand.nameCh">
              <el-option
                v-for="item in brand.children"
                :key="item.id"
                :label="item.nameCh"
                :value="item.id">
              </el-option>
            </el-option-group>
          </el-select>
        </el-form-item>
        <el-form-item label="年款" prop="yearId">
          <el-select v-model="searchForm.yearId" placeholder="请选择年款" clearable filterable>
            <el-option v-for="(item, index) in yearList" :key="index" :value="item.id" :label="item.year"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="developed">
          <el-select v-model="searchForm.developed" placeholder="请选择开发状态" clearable>
            <el-option label="已开发" value="1"></el-option>
            <el-option label="未开发" value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search()" icon="el-icon-search">搜索</el-button>
          <el-button plain @click="reset('searchForm')">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <div class="tableHandle">
        <el-button type="text" icon="el-icon-plus" @click="openAddDialog">新增</el-button>
        <el-button type="text" icon="el-icon-setting" @click="setStatus">设置状态</el-button>
        <el-button type="text" icon="bulkDown-icon" @click="exportData">批量下载</el-button>
        <el-button type="text" icon="bulkImport-icon" @click="openImportDialog()">批量上传</el-button>
        <el-button type="text" icon="el-icon-download" @click="downloadTemplate('SBOM上传模板.xlsx')">
          下载模板
        </el-button>
        <el-button type="text" icon="el-icon-delete" @click="openClearDialog(1)">清除SBOM</el-button>
        <el-tooltip content="统计会重置配件数据来源为EPC，然后根据单车BOM累计，重新设置数据来源为SAP；同时如果替换链条中存在已开发状态的配件，修改整个替换链条的配件开发状态为已开发。" effect="dark" placement="top">
          <el-button icon="el-icon-s-data" type="text" @click="openClearDialog(2)">统计SBOM</el-button>
        </el-tooltip>
      </div>
      <el-table
        style="width:100%;"
        border
        stripe
        ref="table"
        highlight-current-row
        :max-height="maximumHeight"
        :data="tableData"
        :header-cell-style="{
          'text-align': 'center',
          'background-color': 'var(--other-color)',
        }"
        @header-dragend="changeColWidth"
        @selection-change="handleSelectionChange"
        @sort-change="sortTable"
      >
        <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>
        <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="配件编码" sortable="custom" prop="code" min-width="180"></el-table-column>
        <el-table-column show-overflow-tooltip label="配件名称" sortable="custom" prop="name" min-width="160"></el-table-column>
        <el-table-column show-overflow-tooltip label="车型" prop="model" min-width="160"></el-table-column>
        <el-table-column show-overflow-tooltip label="年款" prop="yearName" width="160"></el-table-column>
        <el-table-column show-overflow-tooltip label="单车用量" prop="amount" min-width="80" align="center"></el-table-column>
        <!--        <el-table-column show-overflow-tooltip label="基本计量单位" prop="unit" min-width="105" align="center"></el-table-column>-->
        <el-table-column show-overflow-tooltip label="状态" prop="inUse" min-width="60" align="center">
          <template v-slot="{row}">
            <span style="color: green;" v-if="row.inUse">生效</span>
            <span style="color: red;" v-else>失效</span>
          </template>
        </el-table-column>
        <el-table-column label="开发状态" prop="developed" min-width="80" align="center">
          <template v-slot="{row}">
            <span v-if="row.developed">已开发</span>
            <span style="color: red;" v-else>未开发</span>
          </template>
        </el-table-column>
        <el-table-column label="是否总成件" prop="assembly" min-width="80" align="center">
          <template v-slot="{row}">
            <span style="color: green;" v-if="row.assembly">是</span>
            <span v-else>否</span>
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column label="操作" fixed="right" width="200">
          <template v-slot="{row}">
            <el-button type="text" size="small" @click="getConfigList(row)">
              适用车型配置
            </el-button>
            <el-button v-if="row.inUse" type="text" size="small" @click="toggleStatus(row.id,false)">
              停用
            </el-button>
            <el-button v-else type="text" size="small" @click="toggleStatus(row.id,true)">
              启用
            </el-button>

            <!--              <el-button type="text" size="small" @click="deleteRow(row.id,'configMatch')">
                            删除
                          </el-button>-->
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="currentPage" :limit.sync="pageSize" @pagination="getTableData"/>
    </div>

    <el-dialog v-dialogDrag width="700px !important" title="批量导入SBOM" :visible.sync="batchImportFormVisible" :close-on-click-modal="false">
      <el-form :rules="rules" ref="assignForm" :label-width="formLabelWidth" :model="assignForm">
        <el-form-item label="车辆品牌" prop="brandId">
          <el-select v-model="assignForm.brandId" placeholder="请选择车辆品牌" clearable filterable @change="selectBrand">
            <el-option
              v-for="item in brandList"
              :key="item.id"
              :label="item.nameCh"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="车辆型号" prop="modelId">
          <el-select v-model="assignForm.modelId" placeholder="请选择车辆型号" filterable clearable @change="selectModel">
            <el-option-group
              v-for="(model,group) in carModelMap"
              :key="group"
              :label="group">
              <el-option
                v-for="item in model"
                :key="item.id"
                :label="item.nameCh"
                :value="item.id">
              </el-option>
            </el-option-group>
          </el-select>
        </el-form-item>
        <el-form-item label="车型年款" prop="configId">
          <el-select v-model="assignForm.configId" placeholder="请选择车型年款" filterable clearable>
            <el-option
              v-for="item in configList"
              :key="item.id"
              :label="item.year"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-upload
            class="upload-demo"
            drag
            ref="celUpload"
            action="#"
            :show-file-list="false"
            :before-upload="onBeforeUpload"
            accept=".xlsx"
            :http-request="importData"
            :class="this.assignForm.configId?'':'hide'"
          >
            <!--          <el-button type="text" icon="el-icon-download">批量导入</el-button>-->
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">选择车型年款后上传，只能上传xlx/xlsx文件，且不超过100M</div>
          </el-upload>
        </el-form-item>


      </el-form>
    </el-dialog>
    <!--    清空SBOM-->
    <el-dialog v-dialogDrag width="700px !important" :title="clearFormTitle" :visible.sync="clearFormVisible" :close-on-click-modal="false">
      <el-form :rules="rules" ref="clearForm" :label-width="formLabelWidth" :model="assignForm">
        <el-form-item label="车辆品牌" prop="brandId">
          <el-select v-model="assignForm.brandId" placeholder="请选择车辆品牌" clearable filterable @change="selectBrand">
            <el-option
              v-for="item in brandList"
              :key="item.id"
              :label="item.nameCh"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="车辆型号" prop="modelId">
          <el-select v-model="assignForm.modelId" placeholder="请选择车辆型号" filterable clearable @change="selectModel">
            <el-option-group
              v-for="(model,group) in carModelMap"
              :key="group"
              :label="group">
              <el-option
                v-for="item in model"
                :key="item.id"
                :label="item.nameCh"
                :value="item.id">
              </el-option>
            </el-option-group>
          </el-select>
        </el-form-item>
        <el-form-item label="车型年款" prop="configId">
          <el-select v-model="assignForm.configId" placeholder="请选择车型年款" filterable clearable>
            <el-option
              v-for="item in configList"
              :key="item.id"
              :label="item.year"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div class="submitArea">
        <el-button type="primary" @click="clearSbom()">
          确认
        </el-button>
        <el-button @click="cancel()">
          取消
        </el-button>
      </div>
    </el-dialog>

    <el-dialog v-dialogDrag width="750px !important" title="适用车型" :visible.sync="configSbomTableVisible" :close-on-click-modal="false">
      <div>
        <el-table
          style="width:100%;"
          border
          stripe
          highlight-current-row
          :data="configSbomList"
          :header-cell-style="{
            'text-align': 'center',
            'background-color': 'var(--other-color)',
          }"
        >
          <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
          <el-table-column label="适用车型" prop="model" min-width="160"></el-table-column>
          <el-table-column label="年款" prop="year" min-width="160"></el-table-column>
          <el-table-column label="配置" prop="config" min-width="160"></el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <el-dialog v-dialogDrag width="500px !important" title="设置状态" :visible.sync="statusFormVisible" :close-on-click-modal="false">
      <div>
        <el-form ref="assignForm" :model="statusForm">
          <el-form-item label="状态" prop="inUse">
            <el-radio-group v-model="statusForm.inUse">
              <el-radio :label="true">生效</el-radio>
              <el-radio :label="false">失效</el-radio>
            </el-radio-group>
          </el-form-item>
          <div class="submitArea">
            <el-button type="primary" @click="submitStatus('sbom')">
              确认
            </el-button>
            <el-button @click="cancel()">
              取消
            </el-button>
          </div>
        </el-form>
      </div>
    </el-dialog>

    <!-- 新增SBOM弹窗 -->
    <el-dialog v-dialogDrag width="700px !important" title="新增SBOM" :visible.sync="addFormVisible" :close-on-click-modal="false">
      <el-form :rules="addFormRules" ref="addForm" :label-width="formLabelWidth" :model="addForm">
        <el-form-item label="车辆品牌" prop="brandId">
          <el-select v-model="addForm.brandId" placeholder="请选择车辆品牌" clearable filterable @change="selectBrandForAdd">
            <el-option
              v-for="item in brandList"
              :key="item.id"
              :label="item.nameCh"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="车辆型号" prop="modelId">
          <el-select v-model="addForm.modelId" placeholder="请选择车辆型号" filterable clearable @change="selectModelForAdd">
            <el-option-group
              v-for="(model,group) in carModelMap"
              :key="group"
              :label="group">
              <el-option
                v-for="item in model"
                :key="item.id"
                :label="item.nameCh"
                :value="item.id">
              </el-option>
            </el-option-group>
          </el-select>
        </el-form-item>
        <el-form-item label="车型年款" prop="yearId">
          <el-select v-model="addForm.yearId" placeholder="请选择车型年款" filterable clearable @change="selectYearForAdd">
            <el-option
              v-for="item in configList"
              :key="item.id"
              :label="item.year"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="适用配置" prop="configIds">
          <el-select v-model="addForm.configIds" multiple placeholder="请选择适用配置" filterable clearable>
            <el-option
              v-for="item in configOptionList"
              :key="item.id"
              :label="item.nameCh"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="配件编码" prop="code">
          <el-input v-model.trim="addForm.code" placeholder="请输入配件编码"></el-input>
        </el-form-item>
        <el-form-item label="配件名称" prop="name">
          <el-input v-model.trim="addForm.name" placeholder="请输入配件名称"></el-input>
        </el-form-item>
        <el-form-item label="单车用量" prop="amount">
          <el-input-number v-model="addForm.amount" :min="1" :max="999" :precision="0"></el-input-number>
        </el-form-item>
        <el-form-item label="是否总成件" prop="assembly">
          <el-switch v-model="addForm.assembly"></el-switch>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input type="textarea" v-model="addForm.remark" placeholder="请输入备注"></el-input>
        </el-form-item>
      </el-form>
      <div class="submitArea">
        <el-button type="primary" @click="submitAdd('addForm')">
          确认
        </el-button>
        <el-button @click="cancelAdd()">
          取消
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {handleAlert, tableHeight, tableMaxHeight} from '@/assets/js/common.js'
import Pagination from '@/components/Pagination'
import {
  clearSBOM,
  coverSBOM,
  downloadTemplate,
  exportSBOM,
  importSBOM,
  listBrandTree,
  listConfigByYearId,
  listConfigList,
  listSBOM,
  listYear,
  saveData,
  updateBatchById,
  updateData,
} from '@/api/sbommgt.js'

export default {
  name: 'sbomsbomlist',
  components: {Pagination},
  data() {
    return {
      tableData: [],
      pageSize: 20,
      currentPage: 1,
      total: 0,
      prop: '',
      order: '',
      searchForm: {
        code: '',
        name: '',
        vin: '',
        modelId: '',
        yearId: '',
        developed: ''
      },
      selectedIds: [],
      formLabelWidth: '100px',
      assignForm: {
        configId: '',
        brandId: '',
        modelId: '',
      },
      rules: {
        brandId: [{required: true, message: '车辆品牌不能为空', trigger: ['blur', 'change']}],
        modelId: [{required: true, message: '车辆车型不能为空', trigger: ['blur', 'change']}],
        configId: [{required: true, message: '车辆配置不能为空', trigger: ['blur', 'change']}]
      },
      batchImportFormVisible: false,
      clearFormVisible: false,
      clearFormTitle: "",
      formType: null,
      brandList: [],
      yearList: [],
      carModelList: [],
      carModelMap: {},
      configList: [],
      configMap: {},
      configSbomList: [],
      configSbomTableVisible: false,
      statusFormVisible: false,
      statusForm: {
        inUse: true
      },
      maximumHeight: 0,
      addFormVisible: false,
      addFormRules: {
        brandId: [{required: true, message: '车辆品牌不能为空', trigger: ['blur', 'change']}],
        modelId: [{required: true, message: '车辆车型不能为空', trigger: ['blur', 'change']}],
        yearId: [{required: true, message: '车辆年款不能为空', trigger: ['blur', 'change']}],
        configIds: [{required: true, message: '适用配置不能为空', trigger: ['blur', 'change']}],
        code: [{required: true, message: '配件编码不能为空', trigger: ['blur', 'change']}],
        name: [{required: true, message: '配件名称不能为空', trigger: ['blur', 'change']}],
        amount: [{required: true, message: '单车用量不能为空', trigger: ['blur', 'change']}],
        assembly: [{required: true, message: '是否总成件不能为空', trigger: ['blur', 'change']}]
      },
      addForm: {
        brandId: '',
        modelId: '',
        yearId: '',
        configIds: [],
        code: '',
        name: '',
        amount: 1,
        assembly: false,
        remark: ''
      },
      configOptionList: [],
    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    // 数据
    getTableData() {
      this.$loading.show();
      let params = {
        currentPage: this.currentPage,
        limit: this.pageSize,
        prop: this.prop,
        order: this.order,
      }
      for (let [key, value] of Object.entries(this.searchForm)) {
        params[key] = value;
      }
      listSBOM(params).then(res => {
        this.total = res.data.total
        this.tableData = res.data.data
        this.tableHeightArea();
        this.$loading.hide();
      })
    },
    // 搜索
    keyDown(e) {
      if (e.keyCode === 13) {
        this.search()
      }
    },
    search() {
      this.currentPage = 1
      this.getTableData()
    },
    reset(formName) {
      this.$refs[formName].resetFields();
      this.currentPage = 1
      this.getTableData();
    },
    handleSelectionChange(selections) {
      this.selectedIds = selections.map((item) => {
        return item.id;
      });
    },
    exportData() {
      let params = new FormData();
      for (let [key, value] of Object.entries(this.searchForm)) {
        params.append(key, value);
      }
      exportSBOM(params).then(res => {
        if (!res.data) {
          handleAlert('error', '请稍候重试')
          return;
        }
        this.downloadRes(res);
      }).catch(err => {
        handleAlert('error', '请稍候重试')
      })
    },
    downloadTemplate(templateFilename) {
      downloadTemplate(templateFilename).then(res => {
        if (!res.data) {
          return
        }
        let blob = new Blob([res.data]);
        let url = window.URL.createObjectURL(blob);
        let aLink = document.createElement("a");
        aLink.style.display = "none";
        aLink.href = url;
        aLink.setAttribute("download", templateFilename);
        document.body.appendChild(aLink);
        aLink.click();
        document.body.removeChild(aLink); //下载完成移除元素
        window.URL.revokeObjectURL(url); //释放掉blob对象
      }).catch(err => {
        handleAlert('error', err)
      })
    },
    downloadRes(res) {
      let fileName = decodeURI(res.headers['content-disposition'])
      if (fileName) {
        fileName = fileName.substring(fileName.indexOf('=') + 1)
      }
      let blob = new Blob([res.data]);
      let url = window.URL.createObjectURL(blob);
      let aLink = document.createElement("a");
      aLink.style.display = "none";
      aLink.href = url;
      aLink.setAttribute("download", fileName);
      document.body.appendChild(aLink);
      aLink.click();
      document.body.removeChild(aLink); //下载完成移除元素
      window.URL.revokeObjectURL(url); //释放掉blob对象
    },
    importData(file) {
      this.$loading.show()
      let formData = new FormData();
      formData.append('file', file.file);
      formData.append('yearId', this.assignForm.configId);
      importSBOM(formData).then(res => {
        if (res.data.code === 100) {
          handleAlert('success', '批量导入成功')
          this.getTableData()
        } else {
          this.$alert(res.data.msg, '信息提示', {dangerouslyUseHTMLString: true})
        }
        this.$loading.hide()
      }).catch(err => {
        this.$loading.hide()
        handleAlert('error', '请稍候重试')
      })
    },
    onBeforeUpload(file) {
      if (file.name.match(/\.[^.]*$/g)[0] !== '.xlsx') {
        handleAlert('warning', "上传的文件只能是 xlsx 格式!")
        return false;
      }
      if (file.size / 1024 / 1024 > 100) {
        handleAlert('warning', "上传的文件大小不能超过 100MB!")
        return false;
      }
      return true;
    },
    setStatus() {
      // 判断勾选项
      if (this.selectedIds.length === 0) {
        handleAlert('warning', "请选择SBOM后设置状态")
        return;
      }
      this.statusFormVisible = true;

    },
    submitStatus() {
      updateBatchById({inUse: this.statusForm.inUse, idList: this.selectedIds}, 'sbom').then(res => {
        if (res.data.code === 100) {
          this.getTableData();
          this.statusFormVisible = false;
        } else {
          this.$alert(res.data.msg, '信息提示', {dangerouslyUseHTMLString: true})
        }
      }).catch(err => {
        handleAlert('error', '请稍候重试')
      })
    },
    openImportDialog() {
      // 判断请求数据
      if (this.brandList.length === 0) {
        listBrandTree().then(res => {
          this.brandList = res.data.data
        });
      }
      //重置选择数据
      this.assignForm.brandId = '';
      this.assignForm.modelId = '';
      this.assignForm.configId = '';
      this.batchImportFormVisible = true;
      this.$nextTick(() => this.$refs.assignForm.clearValidate());
    },
    openClearDialog(type) {
      this.formType = type;
      this.clearFormTitle = type === 1 ? "清除SBOM" : "SBOM使用统计"
      // 判断请求数据
      if (this.brandList.length === 0) {
        listBrandTree().then(res => {
          this.brandList = res.data.data
        });
      }
      //重置选择数据
      this.assignForm.brandId = '';
      this.assignForm.modelId = '';
      this.assignForm.configId = '';
      this.clearFormVisible = true;
      //清除之前的校验结果
      this.$nextTick(() => this.$refs.clearForm.clearValidate());
    },
    clearSbom() {
      let formName = 'clearForm';
      this.$refs[formName].validate((valid) => {
          if (valid) {
            this.$loading.show();
            const params = new FormData();
            params.append("yearId", this.assignForm.configId);
            this.formType === 1 ? this.doClearSbom(params) : this.doCoverSbom(params);
          } else {
            this.$loading.hide();
            handleAlert('error', "请完善信息")
          }
        }
      );
    },
    doClearSbom(params) {
      clearSBOM(params).then((res) => {
        if (res.data.code === 100) {
          // handleAlert('success', res.data.msg)
          this.getTableData(this.entityName)
          this.$alert('清除成功', {
            confirmButtonText: '确定',
            type: 'info'
          }).then(() => {
            this.cancel();
          });
        } else {
          handleAlert('error', res.data.msg)
        }
        this.$loading.hide();
      }).catch((err) => {
        this.$loading.hide();
        if (err !== null && err !== "" && err.responseText !== null) {
          handleAlert('error', "提交失败,请重试")
        }
      });
    },
    doCoverSbom(params) {
      coverSBOM(params).then((res) => {
        if (res.data.code === 100) {
          // handleAlert('success', res.data.msg)
          this.$alert('统计完成', {
            confirmButtonText: '确定',
            type: 'info'
          }).then(() => {
            this.cancel();
          });
        } else {
          handleAlert('error', res.data.msg)
        }
        this.$loading.hide();
      }).catch((err) => {
        this.$loading.hide();
        if (err !== null && err !== "" && err.responseText !== null) {
          handleAlert('error', "提交失败,请重试")
        }
      });
    },
    selectBrand(value) {
      this.assignForm.modelId = '';
      this.assignForm.configId = '';
      if (!value) {
        this.carModelMap = {};
        this.configMap = {}
        return;
      }
      this.carModelList = this.brandList.filter(e => e.id === value)[0].children;
      this.carModelMap = this.carModelList.reduce((group, item) => {
        let type = item.type === 1 ? "燃油车" : "新能源";
        group[type] = group[type] ?? [];
        group[type].push(item);
        return group;
      }, {});
    },
    selectModel(value) {
      this.assignForm.configId = '';
      // if (!value) {
      //   this.configList = [];
      // }
      // this.configList = this.carModelList.filter(e => e.id === value)[0].children;
      if (value) {
        listYear({"trainId": value}).then(res => {
          this.configList = res.data.data;
        }).catch(err => {
          handleAlert('error', '请稍候重试')
        })
      } else {
        this.configList = [];
      }
    },
    cancel() {
      this.statusFormVisible = false;
      this.clearFormVisible = false;
      //清理数据
    },
    getConfigList(row) {
      listConfigList({yearId: row.yearId, sbomCode: row.code}).then(res => {
        if (res.data.code === 100) {
          this.configSbomList = res.data.data;
          this.configSbomTableVisible = true;
        } else {
          this.$alert(res.data.msg, '信息提示', {dangerouslyUseHTMLString: true})
        }
      }).catch(err => {
        handleAlert('error', '请稍候重试')
      })
    },
    selectSearchModel(value) {
      if (value) {
        listYear({"trainId": value}).then(res => {
          this.yearList = res.data.data;
          this.searchForm.yearId = '';
        }).catch(err => {
          handleAlert('error', '请稍候重试')
        })
      } else {
        this.yearList = [];
      }
    },
    toggleStatus(id, inUse) {
      updateData({inUse: inUse, id: id}, 'sbom').then(res => {
        if (res.data.code === 100) {
          this.getTableData()
        } else {
          this.$alert(res.data.msg, '信息提示', {dangerouslyUseHTMLString: true})
        }
      }).catch(err => {
        handleAlert('error', '请稍候重试')
      })
    },
    sortTable({column, prop, order}) {
      this.prop = prop;
      this.order = order;
      this.getTableData();
    },
    maximumArea() {
      setTimeout(() => {
        tableHeight()
        setTimeout(() => {
          this.maximumHeight = tableMaxHeight;
        }, 50)
      })
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    },
    openAddDialog() {
      this.addFormVisible = true;
      this.$nextTick(() => {
        this.resetFormData();
      });
    },
    // 处理所有级联数据的清理
    clearCascadeData(level) {
      // level 参数用于控制从哪一级开始清除：1=品牌, 2=型号, 3=年款, 4=配置
      if (level <= 1) {
        // 清除品牌及以下所有级别
        this.addForm.brandId = "";
        this.carModelMap = {};
      }

      if (level <= 2) {
        // 清除型号及以下所有级别
        this.addForm.modelId = "";
        this.configList = [];
      }

      if (level <= 3) {
        // 清除年款及以下所有级别
        this.addForm.yearId = "";
        this.configOptionList = [];
      }

      if (level <= 4) {
        // 清除配置
        this.addForm.configIds = [];
      }
    },

    // 重置表单的统一方法
    resetFormData() {
      this.$refs.addForm.resetFields()
      // 清除所有级联数据
      this.clearCascadeData(1);
    },
    cancelAdd() {
      this.addFormVisible = false;
    },
    selectBrandForAdd(value) {
      if (!value) {
        // 当品牌被清除时，级联清除所有下级数据
        this.clearCascadeData(1);
        return;
      }
      // 正常的品牌选择逻辑
      this.carModelList = this.brandList.filter(e => e.id === value)[0].children;
      this.carModelMap = this.carModelList.reduce((group, item) => {
        let type = item.type === 1 ? "燃油车" : "新能源";
        group[type] = group[type] ?? [];
        group[type].push(item);
        return group;
      }, {});
      // 清除下级数据
      this.clearCascadeData(2);
    },
    selectModelForAdd(value) {
      if (!value) {
        // 当型号被清除时，级联清除所有下级数据
        this.clearCascadeData(2);
        return;
      }
      // 正常的型号选择逻辑
      listYear({"trainId": value}).then(res => {
        this.configList = res.data.data;
      }).catch(err => {
        handleAlert('error', '请稍候重试')
      });
      // 清除下级数据
      this.clearCascadeData(3);
    },
    selectYearForAdd(value) {
      if (!value) {
        // 当年款被清除时，级联清除所有下级数据
        this.clearCascadeData(3);
        return;
      }
      // 正常的年款选择逻辑
      this.addForm.yearId = value;
      listConfigByYearId({"yearId": value}).then(res => {
        this.configOptionList = res.data.data;
      }).catch(err => {
        handleAlert('error', '请稍候重试')
      });
      // 清除下级数据
      this.clearCascadeData(4);
    },
    submitAdd(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$loading.show();
          const params = {};
          for (let [key, value] of Object.entries(this.addForm)) {
            params[key] = value;
          }
          saveData(params, 'sbom').then(res => {
            if (res.data.code === 100) {
              this.getTableData();
              this.addFormVisible = false;
            } else {
              this.$alert(res.data.msg, '信息提示', {dangerouslyUseHTMLString: true})
            }
            this.$loading.hide();
          }).catch(err => {
            this.$loading.hide();
            handleAlert('error', '请稍候重试')
          })
        } else {
          this.$loading.hide();
          handleAlert('error', '请完善信息')
        }
      });
    },


  },
  mounted() {
    this.tableHeightArea();
    this.getTableData();
    listBrandTree().then(res => {
      this.brandList = res.data.data
    });
  },
  activated() {
    window.addEventListener('keydown', this.keyDown);
  },
  deactivated() {
    window.removeEventListener('keydown', this.keyDown, false);
  },
}
</script>
<style scoped>
.el-button {
  margin: 0 10px;
}

.el-divider--horizontal {
  margin-left: -66px;
  width: calc(100% + 110px);
}

.hide {
  display: none;
}
</style>
