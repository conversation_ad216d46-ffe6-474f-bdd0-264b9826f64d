<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" ref="searchForm" label-width="65px" :model="searchForm" class="demo-form-inline">
        <el-form-item label="VIN码" prop="vin">
          <el-input v-model.trim="searchForm.vin" placeholder="请输入VIN码"></el-input>
        </el-form-item>
        <el-form-item label="物料代码" prop="materials">
          <el-input v-model.trim="searchForm.materials" placeholder="请输入物料代码"></el-input>
        </el-form-item>
        <el-form-item label="车辆品牌" prop="brand">
          <el-input v-model.trim="searchForm.brand" placeholder="请输入车辆品牌"></el-input>
        </el-form-item>
        <el-form-item label="车辆型号" prop="model">
          <el-input v-model.trim="searchForm.model" placeholder="请输入车辆型号"></el-input>
        </el-form-item>
        <el-form-item label="车型配置" prop="modelConfig">
          <el-input v-model.trim="searchForm.modelConfig" placeholder="请输入车型配置"></el-input>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="未开始" value="NOT_STARTED"></el-option>
            <el-option label="待确认" value="TO_BE_CONFIRMED"></el-option>
            <el-option label="等待SBOM" value="WAITING_SBOM"></el-option>
            <el-option label="准备完毕" value="READY"></el-option>
            <el-option label="处理中" value="PROCESSING"></el-option>
            <!--            <el-option label="处理异常" value="45"></el-option>-->
            <el-option label="已完成" value="COMPLETED"></el-option>
            <el-option label="已忽略" value="IGNORE"></el-option>
            <el-option label="已停止" value="STOPPED"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search()" icon="el-icon-search">搜索</el-button>
          <el-button plain @click="reset('searchForm')">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <div class="tableHandle">
        <el-button type="text" icon="bulkDown-icon" @click="exportData">批量下载</el-button>
        <el-button type="text" icon="el-icon-s-promotion" @click="startProcess">开始处理</el-button>
      </div>
      <el-table
        style="width:100%;"
        border
        stripe
        ref="table"
        highlight-current-row
        :max-height="maximumHeight"
        :data="tableData"
        :header-cell-style="{
          'text-align': 'center',
          'background-color': 'var(--other-color)',
        }"
        @header-dragend="changeColWidth"
        @selection-change="handleSelectionChange"
      >
        <!-- <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>-->
        <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="VIN" prop="vin" min-width="160"></el-table-column>
        <el-table-column show-overflow-tooltip label="品牌" prop="brand" width="100"></el-table-column>
        <el-table-column show-overflow-tooltip label="车辆型号" prop="model" min-width="100"></el-table-column>
        <el-table-column show-overflow-tooltip label="车辆年款" prop="modelYear" min-width="100"></el-table-column>
        <el-table-column show-overflow-tooltip label="车型配置" prop="modelConfig" min-width="100" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="公告型号" prop="announceModel" min-width="180"></el-table-column>
        <el-table-column show-overflow-tooltip label="物料代码" prop="materials" min-width="220"></el-table-column>
        <el-table-column show-overflow-tooltip label="生产订单号" prop="saleOrder" min-width="150"></el-table-column>
        <el-table-column show-overflow-tooltip label="销售地和形式" prop="marketAndForm" min-width="150"></el-table-column>
        <el-table-column show-overflow-tooltip label="国家" prop="countryName" min-width="150"></el-table-column>
        <el-table-column show-overflow-tooltip label="发动机型号" prop="engineModel" min-width="150"></el-table-column>
        <el-table-column show-overflow-tooltip label="车辆颜色" prop="vehicleColor" min-width="150" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="轮胎规格" prop="tyreSize" min-width="150" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="中控" prop="console" min-width="150" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="座椅数量" prop="seatsNumber" min-width="120" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="EPS" prop="eps" min-width="150" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="轮辋材料" prop="rimMaterial" min-width="120" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="电喷系统" prop="efi" min-width="150"></el-table-column>
        <el-table-column show-overflow-tooltip label="后桥速比" prop="rearAxleRatio" min-width="150"></el-table-column>
        <el-table-column show-overflow-tooltip label="状态" prop="status" fixed="right" min-width="100" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="VIN发货日期" prop="deliveryTime" fixed="right" min-width="150" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="VIN创建日期" prop="vinCreated" min-width="150" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="更新日期" prop="updatedTime" min-width="150" align="center">
          <template v-slot="{row}">
            <span v-if="row.updatedTime">{{ row.updatedTime }}</span>
            <span v-else>{{ row.createTime }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="150">
          <template v-slot="{row}">
            <el-button type="text" size="small" @click="getDetail(row)">
              售后专用件
            </el-button>
            <div v-if="row.status === '已忽略'" style="display: inline;margin-left:10px;">
              <el-button type="text" size="small" @click="revertIgnore(row)">
                取消忽略
              </el-button>
            </div>
            <div v-else-if="row.status === '未开始'||row.status === '待确认'" style="display: inline;margin-left:10px;">
              <el-button type="text" size="small" @click="ignoreVin(row)">
                忽略
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="currentPage" :limit.sync="pageSize" @pagination="getTableData"/>
    </div>
  </div>
</template>
<script>
import {addTabs, handleAlert, tableHeight, tableMaxHeight} from '@/assets/js/common.js'
import Pagination from '@/components/Pagination'
import {
  listVIN,
  startVIN,
  exportVehicleInfo,
  revertIgnoreVIN, ignoreVIN,
} from '@/api/sbommgt.js'

export default {
  name: 'sbomvehiclelist',
  components: {Pagination},
  data() {
    return {
      tableData: [],
      pageSize: 20,
      currentPage: 1,
      total: 0,
      searchForm: {
        vin: '',
        materials: '',
        brand: '',
        model: '',
        modelConfig: '',
        status: '',

      },
      selectedIds: [],
      maximumHeight: 0,
    }
  },
  methods: {
    ignoreVIN,
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    getDetail(row) {
      sessionStorage.materialCode = row.materials;
      this.$router.push('/sbom/afterSalePart/part').then(() => {
          addTabs(this.$route.path, `${row.materials}`);
        }
      );
    },
    // 数据
    getTableData() {
      this.$loading.show();
      let params = {
        currentPage: this.currentPage,
        limit: this.pageSize,
        vin: this.searchForm.vin,
        materials: this.searchForm.materials,
        brand: this.searchForm.brand,
        model: this.searchForm.model,
        modelConfig: this.searchForm.modelConfig,
        status: this.searchForm.status,
      }

      const formData = new FormData();
      for (let paramsKey in params) {
        formData.append(paramsKey, params[paramsKey])
      }
      listVIN(formData).then(res => {
        this.total = res.data.total
        this.tableData = res.data.data
        this.tableHeightArea()
        this.$loading.hide();
      })
    },
    // 搜索
    keyDown(e) {
      if (e.keyCode === 13) {
        this.search()
      }
    },
    search() {
      this.currentPage = 1
      this.getTableData()
    },
    reset(formName) {
      this.$refs[formName].resetFields();
      this.currentPage = 1
      this.getTableData();
    },
    downloadRes(res) {
      let fileName = decodeURI(res.headers['content-disposition'])
      if (fileName) {
        fileName = fileName.substring(fileName.indexOf('=') + 1)
      }
      let blob = new Blob([res.data]);
      let url = window.URL.createObjectURL(blob);
      let aLink = document.createElement("a");
      aLink.style.display = "none";
      aLink.href = url;
      aLink.setAttribute("download", fileName);
      document.body.appendChild(aLink);
      aLink.click();
      document.body.removeChild(aLink); //下载完成移除元素
      window.URL.revokeObjectURL(url); //释放掉blob对象
      this.$loading.hide();
    },
    exportData() {
      let params = new FormData();
      this.$loading.show();
      for (let [key, value] of Object.entries(this.searchForm)) {
        params.append(key, value);
      }
      exportVehicleInfo(params).then(res => {
        if (!res.data) {
          this.$loading.hide();
          handleAlert('error', '请稍候重试')
          return;
        }
        this.downloadRes(res);
      }).catch(err => {
        this.$loading.hide();
        console.error(err)
        handleAlert('error', '请稍候重试')
      })
    },
    handleSelectionChange(selections) {
      console.log(selections)
      this.selectedIds = selections.map((item) => {
        return item.id;
      });
    },
    maximumArea() {
      setTimeout(() => {
        tableHeight()
        setTimeout(() => {
          this.maximumHeight = tableMaxHeight;
        }, 50)
      })
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    },
    startProcess() {
      console.log(1)
      startVIN().then(res => {
        if (res.data.code === 100) {
          handleAlert("success", "操作成功，已开始处理VIN")
        } else {
          handleAlert('error', res.data.msg + " ，请稍候重试")
        }
      }).catch(err => {
        console.error(err)
        handleAlert('error', '请稍候重试')
      })
    },
    revertIgnore(row) {
      revertIgnoreVIN({vin: row.vin}).then((res) => {
        if (res.data.code === 100) {
          handleAlert('success', res.data.msg)
        } else {
          handleAlert('error', res.data.msg)
        }
        this.getTableData();
      }).catch((err) => {
        if (err !== null && err !== "" && err.responseText !== null) {
          handleAlert('error', "提交失败,请重试")
        }
      });
    },
    ignoreVin(row) {
      ignoreVIN({vin: row.vin}).then((res) => {
        if (res.data.code === 100) {
          handleAlert('success', res.data.msg)
        } else {
          handleAlert('error', res.data.msg)
        }
        this.getTableData();
      }).catch((err) => {
        if (err !== null && err !== "" && err.responseText !== null) {
          handleAlert('error', "提交失败,请重试")
        }
      });
    }
  },
  mounted() {
    this.tableHeightArea()
    this.getTableData();
  },
  activated() {
    window.addEventListener('keydown', this.keyDown);
  },
  deactivated() {
    window.removeEventListener('keydown', this.keyDown, false);
  },
}
</script>
<style scoped>
.el-button {
  margin: 0 10px;
}
</style>
