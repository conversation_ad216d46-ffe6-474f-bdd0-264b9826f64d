<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" ref="searchForm" label-width="65px" :model="searchForm" class="demo-form-inline">
        <el-form-item label="物料代码" prop="materialCode">
          <el-input v-model.trim="searchForm.materialCode" placeholder="请输入物料代码"></el-input>
        </el-form-item>
        <el-form-item label="车辆品牌" prop="brand">
          <el-input v-model.trim="searchForm.brand" placeholder="请输入车辆品牌"></el-input>
        </el-form-item>
        <el-form-item label="车辆型号" prop="model">
          <el-input v-model.trim="searchForm.model" placeholder="请输入车辆型号"></el-input>
        </el-form-item>
        <el-form-item label="车辆年款" prop="year">
          <el-input v-model.trim="searchForm.year" placeholder="请输入车辆年款"></el-input>
        </el-form-item>
        <el-form-item label="车辆配置" prop="config">
          <el-input v-model.trim="searchForm.config" placeholder="请输入车辆配置"></el-input>
        </el-form-item>
        <el-form-item label="状态" prop="isProcessed">
          <el-select v-model="searchForm.isProcessed" placeholder="请选择状态" clearable>
            <el-option label="已处理" value="true"></el-option>
            <el-option label="未处理" value="false"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search()" icon="el-icon-search">搜索</el-button>
          <el-button plain @click="reset('searchForm')">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <div class="tableHandle">
        <el-upload
          class="upload-demo inline-block"
          ref="celUpload"
          action="#"
          :show-file-list="false"
          :before-upload="onBeforeUpload"
          accept=".xlsx"
          :http-request="importData"
        >
          <el-button type="text" icon="bulkImport-icon">批量上传</el-button>
        </el-upload>
        <el-button type="text" icon="bulkDown-icon" @click="exportData()">批量下载</el-button>
        <el-button type="text" icon="el-icon-download" @click="downloadTemplate('售后专用件上传模板.xlsx')">下载模板
        </el-button>

      </div>
      <el-table
        style="width:100%;"
        border
        stripe
        ref="mbomTable"
        highlight-current-row
        :max-height="maximumHeight"
        :data="tableData"
        :header-cell-style="{
              'text-align': 'center',
              'background-color': 'var(--other-color)',
            }"
        @header-dragend="changeColWidth"
        @sort-change="sortTable"
      >
        <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="物料代码" prop="materialCode" min-width="200"></el-table-column>
        <el-table-column show-overflow-tooltip label="品牌" prop="brand" min-width="130"></el-table-column>
        <el-table-column show-overflow-tooltip label="车辆型号" prop="model" min-width="130"></el-table-column>
        <el-table-column show-overflow-tooltip label="车辆年款" prop="year" min-width="130"></el-table-column>
        <el-table-column show-overflow-tooltip label="车型配置" prop="config" min-width="130"></el-table-column>
        <el-table-column show-overflow-tooltip label="处理状态" prop="isProcessed" min-width="100" align="center">
          <template v-slot="{row}">
            <span style="color: green;" v-if="row.isProcessed">已处理</span>
            <span style="color: red;" v-else>未处理</span>
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip label="更新人员" prop="updatedByName" min-width="160"></el-table-column>
        <el-table-column show-overflow-tooltip label="更新日期" prop="updatedTime" min-width="160"></el-table-column>
        <el-table-column label="操作" fixed="right" width="200">
          <template v-slot="{row}">
            <el-button type="text" size="small" @click="onlineImport(row)">
              在线导入
            </el-button>
            <el-button type="text" size="small" v-if="row.isProcessed" @click="getDetail(row)">
              明细管理
            </el-button>
            <el-button type="text" size="small" v-if="!row.isProcessed" @click="ignorePartAggregation(row)">
              {{ row.isIgnored ? "取消忽略" : "忽略" }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total>0" :total="total" :page.sync="currentPage" :limit.sync="pageSize" @pagination="getTableData()"/>
    </div>
    <el-dialog v-dialogDrag width="1000px !important" height="900px !important" title="售后专用件在线导入" :visible.sync="addFormVisible" :close-on-click-modal="false">

      <div class="secondFloat">
        <el-form :inline="true" ref="part.searchForm" label-width="65px" :model="part.searchForm" class="demo-form-inline">
          <el-form-item label="物料代码" prop="materialCode">
            <el-input v-model.trim="part.searchForm.materialCode" placeholder="请输入物料代码"></el-input>
          </el-form-item>
          <el-form-item label="配件名称" prop="name">
            <el-input v-model.trim="part.searchForm.name" placeholder="请输入配件名称"></el-input>
          </el-form-item>
          <el-form-item label="配件编码" prop="code">
            <el-input v-model.trim="part.searchForm.code" placeholder="请输入配件编码"></el-input>
          </el-form-item>
          <el-form-item label="车辆品牌" prop="brand">
            <el-select clearable filterable v-model.trim="part.searchForm.brand" placeholder="请选择车辆品牌">
              <el-option
                v-for="item in part.brandList"
                :key="item"
                :label="item"
                :value="item">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="车辆型号" prop="model">
            <el-select clearable filterable v-model.trim="part.searchForm.model" placeholder="请选择车辆型号">
              <el-option
                v-for="item in part.modelList"
                :key="item"
                :label="item"
                :value="item">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="车辆年款" prop="year">
            <el-select clearable filterable v-model.trim="part.searchForm.year" placeholder="请选择车辆年款">
              <el-option
                v-for="item in part.yearList"
                :key="item"
                :label="item"
                :value="item">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="车辆配置" prop="config">
            <el-select clearable filterable v-model.trim="part.searchForm.config" placeholder="请选择车辆配置">
              <el-option
                v-for="item in part.configList"
                :key="item"
                :label="item"
                :value="item">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="search('part')" icon="el-icon-search">搜索</el-button>
            <el-button plain @click="reset('part.searchForm')">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <div class="tableDetail">
        <div class="tableHandle">
          <el-button type="text" icon="bulkDown-icon" @click="confirmImport()">确认导入</el-button>
        </div>
        <el-table
          style="width:100%;"
          border
          stripe
          ref="mbomTable"
          highlight-current-row
          :max-height="540"
          :data="part.tableData"
          :header-cell-style="{
              'text-align': 'center',
              'background-color': 'var(--other-color)',
            }"
          @header-dragend="changeColWidth"
          @sort-change="sortTable"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>
          <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
          <el-table-column show-overflow-tooltip label="配件编码" prop="code" min-width="130"></el-table-column>
          <el-table-column show-overflow-tooltip label="配件名称" prop="name" min-width="130"></el-table-column>
        </el-table>
        <pagination v-show="part.total>0" :total="part.total" :page.sync="part.currentPage" :limit.sync="part.pageSize" @pagination="getPart()"/>
      </div>
    </el-dialog>

  </div>
</template>
<script>
import {addTabs, handleAlert, tableHeight, tableMaxHeight} from '@/assets/js/common.js'
import Pagination from '@/components/Pagination'
import {
  importData,
  downloadTemplate,
  listData,
  exportData,
  listPartAggregation,
  onlineImport,
  listSelection, ignorePartAggregation
} from '@/api/sbommgt.js'

export default {
  name: 'sbomafterSalePartlist',
  components: {Pagination},
  data() {
    return {
      tableData: [],
      pageSize: 20,
      currentPage: 1,
      total: 0,
      searchForm: {
        materialCode: '',
        brand: '',
        model: '',
        year: '',
        config: '',
        isProcessed: '',

      },
      selectedIds: [],
      currentMaterialCode: '',
      maximumHeight: 0,
      addFormVisible: false,
      formLabelWidth: '100px',
      rules: {
        materialCode: [{required: true, message: '物料代码不能为空', trigger: ['blur', 'change']}],
        code: [{required: true, message: '配件编码不能为空', trigger: ['blur', 'change']}],
        name: [{required: true, message: '配件名称不能为空', trigger: ['blur', 'change']}],
      },
      addForm: {
        materialCode: '',
        code: '',
        name: '',
      },
      part: {
        tableData: [],
        pageSize: 15,
        currentPage: 1,
        total: 0,
        brandList: [],
        modelList: [],
        yearList: [],
        configList: [],
        searchForm: {
          materialCode: '',
          brand: '',
          model: '',
          year: '',
          config: '',
          code: '',
          name: '',
        },
      }
    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    // 数据
    getTableData() {
      this.$loading.show();
      let params = new URLSearchParams();
      params.append('currentPage', this.currentPage);
      params.append('limit', this.pageSize);
      params.append('prop', this.prop);
      params.append('order', this.order);

      Object.keys(this.searchForm).forEach(key => {
        params.append(key, this.searchForm[key]);
      });
      listData(params, 'materialAggregation').then(res => {
        this.total = res.data.total;
        this.tableData = res.data.data
        this.tableHeightArea();
        this.$loading.hide();
      })
    },
    getPart() {
      let param = {
        currentPage: this.part.currentPage,
        limit: this.part.pageSize,
        materialCode: this.part.searchForm.materialCode,
        brand: this.part.searchForm.brand,
        model: this.part.searchForm.model,
        year: this.part.searchForm.year,
        config: this.part.searchForm.config,
        code: this.part.searchForm.code,
        name: this.part.searchForm.name,
      };
      let params = new URLSearchParams();
      for (let [key, value] of Object.entries(param)) {
        params.append(key, value);
      }
      listPartAggregation(params).then(res => {
        this.part.total = res.data.total;
        this.part.tableData = res.data.data
        this.tableHeightArea();
      })
    },
    // 搜索
    keyDown(e) {
      if (e.keyCode === 13) {
        this.search()
      }
    },
    search(entity) {
      if (entity === 'part') {
        this.part.currentPage = 1;
        this.getPart();
      } else {
        this.currentPage = 1;
        this.getTableData()
      }
    },
    reset(formName) {
      this.$refs[formName].resetFields();
      if (formName === 'part.searchForm') {
        this.part.currentPage = 1;
        this.getPart();
      } else {
        this.currentPage = 1;
        this.getTableData();
      }
    },
    downloadTemplate(templateFilename) {
      downloadTemplate(templateFilename).then(res => {
        if (!res.data) {
          return
        }
        let blob = new Blob([res.data]);
        let url = window.URL.createObjectURL(blob);
        let aLink = document.createElement("a");
        aLink.style.display = "none";
        aLink.href = url;
        aLink.setAttribute("download", templateFilename);
        document.body.appendChild(aLink);
        aLink.click();
        document.body.removeChild(aLink); //下载完成移除元素
        window.URL.revokeObjectURL(url); //释放掉blob对象
      }).catch(err => {
        handleAlert('error', err)
      })
    },
    downloadRes(res) {
      let fileName = decodeURI(res.headers['content-disposition'])
      if (fileName) {
        fileName = fileName.substring(fileName.indexOf('=') + 1)
      }
      let blob = new Blob([res.data]);
      let url = window.URL.createObjectURL(blob);
      let aLink = document.createElement("a");
      aLink.style.display = "none";
      aLink.href = url;
      aLink.setAttribute("download", fileName);
      document.body.appendChild(aLink);
      aLink.click();
      document.body.removeChild(aLink); //下载完成移除元素
      window.URL.revokeObjectURL(url); //释放掉blob对象
    },
    exportData() {
      let params = new FormData();
      for (let [key, value] of Object.entries(this.searchForm)) {
        params.append(key, value);
      }
      console.log(params)
      exportData(params, 'materialAggregation').then(res => {
        if (!res.data) {
          handleAlert('error', '请稍候重试')
          return;
        }
        this.downloadRes(res);
      }).catch(err => {
        console.error(err)
        handleAlert('error', '请稍候重试')
      })
    },
    handleSelectionChange(selections) {
      console.log(selections)
      this.selectedIds = selections.map((item) => {
        return item.id;
      });
    },
    maximumArea() {
      setTimeout(() => {
        tableHeight()
        setTimeout(() => {
          this.maximumHeight = tableMaxHeight;
        }, 50)
      })
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    },
    onBeforeUpload(file) {
      if (file.name.match(/\.[^.]*$/g)[0] !== '.xlsx') {
        handleAlert('warning', "上传的文件只能是 xlsx 格式!")
        return false;
      }
      if (file.size / 1024 / 1024 > 100) {
        handleAlert('warning', "上传的文件大小不能超过 100MB!")
        return false;
      }
      return true;
    },
    importData(file) {
      this.$loading.show()
      let formData = new FormData();
      formData.append('file', file.file);
      importData(formData, "afterSalePart").then(res => {
        if (res.data.code === 100) {
          handleAlert('success', '批量上传成功')
          this.getTableData()
        } else {
          this.$alert(res.data.msg, '信息提示', {dangerouslyUseHTMLString: true})
        }
        this.$loading.hide()
      }).catch(err => {
        this.$loading.hide()
        handleAlert('error', '请稍候重试')
      })
    },
    sortTable({column, prop, order}) {
      this.prop = prop;
      this.order = order;
      this.getTableData();
    },
    getDetail(row) {
      this.$router.push('/sbom/afterSalePart/part').then(() => {
          addTabs(this.$route.path, `${row.materialCode}`);
        }
      );
      sessionStorage.materialCode = row.materialCode;
    },
    ignorePartAggregation(row) {
      ignorePartAggregation({id: row.id, isIgnored: !row.isIgnored}).then((res) => {
        if (res.data.code === 100) {
          handleAlert('success', res.data.msg)
        } else {
          handleAlert('error', res.data.msg)
        }
        this.getTableData()
      }).catch((err) => {
        if (err !== null && err !== "" && err.responseText !== null) {
          handleAlert('error', "提交失败,请重试")
        }
      });
    },
    onlineImport(row) {
      this.getPart();
      this.listSelection();
      this.currentMaterialCode = row.materialCode;
      this.addFormVisible = true;
    },
    cancel() {
      this.addFormVisible = false;
    },
    confirmImport() {
      // 判断勾选项
      if (this.selectedIds.length === 0) {
        handleAlert('warning', "请选择售后专用件")
        return;
      }
      this.$loading.show()
      let param = {
        selectedIds: this.selectedIds,
        currentMaterialCode: this.currentMaterialCode,
      };
      onlineImport(param).then(res => {
        if (res.data.code === 100) {
          handleAlert('success', '在线导入成功')
          this.getTableData();
        } else {
          this.$alert(res.data.msg, '信息提示', {dangerouslyUseHTMLString: true})
        }
        this.$loading.hide()
      }).catch(err => {
        this.$loading.hide()
        handleAlert('error', '请稍候重试')
      })
    },
    listSelection() {
      listSelection().then(res => {
        const list = res.data.data;
        this.part.brandList = this.getList(list, 'brand');
        this.part.modelList = this.getList(list, 'model');
        this.part.yearList = this.getList(list, 'year');
        this.part.configList = this.getList(list, 'config');
      })
    },
    getList(list, prop) {
      let propList = list.filter(e => e[prop]).map(e => e[prop]);
      return Array.from(new Set(propList));
    },
  },
  mounted() {
    this.tableHeightArea()
    this.getTableData();
  },
  activated() {
    window.addEventListener('keydown', this.keyDown);
  },
  deactivated() {
    window.removeEventListener('keydown', this.keyDown, false);
  },
}
</script>
<style scoped>
.el-button {
  margin: 0 10px;
}
</style>
