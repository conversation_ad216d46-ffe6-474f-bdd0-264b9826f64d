<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" ref="searchForm" label-width="65px" :model="searchForm" class="demo-form-inline">
        <el-form-item label="物料代码" prop="materialCode">
          <el-input v-model.trim="searchForm.materialCode" placeholder="请输入物料代码"></el-input>
        </el-form-item>
        <el-form-item label="配件编码" prop="code">
          <el-input v-model.trim="searchForm.code" placeholder="请输入配件编码"></el-input>
        </el-form-item>
        <el-form-item label="配件名称" prop="name">
          <el-input v-model.trim="searchForm.name" placeholder="请输入配件名称"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search()" icon="el-icon-search">搜索</el-button>
          <el-button plain @click="reset('searchForm')">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <div class="tableHandle">
        <el-button type="text" icon="el-icon-plus" @click="add">新增</el-button>
        <el-button type="text" icon="bulkDown-icon" @click="exportData()">批量下载</el-button>

      </div>
      <el-table
        style="width:100%;"
        border
        stripe
        ref="mbomTable"
        highlight-current-row
        :max-height="maximumHeight"
        :data="tableData"
        :header-cell-style="{
              'text-align': 'center',
              'background-color': 'var(--other-color)',
            }"
        @header-dragend="changeColWidth"
        @sort-change="sortTable"
      >
        <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="物料代码" prop="materialCode" min-width="200"></el-table-column>
        <el-table-column show-overflow-tooltip label="配件编码" prop="code" min-width="130"></el-table-column>
        <el-table-column show-overflow-tooltip label="配件名称" prop="name" min-width="130"></el-table-column>
        <el-table-column show-overflow-tooltip label="更新人员" prop="updatedByName" min-width="160"></el-table-column>
        <el-table-column show-overflow-tooltip label="更新日期" prop="updatedTime" min-width="160"></el-table-column>
        <el-table-column label="操作" fixed="right" width="200">
          <template v-slot="{row}">
            <el-button type="text" size="small" @click="deleteRow(row.id,'afterSalePart')">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total>0" :total="total" :page.sync="currentPage" :limit.sync="pageSize" @pagination="getTableData()"/>

      <el-dialog v-dialogDrag width="700px !important" title="新增售后专用件" :visible.sync="addFormVisible" :close-on-click-modal="false">
        <el-form :rules="rules" ref="addForm" :label-width="formLabelWidth" :model="addForm">
          <el-form-item label="物料代码" prop="materialCode">
            <el-input v-model.trim="addForm.materialCode" placeholder="请输入物料代码" show-word-limit maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="配件编码" prop="code">
            <el-input v-model.trim="addForm.code" placeholder="请输入配件编码" show-word-limit maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="配件名称" prop="name">
            <el-input v-model.trim="addForm.name" placeholder="请输入配件名称" show-word-limit maxlength="50"></el-input>
          </el-form-item>
          <div class="submitArea">
            <el-button type="primary" @click="submit('addForm')">
              确认
            </el-button>
            <el-button @click="cancel()">
              取消
            </el-button>
          </div>
        </el-form>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import {handleAlert, tableHeight, tableMaxHeight} from '@/assets/js/common.js'
import Pagination from '@/components/Pagination'
import {importData, listData, exportData, saveData, deleteData} from '@/api/sbommgt.js'

export default {
  name: 'sbom_afterSalePart_part',
  components: {Pagination},
  data() {
    return {
      tableData: [],
      pageSize: 20,
      currentPage: 1,
      total: 0,
      searchForm: {
        materialCode: '',
        code: '',
        name: '',
      },
      selectedIds: [],
      maximumHeight: 0,
      addFormVisible: false,
      formLabelWidth: '100px',
      rules: {
        materialCode: [{required: true, message: '物料代码不能为空', trigger: ['blur', 'change']}],
        code: [{required: true, message: '配件编码不能为空', trigger: ['blur', 'change']}],
        name: [{required: true, message: '配件名称不能为空', trigger: ['blur', 'change']}],
      },
      addForm: {
        materialCode: '',
        code: '',
        name: '',
      },
    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    // 数据
    getTableData() {
      this.$loading.show();
      let param = {
        currentPage: this.currentPage,
        limit: this.pageSize,
        materialCode: this.searchForm.materialCode,
        name: this.searchForm.name,
        code: this.searchForm.code,
        prop: this.prop,
        order: this.order,
      };
      let params = new URLSearchParams();
      for (let [key, value] of Object.entries(param)) {
        params.append(key, value);
      }
      listData(params, 'afterSalePart').then(res => {
        this.total = res.data.total;
        this.tableData = res.data.data
        this.tableHeightArea();
        this.$loading.hide();
      })
    },
    // 搜索
    keyDown(e) {
      if (e.keyCode === 13) {
        this.search()
      }
    },
    search() {
      this.currentPage = 1
      this.getTableData()
    },
    reset(formName) {
      this.$refs[formName].resetFields();
      this.currentPage = 1
      this.getTableData();
    },

    add() {
      if (this.searchForm.materialCode) {
        this.addForm.materialCode = this.searchForm.materialCode;
      }
      this.addFormVisible = true;
      this.$nextTick(() => {
        this.$refs.addForm.clearValidate();
      })
    },
    cancel() {
      this.addFormVisible = false;
    },
    submit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let params = {};
          for (let [key, value] of Object.entries(this.addForm)) {
            params[key] = value;
          }
          this.$loading.show();
          saveData(params, 'afterSalePart').then((res) => {
            if (res.data.code === 100) {
              handleAlert('success', res.data.msg)
              this.getTableData();
              this.addFormVisible = false;
              this.$loading.hide();
            } else {
              handleAlert('error', res.data.msg)
              this.$loading.hide();
            }
          }).catch((err) => {
            this.$loading.hide();
            if (err !== null && err !== "" && err.responseText !== null) {
              handleAlert('error', "提交失败,请重试")
            }
          });
        } else {
          handleAlert('error', "请完善信息")
          this.$loading.hide();
        }
      });
    },
    downloadRes(res) {
      let fileName = decodeURI(res.headers['content-disposition'])
      if (fileName) {
        fileName = fileName.substring(fileName.indexOf('=') + 1)
      }
      let blob = new Blob([res.data]);
      let url = window.URL.createObjectURL(blob);
      let aLink = document.createElement("a");
      aLink.style.display = "none";
      aLink.href = url;
      aLink.setAttribute("download", fileName);
      document.body.appendChild(aLink);
      aLink.click();
      document.body.removeChild(aLink); //下载完成移除元素
      window.URL.revokeObjectURL(url); //释放掉blob对象
    },
    exportData() {
      let params = new FormData();
      for (let [key, value] of Object.entries(this.searchForm)) {
        params.append(key, value);
      }
      exportData(params, 'afterSalePart').then(res => {
        if (!res.data) {
          handleAlert('error', '请稍候重试')
          return;
        }
        this.downloadRes(res);
      }).catch(err => {
        console.error(err)
        handleAlert('error', '请稍候重试')
      })
    },
    handleSelectionChange(selections) {
      console.log(selections)
      this.selectedIds = selections.map((item) => {
        return item.id;
      });
    },
    maximumArea() {
      setTimeout(() => {
        tableHeight()
        setTimeout(() => {
          this.maximumHeight = tableMaxHeight;
        }, 50)
      })
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    },
    onBeforeUpload(file) {
      if (file.name.match(/\.[^.]*$/g)[0] !== '.xlsx') {
        handleAlert('warning', "上传的文件只能是 xlsx 格式!")
        return false;
      }
      if (file.size / 1024 / 1024 > 100) {
        handleAlert('warning', "上传的文件大小不能超过 100MB!")
        return false;
      }
      return true;
    },
    importData(file) {
      this.$loading.show()
      let formData = new FormData();
      formData.append('file', file.file);
      importData(formData, "afterSalePart").then(res => {
        if (res.data.code === 100) {
          handleAlert('success', '批量导入成功')
          this.dataList()
        } else {
          this.$alert(res.data.msg, '信息提示', {dangerouslyUseHTMLString: true})
        }
        this.$loading.hide()
      }).catch(err => {
        this.$loading.hide()
        handleAlert('error', '请稍候重试')
      })
    },
    sortTable({column, prop, order}) {
      this.prop = prop;
      this.order = order;
      this.getTableData();
    },
    deleteRow(id, entityName) {
      this.$confirm('确定删除?', `删除数据`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteData({id: id}, entityName).then(res => {
          if (res.data.code === 100) {
            handleAlert('success', '删除成功')
            this.getTableData()
          } else {
            this.$alert(res.data.msg, '信息提示', {dangerouslyUseHTMLString: true})
          }
        }).catch(err => {
          handleAlert('error', '请稍候重试')
        })
      });
    },
  },
  mounted() {
    if (sessionStorage.materialCode) {
      this.searchForm.materialCode = sessionStorage.materialCode;
    }
    this.tableHeightArea();
    this.getTableData();
    window.addEventListener('keydown', this.keyDown);
  },
  destroyed() {
    window.removeEventListener('keydown', this.keyDown, false);
  }
}
</script>
<style scoped>
.el-button {
  margin: 0 10px;
}
</style>
