<template>
  <div class="layoutContainer">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane name="first">
        <span slot="label" class="tab-title">
          <el-badge :value="vinTodoNumber" :hidden="vinTodoNumber===0" :max="999" class="item">
           VIN车型配置处理
          </el-badge>
        </span>
        <div class="secondFloat">
          <el-form :inline="true" ref="formInline" label-width="70px" :model="searchForm" class="demo-form-inline">
            <el-form-item label="VIN码" prop="vin">
              <el-input v-model.trim="searchForm.vin" placeholder="请输入VIN码"></el-input>
            </el-form-item>
            <el-form-item label="物料代码" prop="materials">
              <el-input v-model.trim="searchForm.materials" placeholder="请输入物料代码"></el-input>
            </el-form-item>
            <el-form-item label="公告型号" prop="announceModel">
              <el-input v-model.trim="searchForm.announceModel" placeholder="请输入公告型号"></el-input>
            </el-form-item>
            <el-form-item label="车辆品牌" prop="brand">
              <el-input v-model.trim="searchForm.brand" placeholder="请输入车辆品牌"></el-input>
            </el-form-item>
            <el-form-item label="车辆型号" prop="model">
              <el-input v-model.trim="searchForm.model" placeholder="请输入车辆型号"></el-input>
            </el-form-item>
            <el-form-item label="车型配置" prop="modelConfig">
              <el-input v-model.trim="searchForm.modelConfig" placeholder="请输入车型配置"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onSubmit" icon="el-icon-search">搜索</el-button>
              <el-button plain @click="reset('formInline')">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="tableDetail">
          <!-- <div class="tableHandle"> -->
          <!--            <el-button type="text" icon="el-icon-setting" @click="openAssign">确认车型配置</el-button>-->
          <!-- </div> -->
          <el-table
            style="width:100%;"
            border
            stripe
            ref="table"
            highlight-current-row
            :max-height="maximumHeight"
            :data="resultList"
            :header-cell-style="{
              'text-align': 'center',
              'background-color': 'var(--other-color)',
            }"
            @header-dragend="changeColWidth"
            @selection-change="handleSelectionChange"
          >
            <!-- <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>-->
            <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="VIN码" prop="vin" min-width="160"></el-table-column>
            <el-table-column show-overflow-tooltip label="车辆品牌" prop="brand" width="100"></el-table-column>
            <el-table-column show-overflow-tooltip label="车辆型号" prop="model" min-width="100"></el-table-column>
            <el-table-column show-overflow-tooltip label="车辆年款" prop="modelYear" min-width="100"></el-table-column>
            <el-table-column show-overflow-tooltip label="车型配置" prop="modelConfig" min-width="100"></el-table-column>
            <el-table-column show-overflow-tooltip label="公告型号" prop="announceModel" min-width="160"></el-table-column>
            <el-table-column show-overflow-tooltip label="物料代码" prop="materials" min-width="220"></el-table-column>
            <el-table-column show-overflow-tooltip label="发动机型号" prop="engineModel" min-width="150"></el-table-column>
            <el-table-column show-overflow-tooltip label="销售地和形式" prop="marketAndForm" min-width="150"></el-table-column>
            <el-table-column show-overflow-tooltip label="车辆颜色" prop="vehicleColor" min-width="150" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="轮胎规格" prop="tyreSize" min-width="150" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="中控" prop="console" min-width="150" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="座椅数量" prop="seatsNumber" min-width="120" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="EPS" prop="eps" min-width="150" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="轮辋材料" prop="rimMaterial" min-width="120" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="电喷系统" prop="efi" min-width="150"></el-table-column>
            <el-table-column show-overflow-tooltip label="后桥速比" prop="rearAxleRatio" min-width="150"></el-table-column>
            <el-table-column show-overflow-tooltip label="状态" prop="status" fixed="right" min-width="100"></el-table-column>
            <el-table-column show-overflow-tooltip label="VIN发货日期" prop="deliveryTime" fixed="right" min-width="150" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="VIN创建日期" prop="vinCreated" fixed="right" min-width="150" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="创建日期" prop="createTime" fixed="right" min-width="150" align="center"></el-table-column>
            <el-table-column label="操作" fixed="right" width="150">
              <template v-slot="{row}">
                <el-button type="text" size="small" @click="openAssign(row)">
                  配置车型
                </el-button>
                <el-button type="text" size="small" @click="ignoreVIN(row)">
                  忽略
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination v-show="total>0" :total="total" :page.sync="currentPage" :limit.sync="pageSize" @pagination="dataList"/>
        </div>
        <el-dialog v-dialogDrag width="900px !important" title="新增车型配置对照规则" :visible.sync="confirmConfigFormVisible" :close-on-click-modal="false">
          <el-form :rules="rules" ref="assignForm" :model="assignForm" :label-width="formLabelWidth">
            <el-row>
              <el-col :span="12">

                <el-form-item label="SAP车辆品牌" prop="brand">
                  <el-input v-model.trim="assignForm.brand" disabled></el-input>
                </el-form-item>
                <el-form-item label="SAP车辆型号" prop="model">
                  <el-input v-model.trim="assignForm.model" disabled></el-input>
                </el-form-item>
                <el-form-item label="SAP车辆年款" prop="modelYear">
                  <el-input v-model.trim="assignForm.modelYear" disabled></el-input>
                </el-form-item>
                <el-form-item label="SAP车辆配置" prop="modelConfig">
                  <el-input v-model.trim="assignForm.modelConfig" disabled></el-input>
                </el-form-item>
                <el-form-item label="公告型号" prop="announceModel">
                  <el-input v-model.trim="assignForm.announceModel" disabled>
                    <template v-if="assignForm.announceModel && assignForm.announceModel.toUpperCase().endsWith('CNG')" slot="suffix">
                      <el-tooltip content="CNG" placement="top">
                        <img alt="CNG" class="cng-icon" src="/static/imgs/cng.png">
                      </el-tooltip>
                    </template>
                  </el-input>
                </el-form-item>
                <el-form-item label="发动机型号" prop="engineModel">
                  <el-input v-model.trim="assignForm.engineModel" disabled></el-input>
                </el-form-item>
                <el-form-item label="物料代码" prop="materials">
                  <el-input v-model.trim="assignForm.materials" disabled></el-input>
                </el-form-item>
                <el-form-item label="座椅数量" prop="seatsNumber">
                  <el-input v-model.trim="assignForm.seatsNumber" disabled></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="EPC车辆品牌" prop="brandId">
                  <el-select v-model="assignForm.brandId" :disabled="assignForm.disabled" placeholder="请选择车辆品牌" clearable filterable @change="selectBrand">
                    <el-option
                      v-for="item in brandList"
                      :key="item.id"
                      :label="item.nameCh"
                      :value="item.id">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="EPC车辆型号" prop="modelId">
                  <el-select v-model="assignForm.modelId" :disabled="assignForm.disabled" placeholder="请选择车辆型号" filterable clearable @change="selectModel">
                    <el-option-group
                      v-for="(model,group) in carModelMap"
                      :key="group"
                      :label="group">
                      <el-option
                        v-for="item in model"
                        :key="item.id"
                        :label="item.nameCh"
                        :value="item.id">
                      </el-option>
                    </el-option-group>
                  </el-select>
                </el-form-item>
                <el-form-item label="EPC车辆配置" prop="configId">
                  <el-select v-model="assignForm.configId" :disabled="assignForm.disabled" placeholder="请选择车辆配置" filterable clearable>
                    <el-option-group
                      v-for="(model,group) in configMap"
                      :key="group"
                      :label="group">
                      <el-option
                        v-for="item in model"
                        :key="item.id"
                        :label="item.nameCh"
                        :value="item.id">
                      </el-option>
                    </el-option-group>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <div class="submitArea">
              <el-button type="primary" @click="submitAssign('assignForm')">
                确认
              </el-button>
              <el-button @click="cancel()">
                取消
              </el-button>
            </div>
          </el-form>
        </el-dialog>
      </el-tab-pane>
      <el-tab-pane label="MBOM配件供货处理" name="second">
        <span slot="label" class="tab-title">
          <el-badge :value="mbom.todoNumber" :hidden="mbom.todoNumber===0" :max="999" class="item">
         <span>MBOM配件供货处理</span>
          </el-badge>
        </span>
        <div class="secondFloat">
          <el-form :inline="true" ref="mbom.searchForm" label-width="65px" :model="mbom.searchForm" class="demo-form-inline">
            <el-form-item label="配件编码" prop="code">
              <el-input v-model.trim="mbom.searchForm.code" placeholder="请输入配件编码"></el-input>
            </el-form-item>
            <el-form-item label="配件名称" prop="name">
              <el-input v-model.trim="mbom.searchForm.name" placeholder="请输入配件名称"></el-input>
            </el-form-item>
            <el-form-item label="车辆品牌" prop="brandId">
              <el-select v-model="mbom.searchForm.brandId" placeholder="请选择车辆品牌" clearable filterable @change="selectSearchBrand">
                <el-option
                  v-for="item in brandList"
                  :key="item.id"
                  :label="item.nameCh"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="车型年款" prop="yearId">
              <el-select v-model="mbom.searchForm.yearId" placeholder="请选择车型年款" clearable filterable>
                <el-option-group v-for="group in yearList" :key="group.id" :label="group.nameCh">
                  <el-option v-for="item in group.children" :key="item.id" :label="item.nameCh" :value="item.id"></el-option>
                </el-option-group>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="searchMbom()" icon="el-icon-search">搜索</el-button>
              <el-button plain @click="reset('mbom.searchForm')">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="tableDetail">
          <div class="tableHandle">
            <el-button type="text" icon="el-icon-document-checked" @click="confirmAction('3')">确认供货</el-button>
            <el-button type="text" icon="el-icon-document-remove" @click="confirmAction('2')">添加不供货清单</el-button>
            <el-button type="text" icon="bulkDown-icon" @click="exportData('todo')">批量下载</el-button>
            <el-upload
              class="upload-demo inline-block"
              ref="celUpload"
              action="#"
              :show-file-list="false"
              :before-upload="onBeforeUpload"
              accept=".xlsx"
              :http-request="file => importData(file,'todo')"
            >
              <el-button type="text" icon="bulkImport-icon">批量上传</el-button>
            </el-upload>
          </div>
          <el-table
            style="width:100%;"
            border
            stripe
            ref="mbomTable"
            highlight-current-row
            :max-height="maximumHeight"
            :data="mbom.list"
            :header-cell-style="{
              'text-align': 'center',
              'background-color': 'var(--other-color)',
            }"
            @header-dragend="changeColWidth"
            @selection-change="handleMBOMSelectionChange"
            @sort-change="sortTable"
          >
            <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>
            <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="配件编码" sortable="custom" prop="code" min-width="160"></el-table-column>
            <el-table-column show-overflow-tooltip label="配件名称" sortable="custom" prop="name" min-width="160"></el-table-column>
            <el-table-column show-overflow-tooltip label="车型" prop="model" min-width="160"></el-table-column>
            <el-table-column show-overflow-tooltip label="年款" prop="year" min-width="160"></el-table-column>
            <el-table-column show-overflow-tooltip label="状态" prop="status" fixed="right" min-width="100" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="创建日期" prop="createTime" fixed="right" min-width="150"></el-table-column>
            <el-table-column label="操作" fixed="right" width="120">
              <template v-slot="{row}">
                <el-button type="text" size="small" @click="openElectrophoreticDialog(row)">
                  设置电泳件
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination v-show="mbom.total>0" :total="mbom.total" :page.sync="mbom.currentPage" :limit.sync="mbom.pageSize" @pagination="listMbom()"/>


          <!-- 电泳件设置弹窗 -->
          <el-dialog v-dialogDrag width="600px !important" title="新增电泳件" :visible.sync="electrophoreticFormVisible" :close-on-click-modal="false">
            <el-form :rules="electrophoreticRules" ref="electrophoreticForm" :model="electrophoreticForm" :label-width="formLabelWidth">
              <el-form-item label="配件编码" prop="code">
                <el-input v-model.trim="electrophoreticForm.code" disabled></el-input>
              </el-form-item>
              <el-form-item label="配件名称" prop="name">
                <el-input v-model.trim="electrophoreticForm.name" disabled></el-input>
              </el-form-item>
              <el-form-item label="电泳件编码" prop="destCode">
                <el-input v-model.trim="electrophoreticForm.destCode" placeholder="请输入电泳件编码"></el-input>
              </el-form-item>
              <el-form-item label="电泳件名称" prop="destName">
                <el-input v-model.trim="electrophoreticForm.destName" placeholder="请输入电泳件名称"></el-input>
              </el-form-item>
              <div class="submitArea">
                <el-button type="primary" @click="submitElectrophoretic('electrophoreticForm')">
                  确认
                </el-button>
                <el-button @click="cancelElectrophoretic()">
                  取消
                </el-button>
              </div>
            </el-form>
          </el-dialog>
        </div>

      </el-tab-pane>
      <el-tab-pane label="售后专用件" name="third">
        <span slot="label" class="tab-title">
          <el-badge :value="afterSale.todoNumber" :hidden="afterSale.todoNumber===0" :max="999" class="item">
         <span>售后专用件</span>
          </el-badge>
        </span>
        <div class="secondFloat">
          <el-form :inline="true" ref="afterSale.searchForm" label-width="65px" :model="afterSale.searchForm" class="demo-form-inline">
            <el-form-item label="物料代码" prop="materialCode">
              <el-input v-model.trim="afterSale.searchForm.materialCode" placeholder="请输入物料代码"></el-input>
            </el-form-item>
            <el-form-item label="车辆品牌" prop="brand">
              <el-input v-model.trim="afterSale.searchForm.brand" placeholder="请输入车辆品牌"></el-input>
            </el-form-item>
            <el-form-item label="车辆型号" prop="model">
              <el-input v-model.trim="afterSale.searchForm.model" placeholder="请输入车辆型号"></el-input>
            </el-form-item>
            <el-form-item label="车辆年款" prop="year">
              <el-input v-model.trim="afterSale.searchForm.year" placeholder="请输入车辆年款"></el-input>
            </el-form-item>
            <el-form-item label="车辆配置" prop="config">
              <el-input v-model.trim="afterSale.searchForm.config" placeholder="请输入车辆配置"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="listAfterSale()" icon="el-icon-search">搜索</el-button>
              <el-button plain @click="reset('afterSale.searchForm')">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="tableDetail">
          <div class="tableHandle">
            <el-button type="text" icon="bulkDown-icon" @click="exportData('materialAggregation')">批量下载</el-button>
            <el-upload
              class="upload-demo inline-block"
              ref="celUpload"
              action="#"
              :show-file-list="false"
              :before-upload="onBeforeUpload"
              accept=".xlsx"
              :http-request="file => importData(file,'afterSalePart')"
            >
              <el-button type="text" icon="bulkImport-icon">批量上传</el-button>
            </el-upload>
          </div>
          <el-table
            style="width:100%;"
            border
            stripe
            ref="mbomTable"
            highlight-current-row
            :max-height="maximumHeight"
            :data="afterSale.list"
            :header-cell-style="{
              'text-align': 'center',
              'background-color': 'var(--other-color)',
            }"
            @header-dragend="changeColWidth"
            @sort-change="sortTable"
          >
            <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
            <el-table-column show-overflow-tooltip label="物料代码" prop="materialCode" min-width="200"></el-table-column>
            <el-table-column show-overflow-tooltip label="品牌" prop="brand" min-width="130"></el-table-column>
            <el-table-column show-overflow-tooltip label="车辆型号" prop="model" min-width="130"></el-table-column>
            <el-table-column show-overflow-tooltip label="车辆年款" prop="year" min-width="100"></el-table-column>
            <el-table-column show-overflow-tooltip label="车型配置" prop="config" min-width="130"></el-table-column>
            <el-table-column show-overflow-tooltip label="处理状态" prop="isProcessed" min-width="100" align="center">
              <template v-slot="{row}">
                <span style="color: green;" v-if="row.isProcessed">已处理</span>
                <span style="color: red;" v-else>未处理</span>
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip label="更新人员" prop="updatedByName" min-width="160"></el-table-column>
            <el-table-column show-overflow-tooltip label="更新日期" prop="updatedTime" min-width="160"></el-table-column>
            <el-table-column label="操作" fixed="right" width="150">
              <template v-slot="{row}">
                <el-button type="text" size="small" @click="onlineImport(row)">
                  在线导入
                </el-button>
                <el-button type="text" size="small" @click="ignorePartAggregation(row)">
                  忽略
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination v-show="afterSale.total>0" :total="afterSale.total" :page.sync="afterSale.currentPage" :limit.sync="afterSale.pageSize" @pagination="listAfterSale()"/>
        </div>
        <el-dialog v-dialogDrag width="1000px !important" height="900px !important" title="售后专用件在线导入" :visible.sync="addFormVisible" :close-on-click-modal="false">

          <div class="secondFloat">
            <el-form :inline="true" ref="part.searchForm" label-width="65px" :model="part.searchForm" class="demo-form-inline">
              <el-form-item label="物料代码" prop="materialCode">
                <el-input v-model.trim="part.searchForm.materialCode" placeholder="请输入物料代码"></el-input>
              </el-form-item>
              <el-form-item label="配件名称" prop="name">
                <el-input v-model.trim="part.searchForm.name" placeholder="请输入配件名称"></el-input>
              </el-form-item>
              <el-form-item label="配件编码" prop="code">
                <el-input v-model.trim="part.searchForm.code" placeholder="请输入配件编码"></el-input>
              </el-form-item>
              <el-form-item label="车辆品牌" prop="brand">
                <el-select clearable filterable v-model.trim="part.searchForm.brand" placeholder="请选择车辆品牌">
                  <el-option
                    v-for="item in part.brandList"
                    :key="item"
                    :label="item"
                    :value="item">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="车辆型号" prop="model">
                <el-select clearable filterable v-model.trim="part.searchForm.model" placeholder="请选择车辆型号">
                  <el-option
                    v-for="item in part.modelList"
                    :key="item"
                    :label="item"
                    :value="item">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="车辆年款" prop="year">
                <el-select clearable filterable v-model.trim="part.searchForm.year" placeholder="请选择车辆年款">
                  <el-option
                    v-for="item in part.yearList"
                    :key="item"
                    :label="item"
                    :value="item">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="车辆配置" prop="config">
                <el-select clearable filterable v-model.trim="part.searchForm.config" placeholder="请选择车辆配置">
                  <el-option
                    v-for="item in part.configList"
                    :key="item"
                    :label="item"
                    :value="item">
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item>
                <el-button type="primary" @click="search('part')" icon="el-icon-search">搜索</el-button>
                <el-button plain @click="reset('part.searchForm')">重置</el-button>
              </el-form-item>
            </el-form>
          </div>

          <div class="tableDetail">
            <div class="tableHandle">
              <el-button type="text" icon="bulkDown-icon" @click="confirmImport()">确认导入</el-button>
            </div>
            <el-table
              style="width:100%;"
              border
              stripe
              ref="mbomTable"
              highlight-current-row
              :max-height="540"
              :data="part.tableData"
              :header-cell-style="{
              'text-align': 'center',
              'background-color': 'var(--other-color)',
            }"
              @header-dragend="changeColWidth"
              @sort-change="sortTable"
              @selection-change="handlePartSelectionChange"
            >
              <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>
              <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
              <el-table-column show-overflow-tooltip label="配件编码" prop="code" min-width="130"></el-table-column>
              <el-table-column show-overflow-tooltip label="配件名称" prop="name" min-width="130"></el-table-column>
            </el-table>
            <pagination v-show="part.total>0" :total="part.total" :page.sync="part.currentPage" :limit.sync="part.pageSize" @pagination="getPart()"/>
          </div>
        </el-dialog>

      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import {handleAlert, tableHeight, tableMaxHeight} from '@/assets/js/common.js'
import Pagination from '@/components/Pagination'
import {
  assignConfig,
  confirmMbomAction,
  countTodo,
  exportData,
  getMatchRule,
  ignorePartAggregation,
  ignoreVIN,
  importData,
  listBrandTree,
  listData,
  listPartAggregation,
  listSelection,
  listTodo,
  listVINTodo,
  onlineImport, setElectrophoretic,
  todoYearTree,
} from '@/api/sbommgt.js'
import ignore from "ignore";

export default {
  name: 'sbom_todo_list',
  computed: {
    ignore() {
      return ignore
    }
  },
  components: {Pagination},
  data() {
    return {
      activeName: 'first',
      vinTodoNumber: 0,
      selectIdList: [],
      confirmConfigFormVisible: false,
      brandList: [],
      carModelList: [],
      carModelMap: {},
      configList: [],
      configMap: {},
      searchForm: {
        vin: '',
        brand: '',
        model: '',
        modelConfig: '',
        materials: '',
        announceModel: '',
      },
      formLabelWidth: '120px',
      resultList: [],
      pageSize: 20,
      currentPage: 1,
      total: 0,
      prop: '',
      order: '',
      assignForm: {
        configId: '',
        brandId: '',
        modelId: '',
        brand: '',
        model: '',
        modelYear: '',
        modelConfig: '',
        engineModel: '',
        announceModel: '',
        isCng: null,
        materials: '',
        seatsNumber: '',
        disabled: false,
      },
      yearList: [],
      rules: {
        brandId: [{required: true, message: '车辆品牌不能为空', trigger: ['blur', 'change']}],
        modelId: [{required: true, message: '车辆型号不能为空', trigger: ['blur', 'change']}],
        configId: [{required: true, message: '车辆配置不能为空', trigger: ['blur', 'change']}],
        isCng: [{required: true, message: '车辆配置不能为空', trigger: ['blur', 'change']}]
      },
      mbom: {
        todoNumber: 0,
        list: [],
        pageSize: 20,
        currentPage: 1,
        total: 0,
        searchForm: {
          code: '',
          name: '',
          yearId: '',
          brandId: '',
        },
        selectedIds: []
      },
      afterSale: {
        todoNumber: 0,
        list: [],
        pageSize: 20,
        currentPage: 1,
        prop: '',
        order: '',
        total: 0,
        searchForm: {
          materialCode: '',
          brand: '',
          model: '',
          year: '',
          config: '',
        },
        selectedIds: []
      },
      maximumHeight: 0,
      cngDisabled: false,
      matchRuleList: [],

      addFormVisible: false,
      part: {
        tableData: [],
        pageSize: 15,
        currentPage: 1,
        total: 0,
        brandList: [],
        modelList: [],
        yearList: [],
        configList: [],
        selectedIds: [],
        searchForm: {
          materialCode: '',
          brand: '',
          model: '',
          year: '',
          config: '',
          code: '',
          name: '',
        },
      },
      // 电泳件设置相关数据
      electrophoreticFormVisible: false,
      electrophoreticForm: {
        code: '',
        name: '',
        destCode: '',
        destName: '',
      },
      electrophoreticRules: {
        destCode: [{required: true, message: '电泳件编码不能为空', trigger: ['blur', 'change']}],
        destName: [{required: true, message: '电泳件名称不能为空', trigger: ['blur', 'change']}],
      }
    }
  },
  methods: {
    confirmImport() {
      // 判断勾选项
      if (this.part.selectedIds.length === 0) {
        handleAlert('warning', "请选择售后专用件")
        return;
      }
      this.$loading.show()
      let param = {
        selectedIds: this.part.selectedIds,
        currentMaterialCode: this.currentMaterialCode,
      };
      onlineImport(param).then(res => {
        if (res.data.code === 100) {
          handleAlert('success', '在线导入成功')
          this.listAfterSale();
        } else {
          this.$alert(res.data.msg, '信息提示', {dangerouslyUseHTMLString: true})
        }
        this.$loading.hide()
      }).catch(err => {
        this.$loading.hide()
        handleAlert('error', '请稍候重试')
      })
    },
    search(entity) {
      this.part.currentPage = 1;
      this.getPart();
    },
    listSelection() {
      listSelection().then(res => {
        const list = res.data.data;
        this.part.brandList = this.getList(list, 'brand');
        this.part.modelList = this.getList(list, 'model');
        this.part.yearList = this.getList(list, 'year');
        this.part.configList = this.getList(list, 'config');
      })
    },
    getList(list, prop) {
      let propList = list.filter(e => e[prop]).map(e => e[prop]);
      return Array.from(new Set(propList));
    },
    getPart() {
      this.$loading.show();
      let param = {
        currentPage: this.part.currentPage,
        limit: this.part.pageSize,
        materialCode: this.part.searchForm.materialCode,
        brand: this.part.searchForm.brand,
        model: this.part.searchForm.model,
        year: this.part.searchForm.year,
        config: this.part.searchForm.config,
        code: this.part.searchForm.code,
        name: this.part.searchForm.name,
      };
      let params = new URLSearchParams();
      for (let [key, value] of Object.entries(param)) {
        params.append(key, value);
      }
      listPartAggregation(params).then(res => {
        this.part.total = res.data.total;
        this.part.tableData = res.data.data
        this.tableHeightArea();
        this.$loading.hide();
      })
    },
    onlineImport(row) {
      this.getPart();
      this.listSelection();
      this.currentMaterialCode = row.materialCode;
      this.addFormVisible = true;
    },
    ignorePartAggregation(row) {
      ignorePartAggregation({id: row.id, isIgnored: !row.isIgnored}).then((res) => {
        if (res.data.code === 100) {
          handleAlert('success', res.data.msg)
        } else {
          handleAlert('error', res.data.msg)
        }
        this.listAfterSale()
      }).catch((err) => {
        if (err !== null && err !== "" && err.responseText !== null) {
          handleAlert('error', "提交失败,请重试")
        }
      });
    },
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
        if (this.$refs.mbomTable) {
          this.$refs.mbomTable.doLayout();
        }
      })
    },
    handleClick(tab, event) {
      if (tab.name === 'second') {
        todoYearTree().then((res) => {
          if (res.data.code === 100) {
            this.brandList = res.data.data;
            this.yearList = this.brandList.map(e => e.children).flat();
          } else {
            handleAlert('error', res.data.msg)
          }
        }).catch((err) => {
          if (err !== null && err !== "" && err.responseText !== null) {
            handleAlert('error', "提交失败,请重试")
          }
        });
      }
      if (tab.name === 'third') {
        /* todoYearTree().then((res) => {
           if (res.data.code === 100) {
             this.brandList = res.data.data;
             this.yearList = this.brandList.map(e => e.children).flat();
           } else {
             handleAlert('error', res.data.msg)
           }
         }).catch((err) => {
           if (err !== null && err !== "" && err.responseText !== null) {
             handleAlert('error', "提交失败,请重试")
           }
         });*/
      }
    },
    selectSearchBrand(value) {
      if (value) {
        this.yearList = this.brandList.filter(e => e.id === value)[0].children;
      } else {
        this.yearList = this.brandList.map(e => e.children).flat();
      }
    },
    // 数据
    dataList() {
      let params = {
        currentPage: this.currentPage,
        limit: this.pageSize,
      }
      for (let [key, value] of Object.entries(this.searchForm)) {
        params[key] = value;
      }
      this.$loading.show();
      listVINTodo(params).then(res => {
        this.total = res.data.total
        if (!params.vin && !params.brand && !params.model && !params.modelConfig) {
          this.vinTodoNumber = this.total;
        } else {
          countTodo({item: 'vin'}).then(res => {
            this.vinTodoNumber = res.data.total;
          })
        }
        this.resultList = res.data.data
        this.tableHeightArea();
        this.$loading.hide();
      })
    },
    // 搜索
    keyDown(e) {
      if (e.keyCode === 13) {
        if (this.activeName == "first") {
          this.onSubmit()
        } else {
          this.searchMbom()
        }
      }
    },
    onSubmit() {
      this.currentPage = 1
      this.dataList()
    },
    reset(formName) {
      this.$refs[formName].resetFields();
      if (formName === 'formInline') {
        this.currentPage = 1
        this.dataList();
      } else if (formName === 'mbom.searchForm') {
        this.mbom.currentPage = 1;
        this.listMbom();
      }
      if (formName === 'part.searchForm') {
        this.part.currentPage = 1;
        this.getPart();
      } else {
        this.afterSale.currentPage = 1;
        this.listAfterSale();
      }
    },
    async openAssign(row) {
      // 判断勾选项
      /*  if (this.selectIdList.length === 0) {
          handleAlert('warning', "请选择VIN后分配车型")
          return;
        }*/
      this.matchRuleList = [];
      this.assignForm.disabled = false;

      this.assignForm = {
        ...this.assignForm,
        ...row
      };

      //获取CNG类型
      /*  await getCNGType({announceModel: row.announceModel, engineModel: row.engineModel}).then(res => {
          this.cngDisabled = res.data.data !== null;
          this.assignForm.isCng = this.cngDisabled ? res.data.data.toString() : '';
        })*/
      // 判断请求数据
      // if (this.cngDisabled) {
      await listBrandTree().then(res => {
        this.brandList = res.data.data
      });
      /* } else {
         this.brandList = [];
       }*/
      //重置选择数据
      this.assignForm.brandId = '';
      this.assignForm.modelId = '';
      this.assignForm.configId = '';
      this.confirmConfigFormVisible = true;
      this.selectIdList = [row.id];
      this.$nextTick(() => this.$refs.assignForm.clearValidate());
    },
    handleSelectionChange(selections) {
      this.selectIdList = selections.map((item) => {
        return item.id;
      });
    },
    handleMBOMSelectionChange(selections) {
      this.mbom.selectedIds = selections.map((item) => {
        return item.id;
      });
    },
    handlePartSelectionChange(selections) {
      this.part.selectedIds = selections.map((item) => {
        return item.id;
      });
    },
    submitAssign(assignForm) {
      this.$refs[assignForm].validate((valid) => {
        if (valid) {
          this.$loading.show()
          let params = new URLSearchParams();
          params.append("configId", this.assignForm.configId);
          params.append("vinIdList", this.selectIdList);
          // params.append("cng", this.assignForm.isCng);
          assignConfig(params).then((res) => {
            if (res.data.code === 100) {
              handleAlert('success', res.data.msg)
              this.dataList();
              this.confirmConfigFormVisible = false;
            } else {
              handleAlert('error', res.data.msg)
            }
            this.$loading.hide()
          }).catch((err) => {
            this.$loading.hide()
            if (err !== null && err !== "" && err.responseText !== null) {
              handleAlert('error', "提交失败,请重试")
            }
          });
        } else {
          handleAlert('error', "请完善信息")
          this.$loading.hide()
        }
      });
    },
    selectBrand(value) {
      this.assignForm.modelId = '';
      this.assignForm.configId = '';
      if (!value) {
        this.carModelMap = {};
        this.configMap = {}
        return;
      }
      let carTypeMap = {
        1: "燃油车",
        2: "新能源",
        3: "天然气",

      }
      this.carModelList = this.brandList.filter(e => e.id === value)[0].children;
      this.carModelMap = this.carModelList.reduce((group, item) => {
        let type = carTypeMap[item.type];
        group[type] = group[type] ?? [];
        group[type].push(item);
        return group;
      }, {});
    },
    selectModel(value) {
      this.assignForm.configId = '';
      if (!value) {
        this.configMap = {}
      }
      this.configList = this.carModelList.filter(e => e.id === value)[0].children;
      this.configMap = this.configList.reduce((group, item) => {
        let {year} = item;
        group[year] = group[year] ?? [];
        group[year].push(item);
        return group;
      }, {});
    },
    cancel() {
      this.confirmConfigFormVisible = false;
      //清理数据
    },
    searchMbom() {
      this.listMbom();
    },
    listMbom() {
      this.$loading.show();
      let param = {
        currentPage: this.mbom.currentPage,
        limit: this.mbom.pageSize,
        code: this.mbom.searchForm.code,
        name: this.mbom.searchForm.name,
        yearId: this.mbom.searchForm.yearId,
        prop: this.prop,
        order: this.order,
      };
      listTodo(param).then(res => {
        this.mbom.total = res.data.total;
        if (!param.code && !param.name) {
          this.mbom.todoNumber = this.mbom.total;
        } else {
          countTodo({item: 'Todo'}).then(res => {
            this.mbom.todoNumber = res.data.total;
          })
        }
        this.mbom.list = res.data.data;
        this.tableHeightArea();
        this.$loading.hide();
      })
    },
    listAfterSale() {
      this.$loading.show();
      let params = new URLSearchParams();
      params.append("currentPage", this.afterSale.currentPage);
      params.append("limit", this.afterSale.pageSize);
      params.append("prop", this.afterSale.prop);
      params.append("order", this.afterSale.order);
      params.append("isProcessed", false);
      params.append("isIgnored", false);

      for (let [key, value] of Object.entries(this.afterSale.searchForm)) {
        params.append(key, value);
      }


      listData(params, 'materialAggregation').then(res => {
        this.afterSale.total = res.data.total;
        if (!params.get("materialCode") && !params.get("brand") && !params.get("model") && !params.get("year") && !params.get("config")) {
          this.afterSale.todoNumber = this.afterSale.total;
        } else {
          countTodo({item: 'afterSalePart'}).then(res => {
            this.afterSale.todoNumber = res.data.total;
          })
        }
        this.afterSale.list = res.data.data;
        this.tableHeightArea();
        this.$loading.hide();
      })
    },
    confirmAction(action) {
      // 判断勾选项
      if (this.mbom.selectedIds.length === 0) {
        handleAlert('warning', "请选择至少一个MBOM")
        return;
      }
      let message = action === '3' ? "确认供货" : "确认添加到不供货清单";
      this.$confirm(`${message}?`, `确认MBOM数据`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {

        //确定操作
        let params = new URLSearchParams();
        params.append('action', action)
        params.append('todoIdList', this.mbom.selectedIds);
        confirmMbomAction(params).then((res) => {
          if (res.data.code === 100) {
            handleAlert('success', res.data.msg)
            this.listMbom();
            this.confirmConfigFormVisible = false;
          } else {
            handleAlert('error', res.data.msg)
          }
        }).catch((err) => {
          if (err !== null && err !== "" && err.responseText !== null) {
            handleAlert('error', "提交失败,请重试")
          }
        });

      });

    },
    onBeforeUpload(file) {
      if (file.name.match(/\.[^.]*$/g)[0] !== '.xlsx') {
        handleAlert('warning', "上传的文件只能是 xlsx 格式!")
        return false;
      }
      if (file.size / 1024 / 1024 > 100) {
        handleAlert('warning', "上传的文件大小不能超过 100MB!")
        return false;
      }
      return true;
    },
    importData(file, entity) {
      this.$loading.show()
      let formData = new FormData();
      formData.append('file', file.file);
      importData(formData, entity).then(res => {
        if (res.data.code === 100) {
          handleAlert('success', '批量导入成功')
          this.dataList()
        } else {
          this.$alert(res.data.msg, '信息提示', {dangerouslyUseHTMLString: true})
        }
        this.$loading.hide()
      }).catch(err => {
        this.$loading.hide()
        handleAlert('error', '请稍候重试')
      })
    },
    exportData(entityName) {
      let postParam = new FormData();
      let searchForm = entityName === 'mbom' ? this.mbom.searchForm : this.afterSale.searchForm;
      for (let [key, value] of Object.entries(searchForm)) {
        postParam.append(key, value);
      }
      postParam.append("isProcessed", false)
      postParam.append("isIgnored", false);
      exportData(postParam, entityName).then(res => {
        if (!res.data) {
          handleAlert('error', '请稍候重试')
          return;
        }
        this.downloadRes(res);
      }).catch(err => {
        handleAlert('error', '请稍候重试')
      })
    },
    downloadRes(res) {
      let fileName = decodeURI(res.headers['content-disposition'])
      if (fileName) {
        fileName = fileName.substring(fileName.indexOf('=') + 1)
      }
      let blob = new Blob([res.data]);
      let url = window.URL.createObjectURL(blob);
      let aLink = document.createElement("a");
      aLink.style.display = "none";
      aLink.href = url;
      aLink.setAttribute("download", fileName);
      document.body.appendChild(aLink);
      aLink.click();
      document.body.removeChild(aLink); //下载完成移除元素
      window.URL.revokeObjectURL(url); //释放掉blob对象
    },
    sortTable({column, prop, order}) {
      this.prop = prop;
      this.order = order;
      this.listMbom();
    },
    maximumArea() {
      setTimeout(() => {
        tableHeight()
        setTimeout(() => {
          this.maximumHeight = tableMaxHeight;
        }, 50)
      })
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    },
    selectIsCng: async function () {
      if (this.assignForm.isCng !== null) {
        if (this.brandList.length === 0) {
          await listBrandTree().then(res => {
            this.brandList = res.data.data
          });
        }
        if (this.matchRuleList.length === 0) {
          //查询车型对照是否存在
          await getMatchRule({vinId: this.selectIdList[0]}).then(res => {
            this.matchRuleList = res.data.data;
          });
        }
        let first = this.matchRuleList.filter(e => e.cng.toString() === this.assignForm.isCng)[0];
        if (first) {
          this.assignForm.brandId = first.epcBrandId;
          this.selectBrand(this.assignForm.brandId)
          this.assignForm.modelId = first.epcModelId;
          this.selectModel(this.assignForm.modelId);
          this.assignForm.configId = first.configId;
          this.assignForm.disabled = true;
        } else {
          this.assignForm.disabled = false;
        }
      }
    },
    ignoreVIN(row) {
      ignoreVIN({vin: row.vin}).then((res) => {
        if (res.data.code === 100) {
          handleAlert('success', res.data.msg)
        } else {
          handleAlert('error', res.data.msg)
        }
        this.dataList();
      }).catch((err) => {
        if (err !== null && err !== "" && err.responseText !== null) {
          handleAlert('error', "提交失败,请重试")
        }
      });
    },
    /**
     * 打开电泳件设置弹窗
     * @param row 当前行数据
     */
    openElectrophoreticDialog(row) {
      // 重置表单数据
      this.electrophoreticForm = {
        code: row.code || '',
        name: row.name || '',
        destCode: '',
        destName: (row.name || '') + '(电泳)',
      };
      this.electrophoreticFormVisible = true;
      this.$nextTick(() => {
        this.$refs.electrophoreticForm.clearValidate();
      });
    },
    /**
     * 提交电泳件数据
     * @param formName 表单名称
     */
    submitElectrophoretic(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$loading.show();
          let params = new URLSearchParams();
          params.append("code", this.electrophoreticForm.code);
          params.append("name", this.electrophoreticForm.name);
          params.append("destCode", this.electrophoreticForm.destCode);
          params.append("destName", this.electrophoreticForm.destName);

          // 调用保存电泳件的API
          setElectrophoretic(params).then((res) => {
            if (res.data.code === 100) {
              handleAlert('success', '电泳件设置成功');
              this.electrophoreticFormVisible = false;
              // 可以选择刷新列表或其他操作
            } else {
              handleAlert('error', res.data.msg);
            }
            this.$loading.hide();
          }).catch((err) => {
            this.$loading.hide();
            if (err !== null && err !== "" && err.responseText !== null) {
              handleAlert('error', "提交失败,请重试");
            }
          });
        } else {
          handleAlert('error', "请完善信息");
        }
      });
    },
    /**
     * 取消电泳件设置
     */
    cancelElectrophoretic() {
      this.electrophoreticFormVisible = false;
      // 重置表单数据
      this.electrophoreticForm = {
        code: '',
        name: '',
        destCode: '',
        destName: '',
      };
    }
  },
  mounted() {
    this.tableHeightArea();
    this.dataList();
    this.listMbom();
    this.listAfterSale();
    window.addEventListener('keydown', this.keyDown)
  },
  destroyed() {
    window.removeEventListener('keydown', this.keyDown, false)
  },
}
</script>
<style scoped>
.el-dialog .el-table .el-input__inner {
  height: 28px;
  line-height: 28px;
  padding: 0 8px;
}

.el-dialog .el-table .el-input {
  width: 100% !important;
  margin: 2px 0;
}

.imgShow {
  width: 150px;
}

.form-bottom {
  display: flex;
  justify-content: center;
}

.cng-icon {
  width: 100%;
  cursor: pointer;
  vertical-align: middle;
}
</style>
