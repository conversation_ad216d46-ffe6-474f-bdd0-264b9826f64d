<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" ref="searchForm" label-width="70px" :model="searchForm" class="demo-form-inline">
        <el-form-item label="配件编码" prop="code">
          <el-input v-model.trim="searchForm.code" placeholder="请输入配件编码"></el-input>
        </el-form-item>
        <el-form-item label="配件名称" prop="name">
          <el-input v-model.trim="searchForm.name" placeholder="请输入配件名称"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search()" icon="el-icon-search">搜索</el-button>
          <el-button plain @click="reset('searchForm')">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <div class="tableHandle">
        <el-button type="text" icon="el-icon-plus" @click="add">新增</el-button>
        <el-button type="text" icon="bulkDown-icon" @click="exportData">批量下载</el-button>
        <el-upload
          class="upload-demo inline-block"
          ref="celUpload"
          action="#"
          :show-file-list="false"
          :before-upload="onBeforeUpload"
          accept=".xlsx"
          :http-request="importData"
        >
          <el-button type="text" icon="bulkImport-icon">批量上传</el-button>
        </el-upload>
        <el-button type="text" icon="el-icon-download" @click="downloadTemplate('设变件上传模板.xlsx')">
          下载模板
        </el-button>
        <el-button type="text" icon="el-icon-video-play" @click="startTask">
          开始定时任务
        </el-button>
      </div>
      <el-table
        style="width:100%;"
        border
        stripe
        ref="table"
        highlight-current-row
        :max-height="maximumHeight"
        :data="tableData"
        :header-cell-style="{
          'text-align': 'center',
          'background-color': 'var(--other-color)',
        }"
        @header-dragend="changeColWidth"
        @selection-change="handleSelectionChange"
      >
        <!--        <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>-->
        <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
        <el-table-column label="设变前编码" prop="originalCode" min-width="180" show-overflow-tooltip></el-table-column>
        <el-table-column label="设变前名称" prop="originalName" width="160" show-overflow-tooltip></el-table-column>
        <el-table-column label="设变后编码" prop="changedCode" min-width="180" show-overflow-tooltip></el-table-column>
        <el-table-column label="设变后名称" prop="changedName" min-width="160" show-overflow-tooltip></el-table-column>
        <el-table-column label="设变前用量" prop="originalAmount" min-width="90" show-overflow-tooltip align="center"></el-table-column>
        <el-table-column label="设变后用量" prop="changedAmount" min-width="90" show-overflow-tooltip align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="切换车型" prop="modelList" min-width="200"></el-table-column>
        <el-table-column label="可替换性" prop="computability" min-width="100" align="center" show-overflow-tooltip>
          <template v-slot="{row}">
            <span v-if="row.computability">是</span>
            <span style="color: red;" v-else>否</span>
          </template>
        </el-table-column>
        <el-table-column label="设变时间" prop="changedDate" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column label="数据来源" prop="dataSource" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column show-overflow-tooltip label="处理详情" prop="handleDetail" min-width="200"></el-table-column>
        <el-table-column label="通知编号" prop="noticeNo" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column label="标题" prop="title" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column label="发起人" prop="initiator" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column label="发起时间" prop="initiationTime" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column label="状态" prop="status" fixed="right" min-width="80" align="center">
          <template v-slot="{row}">
            <span v-if="row.status === '未开始'">未开始</span>
            <span style="color: orange;" v-if="row.status === '未完成'">未完成</span>
            <span style="color: green;" v-if="row.status === '已完成'">已完成</span>
            <span style="color: red;" v-if="row.status === '已停止'">已停止</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="60">
          <template v-slot="{row}">
            <div v-if="hasPerm('menuAsimssAA6B_102')">
              <el-button type="text" size="small" @click="deleteRow(row.id)">
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="currentPage" :limit.sync="pageSize" @pagination="getTableData"/>
    </div>
    <el-dialog v-dialogDrag width="700px !important" title="新增设变关系" :visible.sync="addFormVisible" :close-on-click-modal="false">
      <el-form :rules="rules" ref="addForm" :label-width="formLabelWidth" :model="addForm">
        <el-form-item label="设变前编码" prop="originalCode">
          <el-input v-model.trim="addForm.originalCode" placeholder="请输入设变前编码" show-word-limit maxlength="100"></el-input>
        </el-form-item>
        <el-form-item label="设变前名称" prop="originalName">
          <el-input v-model.trim="addForm.originalName" placeholder="请输入设变前名称" show-word-limit maxlength="100"></el-input>
        </el-form-item>
        <el-form-item label="设变后编码" prop="changedCode">
          <el-input v-model.trim="addForm.changedCode" placeholder="请输入设变后编码" show-word-limit maxlength="100"></el-input>
        </el-form-item>
        <el-form-item label="设变后名称" prop="changedName">
          <el-input v-model.trim="addForm.changedName" placeholder="请输入设变后名称" show-word-limit maxlength="100"></el-input>
        </el-form-item>
        <el-form-item label="设变前用量" prop="originalAmount">
          <el-input v-model.trim="addForm.originalAmount" placeholder="请输入设变前用量" show-word-limit maxlength="100"></el-input>
        </el-form-item>
        <el-form-item label="设变后用量" prop="changedAmount">
          <el-input v-model.trim="addForm.changedAmount" placeholder="请输入设变后用量" show-word-limit maxlength="100"></el-input>
        </el-form-item>
        <el-form-item label="切换时间" prop="changedDate">
          <el-date-picker
            v-model="addForm.changedDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择切换日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="切换车型" prop="modelList">
          <el-input v-model.trim="addForm.modelList" placeholder="请输入切换车型" show-word-limit maxlength="100"></el-input>
        </el-form-item>
        <el-form-item label="替换性" prop="computability">
          <el-switch
            v-model="addForm.computability"
            active-text="是"
            inactive-text="否">
          </el-switch>
        </el-form-item>
        <div class="submitArea">
          <el-button type="primary" @click="submit('addForm')">
            确认
          </el-button>
          <el-button @click="cancel()">
            取消
          </el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import {handleAlert, tableHeight, tableMaxHeight} from '@/assets/js/common.js'
import Pagination from '@/components/Pagination'
import {
  listChanges,
  exportChanges,
  importChanges, saveData,
  downloadTemplate, deleteData,
  startChanges,
} from '@/api/sbommgt.js'

export default {
  name: 'sbomchangeslist',
  components: {Pagination},
  data() {
    return {
      tableData: [],
      pageSize: 20,
      currentPage: 1,
      total: 0,
      searchForm: {
        code: '',
        name: '',
      },
      selectedIds: [],
      rules: {
        originalCode: [{required: true, message: '设变前编码不能为空', trigger: ['blur', 'change']}],
        originalName: [{required: true, message: '设变前名称不能为空', trigger: ['blur', 'change']}],
        changedCode: [{required: true, message: '设变后编码不能为空', trigger: ['blur', 'change']}],
        changedName: [{required: true, message: '设变后名称不能为空', trigger: ['blur', 'change']}],
        originalAmount: [{required: true, message: '设变前用量不能为空', trigger: ['blur', 'change']}],
        changedAmount: [{required: true, message: '设变后用量不能为空', trigger: ['blur', 'change']}],
        changedDate: [{required: true, message: '设变时间不能为空', trigger: ['blur', 'change']}],
        modelList: [{required: true, message: '切换车型不能为空', trigger: ['blur', 'change']}],
        computability: [{required: true, message: '可替换性不能为空', trigger: ['blur', 'change']}],
      },
      addForm: {
        originalCode: '',
        originalName: '',
        changedCode: '',
        changedName: '',
        originalAmount: '',
        changedAmount: '',
        changedDate: '',
        modelList: '',
        computability: true,
      },
      addFormVisible: false,
      formLabelWidth: '100px',
      maximumHeight: 0,
    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    // 数据
    getTableData() {
      this.$loading.show();
      let params = {
        currentPage: this.currentPage,
        limit: this.pageSize,
        code: this.searchForm.code,
        name: this.searchForm.name,
      }
      listChanges(params).then(res => {
        this.total = res.data.total
        this.tableData = res.data.data
        this.tableHeightArea()
        this.$loading.hide();
      })
    },
    // 搜索
    keyDown(e) {
      if (e.keyCode === 13) {
        this.search()
      }
    },
    search() {
      this.currentPage = 1
      this.getTableData()
    },
    reset(formName) {
      this.$refs[formName].resetFields();
      this.currentPage = 1
      this.getTableData();
    },
    handleSelectionChange(selections) {
      this.selectedIds = selections.map((item) => {
        return item.id;
      });
    },

    exportData() {
      let params = {
        code: this.searchForm.code,
        name: this.searchForm.name,
      };
      this.$loading.show();
      exportChanges(params).then(res => {
        if (!res.data) {
          this.$loading.hide();
          handleAlert('error', '请稍候重试')
          return;
        }
        this.downloadRes(res);
      }).catch(err => {
        this.$loading.hide();
        handleAlert('error', '请稍候重试')
      })
    },
    downloadTemplate(templateFilename) {
      downloadTemplate(templateFilename).then(res => {
        if (!res.data) {
          return
        }
        let blob = new Blob([res.data]);
        let url = window.URL.createObjectURL(blob);
        let aLink = document.createElement("a");
        aLink.style.display = "none";
        aLink.href = url;
        aLink.setAttribute("download", templateFilename);
        document.body.appendChild(aLink);
        aLink.click();
        document.body.removeChild(aLink); //下载完成移除元素
        window.URL.revokeObjectURL(url); //释放掉blob对象
      }).catch(err => {
        handleAlert('error', err)
      })
    },
    downloadRes(res) {
      let fileName = decodeURI(res.headers['content-disposition'])
      if (fileName) {
        fileName = fileName.substring(fileName.indexOf('=') + 1)
      }
      let blob = new Blob([res.data]);
      let url = window.URL.createObjectURL(blob);
      let aLink = document.createElement("a");
      aLink.style.display = "none";
      aLink.href = url;
      aLink.setAttribute("download", fileName);
      document.body.appendChild(aLink);
      aLink.click();
      document.body.removeChild(aLink); //下载完成移除元素
      window.URL.revokeObjectURL(url); //释放掉blob对象
      this.$loading.hide();
    },
    importData(file) {
      this.$loading.show()
      let formData = new FormData();
      formData.append('file', file.file);
      importChanges(formData).then(res => {
        if (res.data.code === 100) {
          handleAlert('success', '批量导入成功')
          this.getTableData()
        } else {
          this.$alert(res.data.msg, '信息提示', {dangerouslyUseHTMLString: true})
        }
        this.$loading.hide()
      }).catch(err => {
        this.$loading.hide()
        handleAlert('error', '请稍候重试')
      })
    },
    onBeforeUpload(file) {
      if (file.name.match(/\.[^.]*$/g)[0] !== '.xlsx') {
        handleAlert('warning', "上传的文件只能是 xlsx 格式!")
        return false;
      }
      if (file.size / 1024 / 1024 > 100) {
        handleAlert('warning', "上传的文件大小不能超过 100MB!")
        return false;
      }
      return true;
    },
    add() {
      this.addFormVisible = true;
      this.$nextTick(() => {
        this.$refs.addForm.clearValidate();
      })
    },
    cancel() {
      this.addFormVisible = false;
    },
    submit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let params = {};
          for (let [key, value] of Object.entries(this.addForm)) {
            params[key] = value;
          }
          this.$loading.show();
          saveData(params, 'designChange').then((res) => {
            if (res.data.code === 100) {
              handleAlert('success', res.data.msg)
              this.getTableData();
              this.addFormVisible = false;
              this.$loading.hide();
            } else {
              handleAlert('error', res.data.msg)
              this.$loading.hide();
            }
          }).catch((err) => {
            this.$loading.hide();
            if (err !== null && err !== "" && err.responseText !== null) {
              handleAlert('error', "提交失败,请重试")
            }
          });
        } else {
          handleAlert('error', "请完善信息")
          this.$loading.hide();
        }
      });
    },
    maximumArea() {
      setTimeout(() => {
        tableHeight()
        setTimeout(() => {
          this.maximumHeight = tableMaxHeight;
        }, 50)
      })
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    },
    deleteRow(id) {
      this.$confirm('确定删除?', `删除数据`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteData({id: id}, 'designChange').then(res => {
          if (res.data.code === 100) {
            handleAlert('success', '删除成功')
            this.getTableData()
          } else {
            this.$alert(res.data.msg, '信息提示', {dangerouslyUseHTMLString: true})
          }
        }).catch(err => {
          handleAlert('error', '请稍候重试')
        })
      });
    },
    startTask() {
      this.$confirm('确定要开始定时任务?', '开始定时任务', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$loading.show();
        startChanges().then(res => {
          this.$loading.hide();
          if (res.data.code === 100) {
            handleAlert('success', '定时任务已开始')
            this.getTableData()
          } else {
            this.$alert(res.data.msg, '信息提示', {dangerouslyUseHTMLString: true})
          }
        }).catch(err => {
          this.$loading.hide();
          handleAlert('error', '请稍候重试')
        })
      }).catch(() => {
        // 用户取消操作，不做处理
      });
    },
  },
  mounted() {
    this.tableHeightArea();
    this.getTableData();
  },
  activated() {
    window.addEventListener('keydown', this.keyDown);
  },
  deactivated() {
    window.removeEventListener('keydown', this.keyDown, false);
  },
}
</script>
<style scoped>
.el-button {
  margin: 0 10px;
}

/* .el-switch__label {
  height: 33px;
} */
</style>
