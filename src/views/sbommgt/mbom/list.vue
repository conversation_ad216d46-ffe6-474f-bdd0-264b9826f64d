<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" ref="searchForm" label-width="55px" :model="searchForm" class="demo-form-inline">
        <!-- <el-form-item label="配件编码" prop="code">
                  <el-input v-model.trim="searchForm.code" placeholder="请输入配件编码"></el-input>
                </el-form-item>
                <el-form-item label="配件名称" prop="name">
                  <el-input v-model.trim="searchForm.name" placeholder="请输入配件名称"></el-input>
                </el-form-item>-->
        <el-form-item label="VIN码" prop="vin">
          <el-input v-model.trim="searchForm.vin" placeholder="请输入VIN码"></el-input>
        </el-form-item>
        <el-form-item label="配件编码" prop="code" label-width="65px">
          <el-input v-model.trim="searchForm.code" placeholder="请输入配件编码"></el-input>
        </el-form-item>
        <el-form-item label="配件名称" prop="name" label-width="65px">
          <el-input v-model.trim="searchForm.name" placeholder="请输入配件名称"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search()" icon="el-icon-search">搜索</el-button>
          <el-button plain @click="reset('searchForm')">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <div class="tableHandle">
        <el-button type="text" icon="bulkDown-icon" @click="exportData">批量下载</el-button>
      </div>
      <el-table
        style="width:100%;"
        border
        stripe
        ref="table"
        highlight-current-row
        :max-height="maximumHeight"
        :data="tableData"
        :header-cell-style="{
          'text-align': 'center',
          'background-color': 'var(--other-color)',
        }"
        @header-dragend="changeColWidth"
        @selection-change="handleSelectionChange"
      >
        <!--        <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>-->
        <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="BOM项目" prop="bomId" min-width="160"></el-table-column>
        <el-table-column show-overflow-tooltip label="配件编码" prop="code" min-width="160"></el-table-column>
        <el-table-column show-overflow-tooltip label="配件名称" prop="name" min-width="160"></el-table-column>
        <el-table-column show-overflow-tooltip label="需求用量" prop="amount" fixed="right" min-width="100" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="基本计量单位" prop="unit" fixed="right" min-width="150" align="center"></el-table-column>
        <el-table-column show-overflow-tooltip label="工厂" prop="manufactory" fixed="right" min-width="150"></el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="currentPage" :limit.sync="pageSize" @pagination="getTableData"/>
    </div>
  </div>
</template>
<script>
import {handleAlert, tableHeight, tableMaxHeight} from '@/assets/js/common.js'
import Pagination from '@/components/Pagination'
import {
  listMBOM,
  exportMBOM
} from '@/api/sbommgt.js'

export default {
  name: 'sbommbomlist',
  components: {Pagination},
  data() {
    return {
      tableData: [],
      pageSize: 20,
      currentPage: 1,
      total: 0,
      searchForm: {
        vin: '',
        code: '',
        name: '',
      },
      selectedIds: [],
      maximumHeight: 0,
    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth(){
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    // 数据
    getTableData() {
      if (!this.searchForm.vin) {
        handleAlert('warning', "请输入VIN后查询MBOM")
        return;
      }
      if (this.searchForm.vin.length !== 17) {
        handleAlert('warning', "请输入17位VIN后查询MBOM")
        return;
      }
      this.$loading.show();
      let {currentPage, pageSize, searchForm: {vin, code, name}} = this;
      let params = {currentPage, limit: pageSize, vin, code, name};
      // const loading = this.$loading({
      //   lock: true,
      //   text: '查询中……',
      //   spinner: 'el-icon-loading',
      //   background: 'rgba(0, 0, 0, 0.7)'
      // });
      listMBOM(params).then(res => {
        this.total = res.data.total
        this.tableData = res.data.data
        this.$loading.hide();
      }).catch(error => {
        this.$loading.hide();
      })
    },
    // 搜索
    keyDown(e) {
      if (e.keyCode === 13) {
        this.search()
      }
    },
    search() {
      this.currentPage = 1;
      this.getTableData()
    },
    reset(formName) {
      this.$refs[formName].resetFields();
      this.currentPage = 1
      // this.getTableData();
    },
    handleSelectionChange(selections) {
      console.log(selections)
      this.selectedIds = selections.map((item) => {
        return item.id;
      });
    },

    exportData() {
      let params = new FormData();
      params.append("vin", this.searchForm.vin);
      exportMBOM(params).then(res => {
        if (!res.data) {
          handleAlert('error', '请稍候重试')
          return;
        }
        this.downloadRes(res);
      }).catch(err => {
        console.error(err)
        handleAlert('error', err)
      })
    },
    downloadRes(res) {
      let fileName = decodeURI(res.headers['content-disposition'])
      if (fileName) {
        fileName = fileName.substring(fileName.indexOf('=') + 1)
      }
      let blob = new Blob([res.data]);
      let url = window.URL.createObjectURL(blob);
      let aLink = document.createElement("a");
      aLink.style.display = "none";
      aLink.href = url;
      aLink.setAttribute("download", fileName);
      document.body.appendChild(aLink);
      aLink.click();
      document.body.removeChild(aLink); //下载完成移除元素
      window.URL.revokeObjectURL(url); //释放掉blob对象
    },
    maximumArea() {
      setTimeout(() => {
        tableHeight()
        setTimeout(() => {
          this.maximumHeight = tableMaxHeight;
        }, 50)
      })
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    }
  },
  mounted() {
    // this.getTableData();
    this.tableHeightArea();
  },
  activated() {
    window.addEventListener('keydown', this.keyDown);
  },
  deactivated() {
    window.removeEventListener('keydown', this.keyDown, false);
  },
}
</script>
<style scoped>
.el-button {
  margin: 0 10px;
}
</style>
