<template>
  <div class="layoutContainer">
    <div class="infoDetail">
      <el-row>
        <el-col :span="5" class="leftData">
          <div>
            <div class="topButton">
              <span style="height:42px;line-height:42px;margin-left: 10px;">手册项目</span>
            </div>
            <div class="scrollClass">
              <el-scrollbar>
                <el-tree
                  ref="tree"
                  node-key="id"
                  :default-expanded-keys="nodeKeyList"
                  :data="listdata"
                  :props="defaultProps"
                  @node-click="handleNodeClick"
                  highlight-current
                >
                  <span slot-scope="{ node, data }" class="custom-tree-node" @mouseenter="mouseenter(data)" @mouseleave="mouseleave(data)">
                    <span>{{data.nameCh}}</span>
                    <span v-show="data.isCurrent" class="attribute" @click="attributeClick(data)">
                      <i class="el-icon-more" style="transform: rotate(90deg);" title="属性"></i>
                    </span>
                  </span>
                </el-tree>
              </el-scrollbar>
            </div>
          </div>
        </el-col>
        <el-col :span="19" class="fromRight manualManage">
          <el-table
            style="width:100%"
            :data="resultList"
            stripe
            highlight-current-row
            :header-cell-style="{
              'text-align': 'center',
              'background-color': 'var(--other-color)',
            }"
          >
            <el-table-column label="手册分类" prop="cnName"></el-table-column>
            <el-table-column label="负责人" prop="principalName"></el-table-column>
            <el-table-column label="开发状态" prop="manualStatus">
              <template slot-scope="{row}">
                <span v-if="row.manualStatus === 1">进行中</span>
                <span v-if="row.manualStatus === 2">暂停中</span>
                <span v-if="row.manualStatus === 3">已验收</span>
              </template>
            </el-table-column>
            <el-table-column label="截止时间" prop="deadlineTime">
              <template slot-scope="{row}">
                <div>
                  {{ row.deadlineTime | conversion("yyyy-MM-dd") }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="风险状态" prop="riskStatus">
              <template slot-scope="{row}">
                <span v-if="row.riskStatus === 1">无风险</span>
                <span v-if="row.riskStatus === 2" style="color:#c30000">低风险</span>
                <span v-if="row.riskStatus === 3" style="color:#c30000">中风险</span>
                <span v-if="row.riskStatus === 4" style="color:#c30000">高风险</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" fixed="right" width="210">
              <template slot-scope="{row}">
                <el-button v-if="hasPerm('menuAsimss3A3B_104')" type="text" size="small" @click="checkClick(row)">查看</el-button>
                <el-button v-if="hasPerm('menuAsimss3A3B_114')" type="text" size="small" @click="manualReview(row)">手册审核</el-button>
                <el-button v-if="hasPerm('menuAsimss3A3B_115')" type="text" size="small" @click="auditDetails(row)">审核明细</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
    </div>
    <el-dialog v-dialogDrag :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
      <el-form v-if="dialogStatus === 'detail'" ref='temp' :model="temp" :label-width="formLabelWidth">
        <el-form-item label="项目名称" prop="projectName">
          <el-input v-model="temp.projectName" readonly></el-input>
        </el-form-item>
        <el-form-item label="手册分类" prop="cnName">
          <el-input v-model="temp.cnName" readonly></el-input>
        </el-form-item>
        <el-form-item label="负责人" prop="principalName">
          <el-input v-model="temp.principalName" readonly></el-input>
        </el-form-item>
        <el-form-item v-for="(item, index) in manualMemberList" :prop="item.code" :key="index" :label="item.alias">
          <el-checkbox-group v-model="roleList[item.code]" >
            <el-checkbox v-for="(itemTwo, indexTwo) in item.children" :key="indexTwo" disabled :label="itemTwo.id">
              {{itemTwo.name}}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="截止时间" prop="deadlineTime">
          <el-date-picker type="date" v-model="temp.deadlineTime" readonly></el-date-picker>
        </el-form-item>
        <el-form-item label="开发状态" prop="manualStatus">
          <el-input v-if="temp.manualStatus === 1" readonly v-model="manualList[0].name"></el-input>
          <el-input v-if="temp.manualStatus === 2" readonly v-model="manualList[1].name"></el-input>
          <el-input v-if="temp.manualStatus === 3" readonly v-model="manualList[2].name"></el-input>
        </el-form-item>
        <el-form-item label="风险状态" prop="riskStatus">
          <el-input v-if="temp.riskStatus === 1" v-model="riskList[0].name" readonly>无风险</el-input>
          <el-input v-if="temp.riskStatus === 2" v-model="riskList[1].name" style="color:#962626" readonly>低风险</el-input>
          <el-input v-if="temp.riskStatus === 3" v-model="riskList[2].name" style="color:#962626" readonly>中风险</el-input>
          <el-input v-if="temp.riskStatus === 4" v-model="riskList[3].name" style="color:#962626" readonly>高风险</el-input>
        </el-form-item>
      </el-form>
      <el-form v-if="dialogStatus == 'attr'" ref='trainTemp' :model="trainTemp" :label-width="formLabelWidth">
        <el-form-item label="主机厂" prop="brand">
          <el-input v-model="trainTemp.brand" readonly></el-input>
        </el-form-item>
        <el-form-item label="项目名称" prop="nameCh">
          <el-input v-model="trainTemp.nameCh" readonly></el-input>
        </el-form-item>
        <el-form-item label="项目状态" prop="projectStatus">
          <el-input v-if="trainTemp.projectStatus == '1'" value="进行中" readonly></el-input>
          <el-input v-if="trainTemp.projectStatus == '2'" value="暂停中" readonly></el-input>
          <el-input v-if="trainTemp.projectStatus == '3'" value="已验收" readonly></el-input>
        </el-form-item>
        <el-form-item label="车型" prop="trainCode">
          <el-input v-model="trainTemp.trainCode" readonly></el-input>
        </el-form-item>
        <el-form-item label="年款" prop="trainYear">
          <el-input v-model="trainTemp.trainYear" readonly></el-input>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import { cmsServerUrl, addTabs, contentSize, tabPath, handleAlert, expandTree } from '@/assets/js/common.js'
import { auditCatalogue, auditManualList, manualUser, projectList, manualType, manualAdd, manualDel,manualMember } from '@/api/cmsmgt.js'
export default {
  name: 'cmsmgtauditlist',
  data () {
    return {
      roleList:[],
      nodeKeyList:[],
      manualMemberList:[],
      projectNameId: "",
      defaultProps: {
        children: 'children',
        label: 'nameCh',
      },
      trainTemp: {
        id:"",
        frimId: "",
        brand: "",
        nameCh: "",
        projectStatus:'',
        trainCode:"",
        trainYear: "",
      },
      temp: {
        id: '',
        projectId: '',
        projectName: '',
        cnName: '',
        manualType: '',
        principalName: '',
        principal: '',
        manualStatus: '',
        deadlineTime: '',
        firstDraftTime: '',
        finalDraftTime: '',
        riskStatus: ''
      },
      manualList: [
        { name: '进行中', code: 1 },
        { name: '暂停中', code: 2 },
        { name: '已验收', code: 3 }
      ],
      riskList: [
        { name: '无风险', code: 1 },
        { name: '低风险', code: 2 },
        { name: '中风险', code: 3 },
        { name: '高风险', code: 4 }
      ],
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        detail: '手册明细',
        attr: '项目属性'
      },
      resultList: [],
      listdata: [],
      pagesize: 20,
      currentPage: 1,
      total: 0,
      formLabelWidth: '100px',
    }
  },
  methods: {
    // 数据
    mouseenter(data) {
      data.isCurrent = true
    },
    mouseleave(data) {
      data.isCurrent = false
    },
    attributeClick(data){
      event.stopPropagation()
      this.trainDetail(data)
      this.dialogFormVisible = true
      this.dialogStatus = 'attr'
      this.$nextTick(function() {
        this.$refs.trainTemp.clearValidate();
      })
    },
    trainDetail(data){
      this.trainTemp.id = data.id
      this.trainTemp.frimId= data.frimId
      this.trainTemp.brand= data.brand
      this.trainTemp.nameCh= data.nameCh
      this.trainTemp.projectStatus= data.projectStatus
      this.trainTemp.trainCode= data.trainCode
      this.trainTemp.trainYear= data.trainYear
    },
    // 目录
    dataList () {
      auditCatalogue().then(res => {
        this.listdata = res.data.data
        this.expandStatus(this.listdata)
      })
    },
     expandStatus(data){
      var nodeExpand = expandTree(data)
      this.nodeKeyList.push(nodeExpand.id)
      this.handleNodeClick(nodeExpand)
      this.$nextTick(() => {
        this.$refs.tree.setCurrentKey(nodeExpand.id);
      });
    },
    // 右侧内容
    manualDetail(id){
      var params = { "projectId" : id}
      auditManualList(params).then(res => {
        this.total = res.data.total
        this.resultList = res.data.data
      })
    },
    handleNodeClick (data) {
      sessionStorage.removeItem("auditId")
      this.manualDetail(data.id);
      this.projectNameId = data.id
      this.temp.projectId = data.id
    },
    resetTemp () {
      this.temp = {
        id: '',
        projectName: '',
        cnName: '',
        principal: '',
        manualStatus: '',
        deadlineTime: '',
        firstDraftTime: '',
        finalDraftTime: '',
        riskStatus: ''
      }
    },
    manualMember(id){
      var oneArray = []
      var twoArray = []
      var threeArray = []
      manualMember(id).then(res => {
        if(res.data.code == 100){
          this.manualMemberList = res.data.data
          this.manualMemberList.forEach(item => {
            this.$set(this.roleList, item.code, [])
            item.children.forEach(itemTwo => {
              if(item.code=="developmentUser"){
                if(itemTwo.accessStatus == true){
                  oneArray.push(itemTwo.id)
                  this.$set(this.roleList, item.code, oneArray)
                }
              }else if(item.code == "auditUser"){
                if(itemTwo.accessStatus == true){
                  twoArray.push(itemTwo.id)
                  this.$set(this.roleList, item.code, twoArray)
                }
              } else if(item.code=="drawingUser"){
                if(itemTwo.accessStatus == true){
                  threeArray.push(itemTwo.id)
                  this.$set(this.roleList, item.code, threeArray)
                }
              }
            })
          })
        }
      })
    },
    checkClick(row){
      this.manualMember(row.id)
      this.dialogFormVisible = true
      this.resetTemp()
      this.dialogStatus = 'detail'
      this.temp = Object.assign({}, row)
    },
    // 手册审核
    manualReview (row) {
      let title = `${row.projectName}(${row.cnName}) 审核`
      // let curl = '/audit' + row.id + '/' + row.manualVersion
      this.$router.push({ name: 'audit', params: { id: row.id, version: row.manualVersion } })
      addTabs(this.$route.path, title);
    },
    // 审核明细
    auditDetails (row) {
      let title =  `${row.projectName}(${row.cnName})`
      // let curl = '/auditDetail'+row.id+'/'+row.manualVersion
      this.$router.push({ name: 'auditDetail', params: { id: row.id, version: row.manualVersion } })
      addTabs(this.$route.path, title);
    },
  },
  mounted () {
    this.dataList()
    contentSize();
  }
}
</script>
