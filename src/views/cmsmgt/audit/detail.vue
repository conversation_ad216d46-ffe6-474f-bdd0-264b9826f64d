<template>
  <div class="layoutContainer auditContent">
    <div class="tableDetail">
      <el-table
        style="width:100%"
        border
        stripe
        highlight-current-row
        :data="resultList"
        :header-cell-style="{
          'text-align': 'center',
          'background-color': 'var(--other-color)',
        }"
      >
        <el-table-column label="Topic名称" prop="directoryName" min-width="150"></el-table-column>
        <el-table-column label="审核状态" prop="status" min-width="150">
            <template slot-scope="{row}">
            <span v-if="row.status === 0">未审核</span>
            <span v-if="row.status === 1">审核通过</span>
            <span v-if="row.status === 2">审核驳回</span>
          </template>
        </el-table-column>
        <el-table-column label="驳回原因" prop="reason" min-width="150"></el-table-column>
        <el-table-column label="审核人" prop="auditorName" width="150"></el-table-column>
        <el-table-column label="审核时间" prop="createdTime" width="140">
          <template slot-scope="{row}">
            <div>
              {{ row.createdTime | conversion("yyyy-MM-dd") }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" prop="auditorName" fixed="right" width="140">
          <template slot-scope="{row}">
            <el-button type="text" v-if="hasPerm('menuAsimss3A3B_115')" size="small" @click="historyAudit(row)">历史审核</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total>0" :total="total" :page.sync="currentPage" :limit.sync="pagesize" @pagination="dataList"/>
      <el-dialog v-dialogDrag width="762px !important" title="历史审核"  :visible.sync="dialogFormVisible">
        <el-table style="width:100%" border :data="historyList">
          <el-table-column label="审核状态" prop="status" width="110">
            <template slot-scope="{row}">
              <span v-if="row.status === 0">未审核</span>
              <span v-if="row.status === 1">审核通过</span>
              <span v-if="row.status === 2">审核驳回</span>
            </template>
          </el-table-column>
          <el-table-column label="驳回原因" prop="reason" width="350"></el-table-column>
          <el-table-column label="审核人" prop="auditorName" width="120"></el-table-column>
          <el-table-column label="审核时间" prop="createdTime" width="140">
            <template slot-scope="{row}">
              <div>
                {{ row.createdTime | conversion("yyyy-MM-dd") }}
              </div>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="audittotal>0" :total="audittotal" :page.sync="auditcurrentPage" :limit.sync="auditpagesize" @pagination="dataList"/>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { sysServerUrl, cmsServerUrl, handleAlert } from '@/assets/js/common.js'
import { auditDetailData, auditHistory } from '@/api/cmsmgt.js'
import Pagination from '@/components/Pagination'
export default {
  name: 'auditDetail',
  components: { Pagination },
  data () {
    return {
      manualId: '',
      manualVersion: '',
      formInline: {
        manualVersion: '',
        queryDirectorName: '',  // Topic名称
        queryAuditStatus: '',   // 审核状态
        beginTime: null,  // 审核开始时间
        endTime: null,   // 审核结束时间
        queryAuditor: ''   // 审核人
      },
      resultList: [],
      versionList: [],
      auditorList: [],
      historyList: [],
      dialogFormVisible:false,
      pagesize: 20,
      currentPage: 1,
      total: 0,
      auditpagesize: 20,
      auditcurrentPage: 1,
      audittotal: 0,
    }
  },
  watch: {
    // 利用watch方法检测路由变化
    // $route: function(to, from) {
    //   if (to.fullPath !== from.fullPath)  {
    //     this.manualId = this.$route.params.id
    //     this.manualVersion =this.$route.params.version
    //     this.dataList()
    //   }
    // }
  },
  methods: {
    // 数据
    dataList () {
      var params = {
        page: this.currentPage,
        limit: this.pagesize,
        manualId: this.manualId,  // 手册ID
        manualVersion: this.manualVersion,    // 手册版本号
      }
      auditDetailData(params).then(res => {
        this.total = res.data.total
        this.resultList = res.data.data
      })
    },
    historyAudit(row){
      let directoryId=row.directoryId
      var params = {
        page: this.auditcurrentPage,
        limit: this.auditpagesize,
        directoryId: directoryId
      }
      auditHistory(params).then(res => {
        if(res.data.code==100){
          this.audittotal = res.data.total
          this.historyList = res.data.data
          this.dialogFormVisible=true
        }else{
          handleAlert('error','查询历史审核数据失败')
        }
      }).catch(function(error){
        handleAlert('error','查询历史审核数据失败')
      })
    },
  },
  mounted () {
    this.manualId = this.$route.params.id
    this.manualVersion =this.$route.params.version
    this.dataList()
  }
}
</script>
