<template>
  <div class="layoutContainer auditContent">
    <div class="infoDetail">
      <el-row>
        <el-col :span="9" class="leftData">
          <div>
            <div class="topButton" v-if="hasPerm('menuAsimss3A3B_116')">
              <el-button type="text" v-if="hasPerm('menuAsimss3A3B_116')" icon="adopt-icon" @click="auditPassClick">审核通过</el-button>
              <el-button type="text" v-if="hasPerm('menuAsimss3A3B_116')" icon="reject-icon" @click="showRejectDlg">审核驳回</el-button>
            </div>
            <div class="scrollClass">
              <el-scrollbar>
                <el-tree ref="tree"
                  :data="listdata"
                  :props="defaultProps"
                  node-key="id"
                  :default-expanded-keys="treeExpandIdList"
                  show-checkbox
                  highlight-current
                  :render-content="renderContent"
                  @node-click="handleNodeClick">
                </el-tree>
              </el-scrollbar>
            </div>
          </div>
        </el-col>
        <el-col :span="15" class="fromRight auditRight">
          <div class="formTitle">
            <div style="padding-left:15px;">
              <span v-if="form.state !== ''">{{form.cnName}} 审核状态：</span>
              <el-button type="text" v-if="form.state == '审核驳回'" style="color:red; text-decoration: underline" @click="rejectCause(form.id)">{{form.state}}</el-button>
              <el-button type="text" v-if="form.state == '审核通过'" style="color:green; font-weight: 700">{{form.state}}</el-button>
              <el-button type="text" v-if="form.state == '未审核'" style="color:blue; font-weight: 700">{{form.state}}</el-button>
            </div>
            <div class="rightBut">
              <el-button type="text" v-if="hasPerm('menuAsimss3A3B_116')" icon="opinion-icon" size="small" @click="auditOpinion(form.id)">审核意见</el-button>
            </div>
          </div>
          <div v-if="srcPath == ''" style="margin:15px 20px;text-align:center">{{pathTip}}</div>
          <iframe v-if="srcPath != ''" :src="srcPath" frameborder="0" width="100%" ></iframe>
        </el-col>
      </el-row>
      <el-dialog v-dialogDrag :width="dialogStatus == 'history' ? '762px !important' : ''" :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
        <el-form :model="dataForm" v-if="this.dialogStatus == 'reject'" :label-width="formLabelWidth" :rules="rules" ref='dataForm' label-position="center">
          <el-form-item label="驳回原因:" prop="rejectReason">
            <el-input type="textarea" rows="3" placeholder="请输入驳回原因" v-model.trim="dataForm.rejectReason"></el-input>
          </el-form-item>
          <el-form-item label="附件" prop="rejectAttach">
            <el-upload
              class="upload-demo"
              style="max-width: 379px;"
              :action="uploadUrl"
              :headers="importHeader"
              :on-success="handleOnSuccess"
              :on-remove="handleOnRemove"
              :before-remove="beforeOnRemove"
              :before-upload="beforeAvatarUpload"
              :on-exceed="handleOnExceed"
              multiple
              :limit="1"
              :file-list="fileList"
              accept=".JPG, .PNG, .JPEG,.jpg, .png, .jpeg,.DOC,.doc,.DOCX,.docx"
            >
              <el-button size="min" icon="el-icon-upload" type="primary">点击上传</el-button>
            </el-upload>
          </el-form-item>
          <div class="submitArea">
            <el-button type="primary" @click="auditRejectClick()" >
              立即提交
            </el-button>
            <el-button @click="dialogFormVisible = false" >
              取消
            </el-button>
          </div>
        </el-form>
        <el-form v-if="this.dialogStatus == 'history'">
          <el-table
            style="width:100%"
            border
            stripe
            highlight-current-row
            :data="historyList"
            :header-cell-style="{
              'text-align': 'center',
              'background-color': 'var(--other-color)',
            }"
          >
            <el-table-column label="审核状态" prop="status" width="110">
              <template slot-scope="{row}">
                <span v-if="row.status === 0">未审核</span>
                <span v-if="row.status === 1">审核通过</span>
                <span v-if="row.status === 2">审核驳回</span>
              </template>
            </el-table-column>
            <el-table-column label="驳回原因" prop="reason" width="350"></el-table-column>
            <el-table-column label="审核人" prop="auditorName" width="120"></el-table-column>
            <el-table-column label="审核时间" prop="createdTime" width="140">
              <template slot-scope="{row}">
                <div>
                  {{ row.createdTime | conversion("yyyy-MM-dd") }}
                </div>
              </template>
            </el-table-column>
          </el-table>
          <pagination v-show="total>0" :total="total" :page.sync="currentPage" :limit.sync="pagesize" @pagination="dataList"/>
        </el-form>
      </el-dialog>
      <el-dialog v-dialogDrag :title="form.cnName + ' 驳回原因'" :visible.sync="dialogVisible" >
        <p style="text-indent:2em;">{{cause.reason}} </p><p>&nbsp;</p>
        <img :src="url + 'cms/manual/image/display?filePath=' + cause.path" style="width:50%">
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'
import { sysServerUrl, cmsServerUrl, contentSize, handleAlert, expandEvents } from '@/assets/js/common.js'
import { auditManualData, auditPass, auditReject, auditHistory,  auditRejectCause } from '@/api/cmsmgt.js'
import { previewURL } from '@/api/releasemgt.js'
export default {
  name: 'audit',
  components: { Pagination },
  data () {
    return {
      defaultProps: {
        children: 'children',
        label: 'cnName',
      },
      dialogStatus: '',
      textMap: {
        reject: '审核驳回',
        history: '审核意见'
      },
      historyList:[],
      pagesize: 20,
      currentPage: 1,
      total: 0,
      form: {
        id: '',
        pid: '',
        manualId: '',
        manualVersion: '',
        state: '',
        effect: '',
        cnName: ''
      },
      formLabelWidth: '100px',
      manualId: 0,
      manualVersion: '',
      manualType: '',
      treeExpandIdList:[],
      listdata: [],
      dialogFormVisible: false,
      uploadUrl: '',
      dataForm: {
        rejectReason: '',
      },
      fileList: [],
      srcPath: '',
      pathTip:'',
      dialogVisible: false,
      url: '',
      cause: {
        id: '',
        reason: '',  // 原因
        path: '', // 图片路径
      },
      rules: {
        rejectReason: [{ required: true, message: '驳回原因不能为空' , trigger: ['blur', 'change']}],
      }
    }
  },
  computed: {
    importHeader: function () {
      return { Authorization: sessionStorage.token };
    }
  },
  methods: {
    // 数据
    dataList () {
      var params = new URLSearchParams()
      params.append('id', this.$route.params.id)
      params.append('manualVersion', this.$route.params.version)
      auditManualData(params).then(res=>{
        this.listdata = res.data.data
        this.expandStatus(this.listdata)
      })
    },
    expandStatus(data){
      var expand = expandEvents(data)
      this.treeExpandIdList.push(expand.id)
      this.handleNodeClick(expand)
      this.$nextTick(() => {
        this.$refs.tree.setCurrentKey(expand.id);
      });
    },
    previewPath(id , isLeafStatus){
      var directoryId = id + '/0'
      if( isLeafStatus == '1'){
        previewURL(directoryId).then(res => {
          if(res.data.code == '100'){
            if(res.data.data != null){
              this.srcPath = res.data.data;
              this.pathTip = ''
            }else{
              this.srcPath = res.data.data
              this.pathTip = '暂无详情页面'
            }
          }else{
            this.srcPath =''
            this.pathTip = res.data.msg
          }
        })
      }
    },
    renderContent(h, { node, data, store }) {
      let isLeaf = data.isLeaf;
      let vStatus= data.auditStatus==null?0:data.auditStatus;
      let style=''
      if(isLeaf==1){
	  	  if(vStatus==2){
	  	    style='red-category'
	  	  }else if(vStatus==1){
          style='green-category'
	  	  }else{
          style='blue-category'
	  	  }
  		}else{
        style='black-category'
  		}
      return(<span class={style} style="font-size:16px;cursor:pointer">{node.label}</span>)
    },
    // 审核通过
    auditPassClick() {
      var checkedNodes = this.$refs.tree.getCheckedNodes();
      if(checkedNodes.length==0){
        handleAlert('error','请先勾选目录节点')
        return false
      }
		  var chkIds="";
			for(var i=0;i<checkedNodes.length;i++){
				if(checkedNodes[i].isLeaf==1){
					chkIds+=checkedNodes[i].id + ",";
				}
		  }
      var params = new URLSearchParams()
      params.append('directoryIds', chkIds)
      params.append('manualId', this.manualId)
      params.append('manualVersion',this.manualVersion)
      auditPass(params).then(res => {
        if(res.data.code==100){
          handleAlert('success',res.data.msg)
          this.dataList()
        }else{
          handleAlert('error', res.data.msg)
        }
      }).catch(function(error){
        handleAlert('error','审核提交失败')
      })
    },
    resetTemp(){
      this.dataForm = {
        rejectReason: '',
      }
      this.$nextTick(function() {
        this.$refs.dataForm.clearValidate();
      })
    },
    showRejectDlg(){
      this.dialogStatus = "reject"
      this.fileList=[]
      var checkedNodes = this.$refs.tree.getCheckedNodes();
      if(checkedNodes.length==0){
        handleAlert('error','请先勾选目录节点')
        return false
      }
      this.dialogFormVisible=true
      this.resetTemp()
    },
    // 审核驳回
    auditRejectClick() {
      if(this.dataForm.rejectReason==''){
        handleAlert('error','请输入驳回原因')
        return false
      }
      var checkedNodes = this.$refs.tree.getCheckedNodes();
		  var chkIds="";
			for(var i=0;i<checkedNodes.length;i++){
			 	if(checkedNodes[i].isLeaf==1){
			 	  chkIds+=checkedNodes[i].id + ",";
			 	}
		  }
      var params = new URLSearchParams()
      params.append('directoryIds', chkIds)
      params.append('manualId',this.manualId)
      params.append('reason', this.dataForm.rejectReason)
      params.append('manualVersion',this.manualVersion)
      let fileName=''
      let filePath=''
      if(this.fileList!=null&&this.fileList.length>0){
        fileName =this.fileList[0].name
        filePath =this.fileList[0].url
      }
      params.append('fileName', fileName)
      params.append('path',filePath)
      auditReject(params).then(res => {
        if(res.data.code==100){
          handleAlert('success','审核驳回提交成功')
          this.dataList()
          this.dialogFormVisible=false
        }
      }).catch(function(error){
        handleAlert('error','审核驳回提交失败')
      })
    },
    handleOnSuccess (res, obj) {
      var file = {name: res.data.fileName, url: res.data.fileUrl}
      this.fileList.push(file)
    },
    beforeOnRemove(file, fileList) {
      return this.$confirm(`确定移除选择文件？`, '删除',{type: 'warning'});
    },
    handleOnRemove(file,fileList){
      if(fileList.length == '0'){
        this.fileList = []
      }
    },
    beforeAvatarUpload (file) {
      var fileName = file.name.substring(file.name.lastIndexOf(".")+1).toLowerCase()
      const extension = fileName === 'png'
      const extension2 = fileName === 'jpg'
      const extension3 = fileName === 'jpeg'
      const extension4 = fileName === 'gif'
      const extension5 = fileName === 'doc'
      const extension6 = fileName === 'docx'
      const isLt2M = file.size / 1024 / 1024 < 5
      if (!extension && !extension2 && !extension3 && !extension4 && !extension5 && !extension6) {
        handleAlert('warning','上传模板只能是 png、jpg、jpeg、gif、doc、docx格式!')
      }
      if (!isLt2M) {
        handleAlert('warning','上传模板大小不能超过 5MB!')
      }
    },
    handleOnExceed (files, fileList) {
      handleAlert('warning',`当前限制选择1个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
    },
    // 历史审核意见
    auditOpinion(id){
      if(id == ''){
        handleAlert("error", "请选择节点信息")
      }else {
        this.dialogStatus="history"
        this.dialogFormVisible=true
        var params = {
          page: this.currentPage,
          limit: this.pagesize,
          directoryId: id
        }
        auditHistory(params).then(res => {
          this.total = res.data.total
          this.historyList = res.data.data
        })
      }
    },
    // 显示详情
    handleNodeClick (data) {
      this.previewPath(data.id, String(data.isLeaf))
      if(data.isLeaf == 0){
        return false;
      }
      if (data.children != null && data.children.length>0) {
        return false;
      }
      this.form.id = data.id
      this.form.pid = data.pid
      this.form.manualId = data.manualId
      this.form.manualVersion = this.manualVersion
      this.form.cnName =  data.cnName
      if (data.isLeaf === 1) {
        let code = data.code
        if (data.auditStatus === 1) {
          this.form.state = '审核通过'
        } else if (data.auditStatus === 2) {
          this.form.state = '审核驳回'
        } else {
          this.form.state = '未审核'
        }
      } else {
        this.form.state = ''
      }
    },
    rejectCause(id){
      var params = new URLSearchParams()
      params.append('id', id)
      auditRejectCause(params).then(res=>{
        if(res.data.code == 100){
          this.cause.id = res.data.data.id
          this.cause.reason = res.data.data.reason  // 原因
          this.cause.path = res.data.data.path // 图片路径
        }
      })
      this.dialogVisible = true
    },
  },
  mounted () {
    this.manualId = this.$route.params.id
    this.manualVersion=this.$route.params.version
    this.uploadUrl = sysServerUrl + 'sys/upload/attach?flag=ManualReason'
    this.url = cmsServerUrl
    this.dataList()
    contentSize()
  }
}
</script>
<style>
  .red-category,
  .green-category,
  .blue-category,
  .black-category{
    color:red;
    font-size: 14px !important;
    margin-left: 5px !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .green-category{
    color:green;
  }
  .blue-category{
    color:blue;
  }
  .black-category{
    color:#333;
  }
  .infoDetail .fromRight.auditRight .formTitle{
    background: #fafafa;
    border-bottom: 1px solid #e7ebef;
    box-sizing: border-box;
    font-weight: bold;
    box-sizing: border-box;
    height: 42px;
    line-height: 42px;
    text-align: left;
  }
  .infoDetail .auditRight .formTitle div{
    display: inline-block;
  }
  .infoDetail .auditRight .formTitle .rightBut{
    float: right;
  }
  .auditContent .el-pagination{
    padding: 3px 20px !important;
  }
  .auditContent .el-dialog .el-select .el-input.el-input--suffix{
    width: 100px !important;
  }
  .auditContent .el-dialog .el-input__inner {
    padding: 0 ;
  }
</style>
