<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" ref="formInline" :model="formInline" class="demo-form-inline">
        <el-form-item label="项目名称" prop="projectName">
          <el-input v-model.trim="formInline.projectName" placeholder="请输入项目名称"></el-input>
        </el-form-item>
        <el-form-item label="负责人" prop="director">
          <el-select v-model="formInline.director" clearable filterable>
            <el-option v-for="(item, index) of principalList" :key="index" :label="item.realName" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="项目状态" prop="projectStatus">
          <el-select v-model="formInline.projectStatus" clearable filterable>
            <el-option label="进行中" value="1"></el-option>
            <el-option label="暂停中" value="2"></el-option>
            <el-option label="已验收" value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="风险状态" prop="riskStatus">
          <el-select v-model="formInline.riskStatus" clearable filterable>
            <el-option label="无风险" value="1"></el-option>
            <el-option label="低风险" value="2"></el-option>
            <el-option label="中风险" value="3"></el-option>
            <el-option label="高风险" value="4"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="车型" prop="trainCode">
          <el-input v-model.trim="formInline.trainCode" placeholder="请输入车型"></el-input>
        </el-form-item>
        <el-form-item label="年款" prop="trainYear">
          <el-input v-model.trim="formInline.trainYear" placeholder="请输入年款"></el-input>
        </el-form-item>
        <el-form-item label="立项时间范围">
          <el-date-picker prop="begintime" align="center" value-format="yyyy-MM-dd" type="date" placeholder="选择开始日期" v-model="formInline.begintime" :picker-options="pickerBeginTime"></el-date-picker>
          <span class="line">至</span>
          <el-date-picker prop="endtime" align="center" value-format="yyyy-MM-dd" type="date" placeholder="选择结束日期" v-model="formInline.endtime" :picker-options="pickerEndTime"></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit" icon="el-icon-search">搜索</el-button>
          <el-button plain @click="reset('formInline')">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <div class="tableHandle" v-if="hasPerm('menuAsimss3A1B_101')">
        <el-button type="text" icon="el-icon-plus" @click="handelAdd()">新增</el-button>
      </div>
      <el-table
        style="width:100%"
        border
        stripe
        highlight-current-row
        :data="resultList"
        :header-cell-style="{
          'text-align': 'center',
          'background-color': 'var(--other-color)',
        }"
      >
        <el-table-column label="序号" type="index" width="60"></el-table-column>
        <el-table-column label="项目名称" prop="name" min-width="150"></el-table-column>
        <el-table-column label="负责人" prop="principalName" min-width="150"></el-table-column>
        <el-table-column label="项目状态" prop="projectStatus" width="100">
          <template slot-scope="{row}">
            <span v-if="row.projectStatus === 1">进行中</span>
            <span v-if="row.projectStatus === 2">暂停中</span>
            <span v-if="row.projectStatus === 3">已验收</span>
          </template>
        </el-table-column>
        <el-table-column label="车型" prop="trainCode" min-width="150">
          <template slot-scope="{row}">
            <span v-if="row.trainCode !=null && row.trainCode !='' ">{{row.modelName+'('+row.trainCode+')'}}</span>
          </template>
        </el-table-column>
        <el-table-column label="年款" prop="trainYear" width="100"></el-table-column>
        <el-table-column label="立项时间" width="140" :picker-options="pickerEstablishTime">
          <template slot-scope="{row}">
            <div>
              {{ row.establishTime | conversion("yyyy-MM-dd HH:mm:ss") }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="截止时间" width="140" :picker-options="pickerDeadlineTime">
          <template slot-scope="{row}">
            <div>
              {{ row.deadlineTime | conversion("yyyy-MM-dd HH:mm:ss") }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="风险状态" prop="riskStatus" width="100">
          <template slot-scope="{row}">
            <span v-if="row.riskStatus === 1">无风险</span>
            <span v-if="row.riskStatus === 2" style="color:#c30000">低风险</span>
            <span v-if="row.riskStatus === 3" style="color:#c30000">中风险</span>
            <span v-if="row.riskStatus === 4" style="color:#c30000">高风险</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="230">
          <template slot-scope="{row}">
            <el-button type="text" v-if="hasPerm('menuAsimss3A1B_104')" size="small" @click="handelDetail(row)">查看</el-button>
            <el-button type="text" v-if="hasPerm('menuAsimss3A1B_103')" size="small" @click="handelEdit(row)">编辑</el-button>
             <el-button type="text" v-if="hasPerm('menuAsimss3A1B_103')" size="small" @click="handelMember(row)">成员</el-button>
            <el-button type="text" v-if="hasPerm('menuAsimss3A1B_102')" size="small" @click="handelDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total>0" :total="total" :page.sync="currentPage" :limit.sync="pagesize" @pagination="dataList"/>
      <el-dialog v-dialogDrag :width="dialogStatus == 'member' ? '450px !important' : ''" :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
        <el-form v-if="dialogStatus === 'detail'" :label-width="formLabelWidth" ref='temp' :model="temp" label-position="center" :validate-on-rule-change="false">
          <el-form-item label="公司" prop="firmId">
            <el-select v-model="temp.firmId" clearable filterable disabled>
              <el-option v-for="(item, index) of firmList" :key="index" :label="item.nameCh" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="项目名称" prop="name">
            <el-input v-model="temp.name" readonly></el-input>
          </el-form-item>
          <el-form-item label="负责人" prop="principal">
            <el-input v-model="temp.principalName" readonly></el-input>
          </el-form-item>
          <el-form-item label="立项时间" prop="establishTime">
            <el-date-picker type="date" v-model="temp.establishTime" readonly></el-date-picker>
          </el-form-item>
          <el-form-item label="截止时间" prop="deadlineTime">
            <el-date-picker type="date" v-model="temp.deadlineTime" readonly></el-date-picker>
          </el-form-item>
          <el-form-item label="项目状态" prop="projectStatus">
            <el-input v-model="temp.projectStatus" readonly></el-input>
          </el-form-item>
          <el-form-item label="风险状态" prop="riskStatus">
            <el-input v-model="temp.riskStatus" readonly></el-input>
          </el-form-item>
          <el-form-item label="车型" prop="trainCode">
            <el-input v-model="temp.trainCode" readonly></el-input>
          </el-form-item>
          <el-form-item label="年款" prop="trainYear">
            <el-input v-model="temp.trainYear" readonly></el-input>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="temp.remark" readonly></el-input>
          </el-form-item>
        </el-form>
        <el-form v-if="dialogStatus === 'add' || dialogStatus === 'edit'" :label-width="formLabelWidth" ref='temp' :model="temp" :rules="rules"  label-position="center" :validate-on-rule-change="false">
          <el-form-item label="公司" prop="firmId">
            <el-select v-model="temp.firmId" @change="firmChanged" clearable filterable :disabled="editStatus">
              <el-option v-for="(item, index) of firmList" :key="index" :label="item.nameCh" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="项目名称" prop="name">
            <el-input v-model.trim="temp.name" placeholder="请输入项目名称"   show-word-limit maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="负责人" prop="principal">
            <el-select v-model="temp.principal" clearable filterable>
              <el-option v-for="(item, index) of principalList" :key="index" :label="item.realName" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="立项时间" prop="establishTime">
           <el-date-picker type="date" v-model="temp.establishTime" placeholder="请选择立项时间" :picker-options="pickerEstablishTime"></el-date-picker>
          </el-form-item>
          <el-form-item label="截止时间" prop="deadlineTime">
            <el-date-picker type="date" v-model="temp.deadlineTime" placeholder="请选择截止时间" :picker-options="pickerDeadlineTime"></el-date-picker>
          </el-form-item>
          <el-form-item label="项目状态" prop="projectStatus">
            <el-select v-model="temp.projectStatus" clearable filterable>
              <el-option v-for="(item, index) of projectStatusList" :key="index" :label="item.name" :value="item.code"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="风险状态" prop="riskStatus">
            <el-select v-model="temp.riskStatus" clearable filterable>
              <el-option v-for="(item, index) of riskStatusList" :key="index" :label="item.name" :value="item.code"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="车型" prop="trainCode">
            <select-tree
            ref="modelSelectTree"
            :options="trainList"
            v-model.trim="temp.trainCode"
            :props="defaultProps"
            @getCurrentNode="getCurrentNode"
            placeholder="请选择车型"
            :isDisabled="dialogStatus === 'edit'? true: false" />
          </el-form-item>
          <el-form-item label="年款" prop="trainYear">
            <el-select v-model="temp.trainYear" clearable filterable :disabled="dialogStatus === 'edit'? true: false">
              <el-option v-for="(item,index) in trainYear" :key="index" :label="item" :value="item"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model.trim="temp.remark" placeholder="请输入备注" show-word-limit maxlength="100"></el-input>
          </el-form-item>
          <div class="submitArea">
            <el-button type="primary" @click="dialogStatus === 'add' ? addClick('temp') : updateData('temp')">
              立即提交
            </el-button>
            <el-button @click="resetForm('temp')">
              重置
            </el-button>
          </div>
        </el-form>
        <el-form class="memberInfo" style="margin:-5px 30px" :label-width="formLabelWidth" v-if="dialogStatus === 'member'" ref='memberForm' :model="memberForm"  :rules="memberRules" >
          <el-form-item v-for="(item, index) in memberList" :prop="item.code" :label="item.alias" :key="index">
            <div style="width:100%">
             <el-tree ref="developmentTree"
                v-if="item.code == 'developmentUser'"
                :data="item.children"
                node-key='id'
                show-checkbox
                default-expand-all
                :default-checked-keys="developmentCheckList"
                :props="memberProps"
                highlight-current
                @check="(click, checked)=>{developmentChange(click, checked)}"
              ></el-tree>
              <el-tree ref="drawingTree"
                v-if="item.code == 'drawingUser'"
                :data="item.children"
                node-key='id'
                show-checkbox
                default-expand-all
                :default-checked-keys="drawingCheckList"
                :props="memberProps"
                highlight-current
                @check="(click, checked)=>{drawingChange(click, checked)}"
              ></el-tree>
              <el-tree ref="auditTree"
                v-if="item.code == 'auditUser'"
                :data="item.children"
                node-key='id'
                show-checkbox
                default-expand-all
                :default-checked-keys="auditCheckList"
                :props="memberProps"
                highlight-current
                @check="(click, checked)=>{auditChange(click, checked)}"
              ></el-tree>
           </div>
          </el-form-item>
          <div class="submitArea">
            <el-button type="primary" @click="updateRole('memberForm')">
              立即提交
            </el-button>
            <el-button @click="resetCode()">
              重置
            </el-button>
          </div>
        </el-form>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { sysServerUrl, cmsServerUrl, getMyDate,  projectDate, handleAlert } from '@/assets/js/common.js'
import SelectTree from '@/components/TreeView/SelectTree.vue';
import Pagination from '@/components/Pagination'
import { projectData, trainInfo, yearInfo, projectUser, projectFirm, projectAdd, projectEdit, projectDel, projectMember, memberUpdate } from '@/api/cmsmgt.js';
export default {
  name: 'cmsmgtprojectlist',
  components: { Pagination, SelectTree },
  data () {
    return {
      developmentCheckList:[],
      auditCheckList:[],
      drawingCheckList:[],
      memberForm:{
        auditUser:"",
        developmentUser:"",
        drawingUser:"",
      },
      roleList:[],
      formInline: {
        projectName: '',
        director: '',
        projectStatus: '',
        begintime: null,
        endtime: null,
        modelName: '',
        trainCode: '',
        trainYear: '',
        riskStatus: ''
      },
      temp: {
        id: '',
        firmId: '',
        name: '',
        principal: '',
        establishTime: '',
        deadlineTime: '',
        projectStatus: '',
        riskStatus: '',
        modelName: '',
        trainCode: '',
        trainYear: '',
        remark: ''
      },
      sltModelYear: '',
      riskStatusList: [
        { name: '无风险', code: 1 },
        { name: '低风险', code: 2 },
        { name: '中风险', code: 3 },
        { name: '高风险', code: 4 }
      ],
      projectStatusList: [
        { name: '进行中', code: 1 },
        { name: '暂停中', code: 2 },
        { name: '已验收', code: 3 }
      ],
      projectId:"",
      dialogFormVisible: false,
      dialogStatus: '',
      editStatus:false,
      textMap: {
        detail: '项目明细',
        edit: '编辑项目',
        add: '新增项目',
        member: '项目成员管理',
      },
      resultList: [],
      firmList: [],
      trainList: [],
      trainYear: [],
      memberList: [],
      // 数据默认字段
      defaultProps: {
        parent: 'pid',   // 父级唯一标识
        value: 'id',          // 唯一标识
        label: 'nameCh',       // 标签显示
        children: 'children', // 子级
      },
      principalList: [],
      modelName: "",
      pagesize: 20,
      currentPage: 1,
      total: 0,
      formLabelWidth: '100px',
      rules: {
        firmId: [{ required: true, message: '公司不能为空', trigger: ['blur', 'change'] }],
        name: [{ required: true, message: '项目名称不能为空', trigger:['blur', 'change'] }],
        principal: [{ required: true, message: '负责人不能为空', trigger: ['blur', 'change'] }],
        establishTime: [{ required: true, message: '立项时间不能为空', trigger: ['blur', 'change'] }],
        deadlineTime: [{ required: true, message: '截止时间不能为空', trigger: ['blur', 'change'] }],
        trainCode: [{ required: true, message: '车型不能为空', trigger: ['blur', 'change'] }],
        trainYear: [{ required: true, message: '年款不能为空', trigger: ['blur', 'change'] }]
      },
      memberRules:{
        developmentUser: [{ required: true, message: '开发人员不能为空', trigger: ['blur' ] }],
        drawingUser: [{ required: true, message: '制图人员不能为空', trigger: ['blur' ] }],
        auditUser: [{ required: true, message: '审核人员不能为空', trigger: ['blur'] }],
      },
      memberProps:{
        children: 'children',
        label: 'name',
      },
      pickerBeginTime: {
        disabledDate: (time) => {
          return this.formInline.endtime != null ? time.getTime() > new Date(this.formInline.endtime) : false //只能选结束日期之前的日期
          //返回---结束时间是否有值？   可选时间小于结束时间   ：  任意时间都可选
        }
      },
      pickerEndTime: {
        disabledDate: (time) => {
          return this.formInline.begintime != null ? time.getTime() < new Date(this.formInline.begintime) : false //只能选开始时间之后的日期
          //返回---开始时间是否有值？   可选时间大于开始时间   ：  任意时间都可选
        }
      },
      pickerEstablishTime: {
        disabledDate: (time) => {
          return this.temp.deadlineTime ? time.getTime() > this.temp.deadlineTime : false //只能选结束日期之前的日期
          //返回---结束时间是否有值？   可选时间小于结束时间   ：  任意时间都可选
        }
      },
      pickerDeadlineTime: {
        disabledDate: (time) => {
          return this.temp.establishTime ? time.getTime() < this.temp.establishTime : false //只能选开始时间之后的日期
          //返回---开始时间是否有值？   可选时间大于开始时间   ：  任意时间都可选
        }
      }
    }
  },
  methods: {
    // 分页查询数据
    dataList () {
      var params = {
        page: this.currentPage,   // 当前页
        limit: this.pagesize,     // 每页显示的条数
        name: this.formInline.projectName,
        principal: this.formInline.director,
        projectStatus: this.formInline.projectStatus,
        establishTime: this.formInline.begintime,  // string 类型
        deadlineTime: this.formInline.endtime,
        trainCode: this.formInline.trainCode,
        trainYear: this.formInline.trainYear,
        riskStatus: this.formInline.riskStatus
      }
      projectData(params).then(res => {
        this.total = res.data.total    // 总条数
        this.resultList = res.data.data   // 数据
      })
    },
    getTrainList(firmId){
      var params = {
        firmId: firmId
      }
      trainInfo(params).then(res => {
        if(res.data.code==100){
          this.trainList = res.data.data
        }
      })
    },
    getTrainYearList(trainId){
      var params = {
        code: trainId
      }
      yearInfo(params).then(res => {
        if(res.data.code==100){
          let yearList = res.data.data
          if(!this.campareSameArray(yearList,this.trainYear)){
            if(this.trainYear&&this.trainYear.length>0){
              this.temp.trainYear=''
            }
            this.trainYear = yearList
          }
        }else{
          this.temp.trainYear=''
          this.trainYear =[]
        }
      })
    },
    campareSameArray(arr1,arr2){
      let ret=false
      if(arr1&&arr1.length>0&&arr2&&arr2.length>0){
        if(arr1.sort().toString()==arr2.sort().toString()){
          ret=true
        }
      }
      return ret
    },
    firmChanged(value){
      if(value!=''){
        this.getTrainList(value)
        this.temp.trainCode=''
        this.temp.trainYear=''
      }
    },
    getCurrentNode(node){

      if(this.temp.trainCode!=null&&node!=null){
        this.getTrainYearList(node.id)
        if(this.temp.trainCode!=node.id){
          this.temp.trainYear=''
        }
        this.$refs['temp'].validateField('trainCode')
      }
    },
    // 搜索
    onSubmit () {
      this.currentPage=1
      this.dataList();
    },
    // 负责人
    getUserList () {
      projectUser().then(res => {
        if (res.data.code === 100) {
          this.principalList = res.data.data
        }
      })
    },
    // 公司
    getFirmList () {
      projectFirm().then(res => {
        if (res.data.code == 100) {
          this.firmList = res.data.data
        }
      })
    },
    resetTemp () {
      this.temp = {
        firmId: '',
        name: '',
        principal: '',
        principalName: '',
        establishTime: '',
        deadlineTime: '',
        projectStatus: '',
        riskStatus: '',
        modelName: '',
        trainCode: '',
        trainYear: '',
        remark: ''
      }
      this.$nextTick(function() {
        this.$refs.temp.clearValidate();
      })
    },
    // 风险状态
    getStatusCode () {
      if(this.temp.riskStatus == null){
        this.temp.riskStatus =""
      } else {
        for (var j = 0; j < this.riskStatusList.length; j++) {
          if (this.riskStatusList[j].code === this.temp.riskStatus) {
            this.temp.riskStatus = this.riskStatusList[j].name
          }
        }
      }
      if(this.temp.projectStatus == null){
        this.temp.projectStatus = ""
      }else {
        for (var k = 0; k < this.projectStatusList.length; k++) {
          if (this.projectStatusList[k].code === this.temp.projectStatus) {
            this.temp.projectStatus = this.projectStatusList[k].name
          }
        }
      }
    },
    resetForm (temp) {
      if(this.dialogStatus == 'edit'){
        this.temp.name= ''
        this.temp.principal= ''
        this.temp.principalName= ''
        this.temp.establishTime= ''
        this.temp.deadlineTime= ''
        this.temp.projectStatus= ''
        this.temp.riskStatus= ''
        this.temp.modelName= ''
        this.temp.remark=''
        this.$nextTick(function() {
          this.$refs.temp.clearValidate();
        })
      } else {
        this.resetTemp()
      }
    },
    // 增加
    handelAdd () {
      var _this = this
      _this.trainList=[]
      _this.trainYear=[]
      _this.dialogFormVisible = true
      _this.dialogStatus = 'add'
      _this.editStatus = false
      setTimeout(() => {
        _this.$refs.modelSelectTree.initSelected('','')
        _this.resetTemp()
      })
    },
    addClick (temp) {
      if(this.temp.firmId == ''){
        handleAlert('error','公司不能为空')
        return false;
      }
       if(this.temp.name == ''){
        handleAlert('error','项目名称不能为空')
        return false;
      }
       if(this.temp.principal == ''){
        handleAlert('error','负责人不能为空')
        return false;
      }
       if(this.temp.establishTime == ''){
        handleAlert('error','立项时间不能为空')
        return false;
      }
       if(this.temp.deadlineTime == ''){
        handleAlert('error','截止时间不能为空')
        return false;
      }
      let inputValue = this.$refs.modelSelectTree.getinputLable();
      let modelValue = this.$refs.modelSelectTree.getLabelModel();
      if (modelValue == "") {
        handleAlert('error','车型不能为空')
        return false;
      }
      if (modelValue != inputValue && inputValue!="") {
        var _this=this
        setTimeout(function(){
          _this.$refs.modelSelectTree.initSelected('','')
          _this.$refs.modelSelectTree.setInput()
        },0);
        handleAlert('error','车型不存在')
        return false;
      }
      this.$refs[temp].validate((valid) => {
        if(valid){
          var params = new URLSearchParams()
          params.append('firmId', this.temp.firmId)
          params.append('name', this.temp.name)
          params.append('principal', this.temp.principal)
          params.append('establishTime', getMyDate(this.temp.establishTime))
          params.append('deadlineTime', getMyDate(this.temp.deadlineTime))
          params.append('projectStatus', this.temp.projectStatus)
          params.append('riskStatus', this.temp.riskStatus)
          params.append('trainCode', this.temp.trainCode)
          params.append('trainYear', this.temp.trainYear)
          params.append('remark', this.temp.remark)
          projectAdd(params).then(res => {
            if (res.data.code === 100) {
              handleAlert('success',res.data.msg)
              this.dataList()
              projectDate()
              this.dialogFormVisible = false
            } else {
              handleAlert('error',res.data.msg)
            }
          })
        }else{
          handleAlert('error','请完善项目信息')
        }
      })

    },
    // 详情
    handelDetail (row) {
      this.dialogFormVisible = true
      this.dialogStatus = 'detail'
      this.resetTemp()
      this.temp = Object.assign({}, row)
      this.getStatusCode()
    },
    // 编辑
    handelEdit (row) {
      var _this=this
      _this.dialogFormVisible = true
      _this.trainList=[]
      _this.trainYear=[]
      _this.getTrainList(row.firmId)
      _this.getTrainYearList(row.trainCode)
      _this.resetTemp()
      _this.temp = Object.assign({}, row)
      if(this.temp.riskStatus == null){
        this.temp.riskStatus = ""
      }
      setTimeout(function(){
        _this.$refs.modelSelectTree.initSelected(row.modelName,row.trainCode)
      },0);
      _this.dialogStatus = 'edit'
      _this.editStatus=true
    },
    updateData (temp) {
      this.$refs[temp].validate((valid) => {
        if(valid){
          var params = new URLSearchParams()
          params.append('id', this.temp.id)
          params.append('firmId', this.temp.firmId)
          params.append('name', this.temp.name)   // 项目名称
          params.append('principal', this.temp.principal)   // 负责人
          params.append('establishTime', getMyDate(this.temp.establishTime))   // 开始时间
          params.append('deadlineTime', getMyDate(this.temp.deadlineTime))    // 结束时间
          params.append('projectStatus', this.temp.projectStatus)    // 项目状态
          params.append('riskStatus', this.temp.riskStatus)   // 风险状态
          params.append('trainCode', this.temp.trainCode)     // 车型
          params.append('trainYear', this.temp.trainYear)     // 车款
          params.append('remark', this.temp.remark)    // 备注
          projectEdit(params).then(res => {
            if (res.data.code === 100) {
              handleAlert('success','保存成功')
              this.dataList()
              projectDate()
              this.dialogFormVisible = false
            } else {
              handleAlert('error',res.data.msg)
            }
          }).catch(err => {
            if (err !== null && err !== '' && err.responseText !== null) {
              handleAlert('error','提交失败,请重试')
            }
          })
        } else {
          handleAlert('error','请完善项目信息')
        }
      })
    },
    roleState(){
      this.$set(this.roleList, "developmentUser", [])
      this.$set(this.roleList, "auditUser", [])
      this.$set(this.roleList, "drawingUser", [])
    },
    memberReset(){
      this.memberForm = {
        developmentUser: "",
        auditUser: "",
        drawingUser: ""
      }
      this.developmentCheckList=[]
      this.auditCheckList=[]
      this.drawingCheckList=[]
      this.$nextTick(function() {
        this.$refs.memberForm.clearValidate();
      })
    },
    // 成员
    handelMember(row){
      this.projectId = row.id
      this.dialogFormVisible = true
      this.dialogStatus = 'member'
      this.memberReset()
      projectMember(row.id).then(res => {
        if(res.data.code == '100'){
          this.memberList = res.data.data
          this.$nextTick(function() {
            this.$refs.memberForm.clearValidate();
          })
          this.memberList.forEach(item => {
            item.children.forEach(itemTwo => {
              itemTwo.children.forEach(itemThree => {
                itemThree.children.forEach(itemFour => {
                  if(itemFour.defaultRoleCode == "developmentUser"){
                    if(itemFour.accessStatus == true){
                      this.developmentCheckList.push(itemFour.id)
                      this.memberForm.developmentUser = this.developmentCheckList.join()
                    }
                  }else if(itemFour.defaultRoleCode == "drawingUser") {
                    if(itemFour.accessStatus == true){
                      this.drawingCheckList.push(itemFour.id)
                      this.memberForm.drawingUser= this.drawingCheckList.join()
                    }
                  }else if(itemFour.defaultRoleCode == "auditUser") {
                    if(itemFour.accessStatus == true){
                      this.auditCheckList.push(itemFour.id)
                      this.memberForm.auditUser = this.auditCheckList.join()
                    }
                  }
                })
              })
            })
          })
        }
      })
    },
    checkList(data,checkList,formUser){
      if (data.length > 0) {
        for(var i = 0 ;i < data.length ;i++){
          if(data[i].children == undefined){
            checkList.push(data[i].id)
          }
        }
        formUser = checkList.join()
      } else {
        checkList = []
        formUser = ""
      }
    },
    developmentChange(click, checked){
      this.checkList(checked.checkedNodes,this.developmentCheckList,this.memberForm.developmentUser)
    },
    drawingChange(click, checked){
      this.checkList(checked.checkedNodes,this.drawingCheckList,this.memberForm.drawingUser)
    },
    auditChange(click, checked){
      this.checkList(checked.checkedNodes,this.auditCheckList,this.memberForm.auditUser)
    },
    updateRole(memberForm){
      if(this.memberForm.developmentUser == ''){
        handleAlert('error','开发人员不能为空')
        return false

      }else if(this.memberForm.auditUser == ''){
        handleAlert('error','审核人员不能为空')
        return false

      }else if(this.memberForm.drawingUser == '') {
        handleAlert('error','制图人员不能为空')
        return false
      }
      this.$refs[memberForm].validate((valid) => {
        if(valid){
          var params={
            id: this.projectId,
            developmentUser: this.memberForm.developmentUser,
            auditUser: this.memberForm.auditUser,
            drawingUser: this.memberForm.drawingUser
          }
          memberUpdate(params).then(res => {
            if(res.data.code == "100"){
              handleAlert('success','提交成功')
              this.dialogFormVisible = false
            }else {
              handleAlert('error','提交失败')
            }
          })
        }else{
          handleAlert('error','请完善手册信息')
        }
      })
    },
    resetCode(){
      this.roleState()
    },
    // 删除
    handelDelete (row) {
      var _this=this
      this.$confirm('确定删除【' + row.name + '】的项目信息?', '删除项目', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        projectDel(row.id).then(res => {
          if (res.data.code === 100) {
            handleAlert('success','删除成功')
            if(this.resultList!=null&&this.resultList.length==1){
              this.currentPage =this.currentPage-1
            }
            this.dataList()
            projectDate()
          }else{
            handleAlert('error','删除失败')
          }
        })
      }).catch((error)=>{
        handleAlert('info','取消删除')
      })
    },
    // 重置
    reset (formInline) {
      if (this.$refs[formInline].resetFields() !== undefined) {
        this.$refs[formInline].resetFields()
      }
      this.formInline.begintime = null
      this.formInline.endtime = null
      this.currentPage = 1
      this.dataList()
    },
  },
  mounted () {
    this.dataList()
    this.getUserList()
    this.getFirmList()
  }
}
</script>
