<template>
  <div class="layoutContainer">
    <div class="infoDetail taskContent">
      <el-row>
        <el-col :span="5" class="leftData">
          <div>
            <div class="topButton">
              <span style="height:42px;line-height:42px;margin-left: 10px;">手册项目</span>
            </div>
            <div class="scrollClass taskdetail">
              <el-scrollbar>
                <el-tree ref="tree" node-key="id" :data="listdata" :default-expanded-keys="treeExpandIdList" :props="defaultProps" @node-click="handleNodeClick">
                  <span slot-scope="{ node, data }" class="custom-tree-node" @mouseenter="mouseenter(data)" @mouseleave="mouseleave(data)">
                    <span>{{data.nameCh}}</span>
                    <span v-show="data.isCurrent" class="attribute" @click="attributeClick(data)">
                      <i class="el-icon-more" style="transform: rotate(90deg);" title="属性"></i>
                    </span>
                  </span>
                </el-tree>
              </el-scrollbar>
            </div>
          </div>
        </el-col>
        <el-col :span="10" class="leftData manualTask">
          <div>
            <div class="taskCenter">
              <div style="margin-left: 10px;height:42px;line-height:42px">
                <el-checkbox style="margin-right: 10px;" v-model="checkAll" @change="checkAllClick">全选</el-checkbox>
                <el-button v-if="hasPerm('menuAsimss3A4B_113')" style="color:#333" type="text" icon="power-icon" @click="authorityClick()">分配任务</el-button>
              </div>
            </div>
            <div class="taskManualList">
              <el-scrollbar>
                <el-tree
                  show-checkbox
                  :data="manualdata"
                  node-key="id"
                  ref="manualTree"
                  :props="defaultProp"
                  :default-expanded-keys="nodeKeyList"
                  :render-content="renderContent"
                  @node-click="handleManualClick"
                  @check="(click, checked)=>{checkAllChange(click, checked)}"
                >
                </el-tree>
              </el-scrollbar>
            </div>
          </div>
        </el-col>
        <el-col :span="9" class="fromRight">
          <div class="rightTitle">
            <span>信息结构属性</span>
          </div>
          <div class="detailInfo">
            <div class="brandArea">
              <div v-if="brandList.nameCh!==''">
                <span><span>名称</span>：</span>
                <span>{{brandList.nameCh}}</span>
              </div>
              <div v-if="brandList.nameEn!==''">
                <span><span>英文名</span>：</span>
                <span>{{brandList.nameEn}}</span>
              </div>
            </div>
            <!-- 项目属性 -->
            <div class="projectArea">
              <div v-if="projectList.name!==''">
                <span><span>项目名称</span>：</span>
                <span>{{projectList.name}}</span>
              </div>
              <div v-if="projectList.principalName!==''">
                <span><span>负责人</span>：</span>
                <span>{{projectList.principalName}}</span>
              </div>
              <div v-if="projectList.projectStatus!==''">
                <span><span>项目状态</span>：</span>
                <span v-if="projectList.projectStatus === 1">进行中</span>
                <span v-if="projectList.projectStatus === 2">暂停中</span>
                <span v-if="projectList.projectStatus === 3">已验收</span>
              </div>
              <div v-if="projectList.trainCode!==''">
                <span><span>车型</span>：</span>
                <span>{{projectList.trainCode}}</span>
              </div>
              <div v-if="projectList.trainYear!==''">
                <span><span>年款</span>：</span>
                <span>{{projectList.trainYear}}</span>
              </div>
              <div v-if="projectList.establishTime!==''">
                <span><span>立项时间</span>：</span>
                <span>{{projectList.establishTime | conversion("yyyy-MM-dd")}}</span>
              </div>
              <div v-if="projectList.deadlineTime!==''">
                <span><span>截止时间</span>：</span>
                <span>{{projectList.deadlineTime | conversion("yyyy-MM-dd")}}</span>
              </div>
              <div v-if="projectList.riskStatus!==''">
                <span><span>风险状态</span>：</span>
                <span v-if="projectList.riskStatus === 1">无风险</span>
                <span v-if="projectList.riskStatus === 2">低风险</span>
                <span v-if="projectList.riskStatus === 3">中风险</span>
                <span v-if="projectList.riskStatus === 4">高风险</span>
              </div>
            </div>
            <!-- 手册属性 -->
            <div class="manualArea">
              <div v-if="manualList.cnName!==''">
                <span><span>手册分类</span>：</span>
                <span>{{manualList.cnName}}</span>
              </div>
              <div v-if="manualList.principalName!==''">
                <span><span>负责人</span>：</span>
                <span>{{manualList.principalName}}</span>
              </div>
              <div v-if="manualList.developmentUser!==''">
                <span><span>开发人员</span>：</span>
                <span>{{manualList.developmentUser}}</span>
              </div>
              <div v-if="manualList.drawingUser!==''">
                <span><span>制图人员</span>：</span>
                <span>{{manualList.drawingUser}}</span>
              </div>
              <div v-if="manualList.auditUser!==''">
                <span><span>审核人员</span>：</span>
                <span>{{manualList.auditUser}}</span>
              </div>
              <div v-if="manualList.deadlineTime!==''">
                <span><span>截止时间</span>：</span>
                <span>{{manualList.deadlineTime | conversion("yyyy-MM-dd")}}</span>
              </div>
              <div v-if="manualList.manualStatus!==''">
                <span><span>开发状态</span>：</span>
                <span v-if="manualList.manualStatus === 1">进行中</span>
                <span v-if="manualList.manualStatus === 2">暂停中</span>
                <span v-if="manualList.manualStatus === 3">已验收</span>
              </div>
              <div v-if="manualList.riskStatus!==''">
                <span><span>风险状态</span>：</span>
                <span v-if="manualList.riskStatus === 1">无风险</span>
                <span v-if="manualList.riskStatus === 2">低风险</span>
                <span v-if="manualList.riskStatus === 3">中风险</span>
                <span v-if="manualList.riskStatus === 4">高风险</span>
              </div>
            </div>
            <!-- 目录属性 -->
            <div class="catalogArea">
              <div v-if="catalogList.code!==''">
                <span><span>编码</span>：</span>
                <span>{{catalogList.code}}</span>
              </div>
              <div v-if="catalogList.cnName!==''">
                <span><span>名称</span>：</span>
                <span>{{catalogList.cnName}}</span>
              </div>
              <div v-if="catalogList.taskUserName!==''">
                <span><span>开发人员</span>：</span>
                <span>{{catalogList.taskUserName}}</span>
              </div>
              <div v-if="catalogList.level!==''">
                <span><span>层级</span>：</span>
                <span>{{catalogList.level}}</span>
              </div>
              <div v-if="catalogList.isLeaf!==''">
                <span><span>是否Topic节点</span>：</span>
                <span v-if="catalogList.isLeaf == '1'">是</span>
                <span v-if="catalogList.isLeaf == '0'">否</span>
              </div>
              <div v-if="catalogList.sort!==''">
                <span><span>排序</span>：</span>
                <span>{{catalogList.sort}}</span>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
      <el-dialog v-dialogDrag :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
        <el-form v-if="dialogStatus == 'power'" class="powerDetail" ref='auditForm' :model="auditForm"  :rules="rules">
          <el-form-item v-for="(item, index) in personnelList" prop="developmentUser" :label="item.alias" :key="index">
            <el-checkbox-group v-model="auditForm.developmentUser">
              <el-checkbox v-for="(itemTwo, indexTwo) in item.children" :key="indexTwo" :label="itemTwo.id">
                {{itemTwo.name}}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <div class="submitArea">
            <el-button type="primary" @click="editClick('auditForm')">
              立即提交
            </el-button>
            <el-button @click="dialogFormVisible= false">
              取消
            </el-button>
          </div>
        </el-form>
        <el-form v-if="dialogStatus == 'attr'" ref='trainTemp' :model="trainTemp">
          <el-form-item label="主机厂" prop="brand" :label-width="formLabelWidth">
            <el-input v-model="trainTemp.brand" readonly></el-input>
          </el-form-item>
          <el-form-item label="项目名称" prop="nameCh" :label-width="formLabelWidth">
            <el-input v-model="trainTemp.nameCh" readonly></el-input>
          </el-form-item>
          <el-form-item label="项目状态" prop="projectStatus" :label-width="formLabelWidth">
            <el-input v-if="trainTemp.projectStatus == '1'" value="进行中" readonly></el-input>
            <el-input v-if="trainTemp.projectStatus == '2'" value="暂停中" readonly></el-input>
            <el-input v-if="trainTemp.projectStatus == '3'" value="已验收" readonly></el-input>
          </el-form-item>
          <el-form-item label="车型" prop="trainCode" :label-width="formLabelWidth">
            <el-input v-model="trainTemp.trainCode" readonly></el-input>
          </el-form-item>
          <el-form-item label="年款" prop="trainYear" :label-width="formLabelWidth">
            <el-input v-model="trainTemp.trainYear" readonly></el-input>
          </el-form-item>
        </el-form>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { contentSize, handleAlert, expandTree } from '@/assets/js/common.js'
import { taskCatalogue, taskManualData, assignTasks, tasksUser } from '@/api/cmsmgt.js'
export default {
  name: "cmsmgttasklist",
  data(){
    return {
      auditForm:{
        developmentUser:[],
      },
      defaultProps:{
        children: 'children',
        label: 'nameCh',
      },
      defaultProp:{
        children: 'children',
        label: 'cnName',
      },
      treeExpandIdList:[],
      formLabelWidth: "100px",
      dialogFormVisible: false,
      trainTemp: {
        id:"",
        frimId: "",
        brand: "",
        nameCh: "",
        projectStatus:'',
        trainCode:"",
        trainYear: "",
      },
      nodeKeyList:[],
      manualId:"",
      manualVersion:"",
      checkId:"",
      listdata: [],
      manualdata: [],
      personnelList:[],
      checkAll:false,
      dialogStatus: "",
      textMap:{
        power: "分配任务",
        attr: "项目属性"
      },
      brandList:{
        nameCh:"",
        nameEn:""
      },
      projectList:{
        name:"",
        principalName:"",
        projectStatus:"",
        trainCode:"",
        trainYear:"",
        establishTime:"",
        deadlineTime:"",
        riskStatus:"",
      },
      manualList:{
        cnName:"",
        principalName:"",
        manualStatus:"",
        deadlineTime:"",
        riskStatus:"",
        auditUser:"",
        developmentUser:"",
        drawingUser:"",
      },
      catalogList:{
        code:"",
        cnName:"",
        level:"",
        isLeaf:"",
        sort:"",
        taskUserName:""
      },
      rules: {
        developmentUser: [{ type: 'array',required: true, message: '开发人员不能为空', trigger: ['blur', 'change'] }],
      }
    }
  },
  methods: {
    mouseenter(data) {
      data.isCurrent = true
    },
    mouseleave(data) {
      data.isCurrent = false
    },
    attributeClick(data){
      event.stopPropagation();
      this.trainDetail(data);
      this.dialogFormVisible = true
      this.dialogStatus = 'attr'
      this.$nextTick(function() {
        this.$refs.trainTemp.clearValidate();
      })
    },
    trainDetail(data){
      this.trainTemp.id = data.id
      this.trainTemp.frimId= data.frimId
      this.trainTemp.brand= data.brand
      this.trainTemp.nameCh= data.nameCh
      this.trainTemp.projectStatus= data.projectStatus
      this.trainTemp.trainCode= data.trainCode
      this.trainTemp.trainYear= data.trainYear
    },
    // 目录
    dataList () {
      taskCatalogue().then(res => {
        this.listdata = res.data.data
        this.expandStatus(this.listdata)
      })
    },
    expandStatus(data){
      var nodeExpand = expandTree(data)
      this.treeExpandIdList.push(nodeExpand.id)
      this.handleNodeClick(nodeExpand)
      this.$nextTick(() => {
        this.$refs.tree.setCurrentKey(nodeExpand.id);
      });
    },
    // 手册项目
    manualDetail(manualId, manualVersion){
      var params = {
        manualId:manualId,
        manualVersion:manualVersion
      }
      taskManualData(params).then((res) => {
        this.manualdata = res.data.data[0].children
      })
    },
    handleNodeClick(data){
      this.checkAll = false
      if(data.type == "firm"){
        this.manualdata=[]
        // $(".taskContent .taskCenter").hide()
        $(".manualTask").hide()
        $(".taskContent .fromRight").css("width", "79%")
        $(".detailInfo .brandArea").show()
        $(".detailInfo .projectArea,.detailInfo .manualArea,.detailInfo .catalogArea").hide()
        this.brandList = Object.assign({}, data)
      } else if(data.type == 'project') {
        this.manualdata=[]
        // $(".taskContent .taskCenter").hide()
        $(".manualTask").hide()
        $(".taskContent .fromRight").css("width", "79%")
        $(".detailInfo .projectArea").show()
        $(".detailInfo .brandArea,.detailInfo .manualArea,.detailInfo .catalogArea").hide()
        this.projectList = Object.assign({}, data)
      } else if(data.type == 'manual') {
        this.manualDetail(data.id, data.manualVersion)
        this.manualId = data.id
        this.manualVersion = data.manualVersion
        this.userData(data.id)
        $(".manualTask").show()
        $(".taskContent .fromRight").removeAttr("style")
        $(".detailInfo .manualArea").show()
        $(".detailInfo .brandArea,.detailInfo .projectArea,.detailInfo .catalogArea").hide()
        this.manualList = Object.assign({}, data)
      }
      this.areaSize();
    },
    // 手册数据
    renderContent(h, { node, data, store }) {
      let isLeaf = data.children.length;
      let vStatus= data.auditStatus == null ? 0:data.auditStatus;
      let style=''
      if(isLeaf > 0){
        style=''
      }else {if(vStatus==1){
        style='green-category'
        } else {
          style=''
        }
      }
      return(<span class={style} style="font-size:15px;cursor:pointer">{node.label}</span>)
    },
    handleManualClick(data) {
      $(".detailInfo .catalogArea").show()
      $(".detailInfo .brandArea,.detailInfo .projectArea,.detailInfo .manualArea").hide()
      this.catalogList = Object.assign({}, data)
      if(data.taskUserName == null){
        this.catalogList.taskUserName = ""
      }else {
        this.catalogList.taskUserName=data.taskUserName
      }
    },
    // 全选
    checkAllClick(){
      if(this.checkAll) {
        this.$refs.manualTree.setCheckedNodes(this.manualdata);
      } else {
        this.$refs.manualTree.setCheckedNodes([]);
      }
    },
    checkAllChange(click, checked) {
      this.checkId = click.pid
      let lengthVal = 0
      this.manualdata.forEach(item => {
        if(item.children.length > 0){
          lengthVal += item.children.length
          item.children.forEach(itemTwo => {
            if(itemTwo.children.length > 0){
              lengthVal += itemTwo.children.length
              itemTwo.children.forEach(itemThree => {
                if(itemThree.children.length >0){
                  lengthVal += itemThree.children.length
                  itemThree.children.forEach(itemFour => {
                    if(itemFour.children.length >0){
                      lengthVal += itemFour.children.length
                      itemFour.children.forEach(itemFive => {
                        if(itemFive.children.length > 0){
                          lengthVal += itemFive.children.length
                        }
                      })
                    }
                  })
                }
              })
            }
          })
        }
      })
      let lengthTotal = this.manualdata.length + lengthVal
      if(checked.checkedNodes.length == lengthTotal){
        this.checkAll=true
      } else {
        this.checkAll=false
      }
    },
    // 权限
    authorityClick(){
      if(this.personnelList[0].children.length > 0){
        this.dialogFormVisible = true
        this.dialogStatus = 'power'
        this.nodeKeyList = []
        this.auditForm.developmentUser = []
        this.$nextTick(function() {
          this.$refs.auditForm.clearValidate();
        })
      }else{
        handleAlert('error',"暂无开发人员")
      }
    },
    userData(id){
      tasksUser(id).then(res => {
        this.personnelList = res.data.data
      })
    },
    // 提交
    editClick(auditForm){
      if(this.$refs.manualTree.getCheckedKeys() == ''){
        handleAlert('error',"手册目录节点不能为空")
        return false
      }
      this.$refs[auditForm].validate((valid) => {
        if(valid){
          var params = new URLSearchParams()
          params.append('directoryIds', this.$refs.manualTree.getCheckedKeys())
          params.append('userIds', this.auditForm.developmentUser.toString())
          assignTasks(params).then(res=>{
            if(res.data.code == 100){
              handleAlert('success',"提交成功")
              this.manualDetail(this.manualId, this.manualVersion)
              this.nodeKeyList.push(this.checkId)
              this.dialogFormVisible = false
            } else {
              handleAlert('error',"提交失败")
            }
          })
        }else{
          handleAlert('error','请完善信息')
        }
      })
    },
    areaSize(){
      setTimeout(() => {
        let butHeight = ""
        if($(".taskCenter").css("display") == "none"){
          butHeight = '0'
        }else{
          butHeight = $('.taskCenter').outerHeight(true)
        }
        let distance = $('.leftData').outerHeight(true) - butHeight;
        $(".taskManualList").css('height', distance)
        window.addEventListener("resize", function(){
          let butHeight = ""
          if($(".taskCenter").css("display") == "none"){
            butHeight = '0'
          }else{
            butHeight = $('.taskCenter').outerHeight(true)
          }
          let distance = $('.leftData').outerHeight(true) - butHeight;
          $(".taskManualList").css('height', distance)
        })
      })
    },
  },
  mounted(){
    contentSize();
    this.dataList();
    this.areaSize();
  },
}
</script>
<style>
  .taskContent .manualTask {
    /* display: none; */
    /* visibility: hidden; */
  }
  .detailInfo {
    padding: 10px 15px;
    font-size: 15px;
    height: calc(100% - 72px);
    overflow-y: auto
  }
  .detailInfo div{
    margin-bottom: 15px;
    display: flex;
  }
  .detailInfo div>span:first-child {
    color:#bababa;
    margin-right: 10px;
  }
  .detailInfo div>span:first-child>span{
    display: inline-block;
    width: 100px;
    white-space: nowrap;
    text-align-last: justify;
    text-align: justify;
  }
  .detailInfo div>span:last-child {
    display: inline-block;
    flex: 1;
    color: #000;
  }
  .taskdetail .el-tree>.el-tree-node>.el-tree-node__children>.el-tree-node>.el-tree-node__content>.custom-tree-node ,
  .el-tree>.el-tree-node>.el-tree-node__children>.el-tree-node>.el-tree-node__content>.custom-tree-node,
  .taskdetail .el-tree>.el-tree-node>.el-tree-node__children>.el-tree-node .el-tree-node>.el-tree-node__content>.custom-tree-node {
    text-decoration: underline
  }
  .el-checkbox__inner {
    width: 16px;
    height: 16px;
  }
  .taskContent .el-dialog .el-tree .el-tree-node .el-checkbox .el-checkbox__inner{
    display: inline-block;
  }
  .taskContent .el-dialog .el-tree .el-tree-node__content {
    margin: 6px 0;
  }
  .taskContent .powerDetail {
    overflow: auto;
  }
  .manualList{
    overflow-x: hidden;
    overflow-y: auto;
  }
  .detailInfo .brandArea,.detailInfo .projectArea,
  .detailInfo .manualArea,.detailInfo .catalogArea{
    display: none
  }
</style>
