<template>
  <div class="layoutContainer">
    <div class="outlineNav">
      <div class="typeManual">
        <div class="manualList">
          <div class="listType">
            <div v-for="(item,index) of manualNameList" :key="index" v-show="index < muanalNum" @click="typeChanged(index, item)" :class="'navtitle ' + index">
              {{ item.cnName }}
            </div>
          </div>
        </div>
        <div class="moreDetail" v-show="manualNameList.length > muanalNum">
          <div>
            <span @click="moreclick()">
              更多
              <i class="el-icon-arrow-down"></i>
            </span>
            <div class="detailList" @mouseleave="leave()">
              <div v-for="(item,index) of manualNameList" :key="index" v-show="index > (muanalNum - 1) " @click="typeChanged(index, item)">
                {{ item.cnName }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="navRight">
        <el-button type="text" v-if="hasPerm('menuAsimss3A5B_101')" icon="el-icon-plus" @click="manualAdd()">新增</el-button>
        <el-button type="text" v-if="hasPerm('menuAsimss3A5B_102')" icon="el-icon-delete" @click="manualDel()">删除</el-button>
        <el-select v-model="firmShowId" @change="firmChanged" clearable filterable>
          <el-option v-for="(item, index) of firmList" :key="index" :label="item.nameCh" :value="item.id"></el-option>
        </el-select>
      </div>
    </div>
    <div class="infoDetail" style="height: calc(100% - 55px);">
      <el-row>
        <el-col :span="7" class="leftData">
          <div>
            <div class="topButton" v-if="hasPerm('menuAsimss3A5B_101') || hasPerm('menuAsimss3A5B_102') || hasPerm('menuAsimss3A5B_107')">
              <el-button type="text" v-if="hasPerm('menuAsimss3A5B_101')" icon="el-icon-plus" @click="addNodeClick">新增</el-button>
              <el-button type="text" v-if="hasPerm('menuAsimss3A5B_102')" icon="el-icon-delete" @click="delNodeClicik">删除</el-button>
              <el-upload
                class="upload-demo inline-block"
                ref="elUpload"
                action="#"
                :show-file-list="false"
                multiple
                :before-upload="onBeforeUpload"
                :http-request="uploadFile"
                accept="ditamap"
              >
                <el-button type="text" v-if="hasPerm('menuAsimss3A5B_107')" icon="import-icon">导入</el-button>
                <el-progress v-show="progressState"  class="imgProgress" :percentage="progressPercent" />
              </el-upload>
            </div>
            <div class="scrollClass">
              <el-scrollbar>
                <el-tree
                  ref="tree"
                  node-key="id"
                  :default-expanded-keys="nodeKeyList"
                  :data="manualCatalogData"
                  :props="defaultProps"
                  @node-click="handleNodeClick"
                  highlight-current
                >
                </el-tree>
              </el-scrollbar>
            </div>
          </div>
        </el-col>
        <el-col :span="17" class="fromRight">
          <div class="formTitle">{{editTitle}}</div>
          <el-form ref="form" :model="form" :rules="newForm">
            <el-form-item label="目录编码" prop="code" :label-width="formLabelWidth">
              <el-input v-model.trim='form.code' placeholder="请输入目录编码" show-word-limit maxlength="50"></el-input>
            </el-form-item>
            <el-form-item label="中文名称" prop="cnName" :label-width="formLabelWidth">
              <el-input v-model.trim="form.cnName" placeholder="请输入中文名称" show-word-limit maxlength="50"></el-input>
            </el-form-item>
            <el-form-item label="Topic节点" prop="isLeaf" :label-width="formLabelWidth">
              <el-radio-group  v-model="form.isLeaf">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="排序" prop="sort" :label-width="formLabelWidth">
              <el-input type="number" min="1" oninput="value=value.replace(/[^\d]/g, '')" v-model.trim="form.sort"></el-input>
            </el-form-item>
            <el-form-item label="备注" prop="remark" :label-width="formLabelWidth">
              <el-input v-model.trim="form.remark" placeholder="请输入备注" show-word-limit maxlength="100"></el-input>
            </el-form-item>
            <el-form-item class="butArea">
              <el-button v-show="butType===''" @click="updateNode()">保存</el-button>
              <el-button v-show="butType==='addNodeBut'" @click="nodeConfirm('form')">确定</el-button>
              <el-button v-show="butType==='addNodeBut'" @click="resetForm('form')">重置</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>
    <el-dialog v-dialogDrag title="手册新增" :visible.sync="dialogFormVisible">
      <el-form ref='temp' :rules="fromTemp" :model="temp">
        <el-form-item label="手册名称" prop="cnName" :label-width="formLabelWidth">
          <el-select v-model="temp.cnName" clearable filterable>
            <el-option v-for="(item, index) of manualTypeList" :key="index" :label="item.name" :value="item.name"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirmClick('temp')">确 定</el-button>
        <el-button @click="manualreset('temp')">重置</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { contentSize,handleAlert } from '@/assets/js/common.js'
import { syllabusFirm, syllabusCatalog, syllabusTreeList, syllabusFile, syllabusNodeDel,
syllabusManualAdd,syllabusNodeAdd, syllabusManualDel,syllabusType,syllabusUpdate } from '@/api/cmsmgt.js'
export default {
  name: 'cmsmgtsyllabuslist',
  data() {
    return {
      progressState:false,
      progressPercent: 0,
      muanalNum:"",
      firmShowId: "",
      editTitle: "当前目录节点信息",
      firmList: [], //公司名称列表
      firmPid: "",
      butType: "",
      manualNameList: [], //手册名称
      manualTypeId:"",
      manualTypeVal:"",
      manualId: 0,
      // 左
      defaultProps: {
        children: 'children',
        label: 'cnName'
      },
      nodeKeyList:[],
      manualCatalogData: [],
      manualTypeCode:"",
      manualTypeList:[],
      activeState: true,
      // 右
      // 当前信息
      form: {
        id: 0,
        pid: 0,
        type: '',
        code: '',
        cnName: '',
        isLeaf: '',
        sort: 1,
        remark: '',
        topicType: '', // 主题类型
        length: 0
      },
      catalogueLevel:"",
      // 弹框
      dialogFormVisible: false,
      formLabelWidth: '100px',
      temp: {
        cnName: "",
      },
      fromTemp: {
        cnName: [{ required: true, message: '手册名称不能为空', trigger: ['blur', 'change'] }],
      },
      newForm:{
        code: [{ required: true, message: '目录编码不能为空', trigger: ['blur', 'change'] }],
        cnName: [{ required: true, message: '中文名称不能为空', trigger: ['blur', 'change'] }],
        isLeaf: [{ required: true, message: '请选择Topic节点', trigger: ['blur', 'change'] }],
      }
    }
  },
  methods: {
    // 大纲数据(公司列表)
    firmData(){
      // 公司列表
      syllabusFirm().then(res => {
        this.firmList = res.data.data
        if(this.firmShowId == ''){
          this.activeState = true
          this.firmShowId = this.firmList[0].id
          this.temp.firmName = this.firmList[0].nameCh
        }else {
          this.activeState=false
        }
        this.manualContent(this.firmShowId)
      })
    },
    manualTypeData(){
      syllabusType().then(res => {
        this.manualTypeList = res.data.data
      })
    },
    // 更改公司
    firmChanged(value, label) {
      if(value!=''){
        this.formClear()
        this.resetTemp()
        this.activeState = true
        this.editTitle = "当前目录节点信息"
        this.nodeKeyList = []
        this.butType=''
        this.firmShowId = value
        for (let i = 0; i < this.firmList.length; i++) {
          if (this.firmShowId == this.firmList[i].id) {
            this.firmPid = this.firmList[i].pid
            this.temp.firmName = this.firmList[i].nameCh
            break
          }
        }
        $(".navtitle").removeClass("navActive");
        this.manualContent(value);
      }
    },
    // 手册目录
    manualTreeData(id){
      var params = id;
      syllabusCatalog(params).then(res => {
        this.manualCatalogData = res.data.data
        this.nodeKeyList.push(res.data.data[0].id)
        this.manualTypeCode = res.data.data[0].manualType
        this.$nextTick(() => {
          this.$refs.tree.setCurrentKey(this.form.id);
        });
      })
    },
    // 手册内容数据
    manualContent(firmId) {
      // 获取手册名称
      var params = {
        firmId: firmId
      }
      syllabusTreeList(params).then(res => {
        this.manualNameList = res.data.data
        if(this.manualNameList.length > 0) {
          this.manualTypeId = this.manualNameList[0].id
          this.manualTypeVal = this.manualNameList[0].manualType
          this.manualTreeData(this.manualNameList[0].id)
        } else {
          this.manualCatalogData = []
          this.manualTypeCode = ''
        }
        setTimeout(() => {
          if(this.activeState == true){
            $(".navtitle.0").addClass("navActive");
          }
          this.areaSize();
        })
      })
    },
    typeChanged(index, item) {
      this.nodeKeyList = []
      this.activeState = false
      this.manualTypeId = item.id
      this.manualTypeVal = item.manualType
      let numVal = this.muanalNum -1
      $(".navtitle").removeClass("navActive");
      this.formClear()
      this.resetTemp()
      if(index > numVal){
        let temp = this.manualNameList[index]
        this.manualNameList[index] = this.manualNameList[numVal]
        this.manualNameList[numVal] = temp
        $(".navtitle." + numVal).addClass("navActive");
      } else {
        $(".navtitle." + index).addClass("navActive");
      }
      this.manualTreeData(item.id)
      $(".moreDetail>div span i").removeClass("el-icon-arrow-up").addClass("el-icon-arrow-down")
    },
    moreclick() {
      event.stopPropagation();
      var dispalyVal = $(".moreDetail>div>div").css("display")
      if(dispalyVal == "none"){
        $(".moreDetail>div>div").show()
        $(".moreDetail>div span i").addClass("el-icon-arrow-up").removeClass("el-icon-arrow-down")
      }else{
        $(".moreDetail>div>div").hide()
        $(".moreDetail>div span i").removeClass("el-icon-arrow-up").addClass("el-icon-arrow-down")
      }
    },
    leave(){
      $(".moreDetail>div>div").hide()
      $(".moreDetail>div span i").removeClass("el-icon-arrow-up").addClass("el-icon-arrow-down")
    },
    formClear(){
      this.form= {
        id: 0,
        pid: 0,
        type: '',
        code: '',
        cnName: '',
        isLeaf: '',
        sort: 1,
        remark: '',
        topicType: '', // 主题类型
        length: 0
      },
      this.$nextTick(function() {
        this.$refs.form.clearValidate();
      })
    },
    // 手册新增
    manualAdd(){
      this.dialogFormVisible=true
      this.resetTemp()
    },
    // 确定
    confirmClick(temp) {
      this.$refs[temp].validate((valid) => {
        if(valid){
          let manualType = ""
          var params = new URLSearchParams()
          params.append('firmId', this.firmShowId)
          params.append('cnName', this.temp.cnName)
          this.manualTypeList.forEach((item) => {
            if(item.name == this.temp.cnName){
              manualType = item.code
              params.append('manualType', manualType)
              syllabusManualAdd(params).then(res => {
                if (res.data.code == 100){
                  this.dialogFormVisible = false
                  handleAlert('success', res.data.msg)
                  this.firmData()
                  this.resetTemp()
                } else if(res.data.code == 101) {
                  handleAlert('error', res.data.msg)
                } else {
                  handleAlert('error', res.data.msg)
                }
              })
            }
          })
        }
      })
    },
    // 重置
    resetForm(){
      this.formClear()
    },
    resetTemp(){
      this.temp ={
        cnName: "",
      }
    },
    manualreset(temp) {
      this.resetTemp()
    },
    // 手册删除
    manualDel(){
      if(this.manualTypeId == ""){
        handleAlert('error','请选中需要删除的手册名称')
        return false;
      } else {
        let curId= this.manualTypeId
        var name = $(".navActive").text()
        this.$confirm('确定删除【'+ name+ '】的相关信息?', '删除手册', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          syllabusManualDel(curId).then(res => {
            if(res.data.code==100){
              handleAlert('success','删除成功')
              this.firmData()
            }else{
              handleAlert('error',res.data.msg)
            }
          })
        }).catch((error)=>{
          handleAlert('info','取消删除')
        })
      }
    },
    // 左
    assignment(data){
      this.butType = ''
      this.editTitle = "当前目录节点信息"
      this.form.id = data.id
      this.form.pid = data.pid
      this.form.type = data.type
      this.form.code = data.code
      this.form.cnName = data.cnName
      this.catalogueLevel = data.level
      this.form.isLeaf = data.isLeaf
      this.form.sort = data.sort
      this.form.remark = data.remark
      if (data.children != null) {
        this.form.length = data.children.length
      }
    },
    handleNodeClick(data){
      this.resetTemp()
      this.nodeKeyList = []
      this.assignment(data)
    },
    addNodeClick(){
      if(this.form.id == 0){
        handleAlert('error','请选择目录节点')
        return false
      }
      this.editTitle = "添加节点信息"
      this.butType='addNodeBut'
      this.catalogueLevel = this.catalogueLevel + 1
      this.formClear()
      this.resetTemp()
    },
    nodeConfirm(form){
      this.$refs[form].validate((valid) => {
        if(valid){
          this.nodeKeyList = []
          var params = new URLSearchParams()
          if(this.form.pid == 0) {
            params.append('pid', this.manualTypeId)
          } else {
            params.append('pid', this.form.id)
          }
          params.append('firmId', this.firmShowId)
          params.append('code', this.form.code)
          params.append('cnName', this.form.cnName)
          params.append('manualType', this.manualTypeCode)
          params.append('level', this.catalogueLevel)
          params.append('isLeaf', this.form.isLeaf)
          params.append('sort', this.form.sort)
          syllabusNodeAdd(params).then(res => {
            if(res.data.code == "100"){
              handleAlert('success',res.data.msg)
              this.manualTreeData(this.manualTypeId)
              this.nodeKeyList.push(this.form.id)
              this.butType=''
              this.editTitle = "当前目录节点信息"
              this.formClear()
            } else {
              handleAlert('error',res.data.msg)
            }
          })
        }else{
          handleAlert('error','请完善信息')
        }
      })
    },
    delNodeClicik(){
      this.nodeKeyList = []
      if(this.form.pid == 0) {
        handleAlert('error','目录根节点无法删除')
        return
      }
      if(this.form.length > 0){
        handleAlert('error','有子节点无法删除')
        return
      }
      if(this.form.id == 0) {
        handleAlert('error','请选中需要删除的目录节点')
        return false;
      }
      var pid = this.form.pid
      var id = this.form.id
      let nodeName= this.form.cnName
      this.$confirm('确定删除【'+ nodeName + '】的目录节点信息?', '删除目录节点', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        syllabusNodeDel(id).then(res => {
          if(res.data.code==100){
            handleAlert('success','删除成功')
            this.manualTreeData(this.manualTypeId)
            this.nodeKeyList.push(pid)
            this.formClear()
          }else{
            handleAlert('error',res.data.msg)
          }
        })
      }).catch((error)=>{
        handleAlert('info','取消删除')
      })
    },
    updateNode(){
      var params = new URLSearchParams()
      params.append('id', this.form.id)
      params.append('code', this.form.code)
      params.append('type', this.form.type)
      params.append('cnName', this.form.cnName)
      params.append('remark', this.form.remark)
      params.append('isLeaf', this.form.isLeaf)
      params.append('sort', this.form.sort)
      // 确认修改
      syllabusUpdate(params).then(res => {
        if (res.data.code === 100) {
          handleAlert('success','保存成功')
          this.manualTreeData(this.manualTypeId)
          this.nodeKeyList.push(this.form.id)
        } else {
          handleAlert('error',res.data.msg)
        }
      })
    },
    // 附件上传
    onBeforeUpload(file) {
      var fileExt =file.name.substring(file.name.lastIndexOf('.')+1).toLowerCase();
      const docExt = fileExt === 'ditamap'
      const isLimit = file.size / 1024 / 1024 < 1024
      if(!docExt) {
        handleAlert('warning',"上传文件只能是 ditamap格式!")
        return false;
      }
      if (!isLimit) {
        handleAlert('warning',"上传文件大小不能超过 1GB!")
        return false;
      }
      return true;
    },
    initialState(){
      setTimeout(() => {
        this.progressPercent = 0
        this.progressState = false
      },100)
    },
    // 批量上传
    uploadFile (param) {
      var _this = this
      var handelProgress
      var formData = new FormData();
      formData.append('firmId', _this.firmShowId)
      formData.append('manualType', _this.manualTypeVal)
      formData.append('file', param.file);
      const uploadProgress = progressEvent => {
        _this.progressState = true
        handelProgress = setInterval(function(){
          if(_this.progressPercent > 96){
            return
          }
          _this.progressPercent += 1
        },120)
      }
      // 根节点编号
      syllabusFile(formData, uploadProgress).then(res => {
        if (res.data.code === 100) {
          _this.progressPercent = 100
          handleAlert('success','导入成功')
          _this.manualContent(firmId)
        }else{
          handleAlert('error',res.data.msg)
        }
        _this.initialState()
      }).catch(function(error){
        handleAlert('error','上传出现异常，稍后重试')
        _this.initialState()
      })
      clearInterval(handelProgress)
		},
    widthSize(){
      var navWidth = $(".outlineNav").outerWidth(true);
      var rightWidth = 400;
      var listwidth = $(".listType").outerWidth(true);
      var butWidth = $(".moreDetail").outerWidth(true);
      var leftArea = (navWidth - rightWidth)/1.1 - butWidth
      if($(window).width() < 1921 && $(window).width() > 1600){
        this.muanalNum = 8
      }else if($(window).width() < 1601  && $(window).width() > 1440){
        this.muanalNum = 6
      }else if($(window).width() < 1441 && $(window).width() > 1360){
        this.muanalNum = 5
      }else if($(window).width() < 1361 && $(window).width() > 1280){
        this.muanalNum = 4
      }else if($(window).width() < 1281 && $(window).width() > 1210){
        this.muanalNum = 4
      } else if($(window).width() < 1210 || $(window).width() > 1024){
        this.muanalNum = 3
      } else if($(window).width() < 1024 && $(window).width() == 1024){
        this.muanalNum = 2
      }
    },
    areaSize(){
      var _this = this
      _this.widthSize();
      window.addEventListener("resize",function(){
        _this.widthSize();
      })
    },
  },
  mounted() {
    this.firmData()
    this.manualTypeData()
    this.areaSize()
    contentSize();
  }
}
</script>
<style>
  .navActive {
    color: var(--theme-color);
    border-bottom:1px solid  var(--theme-color);
  }
  .outlineNav {
    height: 40px;
    line-height: 40px;
    margin:8px 0px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .typeManual{
    flex:1;
    margin-right: 10px;
  }
  .typeManual,
  .typeManual .listType {
    display: flex;
  }
  .typeManual .manualList {
    overflow: hidden;
  }
  .typeManual .listType{
    width: max-content;
  }
  .typeManual .listType div{
    padding: 0 25px;
    cursor: pointer;
    height: 40px;
    line-height: 40px;
    white-space: nowrap;
  }
  .moreDetail{
    height: 40px;
    line-height: 40px;
    margin-left: 10px;
  }
  .moreDetail>div{
    position: relative;
  }
  .moreDetail div span {
    height: 30px;
    line-height: 30px;
    background: #eee;
    padding: 0 10px;
    display: inline-block;
    border-radius: 5px;
    cursor: pointer;
    white-space: nowrap;
    font-size: 14px;
  }
  .moreDetail>div>div{
    display: none;
    position: absolute;
    top: 110%;
    z-index: 99;
    padding: 5px 0;
    background: #FFF;
    border-radius: 5px;
    box-shadow: 0 2px 12px 0 rgba(76,76,76,0.5);
  }
  .moreDetail>div>div div{
    height: 30px;
    line-height: 30px;
    white-space: nowrap;
    padding: 5px 20px;
  }
  .moreDetail>div>div div:hover{
    background: #e9f2fb;
  }
  .moreDetail>div span i {
    font-size: 14px;
    color:#000;
    margin-left: 0px;
  }
  .outlineNav .el-input__inner {
    height: 30px;
    line-height: 30px;
  }
  .outlineNav .el-input__icon {
    line-height: 30px;
  }
  .navRight {
    width: 400px;
    text-align: right;
  }
  .navRight .el-button {
    height: 30px;
    line-height: 30px;
    padding: 0
  }
  .navRight .el-select {
    margin-left: 20px;
    width: 200px;
  }
  .makerSelect{
    margin-bottom: 5px;
  }
  @media screen and (min-width:1440px) and (max-width:1366px){

  }
  @media screen and (min-width:1366px) and (max-width:1280px){
    .typeManual {
      font-size: 14px;
    }
    .typeManual .manualList div {
      padding: 0 15px;
    }
  }
  @media screen and (min-width:1280px) and (max-width:1024px){

  }
</style>
