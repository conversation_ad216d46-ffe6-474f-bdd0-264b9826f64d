<template>
  <div class="layoutContainer">
    <div class="infoDetail">
      <el-row>
        <el-col :span="9" class="leftData">
          <div>
            <div class="topButton" v-if="hasPerm('menuAsimss3A2B_101') || hasPerm('menuAsimss3A2B_102') || hasPerm('menuAsimss3A2B_107')">
              <el-button type="text" v-if="hasPerm('menuAsimss3A2B_101')" icon="el-icon-plus" @click="addNode">新增</el-button>
              <el-button type="text" v-if="hasPerm('menuAsimss3A2B_102')" icon="el-icon-delete" @click="delNode">删除</el-button>
              <el-upload
                class="upload-demo inline-block"
                ref="batchUpload"
                action="#"
                :show-file-list="false"
                multiple
                :before-upload="onBeforeUpload"
                :http-request="uploadFile"
                accept="ditamap"
              >
                <el-button type="text" v-if="hasPerm('menuAsimss3A2B_107')" icon="import-icon">导入</el-button>
                <el-progress v-if="showUploadProcess"  class="imgProgress" :percentage="percentage"/>
              </el-upload>
            </div>
            <div class="scrollClass">
              <el-scrollbar>
                <el-tree ref="tree" :data="listdata" :props="defaultProps"
                  node-key="id" :default-expanded-keys="nodeKeyList"
                  @node-click="handleNodeClick"  highlight-current>
                </el-tree>
              </el-scrollbar>
            </div>
          </div>
        </el-col>
        <el-col :span="15" class="fromRight">
          <div class="formTitle">{{editTitle}}</div>
          <el-form ref="form" :model="form" :rules="newForm" :label-width="formLabelWidth">
            <el-form-item label="目录编码" prop="code">
              <el-input v-model='form.code' placeholder="请输入目录编码" show-word-limit maxlength="50"></el-input>
            </el-form-item>
            <el-form-item label="中文名称" prop="cnName">
              <el-input v-model="form.cnName" placeholder="请输入中文名称" show-word-limit maxlength="50"></el-input>
            </el-form-item>
            <el-form-item label="Topic节点" prop="isLeaf">
              <el-radio-group v-model="form.isLeaf">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="排序" prop="sort">
              <el-input type="number" min="0" oninput="value=value.replace(/[^\d]/g, '')" v-model="form.sort"></el-input>
            </el-form-item>
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" placeholder="请输入备注" show-word-limit maxlength="100"></el-input>
            </el-form-item>
            <el-form-item class="butArea">
              <el-button v-show="butType==='addNodeBut'" @click="nodeConfirm('form')">确定</el-button>
              <el-button v-show="butType===''" @click="updateNode()">保存</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <!-- <el-dialog title="批量上传"  v-if="dialogStatus === 'upload'" :visible.sync="dialogFormVisible">
        <div class="upload-area">
          <el-upload
            class="upload-demo"
            ref="Upload"
            action="#"
            multiple
            :before-upload="onBeforeUpload"
            :http-request="uploadFile"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text" style="cursor: pointer;"><em>点击上传</em></div>
          </el-upload>
          <el-progress v-if="showUploadProcess" style="width:400px;margin:0 auto;height:10px" :percentage="percentage"/>
        </div>
      </el-dialog> -->
    </div>
  </div>
</template>
<script>
import { sysServerUrl,cmsServerUrl,contentSize,handleAlert } from '@/assets/js/common.js'
import { catalogData, catalogImport,catalogUpload, catalogAdd, catalogEdit, catalogDel, syllabusType } from '@/api/cmsmgt.js'
export default {
  name: 'manualCatalog',
  data () {
    return {
      butType: "",
      editTitle: "当前目录节点信息",
      defaultProps: {
        children: 'children',
        label: 'cnName'
      },
      nodeKeyList: [],
      form: {
        id: 0,
        pid: 0,
        type: '',
        code: '',
        cnName: '',
        isLeaf: '',
        sort: 1,
        remark: '',
      },
      catalogueLevel:"",
      listdata: [],
      formLabelWidth: '100px',
      childrenList: [],
      percentage: 0,
      showUploadProcess:false,
      manualId: 0,
      manualVersion: '',
      newForm:{
        code: [{ required: true, message: '目录编码不能为空', trigger: ['blur', 'change'] }],
        cnName: [{ required: true, message: '中文名称不能为空', trigger: ['blur', 'change'] }],
        isLeaf: [{ required: true, message: '请选择Topic节点', trigger: ['blur', 'change'] }],
      },
      manualTypeId:""
    }
  },
  methods: {
    dataList () {
      var params = new URLSearchParams()
      params.append('id', this.$route.params.id)
      params.append('manualVersion', this.$route.params.manualVersion)
      catalogData(params).then(res => {
        if(res.data.code==100){
          this.listdata = res.data.data
          this.manualTypeId = res.data.data[0].id
          this.nodeKeyList.push(res.data.data[0].id)
          this.$nextTick(() => {
            this.$refs.tree.setCurrentKey(this.form.id);
          });
        }
      })
    },
    // 文件上传之前
    onBeforeUpload(file){
      // 获取文件后缀
      var fileExt =file.name.substring(file.name.lastIndexOf('.')+1).toLowerCase();
      // 文件后缀是否是 zip
      const zipExt = fileExt === 'ditamap'
      // 文件大小不能超过1G
      const isLimit = file.size / 1024 / 1024 < 1024
      if(!zipExt) {
        handleAlert('warning',"上传文件只能是 ditamap格式!")
        return false;
      }
      if (!isLimit) {
        handleAlert('warning',"上传文件大小不能超过 1GB!")
        return false;
      }
    },
    initialState(){
      this.percentage = 0
      this.showUploadProcess= false
    },
    // 批量上传
    uploadFile (file) {
      var _this = this
      _this.initialState()
      var formData = new FormData();
      formData.append('manualId', _this.manualId);
      formData.append('manualVersion', _this.manualVersion);
      formData.append('file', file.file);
      const uploadProgress = progressEvent => {
        _this.showUploadProcess = true
        _this.percentage = Math.floor((progressEvent.loaded * 100) / progressEvent.total)
      }
      catalogUpload(formData, uploadProgress).then(res => {
        if(res.data.code == 100){
          handleAlert('success','上传已完成')
          _this.dataList()
          _this.dialogFormVisible = false
          _this.initialState()
        }else{
          handleAlert('error','导入失败')
          _this.initialState()
        }
      }).catch(function(error){
        handleAlert('error','上传出现异常，稍后重试')
        _this.initialState()
      })
    },
    batchImport(fileId){
      var params = {
        manualId: this.manualId,
        fileId: fileId
      }
      catalogImport(params).then(res => {
        if(res.data.code==100){
          this.dialogFormVisible = false
          handleAlert('success','上传已完成,系统正在解析处理数据，请稍等...')
          this.dataList()
        }
      })

    },
    // form
    formClear(){
      this.form= {
        id: 0,
        pid: 0,
        type: '',
        code: '',
        cnName: '',
        isLeaf: '',
        sort: 1,
        remark: '',
      }
      this.$refs.form.resetFields()
    },
    handleNodeClick (data) {
      this.assignment(data)
    },
    assignment(data){
      this.butType=''
      this.editTitle = "当前目录节点信息"
      this.childrenList = data.children
      this.form.id=data.id
      this.form.pid=data.pid
      this.form.type=data.type
      this.form.code = data.code
      this.form.cnName = data.cnName
      this.catalogueLevel = data.level
      this.form.isLeaf = data.isLeaf
      this.form.sort = data.sort
      this.form.remark = data.remark
    },
    addNode(){
      if(this.form.id == ''){
        handleAlert('error','请选择目录节点')
        return false
      }
      this.editTitle = "添加节点信息"
      this.butType='addNodeBut'
      this.catalogueLevel = this.catalogueLevel + 1
      this.$refs.form.resetFields()
      if(this.form.pid == '0'){
        this.form.pid = this.manualTypeId
      } else {
        this.form.pid = this.form.id
      }
    },
    // 新增
    nodeConfirm(form){
      this.nodeKeyList = []
      this.$refs[form].validate((valid) => {
        if (valid) {
          var params = new URLSearchParams()
          params.append('pid', this.form.pid)
          params.append('manualId', this.manualId)
          params.append('manualVersion', this.manualVersion)
          params.append('code', this.form.code)
          params.append('cnName', this.form.cnName)
          params.append('level', this.catalogueLevel)
          params.append('isLeaf', this.form.isLeaf)
          params.append('sort', this.form.sort)
          params.append('remark', this.form.remark)
          catalogAdd(params).then(res => {
            if (res.data.code === 100) {
              handleAlert('success',res.data.msg)
              this.dataList()
              this.nodeKeyList.push(this.form.pid)
              this.butType=''
              this.editTitle = "当前目录节点信息"
              this.formClear()
            } else {
              handleAlert('error',res.data.msg)
            }
          })
        }else{
          handleAlert('error','请完善信息')
        }
      })
    },
    // 修改
    updateNode(){
      this.nodeKeyList = []
      var params = new URLSearchParams()
      params.append('manualId', this.manualId)
      params.append('manualVersion', this.manualVersion)
      params.append('id', this.form.id)
      params.append('pid', this.form.pid)
      params.append('code', this.form.code)
      params.append('cnName', this.form.cnName)
      params.append('level', this.catalogueLevel)
      // if (this.form.enName) {
      //   params.append('enName', this.form.enName)
      // }
      // if (this.form.nickName) {
      //   params.append('nickName', this.form.nickName)
      // }
      // if (this.form.url) {
      //   params.append('url', this.form.url)
      // }
      // params.append('level', this.form.level)
      params.append('isLeaf', this.form.isLeaf)
      params.append('sort', this.form.sort)
      if (this.form.remark) {
        params.append('remark', this.form.remark)
      }

      catalogEdit(params).then(res => {
        if (res.data.code === 100) {
          handleAlert('success',res.data.msg)
          this.dataList()
          this.nodeKeyList.push(this.form.id)
        } else {
          handleAlert('error',res.data.msg)
        }
      })
    },
    // 删除
    delNode () {
      this.nodeKeyList = []
      let curId = this.form.id
      let nodeName = this.form.cnName
      if(this.form.pid == 0) {
        handleAlert('error','目录根节点无法删除')
        return
      }
      if(this.childrenList.length > 0){
        handleAlert('error','有子节点无法删除')
        return false;
      }
      if(curId==0){
        handleAlert('error','请选中需要删除的目录节点')
        return false;
      }
      this.$confirm('确定删除【'+ nodeName+ '】的目录节点信息?', '删除目录节点', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        catalogDel(curId).then(res => {
          if(res.data.code==100){
            handleAlert('success','删除成功')
            this.dataList()
             this.nodeKeyList.push(this.form.pid)
            // this.resetForm('form')
          }else{
            handleAlert('error',res.data.msg)
          }
        })
      }).catch((error)=>{
        handleAlert('info','取消删除')
      })
    },
  },
  mounted () {
    this.manualId =this.$route.params.id
    this.manualVersion =this.$route.params.manualVersion
    // this.uploadUrl = cmsServerUrl+"sys/upload/procFile"
    this.dataList()
    contentSize()
  }
}
</script>
<style>
  .upload-area .upload-demo{
    width: 400px;
    height: 200px;
    margin: 0 auto;
    /* text-align: left;
    width: 400px;
    height: 180px;
    margin: 0 auto;
    padding: 20px;
    border-radius: 10px;
    border: 1px solid #dedede; */
  }
  .upload-area .el-progress-bar__outer {
    height: 10px !important;
  }
</style>
