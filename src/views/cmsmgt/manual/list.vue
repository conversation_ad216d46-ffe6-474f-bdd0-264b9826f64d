<template>
  <div class="layoutContainer">
    <div class="infoDetail">
      <el-row>
        <el-col :span="5" class="leftData">
          <div>
            <div class="topButton">
              <span style="height:42px;line-height:42px;margin-left: 10px;">手册项目</span>
            </div>
            <div class="scrollClass">
              <el-scrollbar>
                <el-tree
                  ref="tree"
                  node-key="id"
                  :default-expanded-keys="nodeKeyList"
                  :data="listdata"
                  :props="defaultProps"
                  @node-click="handleNodeClick"
                  highlight-current
                >
                  <span slot-scope="{ node, data }" class="custom-tree-node" @mouseenter="mouseenter(data)" @mouseleave="mouseleave(data)">
                    <span>{{data.nameCh}}</span>
                    <span v-show="data.isCurrent" class="attribute" @click="attributeClick(data)">
                      <i class="el-icon-more" style="transform: rotate(90deg);" title="属性"></i>
                    </span>
                  </span>
                </el-tree>
              </el-scrollbar>
            </div>
          </div>
        </el-col>
        <el-col :span="19" class="fromRight manualManage">
          <div class="rightTitle" v-if="hasPerm('menuAsimss3A2B_101')">
            <el-button type="text" icon="el-icon-plus" @click="handelAdd()">新增</el-button>
          </div>
          <el-table
            style="width:100%"
            :data="resultList"
            stripe
            highlight-current-row
            :header-cell-style="{
              'text-align': 'center',
              'background-color': 'var(--other-color)',
            }"
            :cell-style="{'text-align':'center'}"
          >
            <el-table-column label="手册分类" prop="cnName"></el-table-column>
            <el-table-column label="负责人" prop="principalName"></el-table-column>
            <el-table-column label="开发状态" prop="manualStatus">
              <template slot-scope="{row}">
                <span v-if="row.manualStatus === 1">进行中</span>
                <span v-if="row.manualStatus === 2">暂停中</span>
                <span v-if="row.manualStatus === 3">已验收</span>
              </template>
            </el-table-column>
            <el-table-column label="截止时间" prop="deadlineTime">
              <template slot-scope="{row}">
                <div>
                  {{ row.deadlineTime | conversion("yyyy-MM-dd") }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="风险状态" prop="riskStatus">
              <template slot-scope="{row}">
                <span v-if="row.riskStatus === 1">无风险</span>
                <span v-if="row.riskStatus === 2" style="color:#c30000">低风险</span>
                <span v-if="row.riskStatus === 3" style="color:#c30000">中风险</span>
                <span v-if="row.riskStatus === 4" style="color:#c30000">高风险</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" fixed="right" width="300">
              <template slot-scope="{row}">
                <el-button type="text" v-if="hasPerm('menuAsimss3A2B_109')" size="small" @click="catalogClick(row)">手册目录</el-button>
                <el-button type="text" v-if="hasPerm('menuAsimss3A2B_110')" size="small" @click="contentClick(row)">手册内容</el-button>
                <el-button type="text" v-if="hasPerm('menuAsimss3A2B_120')" size="small" @click="imageClick(row)">手册图片</el-button>
                <el-dropdown style="margin-left: 10px;" v-if="hasPerm('menuAsimss3A2B_104') || hasPerm('menuAsimss3A2B_103') || hasPerm('menuAsimss3A2B_102') || hasPerm('menuAsimss3A2B_110') || hasPerm('menuAsimss3A2B_111')">
                  <el-button type="text" size="small">
                    更多<i class="el-icon-arrow-down el-icon--right"></i>
                  </el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item v-if="hasPerm('menuAsimss3A2B_110')" @click.native="handelManual(row)">新页面浏览</el-dropdown-item>
                    <el-dropdown-item v-if="hasPerm('menuAsimss3A2B_104')" @click.native="handelDetail(row)">查看</el-dropdown-item>
                    <el-dropdown-item v-if="hasPerm('menuAsimss3A2B_103')" @click.native="handelEdit(row)">编辑</el-dropdown-item>
                    <el-dropdown-item v-if="hasPerm('menuAsimss3A2B_102')" @click.native="handelDelete(row)">删除</el-dropdown-item>
                    <el-dropdown-item v-if="hasPerm('menuAsimss3A2B_111')" @click.native="version(row)">版本</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
    </div>
    <el-dialog v-dialogDrag :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
      <el-form v-if="dialogStatus === 'detail'" ref='temp' :label-width="formLabelWidth" :model="temp" label-position="center" :validate-on-rule-change="false">
        <el-form-item label="项目名称" prop="projectName">
          <el-input v-model="temp.projectName" readonly></el-input>
        </el-form-item>
        <el-form-item label="手册分类" prop="cnName">
          <el-input v-model="temp.cnName" readonly></el-input>
        </el-form-item>
        <el-form-item label="语种" prop="languageName">
          <el-input v-model="temp.languageName" readonly></el-input>
        </el-form-item>
        <el-form-item label="负责人" prop="principalName">
          <el-input v-model="temp.principalName" readonly></el-input>
        </el-form-item>
        <el-form-item v-for="(item, index) in manualMemberList" :prop="item.code" :key="index" :label="item.alias">
          <el-checkbox-group v-model="roleList[item.code]" >
            <el-checkbox v-for="(itemTwo, indexTwo) in item.children" :key="indexTwo" disabled :label="itemTwo.id">
              {{itemTwo.name}}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="截止时间" prop="deadlineTime">
          <el-date-picker type="date" v-model="temp.deadlineTime" readonly></el-date-picker>
        </el-form-item>
        <el-form-item label="开发状态" prop="manualStatus">
          <el-input v-if="temp.manualStatus === 1" readonly v-model="manualList[0].name"></el-input>
          <el-input v-if="temp.manualStatus === 2" readonly v-model="manualList[1].name"></el-input>
          <el-input v-if="temp.manualStatus === 3" readonly v-model="manualList[2].name"></el-input>
        </el-form-item>
        <el-form-item label="风险状态" prop="riskStatus">
          <el-input v-if="temp.riskStatus === 1" v-model="riskList[0].name" readonly>无风险</el-input>
          <el-input v-if="temp.riskStatus === 2" v-model="riskList[1].name" style="color:#962626" readonly>低风险</el-input>
          <el-input v-if="temp.riskStatus === 3" v-model="riskList[2].name" style="color:#962626" readonly>中风险</el-input>
          <el-input v-if="temp.riskStatus === 4" v-model="riskList[3].name" style="color:#962626" readonly>高风险</el-input>
        </el-form-item>
      </el-form>
      <el-form v-if="dialogStatus === 'add' || dialogStatus === 'edit'" :label-width="formLabelWidth" ref='temp' :model="temp" :rules="rules" label-position="center" :validate-on-rule-change="false">
        <el-form-item label="手册分类" prop="manualType">
          <el-select v-model="temp.manualType" clearable filterable :disabled="dialogStatus === 'edit'? true: false">
            <el-option v-for="(item, index) in manualTypeList" :key="index" :label="item.name" :value="item.code"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="语种" prop="language">
          <el-select v-model="temp.language" clearable filterable>
            <el-option v-for="(item, index) of languageList" :key="index" :label="item.name" :value="item.code"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="负责人" prop="principal">
          <el-select v-model="temp.principal" clearable filterable>
            <el-option v-for="(item, index) of principalList" :key="index" :label="item.realName" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <div v-if="dialogStatus === 'add'">
          <el-form-item  v-for="(item, index) in addMemberList" :prop="item.code" :key="index" :label="item.alias">
            <el-checkbox-group v-model="roleList[item.code]">
              <el-checkbox v-for="(itemTwo, indexTwo) in item.children" @change="handelCheckBox()" :key="indexTwo" :label="itemTwo.id">
                {{itemTwo.name}}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </div>
        <div v-if="dialogStatus === 'edit'">
          <el-form-item v-for="(item, index) in manualMemberList" :prop="item.code" :key="index" :label="item.alias">
            <el-checkbox-group v-model="roleList[item.code]">
              <el-checkbox v-for="(itemTwo, indexTwo) in item.children" @change="handelCheckBox()" :key="indexTwo" :label="itemTwo.id">
                {{itemTwo.name}}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </div>
        <el-form-item label="截止时间" prop="deadlineTime">
          <el-date-picker type="date" v-model="temp.deadlineTime" placeholder="请选择截止时间" :picker-options="pickerDeadlineTime"></el-date-picker>
        </el-form-item>
           <el-form-item label="开发状态" prop="manualStatus">
          <el-select v-model="temp.manualStatus" clearable filterable>
            <el-option v-for="(item, index) of manualList" :key="index" :label="item.name" :value="item.code"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="风险状态" prop="riskStatus">
          <el-select v-model="temp.riskStatus" clearable filterable>
            <el-option v-for="(item, index) of riskList" :key="index" :label="item.name" :value="item.code"></el-option>
          </el-select>
        </el-form-item>
        <div class="submitArea">
          <el-button type="primary" @click="dialogStatus === 'add' ? addClick('temp') : updateData('temp')">
            立即提交
          </el-button>
          <el-button @click="resetForm('temp')">
            重置
          </el-button>
        </div>
      </el-form>
      <el-form v-if="dialogStatus == 'attr'" :label-width="formLabelWidth" ref='trainTemp' :model="trainTemp">
        <el-form-item label="主机厂" prop="brand">
          <el-input v-model="trainTemp.brand" readonly></el-input>
        </el-form-item>
        <el-form-item label="项目名称" prop="nameCh">
          <el-input v-model="trainTemp.nameCh" readonly></el-input>
        </el-form-item>
        <el-form-item label="项目状态" prop="projectStatus">
          <el-input v-if="trainTemp.projectStatus == '1'" value="进行中" readonly></el-input>
          <el-input v-if="trainTemp.projectStatus == '2'" value="暂停中" readonly></el-input>
          <el-input v-if="trainTemp.projectStatus == '3'" value="已验收" readonly></el-input>
        </el-form-item>
        <el-form-item label="车型" prop="trainCode">
          <el-input v-model="trainTemp.trainCode" readonly></el-input>
        </el-form-item>
        <el-form-item label="年款" prop="trainYear">
          <el-input v-model="trainTemp.trainYear" readonly></el-input>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import { sysServerUrl, cmsServerUrl, getMyDate, addTabs, contentSize, tabPath, handleAlert, expandTree } from '@/assets/js/common.js'
import { manualCatalogue, manualContent, manualMemberProject,manualMember, manualUser, projectList, manualType, manualAdd, manualEdit, manualDel, languageTypeData } from '@/api/cmsmgt.js'
export default {
  name: 'cmsmgtmanuallist',
  data () {
    return {
      roleList:[],
      nodeKeyList:[],
      stateVal:"",
      projectNameId: "",
      defaultProps: {
        children: 'children',
        label: 'nameCh',
      },
      trainTemp: {
        id:"",
        frimId: "",
        brand: "",
        nameCh: "",
        projectStatus:'',
        trainCode:"",
        trainYear: "",
      },
      projectId:"",
      temp: {
        id: '',
        projectName: '',
        cnName: '',
        manualType: '',
        language:'chinese',
        languageName:'',
        principalName: '',
        principal: '',
        manualStatus: 1,
        deadlineTime: '',
        firstDraftTime: '',
        finalDraftTime: '',
        riskStatus: 1,
        auditUser:"",
        developmentUser:"",
        drawingUser:""
      },
      manualList: [
        { name: '进行中', code: 1 },
        { name: '暂停中', code: 2 },
        { name: '已验收', code: 3 }
      ],
      riskList: [
        { name: '无风险', code: 1 },
        { name: '低风险', code: 2 },
        { name: '中风险', code: 3 },
        { name: '高风险', code: 4 }
      ],
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        detail: '手册明细',
        edit: '编辑手册',
        add: '新增手册',
        attr: '项目属性'
      },
      manualMemberList:[],
      addMemberList: [],
      principalList: [],
      resultList: [],
      listdata: [],
      newListData: [],
      pagesize: 20,
      currentPage: 1,
      total: 0,
      projectList: [],
      manualTypeList: [],
      languageList:[],
      formLabelWidth: '100px',
      rules: {
        manualType: [{ required: true, message: '手册分类不能为空', trigger: ['blur', 'change'] }],
        principal: [{ required: true, message: '负责人不能为空', trigger: ['blur', 'change'] }],
        manualStatus: [{ required: true, message: '开发状态不能为空', trigger: ['blur', 'change'] }],
        deadlineTime: [{ required: true, message: '截止时间不能为空', trigger: ['blur', 'change'] }],
        riskStatus: [{ required: true, message: '风险状态不能为空', trigger: ['blur', 'change'] }],
        auditUser: [{ required: true, message: '审核人员不能为空', trigger: ['blur', 'change'] }],
        developmentUser: [{ required: true, message: '开发人员不能为空', trigger: ['blur', 'change'] }],
        drawingUser: [{ required: true, message: '制图人员不能为空', trigger: ['blur', 'change'] }],
        language: [{ required: true, message: '语种不能为空', trigger: ['blur', 'change'] }],
      },
	    // 截止时间
      pickerDeadlineTime: {
        disabledDate: (time) => {
          return (this.temp.finalDraftTime ? time.getTime() < this.temp.finalDraftTime : false) || (this.temp.firstDraftTime ? time.getTime() < this.temp.firstDraftTime : false)  //只能选开始时间之后的日期
          //返回---终稿时间是否有值？   可选时间大于开始时间   ：  任意时间都可选            初稿时间是否有值
        }
      }
    }
  },
  methods: {
    // 数据
    mouseenter(data) {
      data.isCurrent = true
    },
    mouseleave(data) {
      data.isCurrent = false
    },
    attributeClick(data){
      event.stopPropagation()
      this.trainDetail(data)
      this.dialogFormVisible = true
      this.dialogStatus = 'attr'
      this.$nextTick(function() {
        this.$refs.trainTemp.clearValidate();
      })
    },
    trainDetail(data){
      this.trainTemp.id = data.id
      this.trainTemp.frimId= data.frimId
      this.trainTemp.brand= data.brand
      this.trainTemp.nameCh= data.nameCh
      this.trainTemp.projectStatus= data.projectStatus
      this.trainTemp.trainCode= data.trainCode
      this.trainTemp.trainYear= data.trainYear
    },
    // 目录
    dataList () {
      manualCatalogue().then(res => {
        this.listdata = res.data.data
        this.expandStatus(this.listdata)
      })
    },
    expandStatus(data){
      var nodeExpand = expandTree(data)
      this.nodeKeyList.push(nodeExpand.id)
      this.handleNodeClick(nodeExpand)
      this.$nextTick(() => {
        this.$refs.tree.setCurrentKey(nodeExpand.id);
      });
    },
    // 右侧内容
    manualDetail(id){
      var params = { "projectId" : id}
      manualContent(params).then(res => {
        this.total = res.data.total
        this.resultList = res.data.data
      })
    },
    handleNodeClick (data) {
      this.manageClick(data.id, data.firmId)
    },
    manageClick(id, firmId){
       this.manualDetail(id);
      if (firmId) {
        $(".manualManage .rightTitle").show()
        this.projectId = id;
        this.getNumberLsit(id)
      } else {
        $(".manualManage .rightTitle").hide()
      }
    },
    // 获取用户列表
    getUserList () {
      manualUser().then(res => {
        if (res.data.code === 100) {
          this.principalList = res.data.data
        }
      })
    },
    // 获取语种列表
    getlanguageType(){
      this.languageList = JSON.parse(sessionStorage.getItem('language'))
    },
    // 获取成员列表
    getNumberLsit(id){
      manualMemberProject(id).then(res => {
        if(res.data.code == 100){
          this.addMemberList = res.data.data
          this.addMemberList.forEach(item => {
            this.$set(this.roleList, item.code, [])
          })
        }
      })
    },
    // 获取项目列表
    getProjectList () {
      projectList().then(res => {
        if (res !== null && res.data.code === 100) {
          this.projectList = res.data.data
        }
      })
    },
    // 获取手册分类
    getManualList () {
      manualType().then(res => {
        if (res !== null && res.data.code === 100) {
          this.manualTypeList = res.data.data
        }
      })
    },
    resetTemp () {
      this.temp = {
        id: '',
        projectName: '',
        cnName: '',
        language:'chinese',
        languageName:'',
        principal: '',
        manualStatus: 1,
        deadlineTime: '',
        auditUser:"",
        developmentUser:"",
        drawingUser:"",
        firstDraftTime: '',
        finalDraftTime: '',
        riskStatus: 1
      }
      this.$set(this.roleList, "developmentUser", [])
      this.$set(this.roleList, "auditUser", [])
      this.$set(this.roleList, "drawingUser", [])
      this.$nextTick(function() {
        this.$refs.temp.clearValidate();
      })
    },
    // 手册分类
    updatedCode () {
      for (var i = 0; i < this.manualTypeList.length; i++) {
        if (this.manualTypeList[i].code === this.temp.manualType) {
          this.temp.cnName = this.manualTypeList[i].name
        }
      }
    },
    // 新增
    handelAdd () {
      this.dialogFormVisible = true
      this.resetTemp()
      this.dialogStatus = 'add'
    },
    handelCheckBox(){
      this.temp.developmentUser = this.roleList["developmentUser"].toString()
      this.temp.auditUser = this.roleList["auditUser"].toString()
      this.temp.drawingUser = this.roleList["drawingUser"].toString()
    },
    addClick (temp) {
      this.handelCheckBox()
      var developmentVal = this.roleList["developmentUser"].toString()
      var auditVal = this.roleList["auditUser"].toString()
      var drawingVal = this.roleList["drawingUser"].toString()
      if(drawingVal !==''){
        this.temp.drawingUser = drawingVal
      }
      if(auditVal !==''){
        this.temp.auditUser = auditVal
      }
      if(developmentVal !==''){
        this.temp.developmentUser = developmentVal
      }
      this.$refs[temp].validate((valid) => {
        if(valid){
          var params = new URLSearchParams()
          this.updatedCode()
          params.append('projectId', this.projectId)  // 项目id
          params.append('cnName', this.temp.cnName)        // 手册名称
          params.append('manualType', this.temp.manualType)  // 手册分类
          params.append('principal', this.temp.principal)  // 负责人
          params.append('language', this.temp.language)  // 语种
          params.append('auditIds', auditVal)  // 审核人员
          params.append('developmentIds', developmentVal)  // 开发人员
          params.append('drawingIds', drawingVal)  // 制图人员
          params.append('deadlineTime', getMyDate(this.temp.deadlineTime))  // 截止时间
          params.append('riskStatus', this.temp.riskStatus)   // 风险状态
          // 增加
          manualAdd(params).then(res => {
            if (res.data.code === 100) {
              handleAlert('success',res.data.msg)
              this.manualDetail(this.projectId)
              this.dialogFormVisible = false
            } else {
              handleAlert('error',res.data.msg)
            }
          })
        }else{
          handleAlert('error','请完善手册信息')
        }
      })
    },
    manualMemberData(id){
      var oneArray = []
      var twoArray = []
      var threeArray = []
      manualMember(id).then(res => {
        if(res.data.code == 100){
          this.manualMemberList = res.data.data
          this.$nextTick(function() {
            this.$refs.temp.clearValidate();
          })
          this.manualMemberList.forEach(item => {
            this.$set(this.roleList, item.code, [])
            item.children.forEach(itemTwo => {
              if(item.code=="developmentUser"){
                if(itemTwo.accessStatus == true){
                  oneArray.push(itemTwo.id)
                  this.$set(this.roleList, item.code, oneArray)
                }
              }else if(item.code == "auditUser"){
                if(itemTwo.accessStatus == true){
                  twoArray.push(itemTwo.id)
                  this.$set(this.roleList, item.code, twoArray)
                }
              } else if(item.code=="drawingUser"){
                if(itemTwo.accessStatus == true){
                  threeArray.push(itemTwo.id)
                  this.$set(this.roleList, item.code, threeArray)
                }
              }
            })
          })
        }
      })
    },
    // 查看
    handelDetail (row) {
      this.dialogFormVisible = true
      this.dialogStatus = 'detail'
      this.resetTemp()
      this.manualMemberData(row.id)
      this.temp = Object.assign({}, row)
    },
    // 编辑
    handelEdit (row) {
      this.dialogFormVisible = true
      this.dialogStatus = 'edit'
      this.resetTemp()
      this.manualMemberData(row.id)
      this.temp = Object.assign({}, row)
    },
    updateData (temp) {
      this.handelCheckBox()
      var developmentVal = this.roleList["developmentUser"].toString()
      var auditVal = this.roleList["auditUser"].toString()
      var drawingVal = this.roleList["drawingUser"].toString()
      if(drawingVal !==''){
        this.temp.drawingUser = drawingVal
      }
      if(auditVal !==''){
        this.temp.auditUser = auditVal
      }
      if(developmentVal !==''){
        this.temp.developmentUser = developmentVal
      }
      this.$refs[temp].validate((valid) => {
        if(valid){
            var developmentVal = this.roleList["developmentUser"].toString()
          var auditVal = this.roleList["auditUser"].toString()
          var drawingVal = this.roleList["drawingUser"].toString()
          this.updatedCode()
          var params = new URLSearchParams()
          params.append('id', this.temp.id)
          params.append('projectId', this.projectId)
          params.append('cnName', this.temp.cnName)
          params.append('manualType', this.temp.manualType)
          params.append('principal', this.temp.principal)
          params.append('language', this.temp.language)
          params.append('auditIds', auditVal)
          params.append('developmentIds', developmentVal)
          params.append('drawingIds', drawingVal)
          params.append('manualStatus', this.temp.manualStatus)
          params.append('deadlineTime', getMyDate(this.temp.deadlineTime))
          // params.append('firstDraftTime', getMyDate(this.temp.firstDraftTime))
          // params.append('finalDraftTime', getMyDate(this.temp.finalDraftTime))
          params.append('riskStatus', this.temp.riskStatus)
          manualEdit(params).then(res => {
            if (res.data.code === 100) {
              handleAlert('success',res.data.msg)
              this.manualDetail(this.projectId)
              this.dialogFormVisible = false
            } else {
              handleAlert('error',res.data.msg)
            }
          })
        }else{
          handleAlert('error','请完善手册信息')
        }
      })
    },
    resetForm(temp) {
      if(this.dialogStatus == 'edit') {
        this.temp.principal= '',
        this.temp.manualStatus= '',
        this.temp.deadlineTime= '',
        this.temp.firstDraftTime= '',
        this.temp.finalDraftTime= '',
        this.temp.riskStatus= ''
        this.$nextTick(function() {
          this.$refs.temp.clearValidate();
        })
      } else {
        this.resetTemp()
      }
    },
    // 查看手册内容
    handelManual(row){
      var title = row.projectName + ">" + row.cnName
      sessionStorage.setItem("manualTitle", title)
      const {href} = this.$router.resolve({name: 'checkContent', params: {id: row.id, manualVersion: row.manualVersion } })
      window.open(href, '_blank');
    },
    // 删除
    handelDelete (row) {
      this.$confirm('确定删除项目为【' + row.projectName +'】中的【'+ row.cnName + '】的相关信息?', '删除手册', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        manualDel(row.id).then(res => {
          if (res.data.code === 100) {
            handleAlert('success','删除成功')
            this.manualDetail(row.projectId)
          } else {
            handleAlert('error','删除失败')
          }
        })
      }).catch((error)=>{
        handleAlert('info','取消删除')
      })
    },
    // 手册版本
    version (row) {
      var title = `${ row.projectName }(${row.cnName}) 版本管理`
      // var curl = "/version" + row.id
      this.$router.push({ name: 'version', params: { id: row.id } })
      addTabs(this.$route.path, title);
    },
    // 手册目录
    catalogClick (row) {
      let title = `${row.projectName}(${row.cnName}) 目录管理`
      this.$router.push({ name: 'manualCatalog', params: {id: row.id, manualVersion: row.manualVersion } })
      addTabs(this.$route.path, title);
    },
    // 手册内容
    contentClick (row, name, url) {
      window.localStorage.setItem("projectName",JSON.stringify(row.projectName));
      window.localStorage.setItem("cnName",JSON.stringify(row.cnName));
      let title =`${row.projectName}(${row.cnName}) 内容管理`
      this.$router.push({ name: 'manualContent', params: {id: row.id, manualVersion: row.manualVersion } })
      addTabs(this.$route.path, title);
    },
    // 手册图片
    imageClick (row) {
      let title = `${row.projectName}(${row.cnName}) 图片管理`
      this.$router.push({ name: 'manualImage', params: {id: row.id, manualVersion: row.manualVersion } })
      addTabs(this.$route.path, title);
    },
  },
  mounted () {
    this.dataList()
    this.getUserList()
    this.getlanguageType()
    this.getProjectList()
    this.getManualList()
    contentSize();
  },
  created(){
 this.getlanguageType()
  }
}
</script>
<style>
  .infoDetail .manualManage .rightTitle {
    display: none;
  }
</style>
