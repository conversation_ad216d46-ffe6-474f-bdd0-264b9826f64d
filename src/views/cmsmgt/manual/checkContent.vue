<template>
  <div class="layoutContainer">
    <div class="infoDetail manualContainer">
      <el-row>
        <el-col :span="7" class="leftData">
          <div class="manualMenu">
            <div class="scrollClass">
              <el-scrollbar>
                <el-tree
                  ref="tree"
                  :data="listdata"
                  node-key="id"
                  :default-expanded-keys="treeExpandIdList"
                  :props="defaultProps"
                  @node-click="handleNodeClick"
                ></el-tree>
              </el-scrollbar>
            </div>
            <div class="foldBut" @click="foldClick()">
              <i class="el-icon-d-arrow-left"></i>
            </div>
          </div>
          <div class="openBut" @click="openClick()">
            <i class="el-icon-d-arrow-right"></i>
          </div>
        </el-col>
        <el-col :span="17" class="fromRight checkCenter" v-if="isNotSvg">
          <div id="header">
            <!--iframe引入-->
            <iframe
              disabled="true"
              id="editFrame"
              marginwidth="0"
              marginheight="0"
              width="100%"
              height="100%"
              src="static/editor/lib/index.html"
              frameborder="no"
            ></iframe>
          </div>
        </el-col>
        <el-col :span="17" class="fromRight" v-else>
          <div class="fileImg">
            <div id="mysvg" style="height: 100%"></div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>
import { cmsServerUrl, contentSize, handleAlert } from "@/assets/js/common.js";
import {
  contentData,
  contentNodeDetail,
} from "@/api/cmsmgt.js";
import $ from "jquery";
import { loadSvg } from "@/plugins/mysvg.js";
export default {
  name: "checkContent",
  data() {
    return {
      defaultProps: {
        children: "children",
        label: "cnName",
      },
      formLabelWidth: "100px",
      listdata: [],
      treeExpandIdList: [],
      manualId: 0,
      manualVersion: "",
      cnName: "",
      directoryId: 0,
      isNotSvg: true,
    };
  },
  watch: {
    // 利用watch方法检测路由变化
    $route: function (to, from) {
      if (to.path !== from.path) {
        this.url = cmsServerUrl;
        this.manualId = to.params.id;
        this.manualVersion = to.params.manualVersion;
        this.dataList();
        this.isNotSvg = true;
      }
    },
  },
  created() {
    var title = sessionStorage.getItem("manualTitle")
    document.title = title;
  },
  methods: {
    dataList() {
      var params = new URLSearchParams();
      params.append("id", this.$route.params.id);
      params.append("manualVersion", this.$route.params.manualVersion);
      contentData(params).then((res) => {
        this.listdata = res.data.data;
        this.treeExpandIdList.push(this.listdata[0].id);
      });
    },
    foldClick(){
      $(".leftData .manualMenu").hide()
      $(".openBut").show()
      $(".manualContainer .leftData").css("width", "30px")
      $(".manualContainer .fromRight").css("width", "calc(100% - 30px)")
    },
    openClick(){
      $(".leftData .manualMenu").show()
      $(".openBut").hide()
      $(".manualContainer .leftData,.manualContainer .fromRight").removeAttr("style")
    },
    handleNodeClick(treeNode) {
      let directoryId = treeNode.id;
      let isLeaf = treeNode.isLeaf;
      if (isLeaf == 1) {
        this.directoryId = directoryId;
        // 获取编辑器内容
        contentNodeDetail(directoryId).then((res) => {
          if (res.data.data != null) {
            this.childWindow.setContent(res.data.data.content);
          } else {
            this.childWindow.setContent("<br>");
          }
        });
      }
    },

  },
  mounted() {
    this.url = cmsServerUrl;
    this.manualId = this.$route.params.id;
    this.manualVersion = this.$route.params.manualVersion;
    this.childWindow = document.getElementById("editFrame").contentWindow; //获取子窗体的window对象.
    // 初始化
    this.dataList();
    contentSize();
    const iframe = document.getElementById("editFrame");
    //注意： 如果本身iframe嵌套的盒子有延迟 则下面判断最好也加延迟
    var that = this;
    // 处理兼容行问题
    if (iframe.attachEvent) {
      iframe.attachEvent("onload", function () {
        that.childWindow.paramUrl(cmsServerUrl, that.manualId);
        // that.childWindow.setToken(sessionStorage.getItem('token'))
        that.childWindow.setToken(sessionStorage.token)
      });
    } else {
      iframe.onload = function () {
        that.childWindow.paramUrl(cmsServerUrl, that.manualId);
        // that.childWindow.setToken(sessionStorage.getItem('token'))
        that.childWindow.setToken(sessionStorage.token)
      };
    }
  },
};
</script>
<style>
.checkCenter #header {
  width: 100%;
  height: calc(100% + 1px)
}
.infoDetail .leftData>.manualMenu{
  margin-right: 0;
}
.manualContainer .fromRight{
  overflow: hidden !important;
}
.leftData .manualMenu{
  display: flex;
}
.manualMenu .scrollClass {
  flex: 1;
  overflow: hidden;
}
.manualMenu .foldBut,
.leftData .openBut{
  width: 20px;
  cursor: pointer;
  position: relative;
}
.manualContainer .leftData .openBut{
  display: none;
  border:1px solid #cfd5de;
  height: calc(100% - 10px);
  margin-right: 10px;
}
.manualMenu .foldBut .el-icon-d-arrow-left:before,
.openBut .el-icon-d-arrow-right:before{
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%,-50%);
  font-weight: bold;
  color: var(--theme-color);
}
.code-mode-select {
  /* height: 95%; */
  position: absolute;
  z-index: 2;
  right: 10px;
  top: 10px;
  max-width: 130px;
}

.fileImg {
  width: 100%;
  height: 95%;
}
</style>
<style>
.scrollClass {
  height: 70vh;
}
</style>
