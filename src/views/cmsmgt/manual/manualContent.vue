<template>
  <div class="layoutContainer">
    <div class="infoDetail manualContainer">
      <el-row>
        <el-col :span="7" class="leftData">
          <div class="manualMenu">
            <div class="scrollClass">
              <el-scrollbar>
                <el-tree
                  ref="tree"
                  :data="listdata"
                  node-key="id"
                  :default-expanded-keys="treeExpandIdList"
                  :props="defaultProps"
                  @node-click="handleNodeClick"
                ></el-tree>
              </el-scrollbar>
            </div>
            <div class="foldBut" @click="foldClick()">
              <i class="el-icon-d-arrow-left"></i>
            </div>
          </div>
          <div class="openBut" @click="openClick()">
            <i class="el-icon-d-arrow-right"></i>
          </div>
        </el-col>
        <el-col :span="17" class="fromRight manualCenter" v-if="isNotSvg">
          <div v-if="isEmigration"
            class="formTitle"
            style="
              text-align: left;
              padding: 0 15px;
              color: #000;
              height: 42px;
              line-height: 42px;
            "
          >
            <!-- <el-button type="primary"  :disabled="showViewBtn" >预览效果</el-button> -->
            <!-- <el-button type="text" icon="preview-icon" @click="preview('预览效果', '/preview')">预览效果</el-button> -->
            <el-button type="text" icon="preserve-icon" @click="updateTopic"
              >保存</el-button
            >
            <!-- 2023-02-08 先禁用，因为版本管理暂时没该功能 -->
            <el-button v-if="false" type="text" icon="save-icon" @click="showAddVersionDlg"
              >另存为</el-button
            >
            <el-button type="text" icon="el-icon-refresh" @click="showAddRefresh">刷新</el-button>
            <el-button type="text" icon="checkIn-icon" @click="immigration"
              >签入</el-button
            >
          </div>
          <div v-else
            class="formTitle"
            style="
              text-align: left;
              padding: 0 15px;
              color: #000;
              height: 42px;
              line-height: 42px;
            "
          >
            <el-button type="text" v-if="isLeaf === 1" icon="checkOut-icon" @click="emigration" :disabled="isAuthority"
              >签出</el-button
            >
          </div>
          <!-- <textarea ref="textarea" v-if="isSvg" style="height:100%"></textarea> -->
          <div id="header">
            <!--iframe引入   style="pointer-events: none;"-->
            <iframe
              disabled="true"
              id="editFrame"
              marginwidth="0"
              marginheight="0"
              width="100%"
              height="100%"
              src="static/editor/lib/index.html"
              frameborder="no"
            ></iframe>
          </div>
        </el-col>
        <el-col :span="17" class="fromRight" v-else>
          <div class="fileImg">
            <div id="mysvg" style="height: 100%"></div>
          </div>
        </el-col>
      </el-row>
      <el-dialog v-dialogDrag title="确认生成新版本" :visible.sync="dialogFormVisible">
        <el-form ref="dataForm" label-position="center" :label-width="formLabelWidth">
          <el-form-item
            label="版本备注:"
            prop="versionRemark"
          >
            <el-input
              type="textarea"
              rows="5"
              placeholder="请输入版本备注"
              v-model.trim="versionRemark"
              aria-required="true"
            ></el-input>
          </el-form-item>
          <div class="submitArea">
            <el-button type="primary" @click="addTopic"> 确认 </el-button>
            <el-button @click="dialogFormVisible = false"> 取消 </el-button>
          </div>
        </el-form>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { cmsServerUrl, contentSize, addTabs, handleAlert } from "@/assets/js/common.js";
import {
  contentData,
  contentNodeDetail,
  contentSaveAs,
  contentInsert,
  contentRefresh,
  contentImmigration,
  contentEmigration,
  contentAuthority,
} from "@/api/cmsmgt.js";
import $ from "jquery";
import { loadSvg } from "@/plugins/mysvg.js";
export default {
  name: "manualContent",
  data() {
    return {
      defaultProps: {
        children: "children",
        label: "cnName",
      },
      formLabelWidth: "100px",
      listdata: [],
      treeExpandIdList: [],
      manualId: 0,
      manualVersion: "",
      projectName: "",
      cnName: "",
      directoryId: 0,
      versionRemark: "",
      dialogFormVisible: false,
      showEditArea: false,
      showViewBtn: true,
      isNotSvg: true,
      isSvg: true,
      isAuthority: true,   // 编辑权限
      isEmigration: false,   // 迁出
      childWindow: null, // 子窗体（编辑器）的window对象
      isLeaf: '',    // 叶子节点
    };
  },
  watch: {
    // 利用watch方法检测路由变化
    $route: function (to, from) {
      if (to.path !== from.path) {
        this.url = cmsServerUrl;
        this.manualId = to.params.id;
        this.manualVersion = to.params.manualVersion;
        this.dataList();
        this.isNotSvg = true;
      }
    },
  },
  methods: {
    dataList() {
      var params = new URLSearchParams();
      params.append("id", this.$route.params.id);
      params.append("manualVersion", this.$route.params.manualVersion);
      contentData(params).then((res) => {
        this.listdata = res.data.data;
        this.treeExpandIdList.push(this.listdata[0].id);
      });
    },
    foldClick(){
      $(".leftData .manualMenu").hide()
      $(".openBut").show()
      $(".manualContainer .leftData").css("width", "30px")
      $(".manualContainer .fromRight").css("width", "calc(100% - 30px)")
    },
    openClick(){
      $(".leftData .manualMenu").show()
      $(".openBut").hide()
      $(".manualContainer .leftData,.manualContainer .fromRight").removeAttr("style")
    },
    handleNodeClick(treeNode) {
      this.isEmigration = false;
      this.isAuthority = true;
      let directoryId = treeNode.id;
      this.isLeaf = treeNode.isLeaf;

      if (this.isLeaf == 1) {
        this.directoryId = directoryId;

        // 获取编辑器内容
        contentNodeDetail(directoryId).then((res) => {
          if (res.data.data != null) {
            this.childWindow.setContent(res.data.data.content);
          } else {
            this.childWindow.setContent("<br>");
          }
        });

        // 有没有权限
        contentAuthority(directoryId).then(res => {
          if (res.data.data == "true") {
            this.isAuthority = false;
          }else if(res.data.data == "emigration"){
            this.isEmigration = true;
            // handleAlert('warning', "已经有人在编辑了")
          }else{
            this.isAuthority = true;
            // handleAlert('warning', "您还没有权限哦")
          }
        })
      }
    },
    // 保存当前版本
    updateTopic() {
      var formData = new FormData();
      formData.append("directoryId", this.directoryId); // 节点的id
      formData.append("content", this.childWindow.getContent()); // 节点内容
      contentInsert(formData)
        .then((res) => {
          if (res.data.code === 100) {
            handleAlert('success', "保存成功")
          } else {
            handleAlert('error', res.data.msg)
          }
        }).catch(function (err) {
          if (err !== null && err !== '' && err.responseText !== null) {
            handleAlert('error', "保存取消")
          }
        });
    },
    showAddVersionDlg() {
      this.dialogFormVisible = true;
    },
    // 添加新版本, 另存为
    addTopic() {
      if (this.versionRemark === "" || this.versionRemark.length === 0) {
        handleAlert('error','请输入版本备注')
        return false;
      }
      if (this.versionRemark.length > 200) {
        handleAlert('error','版本备注最多只能输入200个字符')
        return false;
      }
      var formData = new FormData();
      formData.append("directoryId", this.directoryId);
      formData.append("content", this.childWindow.getContent());
      formData.append("remark", this.versionRemark);

      contentSaveAs(formData)
        .then((res) => {
          if (res.data.code === 100) {
            handleAlert('success','保存新版本成功')
            this.dialogFormVisible = false;
          } else {
            handleAlert('error',res.data.msg)
          }
        }).catch(function (err) {
          if (err !== null && err !== '' && err.responseText !== null) {
            handleAlert('error',"保存新版本失败")
          }
        });
    },
    // 刷新
    showAddRefresh(){
      var formData = new FormData();
      formData.append("directoryId", this.directoryId); // 节点的id
      formData.append("content", this.childWindow.getContent()); // 节点内容
      contentRefresh(formData).then((res) => {
        if (res.data.code === 100) {
          handleAlert('success',"刷新成功")
        } else {
          handleAlert('error',res.data.msg)
        }
        if (res.data.data != null) {
          this.childWindow.setContent(res.data.data);
        } else {
          this.childWindow.setContent("<br>");
        }
      });
    },
    // 签入
    immigration(){
      var formData = new FormData();
      formData.append("directoryId", this.directoryId); // 节点的id
      formData.append("content", this.childWindow.getContent()); // 节点内容
      contentImmigration(formData).then(res => {
        if (res.data.code == 100) {
          handleAlert('success',"操作成功")
          this.isEmigration = false;
        }else {
          handleAlert('error',res.data.msg)
        }
      }).catch(err => {
        if (err !== null && err !== '' && err.responseText !== null) {
          handleAlert('error',"操作失败")
        }
      })
    },

    // 签出
    emigration(){
      contentEmigration(this.directoryId).then(res => {
          if (res.data.data) {
            this.isEmigration = true;
          }else{
            handleAlert('warning', "已经有人在编辑了")
            this.isEmigration = false;
          }
      }).catch(err => {
        if (err !== null && err !== '' && err.responseText !== null) {
        }
        this.isEmigration = false;
      })
    },
    // 预览效果
    // preview(name, url) {
    //   this.projectName = JSON.parse(window.localStorage.getItem("projectName"));
    //   this.cnName = JSON.parse(window.localStorage.getItem("cnName"));
    //   let title =
    //     name +
    //     "-" +
    //     this.projectName +
    //     "-" +
    //     this.cnName +
    //     "-" +
    //     "-" +
    //     this.$route.path;
    //   let curl = url + "/" + this.manualId + "/" + this.manualVersion;
    //   this.$router.push({
    //     name: "preview",
    //     params: { id: this.manualId, manualVersion: this.manualVersion },
    //   });
    //   addTabs(this.$route.path, title);
    // },
  },
  mounted() {
    this.url = cmsServerUrl;
    this.manualId = this.$route.params.id;
    this.manualVersion = this.$route.params.manualVersion;

    this.childWindow = document.getElementById("editFrame").contentWindow; //获取子窗体的window对象.
    // this.isNotSvg = true
    // 初始化
    this.dataList();
    contentSize();
    this.isSvg = false;
    const iframe = document.getElementById("editFrame");
    //注意： 如果本身iframe嵌套的盒子有延迟 则下面判断最好也加延迟
    var that = this;
    // 处理兼容行问题
    if (iframe.attachEvent) {
      iframe.attachEvent("onload", function () {
        that.childWindow.paramUrl(cmsServerUrl, that.manualId);
        // that.childWindow.setToken(sessionStorage.getItem('token'))
        that.childWindow.setToken(sessionStorage.token)
      });
    } else {
      iframe.onload = function () {
        that.childWindow.paramUrl(cmsServerUrl, that.manualId);
        // that.childWindow.setToken(sessionStorage.getItem('token'))
        that.childWindow.setToken(sessionStorage.token)
      };
    }
  },
};
</script>
<style>
.manualCenter #header {
  width: 100%;
  height: calc(100% - 41px)
}
</style>

