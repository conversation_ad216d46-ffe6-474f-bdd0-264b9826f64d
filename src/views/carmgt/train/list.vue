<template>
  <div class="layoutContainer">
    <div class="infoDetail">
      <el-row>
        <el-col :span="8" class="leftData">
          <div>
            <div class="topButton" v-if="hasPerm('menuAsimss2A1B_101') || hasPerm('menuAsimss2A1B_102') || hasPerm('menuAsimss2A1B_107') || hasPerm('menuAsimss2A1B_108')">
              <el-button type="text" v-if="hasPerm('menuAsimss2A1B_101')" icon="el-icon-plus" @click="addRoot">新增根节点</el-button>
              <el-button type="text" v-if="hasPerm('menuAsimss2A1B_101')" icon="el-icon-plus" @click="addSub">新增子节点</el-button>

              <el-button type="text" v-if="hasPerm('menuAsimss2A1B_107')" size="min" icon="bulkImport-icon" @click="uploadTrain">批量上传</el-button>
              <el-button type="text" v-if="hasPerm('menuAsimss2A1B_107') || hasPerm('menuAsimss2A1B_108')" icon="el-icon-download" @click="downModelClick()">下载模板</el-button>
              <el-button type="text" v-if="hasPerm('menuAsimss2A1B_102')" icon="el-icon-delete" @click="del">删除</el-button>
            </div>
            <div class="scrollClass">
              <el-scrollbar>
                <el-tree ref="tree" node-key="id" :default-expanded-keys="nodeKeyList" :data="listdata" :props="defaultProps" @node-click="handleNodeClick"></el-tree>
              </el-scrollbar>
            </div>
          </div>
        </el-col>
        <el-col :span="16" class="fromRight">
          <div class="formTitle" v-html="editTitle"></div>
          <el-form ref="form" :model="form" :rules="rules" :label-width="formLabelWidth" :validate-on-rule-change="false">
            <!-- <el-form-item class="bgc" label="编码" prop="code">
              <el-input class="formCode" :disabled="butType !=='addNodeBut'" placeholder="请输入编码" oninput="value=value.replace(/[^\w_-]/ig,'')" v-model.trim='form.code' show-word-limit maxlength="20"></el-input>
            </el-form-item> -->
            <el-form-item class="bgc" label="节点类型" prop="trainCode">
              <el-select v-model="form.trainCode" :disabled="true">
                <el-option v-for="(item,index) in trainTypeList" :key="index" :label="item.name" :value="item.code"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="中文名称" prop="nameCh">
              <el-input v-model.trim="form.nameCh" placeholder="请输入中文名称" show-word-limit maxlength="50"></el-input>
            </el-form-item>
            <el-form-item v-if="form.trainCode == 'train'" label="内部代号" prop="alias">
              <el-input v-model.trim="form.alias" placeholder="请输入内部代号" show-word-limit maxlength="50"></el-input>
            </el-form-item>
            <!-- <el-form-item label="英文名称" prop="nameEn">
              <el-input v-model.trim="form.nameEn" placeholder="请输入英文名称" oninput="value=value.replace(/[^\w]/g,'')" show-word-limit maxlength="50"></el-input>
            </el-form-item> -->
            <!-- 2020-08-02 添加适用国家carTypeList -->
            <el-form-item v-if="form.trainCode == 'train'" label="车型类型" prop="carType">
              <el-select v-model="form.carType" filterable>
                  <el-option v-for="(item, index) of carTypeList" :key="index" :label="item.name" :value="item.code"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item v-if="form.trainCode == 'train'" label="适用国家" prop="country">
              <el-select v-model="form.country" clearable filterable placeholder="请选择">
                  <el-option v-for="(item, index) of userCountryList" :key="index" :label="item.name" :value="item.code"></el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="图片" prop="image">
              <el-upload
                class="upload-demo"
                action="#"
                :on-success="handlesuccess"
                :on-remove="handleRemove"
                :before-remove="beforeRemove"
                :before-upload="beforeAvatarUpload"
                :on-exceed="handleExceed"
                :limit="1"
                :file-list="fileList"
                :http-request="uploadCar"
                accept=".JPG, .PNG, .JPEG,.jpg, .png, .jpeg"
                list-type="picture"
              >
                <el-button size="min" icon="el-icon-upload" type="primary">选择图片</el-button>
              </el-upload>
              <!-- <img :src="imgurl" alt="" style="width:80px;"> -->
            </el-form-item>
            <el-form-item label="说明" prop="remark">
              <el-input show-word-limit maxlength="50" placeholder="请输入说明" v-model.trim="form.remark"></el-input>
            </el-form-item>
            <el-form-item label="排序" prop="sort">
              <el-input type="number" placeholder="请输入排序"  :min="1" :max="9999"  @input="e => form.sort=parserNumber(e,1,9999)" v-model.trim="form.sort"></el-input>
            </el-form-item>
            <el-form-item label="生效状态" prop="useFlag" id="state_whether">
              <el-switch v-model="form.whether"></el-switch>
            </el-form-item>
            <el-form-item class="butArea">
              <el-button v-show="butType===''" type="primary" @click="preserve('form')">保存</el-button>
              <el-button v-show="butType==='addNodeBut'" type="primary" @click="preserve('form')">确定</el-button>
              <el-button v-show="butType==='addNodeBut'" @click="resetForm()">重置</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>

      <el-dialog v-dialogDrag title="批量上传" :visible.sync="dialogFileVisible">
        <div style="width: 100%; height: 100%; ">
          <!-- 选择主机厂 -->
        <el-form    >
          <el-form-item label="主机厂" prop="firmId">
            <el-select v-model="firmId" clearable filterable>
              <el-option v-for="(item, index) of listdata" :key="index" :label="item.nameCh" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <!-- 选择文件 -->
          <div style="margin: auto; justify-content: center; display: flex;">
                <el-upload
                  class="upload-demo inline-block"
                  ref="elUpload"
                  action="#"
                  :show-file-list="false"
                  multiple
                  :limit="1"
                  :file-list="excelList"
                  :before-upload="onBeforeUpload"
                  :http-request="uploadFile"
                  :on-change="onUploadChange"
                >
                <el-button v-if="hasPerm('menuAsimss2A1B_107')">选择文件</el-button>
              </el-upload>
            </div>
        </div>
        <div style="height: 30px;"></div>

      </el-dialog>

    </div>
  </div>
</template>
<script>
import { sysServerUrl, contentSize, handleAlert } from '@/assets/js/common.js'
import { importAttach, trainData, trainTypeDate, getTrainInfo, trainAdd, trainUpdate,trainDel,userCountryData, importTrain, trainTemplate, trainCarTypeList } from '@/api/sysmgt.js'
export default {
  name: 'carmgttrainlist',
  data () {
    return {
      defaultProps: {
        children: 'children',
        label: 'nameCh'
      },
      nodeKeyList:[],
      editTitle: '当前信息',
      sltNodeId: '',
      sltNodeType: '',
      sltNodeName: '',
      form: {
        id: '',
        pid: '',
        code: '',
        trainCode: '',
        nameCh: '',
        nameEn: '',
        alias: '',
        image: '',
        imgName: '',
        remark: '',
        sort: 1,
        country: 'cn',
        carType: '1',
        useFlag: 1,
        whether: true
      },
      carTypeList: [],
      butType: '',
      listdata: [],
      userCountryList: [],
      trainTypeList: [],
      formLabelWidth: '100px',
      fileList: [],
      firmId: '',
      dialogFileVisible: false,
      excelList: [],
      isFlag: true,
      rules: {
        // code: [{ required: true, message: '编码不能为空', trigger: ['blur', 'change'] }],
        trainCode: [{ required: true, message: '节点类型不能为空', trigger: ['blur', 'input'] }],
        nameCh: [{ required: true, message: '中文名称不能为空', trigger: ['blur', 'change'] }]
      }
    }
  },
  methods: {
    // 查询所有车系
    dataList () {
      trainData().then(res => {
        this.listdata = res.data.data
        this.$nextTick(() => {
          this.$refs.tree.setCurrentKey(this.form.id);
        });
      })
    },
    getTrainTypeList () {
      trainTypeDate().then(res =>{
        if (res.data.code === 100) {
          this.trainTypeList = res.data.data
        }
      })
    },
    getCarTypeList(){
      trainCarTypeList().then(res => {
        this.carTypeList = res.data.data
      })
    },
    // 获取国家
    getUserCountryList(){
        userCountryData().then(res=>{
        this.userCountryList = res.data.data
      })
    },
    resetTemp () {
      let code = this.form.trainCode
      let pid = this.form.pid
      this.form = {
        id: '',
        pid: pid,
        code: '',
        trainCode: code,
        nameCh: '',
        nameEn: '',
        alias: '',
        image: '',
        imgName: '',
        remark: '',
        sort: 1,
        country: 'cn',
        carType: '1',
        useFlag: 1,
        whether: true
      },
      this.$nextTick(function() {
        this.$refs.form.clearValidate();
      })
    },
    addRoot(){
      this.resetTemp()
      // $(".formCode > input").removeAttr("readonly")
      this.butType='addNodeBut'
      this.editTitle='新增主机厂根节点'
      this.fileList = []
      this.form.trainCode='firm'
      this.form.pid='0'
      this.form.id = ''
      this.form.code = ''
      this.form.nameCh = ''
      this.form.alias = ''
      this.form.nameEn = ''
      this.form.image = ''
      this.form.remark = ''
      this.form.sort = 1
      this.sltNodeId=''
      this.sltNodeType=''
      this.sltNodeName =''
      this.form.country = 'cn'
      this.form.carType = '1'
      this.form.useFlag = 1
      this.form.whether = true
    },
    addSub(){
      if(this.sltNodeId==''){
        handleAlert('warning','请先选中父级节点')
        return false
      }
      if(this.sltNodeType=='train'){
        handleAlert('warning','选中的父级节点不能为车系节点')
        return false
      }
      this.resetTemp()
      if(this.sltNodeType=='firm'){
        this.form.trainCode = 'brand'
      }else if(this.sltNodeType=='brand'){
        this.form.trainCode = 'train'
      }
      // $(".formCode > input").removeAttr("readonly")
      this.butType='addNodeBut'
      this.editTitle='当前选中父级节点：'+this.sltNodeName
      this.fileList = []
      this.form.pid=this.sltNodeId
      this.form.id = ''
      this.form.code = ''
      this.form.nameCh = ''
      this.form.nameEn = ''
      this.form.alias = ''
      this.form.image = ''
      this.form.remark = ''
      this.form.country = 'cn'
      this.form.carType = '1'
      this.form.sort = 1
      this.sltNodeId=''
      this.sltNodeType=''
      this.sltNodeName =''
      this.form.useFlag = 1
      this.form.whether = true
    },
    del(){
      var _this=this
      if(this.sltNodeId==''){
        handleAlert('warning','请先选中要删除的节点')
        return false
      }else{
        _this.$confirm('确定删除【' +  this.sltNodeName + '】的目录节点?', '删除目录节点', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          //删除节点
          _this.deleteNode(this.sltNodeId)
        }).catch((error)=>{
          handleAlert('info','取消删除')
        })
      }
    },
    // 显示详情，点击获取当前车系信息
    handleNodeClick (data) {
      this.resetTemp()
      $(".formCode > input").attr("background-color","readonly");
      this.fileList = []
      var params = '?id=' + data.id
      this.butType=''
      this.editTitle='当前信息'
      getTrainInfo(params).then(res => {
        if(res.data.code == '100'){
          this.sltNodeId=res.data.data.id
          this.sltNodeType= res.data.data.train_code
          this.sltNodeName =res.data.data.name_ch
          this.form.id = res.data.data.id
          this.form.pid= res.data.data.pid
          this.form.code = res.data.data.code
          this.form.trainCode = res.data.data.train_code
          this.form.nameCh = res.data.data.name_ch
          this.form.nameEn = res.data.data.name_en
          this.form.alias = res.data.data.alias
          this.form.image = res.data.data.image
          this.form.remark = res.data.data.remark
          this.form.country = res.data.data.countryCodeList[0]
          this.form.carType = res.data.data.car_type ? res.data.data.car_type.toString() : '1'
          this.form.whether = res.data.data.use_flag == 1
          this.form.sort = res.data.data.sort
          if(this.form.image !=null &&this.form.image.length>0){
            var clickName = { url: sysServerUrl + 'sys/upload/display?filePath=' + this.form.image }
            this.fileList.push(clickName)
          }
        }
      })
    },
    handlesuccess (file, fileList) {
      this.form.image = file.data.fileUrl
      this.fileList = []
      if(this.form.image != null && this.form.image.length > 0){
        var clickName = { url: sysServerUrl + 'sys/upload/display?filePath=' + this.form.image }
        this.fileList.push(clickName)
      }
      this.isFlag = true;
    },
    beforeAvatarUpload (file) {

      var fileName = file.name.substring(file.name.lastIndexOf(".")+1).toLowerCase()
      const extension = fileName === 'png'
      const extension2 = fileName === 'jpg'
      const extension3 = fileName === 'jpeg'
      const extension4 = fileName === 'gif'
      const isLt2M = file.size / 1024 / 1024 < 5
      if (!extension && !extension2 && !extension3 && !extension4) {
        handleAlert('warning','上传模板只能是 png、jpg、jpeg、gif格式!')
        this.isFlag = false;
        return false;
      }
      if (!isLt2M) {
        handleAlert('warning','上传模板大小不能超过 5MB!')
        this.isFlag = false;
        return false;
      }
      // return isLt2M
    },

    uploadCar(param){
      var _this= this
			var formData = new FormData();
			formData.append('file', param.file);
      formData.append('flag', 'carTrain');
      importAttach(formData).then(res => {
        if (res.data.code === 100) {
          _this.form.image = res.data.data.fileUrl
          _this.fileList = []
          if(_this.form.image != null && _this.form.image.length > 0){
            var clickName = { url: sysServerUrl + 'sys/upload/display?filePath=' + _this.form.image }
            _this.fileList.push(clickName)
          }

          _this.isFlag = true;
        }
      }).catch(function(error){
      })
    },

    handleRemove (file, fileList) {
      if(fileList.length == '0'){
        this.fileList = []
        this.form.image = ''
        this.isFlag = true;
      }
    },
    handleExceed (files, fileList) {
      handleAlert('warning',`当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
    },
    beforeRemove (file, fileList) {
      if(this.isFlag){
        return this.$confirm(`确定移除选择文件？`, '删除',{type: 'warning'});
      }
    },
    preserve(form){
      var _this=this
      this.nodeKeyList = []
      this.$refs[form].validate((valid) => {
        if (valid) {
          this.$loading.show()
          var params = new URLSearchParams()
          params.append('id', this.form.id)
          params.append('pid', this.form.pid)
          params.append('code',this.form.code)
          params.append('carType',this.form.carType)
          if (this.form.trainCode == 'train' && this.form.country && this.form.country.length>0) {
            params.append('sltCountry',this.form.country.toString())
          }

          params.append('trainType', 'trainType')
          params.append('useFlag', this.form.whether ? 1 : 0)
          params.append('trainCode', this.form.trainCode)
          params.append('nameCh', this.form.nameCh)
          params.append('nameEn', this.form.nameEn)

          if(this.form.alias !== null && this.form.alias !== undefined){
            params.append('alias', this.form.alias)
          }
          if(this.form.image){
            params.append('image', this.form.image)
          }

          if(this.form.remark !== null && this.form.remark !== undefined){
            params.append('remark', this.form.remark)
          }

          params.append('sort', this.form.sort)
          if(this.form.id!=null && this.form.id!='' && this.form.id!='null'){
            _this.updateNode(params)
            this.nodeKeyList.push(this.form.id)
          }
          else{
            _this.addNode(params)
            this.nodeKeyList.push(this.form.pid)
          }
        } else {
          this.$loading.hide()
          handleAlert('error','请完善表单信息')
        }
      })
    },
    // 新增节点
    addNode(params){
      var _this=this
      trainAdd(params).then(res => {
        if(res.data.code==100){
          handleAlert('success','新增节点成功')
          _this.dataList()
          this.butType=''
          this.editTitle='当前信息'
          // this.resetTemp()
        }else{
          handleAlert('error',res.data.msg)
        }
        this.$loading.hide()
      }).catch(function(error){
        handleAlert('error','新增节点失败')
        this.$loading.hide()
      })
    },
    // 修改
    updateNode (params) {
      var _this=this
      trainUpdate(params).then(res => {
        if(res.data.code==100){
          handleAlert('success','保存节点信息成功')
          _this.dataList()
        }else{
          handleAlert('error',res.data.msg)
        }
        this.$loading.hide()
      }).catch(function(error){
        handleAlert('error','保存节点信息失败')
        this.$loading.hide()
      })
    },
    // 删除节点
    deleteNode(nodeId){
      var _this=this
      var params = new URLSearchParams()
      params.append('id', nodeId)
      trainDel(params).then(res => {
        if(res.data.code==100){
          handleAlert('success','删除节点成功')
          _this.dataList()
          this.nodeKeyList.push(_this.form.pid)
          _this.resetTemp()
          _this.form.trainCode = '';
          _this.form.carType = '';
        }else{
          handleAlert('error', '删除失败：' + res.data.msg)
        }
      }).catch(function(error){
        handleAlert('error','删除节点失败,出现异常')
      })
    },
    // 重置
    resetForm () {
      this.resetTemp()
    },


    // ============= 批量上传
    uploadTrain(){
      this.firmId = null;
      this.dialogFileVisible = true;
    },
    // 附件上传
    onBeforeUpload(file) {
      if (this.firmId == null || this.firmId.length <=0) {
        handleAlert('warning', "请选择主机厂")
        return false
      }
      var fileExt =file.name.substring(file.name.lastIndexOf('.')+1).toLowerCase();
      var text=""
      const docExt = fileExt === 'xls'
      const docxExt = fileExt === 'xlsx'
      const isLimit = file.size / 1024 / 1024 < 2
      if(!docxExt) {
        text="上传文件只能是xlsx格式!";
        handleAlert('warning',text)
        return false;
      }
      if (!isLimit) {
        text="上传文件大小不能超过 2MB!";
        handleAlert('warning',text)
        return false;
      }
      return true;
    },
    onUploadChange(file){
      //this.files.push(file.raw)
    },
    // 批量上传
    uploadFile (param) {
      var _this= this
			var formData = new FormData();
			formData.append('file', param.file);
      formData.append('firmId', _this.firmId);
      importTrain(formData).then(res => {
        if (res.data.code === 100) {
          handleAlert('success','批量导入成功')
          this.dataList()
          this.dialogFileVisible = false;
        }else{
          _this.$alert(res.data.msg,'导入失败',{dangerouslyUseHTMLString:true})
        }
        _this.excelList = []
      }).catch(function(error){
        _this.excelList = []
        _this.$alert('系统出现异常，导入失败','信息提示',{dangerouslyUseHTMLString:true})
      })
		},
    // 下载车型模板
    downModelClick () {

      trainTemplate().then(res => {
                  if(!res.data){
                       return
                   }
                  var name = "车型模板.xlsx";
                  var blob = new Blob([res.data]);
                  var url = window.URL.createObjectURL(blob);
                  var aLink = document.createElement("a");
                  aLink.style.display = "none";
                  aLink.href = url;
                  aLink.setAttribute("download", name);
                  document.body.appendChild(aLink);
                  aLink.click();
                  document.body.removeChild(aLink); //下载完成移除元素
                  window.URL.revokeObjectURL(url); //释放掉blob对象
                })

    },
  },
  mounted () {
    this.dataList()
    this.getTrainTypeList()
    this.getUserCountryList()
    this.getCarTypeList()
    contentSize()
  },
}
</script>
<style>
  .bgc .el-input.is-disabled .el-input__inner {
    background-color: #F4F4F5;
  }
  #state_whether .el-switch {
    height: 38px !important;
    line-height: 32px !important;
  }
</style>
