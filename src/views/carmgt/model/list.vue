<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" ref="formInline" label-width="50px" :model="formInline" class="demo-form-inline">
        <el-form-item label="车型" prop="carTrain">
          <!-- <el-input v-model.trim="formInline.carTrain" placeholder="请输入车型"></el-input> -->
          <el-select v-model="formInline.carTrain" placeholder="请选择车型" clearable filterable>
              <el-option-group v-for="group in brandTree" :key="group.id" :label="group.nameCh">
                  <el-option v-for="item in group.children" :key="item.id" :label="item.nameCh" :value="item.id"></el-option>
              </el-option-group>
          </el-select>
        </el-form-item>
        <el-form-item label="年款" prop="year">
          <el-input v-model.trim="formInline.year" placeholder="请输入年款"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit" icon="el-icon-search">搜索</el-button>
          <el-button plain @click="reset('formInline')">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <div class="tableHandle" v-if="hasPerm('menuAsimss2A2B_101') || hasPerm('menuAsimss2A2B_107') || hasPerm('menuAsimss2A2B_108')">
        <!-- <el-button type="text" v-if="hasPerm('menuAsimss2A2B_101')" icon="el-icon-plus" @click="handeladd()">新增</el-button> -->
        <el-button type="text" @click="addYear()" icon="el-icon-plus">新增年款</el-button>
        <el-upload
          class="upload-demo inline-block"
          ref="melUpload"
          action="#"
          :show-file-list="false"
           multiple
          :limit="1"
          :file-list="fileList"
          :before-upload="onBeforeUpload"
          :http-request="uploadModel"
          accept="xls、xlsx"
        >
          <el-button type="text" v-if="hasPerm('menuAsimss2A2B_107')" icon="bulkImport-icon">批量导入配置</el-button>
        </el-upload>
        <el-button type="text" v-if="hasPerm('menuAsimss2A2B_107') || hasPerm('menuAsimss2A2B_108')" icon="el-icon-download" @click="downModelClick()">下载配置模板</el-button>
        <!-- <el-upload
          class="upload-demo inline-block"
          ref="celUpload"
          action="#"
          :show-file-list="false"
           multiple
          :limit="1"
          :file-list="fileList"
          :before-upload="onBeforeUpload"
          accept="xls、xlsx"
          :http-request="uploadConfig"
        >
          <el-button type="text" v-if="hasPerm('menuAsimss2A2B_107')" icon="bulkImport-icon">批量导入配置</el-button>
        </el-upload>
        <el-button type="text" v-if="hasPerm('menuAsimss2A2B_108')" icon="el-icon-download" @click="downConfigClick()">下载配置模板</el-button> -->
      </div>
      <el-table
        style="width:100%;"
        border
        ref="table"
        highlight-current-row
        :max-height="maximumHeight"
        :data="resultList"
        row-key="id"
        default-expand-all
        :tree-props="{children:'children',hasChildren:'hasChildren'}"
        :row-style="rowStyle"
        :header-cell-style="{
          'text-align': 'center',
          'background-color': 'var(--other-color)',
        }"
        @header-dragend="changeColWidth"
      >
        <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
        <el-table-column label="品牌" prop="brandName" min-width="150" show-overflow-tooltip></el-table-column>
        <el-table-column label="车型" prop="trainName" min-width="150" show-overflow-tooltip></el-table-column>
        <el-table-column label="年款" prop="year" min-width="100" show-overflow-tooltip></el-table-column>
        <!-- <el-table-column label="编码" prop="code" min-width="150" show-overflow-tooltip></el-table-column> -->
        <el-table-column label="配置名称" prop="nameCh" min-width="150" show-overflow-tooltip>
          <template slot-scope="{row}">
            <span v-if="row.modelType === 0"></span>
            <span v-else >{{ row.nameCh }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column label="英文名称" prop="nameEn" min-width="150"></el-table-column> -->
        <!-- <el-table-column label="图片" prop="image" width="100">
          <template slot-scope="{row}">
            <span v-if="row.image == null || row.image === ''">无</span>
            <el-button v-if="row.image !== null && row.image !== ''" type="text" size="small" @click="check(row)">查看</el-button>
          </template>
        </el-table-column> -->
        <el-table-column label="排序" prop="sort" width="60"  align="center"></el-table-column>
        <el-table-column label="状态" prop="useFlag" width="60" align="center">
          <template slot-scope="{row}">
            <span v-if="row.useFlag === 1" style="color:#009933">生效</span>
            <span v-else style="color:#c30000">失效</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="260">
          <template slot-scope="{row}">
            <!-- <el-button type="text" v-if="hasPerm('menuAsimss2A2B_104') && row.modelType == '1'" size="small" @click="handelDetile(row)">详情</el-button> -->
            <el-button type="text" v-if="hasPerm('menuAsimss2A2B_101') && row.modelType == '0'" size="small" @click="handeladd(row)">新增配置</el-button>
            <el-button type="text" v-if="hasPerm('menuAsimss2A2B_103') && row.useFlag==1" size="small" @click="forbidden(row)">停用</el-button>
            <el-button type="text" v-if="hasPerm('menuAsimss2A2B_103') && row.useFlag==0" size="small" @click="editUseFlag(row, 1)">启用</el-button>
            <!-- 年款的编辑 -->
            <el-button type="text" v-if="hasPerm('menuAsimss2A2B_103') && row.modelType == '0'" size="small" @click="editYearHand(row)">编辑</el-button>
            <el-button type="text" v-if="hasPerm('menuAsimss2A2B_103') && row.modelType == '1'" size="small" @click="handeledit(row)">编辑</el-button>
            <!-- <el-button type="text" v-if="hasPerm('menuAsimss2A2B_103') && row.modelType == '1'" size="small" @click="config(row)">车型配置</el-button> -->
            <el-button type="text" v-if="hasPerm('menuAsimss2A2B_102')" size="small" @click="handeldelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total>0" :total="total" :page.sync="currentPage" :limit.sync="pagesize" @pagination="dataList"/>
      <el-dialog v-dialogDrag :width="dialogStatus == 'config' ? '700px !important' : ''"  :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" v-if="isDialog">
        <!-- 查看 -->
        <div v-if="dialogStatus === 'check'" style="text-align: center; " >
          <img :src="urlImg" alt="" style="width:90%;height:70%">
        </div>
        <!-- 详情 -->
        <el-form v-if="dialogStatus === 'detail'" :rules="rules" ref='dataForm' :label-width="formLabelWidth" :model="dataForm" label-position="center" :validate-on-rule-change="false">
          <el-form-item label="车型: " prop="trainId">
            <span>{{ dataForm.trainName }}</span>
            <!-- <el-input v-model="dataForm.trainName" readonly></el-input> -->
          </el-form-item>
          <el-form-item label="年款: " prop="year">
            <span>{{ dataForm.year }}</span>
            <!-- <el-input v-model="dataForm.year" readonly></el-input> -->
          </el-form-item>
          <!-- <el-form-item label="编码: " prop="code">
            <span>{{ dataForm.code }}</span>

          </el-form-item> -->
          <el-form-item label="配置名称: " prop="nameCh">
            <span>{{ dataForm.nameCh }}</span>
            <!-- <el-input v-model="dataForm.nameCh" readonly></el-input> -->
          </el-form-item>
          <!-- <el-form-item label="英文名称" prop="nameEn">
            <el-input v-model="dataForm.nameEn" readonly></el-input>
          </el-form-item> -->
          <!-- <el-form-item label="别名: " prop="alias">
            <span>{{ dataForm.alias }}</span>
            <el-input v-model="dataForm.alias" readonly></el-input>
          </el-form-item> -->
          <el-form-item label="排序: " prop="sort">
            <span>{{ dataForm.sort }}</span>
            <!-- <el-input v-model="dataForm.sort" readonly></el-input> -->
          </el-form-item>
          <!-- <el-form-item label="图片" prop="image" :label-width="formLabelWidth">
            <div v-if="dataForm.image !==null && dataForm.image !=='' && dataForm.image !== 'null' && imgList !=null && imgList.length>0 ">
              <div v-for="(item, index) in imgList" :key="index">
                <img :src="item.url" class="imgShow" >
              </div>
            </div>
            <div v-if="dataForm.image == null || dataForm.image == '' || dataForm.image == 'null' || imgList == null ||  imgList.length== 0 " >
              无
            </div>
          </el-form-item> -->
        </el-form>
        <!-- 编辑 -->
        <el-form v-if="dialogStatus === 'edit'" :rules="rules" :label-width="formLabelWidth"  ref='dataForm' :model="dataForm" label-position="center" :validate-on-rule-change="false">
          <!-- <el-form-item label="车系" prop="trainId">
            <el-input v-model.trim="dataForm.trainName" placeholder="请输入车系" :disabled="true"></el-input>
          </el-form-item>
          <el-form-item label="车型年款" prop="year">
            <el-input v-model="dataForm.year"  @input="e => dataForm.year= validForbid(e)" show-word-limit maxlength="100"></el-input>
          </el-form-item> -->
          <el-form-item label="车型" prop="trainId">
            <el-input v-model.trim="dataForm.trainName" readonly disabled></el-input>
          </el-form-item>
          <el-form-item label="年款" prop="year">
            <el-input v-model.trim="dataForm.year" readonly disabled></el-input>
          </el-form-item>
          <!-- <el-form-item label="编码" prop="code">
            <el-input v-model.trim="dataForm.code"  @input="e => dataForm.code= validForbid(e)"  disabled placeholder="请输入编码"></el-input>
          </el-form-item> -->
          <el-form-item label="配置名称" prop="nameCh">
            <el-input v-model.trim="dataForm.nameCh" placeholder="请输入配置名称" show-word-limit maxlength="20"></el-input>
          </el-form-item>
          <!-- <el-form-item label="英文名称" prop="nameEn">
            <el-input show-word-limit maxlength="50" placeholder="请输入英文名称" oninput="value=value.replace(/[^\w]/g,'')" v-model.trim="dataForm.nameEn"></el-input>
          </el-form-item> -->
          <!-- <el-form-item label="别名" prop="alias">
            <el-input show-word-limit maxlength="20" placeholder="请输入别名" v-model.trim="dataForm.alias"></el-input>
          </el-form-item> -->
          <el-form-item label="排序" prop="sort">
            <el-input type="number" :min="1" :max="9999"  @input="e => dataForm.sort=parserNumber(e,1,9999)" v-model.trim="dataForm.sort" placeholder="请输入排序"></el-input>
          </el-form-item>
          <!-- <el-form-item label="状态" prop="useFlag">
              <el-switch v-model="dataForm.whether"></el-switch>
            </el-form-item> -->
          <!-- <el-form-item label="图片" prop="image">
            <el-upload
              class="upload-demo"
              style="max-width: 379px;"
              :action="uploadUrl"
              :on-success="handlesuccess"
              :on-remove="handleRemove"
              :before-remove="beforeRemove"
              :before-upload="beforeAvatarUpload"
              :on-exceed="handleExceed"
              multiple
              :limit="1"
              :file-list="imgList"
              accept=".JPG, .PNG, .JPEG,.jpg, .png, .jpeg"
              list-type="picture"
            >
              <el-button size="min" icon="el-icon-upload" type="primary">选择图片</el-button>
            </el-upload>
          </el-form-item> -->
          <div class="submitArea">
            <el-button type="primary" @click="editClick('dataForm')">
              立即提交
            </el-button>
            <el-button @click="editResetForm()" v-if="false">
              重置
            </el-button>
          </div>
        </el-form>
        <!-- 新增 -->
        <el-form v-if="dialogStatus === 'add'" :rules="rules" ref='dataForm' :label-width="formLabelWidth" :model="dataForm" label-position="center" :validate-on-rule-change="false">
          <!-- <el-form-item label="车系" prop="trainId">
            <select-tree ref="addSelectTree"
            :options="trainList"
            v-model="dataForm.trainId"
            :props="defaultProps"
            @slectNode="slectTreeNode"
            placeholder="请选择车系"  />
          </el-form-item>
          <el-form-item label="车型年款" prop="year">
            <el-input v-model.trim="dataForm.year"
              show-word-limit maxlength="100"  @input="e => dataForm.year= validForbid(e)"
              placeholder="请输入车型年款"></el-input>
          </el-form-item> -->
          <el-form-item label="车型" prop="trainId">
            <el-input v-model.trim="dataForm.trainName" readonly disabled></el-input>
          </el-form-item>
          <el-form-item label="年款" prop="year">
            <el-input v-model.trim="dataForm.year" readonly disabled></el-input>
          </el-form-item>
          <!-- <el-form-item label="编码" prop="code">
            <el-input v-model.trim="dataForm.code" @input="e => dataForm.code= validForbid(e)" show-word-limit maxlength="100" placeholder="请输入编码"></el-input>
          </el-form-item> -->
          <el-form-item label="配置名称" prop="nameCh">
            <el-input v-model.trim="dataForm.nameCh" show-word-limit maxlength="20" placeholder="请输入配置名称"></el-input>
          </el-form-item>
          <!-- <el-form-item label="英文名称" prop="nameEn">
            <el-input v-model.trim="dataForm.nameEn" show-word-limit maxlength="50" oninput="value=value.replace(/[^\w]/g,'')" placeholder="请输入英文名称"></el-input>
          </el-form-item> -->
          <!-- <el-form-item label="别名" prop="alias">
            <el-input v-model.trim="dataForm.alias" show-word-limit maxlength="20"  placeholder="请输入别名"></el-input>
          </el-form-item> -->
          <el-form-item label="排序" prop="sort">
            <el-input type="number" :min="1" :max="9999"  @input="e => dataForm.sort=parserNumber(e,1,9999)"  v-model="dataForm.sort" placeholder="请输入排序"></el-input>
          </el-form-item>
          <el-form-item label="生效状态" prop="useFlag">
            <el-switch v-model="dataForm.whether" ></el-switch>
          </el-form-item>
          <!-- <el-form-item label="图片" prop="image">
              <el-upload
                class="upload-demo"
                style="max-width: 379px;"
                :action="uploadUrl"
                :on-success="handlesuccess"
                :on-remove="handleRemove"
                :before-remove="beforeRemove"
                :before-upload="beforeAvatarUpload"
                :on-exceed="handleExceed"
                multiple
                :limit="1"
                :file-list="imgList"
                accept=".JPG, .PNG, .JPEG,.jpg, .png, .jpeg"
                list-type="picture"
              >
                <el-button size="min" icon="el-icon-upload" type="primary">选择图片</el-button>
              </el-upload>
          </el-form-item> -->
          <div class="submitArea">
            <el-button type="primary" @click="addClick('dataForm')">
              立即提交
            </el-button>
            <el-button @click="resetForm()">
              重置
            </el-button>
          </div>
        </el-form>
        <!-- 车型配置 -->
        <el-form v-if="dialogStatus === 'config'">
          <div class="tableHandle">
            <el-button type="text" icon="el-icon-plus" v-if="hasPerm('menuAsimss2A2B_101')" @click="addConfig">新增</el-button>
            <el-button type="text"  icon="el-icon-delete" v-if="hasPerm('menuAsimss2A2B_102')" @click="delAllConfig">删除全部配置</el-button>
          </div>
          <el-table
            style="width:800px"
            border
            highlight-current-row
            stripe
            :data="modelCfgList"
            :header-cell-style="{
              'text-align': 'center',
              'background-color': 'var(--other-color)',
            }"
          >
            <el-table-column label="配置代码" min-width="160">
              <template slot-scope="{row}">
                <el-input v-model.trim="row.code" oninput="value=value.replace(/^\s+|\s+$/g,'')"  show-word-limit maxlength="50" style="padding:0 38px 0 0" placeholder="请输入车型代码"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="配置名称" min-width="150">
              <template slot-scope="{row}">
                <el-input v-model.trim="row.alias" oninput="value=value.replace(/^\s+|\s+$/g,'')"  show-word-limit maxlength="50" style="padding:0 38px 0 0" :value="row.alias" placeholder="请输入手册编码"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="排序" width="80">
              <template slot-scope="{row}">
                <el-input type="number"  :min="1" :max="9999"  @input="e => row.sort=parserNumber(e,1,9999)"  v-model="row.sort" :value="row.sort" placeholder="请输入排序号"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template slot-scope="{row}">
                <el-button type="text" size="small" @click="handelSaveConfig(row)">保存</el-button>
                <el-button type="text" size="small" v-if="hasPerm('menuAsimss2A2B_102')" @click="handelDelConfig(row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form>
      </el-dialog>
      <el-dialog v-dialogDrag width="700px !important" title="新增年款" :visible.sync="dialogYearFormVisible" :before-close="closeYearForm" :destroy-on-close="true" v-if="dialogYearFormVisible">
        <el-form :rules="rulesYear" :label-width="formLabelWidth" :model="yearForm" ref="rulesYear">
          <el-form-item label="车型" prop="trainId">
            <select-tree ref="addSelectTree"
            :options="trainList"
            v-model="yearForm.trainId"
            :props="defaultProps"
            :expand_on_click_node="true"
            :check_on_click_node="false"
            @slectNode="slectTreeNode"
            placeholder="请选择车型"  />
            <!-- <el-select v-model="clearData.carTrainId" placeholder="请选择车系" @change="getTrainYearList" clearable filterable>
              <el-option-group v-for="group in trainList" :key="group.id" :label="group.nameCh">
                <el-option v-for="item in group.children" :key="item.id" :label="item.nameCh" :value="item.id"></el-option>
              </el-option-group>
            </el-select> -->
          </el-form-item>
          <el-form-item label="年款" prop="year">
            <el-input v-model.trim="yearForm.year" @change="codeValue"
              show-word-limit maxlength="50"
              placeholder="请输入年款"></el-input>
          </el-form-item>
          <!-- <el-form-item label="编码" prop="code">
            <el-input v-model.trim="yearForm.code" readonly disabled placeholder="请输入编码"></el-input>
          </el-form-item> -->
          <!-- <el-form-item label="名称" prop="nameCh">
            <el-input v-model.trim="yearForm.nameCh" readonly disabled placeholder="请输入名称"></el-input>
          </el-form-item> -->
          <!--    20241116-EPC内部需求-楚文龙  1、目录管理中，车型下面显示改车型的“说明”，建立车型时的说明。2、配置管理中，车型年款，也增加个“说明”。3、目录管理中，年款下面也显示改年款的“说明”，建立年款时的说明      -->
          <el-form-item label="说明" prop="remark">
            <el-input v-model.trim="yearForm.remark"
                      show-word-limit maxlength="50"
                      placeholder="请输入说明"></el-input>
          </el-form-item>
          <el-form-item label="排序" prop="sort">
            <el-input type="number" :min="1" :max="9999"  @input="e => yearForm.sort=parserNumber(e,1,9999)"  v-model="yearForm.sort" placeholder="请输入排序"></el-input>
          </el-form-item>
          <el-form-item label="生效状态" prop="useFlag">
            <el-switch v-model="yearForm.whether" ></el-switch>
          </el-form-item>
          <div class="submitArea">
            <el-button type="primary" @click="addYearClick()">
              立即提交
            </el-button>
          </div>
        </el-form>
      </el-dialog>


      <!-- 编辑年款 -->
      <el-dialog v-dialogDrag width="700px !important" title="编辑年款" :visible.sync="dialogEditYearFormVisible" :destroy-on-close="true">
          <el-form>
              <!-- <el-form-item label="国家" prop="country">
                <el-input v-model.trim="editYear.country" readonly disabled></el-input>
              </el-form-item> -->
              <el-form-item label="车型" prop="trainName">
                <el-input v-model.trim="editYear.trainName" readonly disabled></el-input>
              </el-form-item>
              <el-form-item label="年款" prop="year">
                <el-input v-model.trim="editYear.year" show-word-limit maxlength="50" placeholder="请输入年款"></el-input>
              </el-form-item>
            <el-form-item label="说明" prop="remark">
              <el-input v-model.trim="editYear.remark"
                        show-word-limit maxlength="50"
                        placeholder="请输入说明"></el-input>
            </el-form-item>
              <el-form-item label="排序" prop="sort">
            <el-input type="number" :min="1" :max="9999"  @input="e => editYear.sort=parserNumber(e,1,9999)"  v-model="editYear.sort" placeholder="请输入排序"></el-input>
          </el-form-item>
              <div class="submitArea">
                <el-button type="primary" @click="editYearClick()">
                  立即提交
                </el-button>
              </div>
          </el-form>
      </el-dialog>

    </div>
  </div>
</template>
<script>
import { sysServerUrl, handleAlert, tableHeight, tableMaxHeight } from '@/assets/js/common.js'
import $ from 'jquery'
import Pagination from '@/components/Pagination'
import SelectTree from '@/components/TreeView/SelectTree.vue';
import {
  modelData,
  modelTrainList,
  modelAdd,
  modelEdit,
  modelDel,
  modelImport,
  modelConfigData,
  configBatchDel,
  modelConfigDel,
  configSave,
  modelTemplate,
  configTemplate,
  modelYearAdd,
  editUseFlag,
  modelYearDel,
  modelEditYear,
} from '@/api/sysmgt.js'
export default {
  name: 'carmgtmodellist',
  components: { Pagination ,SelectTree },
  data () {
    return {
      formInline: {
        brand: '',
        carTrain: '',
        code: '',
        nameCh: '',
        year: ''
      },
      dataForm: {
        id: '',
        trainId: '',
        trainName: '',
        file: '',
        year: '',
        code: '',
        nameCh: '',
        nameEn: '',
        alias: '',
        modelType: '',
        marketTime: '',
        createdTime: '',
        createdUser: '',
        sort: 1,
        image: '',
        whether: true,
      },
      // 默认选中值
      sltTrainId: '',
      trainList: [],
      trainCode: '',
      // 数据默认字段
      defaultProps: {
        parent: 'pid',   // 父级唯一标识
        value: 'id',          // 唯一标识
        label: 'nameCh',       // 标签显示
        children: 'children', // 子级
      },
      imgList: [],
      isFlag: true,
      isDialog: false,
      fileList: [],
      urlImg: '',
      dialogFormVisible: false,
      formLabelWidth: '100px',
      dialogStatus: '',
      textMap: {
        edit: '编辑配置',
        add: '新增配置',
        detail: '详情信息',
        config: '配置',
        check: '图片'
      },
      resultList: [],
      pagesize: 10,
      currentPage: 1,
      total: 0,
      modelId: '',
      modelCfgList: [],
      rules: {
        trainId: [{ required: true, message: '车型不能为空', trigger: ['blur', 'change'] }],
        year: [{ required: true, message: '年款不能为空', trigger: ['blur', 'change'] }],
        code: [{ required: true, message: '编码不能为空', trigger: ['blur', 'change'] }],
        nameCh: [{ required: true, message: '名称不能为空', trigger: ['blur', 'change'] }]
      },
      rulesYear:{
        trainId: [{ required: true, message: '车型不能为空', trigger: ['blur', 'change'] }],
        year: [{ required: true,  message: '年款不能为空', trigger: ['blur', 'change'] }],
      },
      trainName: '',
      dialogYearFormVisible: false,
      yearForm: {
        trainId: '',
        year: '',
        code: '',
        nameCh: '',
        sort: 1,
        whether: true,
        remark:""
      },
      // 品牌-车型-年款树
      brandTree: [],
      yearList: [],
      maximumHeight: 0,

      dialogEditYearFormVisible: false,
      editYear:{
        country: '',
        trainName: '',
        id: '',
        year: '',
        sort: 1,
        remark:""
      }
    }
  },
  methods: {
    rowStyle({ row, rowIndex }) {
      let styleJson = {
        "background": "var(--cell-bgColor)",
      }
      if (row.children == null) {
        return styleJson;
      } else {
        return {};
      }
    },
    // 监听表格列宽变化
    changeColWidth(){
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    getTrainList () {
      modelTrainList().then(res => {
        this.trainList = res.data.data
        this.brandTree = []
        if (this.trainList && this.trainList.length > 0) {
          this.brandTree = this.trainList[0].children
        }
      })
    },
    // 数据
    dataList () {
      this.$loading.show();
      var params = new URLSearchParams()
      params.append('page', this.currentPage)
      params.append('limit', this.pagesize)
      params.append('trainId', this.formInline.carTrain)
      params.append('year', this.formInline.year)
      modelData(params).then(res => {
        if (res.data.code == 100) {
          this.total = res.data.total
          this.resultList = res.data.data
          if (!this.resultList) {
            this.resultList = []
          }
        } else {
          handleAlert('error', res.data.msg);
        }
        this.tableHeightArea()
        this.$loading.hide();
      })
    },
    slectTreeNode(v){
      // this.$refs.dataForm.validateField('trainId')
      this.trainCode = '';
      this.trainName = '';
      this.yearForm.code = '';
      this.yearForm.nameCh = '';
      this.yearForm.whether = true;
      for (let i = 0; i < this.trainList.length; i++) {
        for (let j = 0; j < this.trainList[i].children.length; j++) {
          for (let k = 0; k < this.trainList[i].children[j].children.length; k++) {
            if (this.trainList[i].children[j].children[k].id == v) {
              this.trainCode = this.trainList[i].children[j].children[k].code;
              this.trainName = this.trainList[i].children[j].children[k].nameCh;
            }
          }
        }

      }
      this.codeValue();
    },
    handlesuccess (file, fileList) {
      this.dataForm.image = file.data.fileUrl
      this.imgList = []
      if(this.dataForm.image != null && this.dataForm.image.length > 0){
        var img = {url: sysServerUrl + 'sys/upload/display?filePath=' + this.dataForm.image }
        this.imgList.push(img)
      }
      this.isFlag = true;
    },
    beforeAvatarUpload (file) {
      var fileName = file.name.substring(file.name.lastIndexOf(".")+1).toLowerCase()
      const extension = fileName === 'png'
      const extension2 = fileName === 'jpg'
      const extension3 = fileName === 'jpeg'
      const extension4 = fileName === 'gif'
      const isLt2M = file.size / 1024 / 1024 < 10
      if (!extension && !extension2 && !extension3 && !extension4) {
        handleAlert('warning','上传模板只能是 png、jpg、jpeg、gif格式!')
        this.isFlag = false;
        return false;
      }
      if (!isLt2M) {
        handleAlert('warning','上传模板大小不能超过 10MB!')
        this.isFlag = false;
        return false;
      }
      // return isLt2M
    },
    handleRemove (file, fileList) {
      if(fileList.length == "0"){
        this.imgList=[]
        this.dataForm.image = ''
        this.isFlag = true;
      }
    },
    handleExceed (files, fileList) {
      handleAlert('warning',`当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
    },
    beforeRemove (file, fileList) {
      if (this.isFlag) {
        return this.$confirm(`确定移除选择文件？`, '删除',{type: 'warning'});
      }
    },

    // 添加年款组
    addYear(){
      // 重置表单
      this.yearForm.whether = true;
      this.trainCode = '';
      this.trainName = '';
      this.yearForm.code = '';
      this.yearForm.nameCh = '';
      this.yearForm.year = '';
      this.yearForm.trainId = '';
      this.yearForm.sort = 1;
      let _this = this;
      this.yearForm.remark = '';
      // if (this.$refs.rulesYear) {
      //   this.$nextTick(function() {
      //   this.$refs.rulesYear.clearValidate();
      // })
      // }
      setTimeout(() => {
        _this.$refs.addSelectTree.initSelected('','')
      });
      // 打开弹窗
      this.dialogYearFormVisible = true;
    },
    closeYearForm(){
      this.yearForm.whether = true;
      this.trainCode = '';
      this.trainName = '';
      this.yearForm.code = '';
      this.yearForm.nameCh = '';
      this.yearForm.year = '';
      this.yearForm.trainId = '';
      this.yearForm.sort = 1;
      this.yearForm.remark = '';
      let _this = this;
      if (this.$refs.rulesYear) {
        this.$nextTick(function() {
        this.$refs.rulesYear.clearValidate();
      })
      setTimeout(() => {
        _this.$refs.addSelectTree.initSelected('','')
        _this.yearForm.trainId = '';
      });

      }
      this.dialogYearFormVisible = false
    },
    // 提交 添加年款组
    addYearClick(){
      if (!this.yearForm.trainId || this.yearForm.trainId == '') {
        handleAlert('error', '请选择车系');
        return false;
      }
      if (!this.yearForm.year || this.yearForm.year == '') {
        handleAlert('error', '请输入年款');
        return false;
      }
      if(!this.yearForm.code || this.yearForm.code == ''){
        handleAlert('error', '请选择车系');
        return false;
      }
      this.$loading.show()
      var params = new URLSearchParams()
      params.append('trainId', this.yearForm.trainId)
      params.append('trainYear', this.yearForm.year)
      params.append('sort', this.yearForm.sort)
      params.append('useFlag', this.yearForm.whether ? 1 : 0)
      if (this.yearForm.remark != null && this.editYear.remark != undefined){
        params.append('remark', this.yearForm.remark)
      }else{
        params.append('remark', "")
      }
      modelYearAdd(params).then(res => {
        if(res.data.code === 100){
          handleAlert('success','保存成功')
          this.dataList()
          this.dialogYearFormVisible = false
        }else{
          handleAlert('error',res.data.msg)
        }
        this.$loading.hide()
      }).catch(e => {
        handleAlert('error','系统开小差了，刷新一下吧')
        this.$loading.hide()
      })
    },
    // 搜索
    keyDown(e) {
      if (e.keyCode === 13) {
        this.onSubmit();
      }
    },
    onSubmit () {
      this.currentPage=1;
      this.dataList();
    },
    resetTemp () {
      let trainId = this.dataForm.trainId
      let trainName = this.dataForm.trainName
      let year = this.dataForm.year
      this.dataForm = {
        id: '',
        trainId: trainId,
        trainName: trainName,
        file: '',
        year: year,
        code: '',
        nameCh: '',
        nameEn: '',
        alias: '',
        modelType: '',
        marketTime: '',
        createdTime: '',
        createdUser: '',
        sort: 1,
        image: '',
        whether: true,
      }
      this.$nextTick(function() {
        this.$refs.dataForm.clearValidate();
      })
    },
    // 附件上传
    onBeforeUpload(file) {
      var _this =this
      var fileExt =file.name.substring(file.name.lastIndexOf('.')+1).toLowerCase();
      const docExt = fileExt === 'xls'
      const docxExt = fileExt === 'xlsx'
      const isLimit = file.size / 1024 / 1024 < 50
      if(!docExt && !docxExt) {
        handleAlert('warning',"上传的文件只能是 xls、xlsx格式!")
        return false;
      }
      if (!isLimit) {
        handleAlert('warning',"上传的文件大小不能超过 50MB!")
        return false;
      }
      return true;
    },
    // 批量上传车型
    uploadModel (param) {
        var _this = this
			  var formData = new FormData();
        formData.append('file', param.file);
        modelImport(formData).then(res => {
          _this.fileList = []
          if (res.data.code === 100) {
            handleAlert('success','批量导入成功')
            this.dataList()
          }else{
            _this.$alert(res.data.msg,'信息提示',{dangerouslyUseHTMLString:true})
          }
        }).catch(function(error){
          _this.fileList = []
          _this.$alert('系统出现异常，导入失败','信息提示',{dangerouslyUseHTMLString:true})
        })
		},
    // 批量上传车型配置
    uploadConfig (param) {
      var _this =this
			var formData = new FormData();
			formData.append('file', param.file);
      _this.$axios({
        method: 'post',
        url: sysServerUrl + 'sys/car/model/batchImportCarCfg',
        data: formData
      }).then(res => {
        if (res.data.code === 100) {
          handleAlert('success','批量导入成功')
          this.dataList()
        }else{
          _this.$alert(res.data.msg,'信息提示',{dangerouslyUseHTMLString:true})
        }
        _this.fileList = []
      }).catch(function(error){
        _this.fileList = []
        _this.$alert('系统出现异常，导入失败','信息提示',{dangerouslyUseHTMLString:true})
      })
		},
    // 下载车型模板
    downModelClick () {
      var params ='';
      modelTemplate(params).then(res => {
                  if(!res.data){
                       return
                   }
                  var name = "配置导入模板.xlsx";
                  var blob = new Blob([res.data]);
                  var url = window.URL.createObjectURL(blob);
                  var aLink = document.createElement("a");
                  aLink.style.display = "none";
                  aLink.href = url;
                  aLink.setAttribute("download", name);
                  document.body.appendChild(aLink);
                  aLink.click();
                  document.body.removeChild(aLink); //下载完成移除元素
                  window.URL.revokeObjectURL(url); //释放掉blob对象
                })

    },
    // 下载配置模板
    downConfigClick () {
      var params ='';
      configTemplate(params).then(res => {
        if(!res.data){
             return
         }
        var name = "车型配置导入模板.xlsx";
        var blob = new Blob([res.data]);
        var url = window.URL.createObjectURL(blob);
        var aLink = document.createElement("a");
        aLink.style.display = "none";
        aLink.href = url;
        aLink.setAttribute("download", name);
        document.body.appendChild(aLink);
        aLink.click();
        document.body.removeChild(aLink); //下载完成移除元素
        window.URL.revokeObjectURL(url); //释放掉blob对象
      })
    },
    // 新增
    handeladd (row) {
      var _this=this
      _this.dialogFormVisible = true
      _this.isDialog = true
      _this.imgList=[]
      _this.dialogStatus = 'add'
      _this.resetTemp()
      this.dataForm.year = row.year
      this.dataForm.trainId = row.trainId
      this.dataForm.trainName = row.trainName

    },
    addClick (dataForm) {
      // let inputValue = this.$refs.addSelectTree.getinputLable();
      // let modelValue = this.$refs.addSelectTree.getLabelModel();
      // if (modelValue == "") {
      //   handleAlert('error','没有选择车系哦')
      //   return false;
      // }
      // if (modelValue != inputValue && inputValue!="") {
      //   var _this=this
      //   setTimeout(function(){
      //     _this.$refs.addSelectTree.initSelected('','')
      //     _this.$refs.addSelectTree.setInput()
      //   },0);
      //   handleAlert('error',"不存在 " + inputValue + " 车系")
      //   return false;
      // }
      this.$refs[dataForm].validate((valid) => {
        if (valid) {
          this.$loading.show();
          var params = new URLSearchParams()
          params.append('code', this.dataForm.code)
          params.append('file', this.dataForm.file)
          if(this.dataForm.alias=='null'){
            this.dataForm.alias=''
          }
          params.append('alias', this.dataForm.alias)
          if(this.dataForm.image=='null'){
            this.dataForm.image=''
          }
          params.append('image', this.dataForm.image)
          params.append('marketTime', this.dataForm.marketTime)
          params.append('modelType', this.dataForm.modelType)
          if(this.dataForm.nameCh=='null'){
            this.dataForm.nameCh=''
          }
          params.append('nameCh', this.dataForm.nameCh)
          if(this.dataForm.nameEn=='null'){
            this.dataForm.nameEn=''
          }
          params.append('useFlag', this.dataForm.whether ? 1 : 0)
          params.append('nameEn', this.dataForm.nameEn)
          params.append('sort', this.dataForm.sort)
          params.append('trainId', this.dataForm.trainId)
          params.append('year', this.dataForm.year)
          modelAdd(params).then(res => {
            if(res.data.code === 100){
              handleAlert('success','保存成功')
              this.dataList()
              this.dialogFormVisible = false
            }else{
              handleAlert('error',res.data.msg)
            }
            this.$loading.hide();
          })
        } else {
          handleAlert('error','请完善车型信息')
          this.$loading.hide();
        }
      })
    },
    editResetForm(){
      this.dataForm.file= ''
      this.dataForm.year= ''
      this.dataForm.code= ''
      this.dataForm.nameCh= ''
      this.dataForm.nameEn= ''
      this.dataForm.alias= ''
      this.dataForm.modelType= ''
      this.dataForm.marketTime= ''
      this.dataForm.createdTime= ''
      this.dataForm.createdUser= ''
      this.dataForm.sort= 1
      this.dataForm.image= ''
      this.dataForm.whether = true
    },
    resetForm(){
      this.resetTemp()
    },
    // 查看
    check (row) {
      this.isDialog = true
      this.dialogStatus = 'check'
      this.dialogFormVisible = true
      this.urlImg = sysServerUrl + 'sys/upload/display?filePath=' + row.image
    },
    // 详情
    handelDetile (row) {
      this.dialogStatus = 'detail'
      this.dialogFormVisible = true
      this.isDialog = true
      this.resetTemp()
      this.dataForm = Object.assign({}, row)
      this.imgList = []
      if(row.image !== ''){
        var img = {url: sysServerUrl + 'sys/upload/display?filePath=' + row.image }
        this.imgList.push(img)
      }
    },
    // 编辑年款
    editYearHand(row){
      this.editYear.country = row.countryName;
      this.editYear.trainName = row.trainName;
      this.editYear.id = row.id;
      this.editYear.year = row.year;
      this.editYear.sort = row.sort ? row.sort : 1;
      this.editYear.remark = row.remark;
      this.dialogEditYearFormVisible = true;
    },
    editYearClick(){
      if(!this.editYear.year){
        handleAlert('warning', "年款不能为空")
        return false
      }
      this.$loading.show();
      var params = new URLSearchParams()
      params.append('id', this.editYear.id)
      params.append('year', this.editYear.year)
      params.append('sort', this.editYear.sort)
      if (this.editYear.remark != null && this.editYear.remark != undefined){
        params.append('remark', this.editYear.remark)
      }
      modelEditYear(params).then(res => {
        if(res.data.code == 100){
          handleAlert('success', "修改成功")
          this.dataList()
          this.dialogEditYearFormVisible = false;
        }else {
          handleAlert('error',res.data.msg)
        }
        this.$loading.hide();
      })
    },
    // 编辑
    handeledit (row) {
      this.dialogFormVisible = true
      this.dialogStatus = 'edit'
      this.isDialog = true
      this.resetTemp()

      this.dataForm = Object.assign({}, row)
      this.dataForm.sort = row.sort ? row.sort : 1;
      // this.dataForm.whether = row.useFlag == 1
      this.imgList=[]
      if(row.image !== null && row.image !== '' && row.image !== 'null'){
        var img = {url: sysServerUrl + 'sys/upload/display?filePath=' + row.image }
        this.imgList.push(img)
      }
      this.trainCode = row.trainCode
    },
    editClick (dataForm) {
      this.$refs[dataForm].validate((valid) => {
        if (valid) {
          this.$loading.show();
          var params = new URLSearchParams()
          params.append('id', this.dataForm.id)
          params.append('trainId', this.dataForm.trainId)
          params.append('file', this.dataForm.file)
          params.append('year', this.dataForm.year)
          params.append('code', this.dataForm.code)
          params.append('useFlag', this.dataForm.whether ? 1 : 0)
          if(this.dataForm.nameCh=='null'){
            this.dataForm.nameCh=''
          }
          params.append('nameCh', this.dataForm.nameCh)
          if(this.dataForm.nameEn=='null'){
            this.dataForm.nameEn=''
          }
          params.append('nameEn', this.dataForm.nameEn)

          if(!this.dataForm.alias || this.dataForm.alias=='null' || this.dataForm.alias == 'undefined'){
            this.dataForm.alias=''
          }
          params.append('alias', this.dataForm.alias)
          if(this.dataForm.image=='null'){
            this.dataForm.image=''
          }
          params.append('image', this.dataForm.image)
          if(!this.dataForm.sort || this.dataForm.sort=='null' || this.dataForm.sort == 'undefined'){
            this.dataForm.sort= 1
          }
          params.append('sort', this.dataForm.sort)

          params.append('modelType', this.dataForm.modelType)
          modelEdit(params).then(res => {
            if (res.data.code === 100) {
              handleAlert('success',res.data.msg)
              this.dataList()
              this.dialogFormVisible = false
            } else {
              handleAlert('error',res.data.msg)
            }
            this.$loading.hide();
          })
        } else {
          handleAlert('error','请完善车型信息')
          this.$loading.hide();
        }
      })
    },
    // 删除
    handeldelete (row) {
      let del = null;
      if(row.modelType == 0){
        del = modelYearDel
      }else{
        del = modelDel
      }
      this.$confirm('确定删除【' + row.nameCh + '】的配置信息?', '删除配置', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var params = new URLSearchParams()
        params.append('id', row.id)
        del(params).then(res => {
          if (res.data.code === 100) {
            handleAlert('success','删除成功')
            if(this.resultList!=null&&this.resultList.length==1){
              this.currentPage =this.currentPage-1 > 0 ? this.currentPage-1 : this.currentPage
            }
            this.dataList()
          }else{
            handleAlert('error',res.data.msg)
          }
        })
      }).catch((error) => {
        handleAlert('info','取消删除')
      })
    },

    forbidden(row){
      let _this = this
      this.$confirm('确定要将配置【' + (row.modelType === 0 ? row.year : row.nameCh) + '】停用吗?', '停用配置', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        _this.editUseFlag(row, 0)
      })
    },

    editUseFlag(row, type){
      var params = new URLSearchParams()
        params.append('id', row.id)
        params.append('useFlag', type)
        editUseFlag(params).then(res => {
            if (res.data.code === 100) {
              handleAlert('success', "操作成功")
              this.dataList()
            } else {
              handleAlert('error',res.data.msg)
            }
      })
    },


    // 车型配置
    config (row) {
      this.isDialog = true
      this.textMap.config = ""
      this.modelId = row.id
      this.dialogStatus = 'config'
      this.textMap.config =  '['+row.nameCh +'] '+ " 车型配置"
      this.dialogFormVisible = true
      this.getCfgList()
    },
    getCfgList () {
      var params = '?id=' + this.modelId
      modelConfigData(params).then(res => {
        this.modelCfgList = res.data.data
      })
    },
    addConfig () {
      var sortVal = ''
      if (this.modelCfgList.length !== 0) {
        sortVal = Number(this.modelCfgList[this.modelCfgList.length - 1].sort) + Number(1)
        if(sortVal>9999){
          sortVal=9999
        }
      } else {
        sortVal = 1
      }

      var params = '?id=' + this.modelId
      modelConfigData(params).then(res => {
          this.modelCfgList = res.data.data
          this.modelCfgList.push({
          code: '',
          alias: '',
          sort: sortVal
        })
      })


    },
    delAllConfig () {
      this.$confirm('确认删除全部配置代码？', '删除全部配置',{
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var params = '?modelId=' + this.modelId
        configBatchDel(params).then(res => {
          if (res.data.code === 100) {
            handleAlert('success','删除成功')
            this.getCfgList()
          }else{
            handleAlert('error',res.data.msg)
          }
        })
      }).catch((error)=>{
        handleAlert('info','取消删除')
      })
    },
    // 保存当前信息
    handelSaveConfig (row) {
      let curCode= row.code
      let curAlias =row.alias
      if(curCode==null||curCode.length==0){
        handleAlert('error','请输入配置代码')
        return;
      }
      if(curAlias==null||curAlias.length==0){
        handleAlert('error','请输入手册编码')
        return;
      }
      this.$loading.show();
      var params = new URLSearchParams()
      if (row.id === undefined) {
        row.id = ''
      }
      if (row.pid === undefined) {
        row.pid = this.modelId
      }
      params.append('id', row.id)
      params.append('pid', row.pid)
      params.append('code', row.code)
      params.append('modelType', '2')
      params.append('alias', row.alias)
      params.append('sort', row.sort)
      configSave(params).then(res => {
        if (res.data.code === 100) {
          handleAlert('success','保存成功')
          this.getCfgList()
        }else{
          handleAlert('error',res.data.msg)
        }
        this.$loading.hide();
      })
    },
    // 删除当前行
    handelDelConfig (row) {
      var _this=this
      this.$confirm('确定删除当前行配置代码?', '删除配置', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var params = '?id=' + row.id
        modelConfigDel(params).then(res => {
          if (res.data.code === 100) {
            handleAlert('success','删除成功')
            this.getCfgList()
          }else{
            handleAlert('error',res.data.msg)
          }
        })
      }).catch((error)=>{
        handleAlert('info','取消删除')
      })
    },
    reset (formInline) {
      if (this.$refs[formInline].resetFields() !== undefined) {
        this.$tefs[formInline].resetFields()
      }
      this.currentPage = 1
      this.dataList()
    },
    // // 车型编码自动赋值
    codeValue(){
      if (this.trainCode != '' && this.yearForm.year != '') {
        this.yearForm.code = this.trainCode + "-" + this.yearForm.year
        this.yearForm.nameCh = this.trainName + " " + this.yearForm.year
      }
    },
    isInTrainList(value){
      for (let i = 0; i < this.trainList.length; i++) {
        for (let j = 0; j < this.trainList[i].children.length; j++) {
          for (let k = 0; k < this.trainList[i].children[j].children.length; k++) {
            if (this.trainList[i].children[j].children[k].nameCh == value) {
              return true;
            }
          }
        }
      }
      return false;
    },
    maximumArea() {
      setTimeout(() => {
        tableHeight()
        setTimeout(() => {
          this.maximumHeight = tableMaxHeight;
        }, 50)
      })
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    },
  },
  mounted () {
    this.tableHeightArea()
    this.dataList()
    this.getTrainList()
  },
  activated() {
    window.addEventListener('keydown', this.keyDown);
  },
  deactivated() {
    window.removeEventListener('keydown', this.keyDown, false);
  },
}
</script>
<style>
  .el-dialog .el-table .el-input__inner{
    height: 28px;
    line-height: 28px;
    padding: 0 8px;
  }
  .el-dialog .el-table .el-input{
    width: 100% !important;
    margin: 2px 0;
  }
  .imgShow{
    width: 150px;
  }
</style>
