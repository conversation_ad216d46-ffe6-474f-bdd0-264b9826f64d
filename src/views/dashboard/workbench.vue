<template>
  <div class="workbench">
    <div class="mineContainer">
      <el-row :gutter="15">
        <el-col :xs="24" :sm="24" :md="16" :lg="16" :xl="16" class="leftContent">
          <div class="projectInfo">
            <div class="userInfo">
              <div class="headPortrait">
                <img v-if="baseData.headimgurl == null" src="../../assets/image/defaultAvatar.png" alt="">
                <img v-if="baseData.headimgurl" :src="userInfo.headimgurl" alt="">
              </div>
              <div class="greetContent">
                <p> {{baseData.realName}}，{{greetingsText}}</p>
              </div>
            </div>
            <div class="taskInfo" @click="jumpMenuGo('1')">
              <div>
                <img src="../../assets/image/indexIcon/handleIcon.png" alt="">
              </div>
              <div>
                <p>待办</p>
                <p class="numberInfo">{{ menuDataList.taskNum }}</p>
              </div>
            </div>
            <div class="replyInfo" @click="jumpMenuGo('2')">
              <div>
                <img src="../../assets/image/indexIcon/replyIcon.png" alt="">
              </div>
              <div>
                <p>待回复</p>
                <p class="numberInfo">{{ menuDataList.replyNum }}</p>
              </div>
            </div>
          </div>
          <div class="quickEntryInfo">
            <p>
              <span>快捷入口</span>
              <span @click="quickEntryClick()">设置</span>
            </p>
            <div class="quickEntryArea">
              <div v-for="(item, index) in quickEntryList" :key="index">
                <div @click="menuClick(item)">
                  <img :src="require('../../' + imgSrc + iconList.get(item.name))" alt="">
                  <p>{{ item.name }}</p>
                </div>
              </div>
            </div>
            <div v-if="quickEntryList.length == 0" class="tipNoneData">
              暂无数据
            </div>
          </div>
          <div class="statisticsInfo">
            <p>近一周</p>
            <div>
              <div id="myChart" style="width:100%;height:300px"></div>
            </div>
          </div>
        </el-col>
        <el-col :xs="24" :sm="24" :md="8" :lg="8" :xl="8" class="rightContent">
          <div class="noticeDetail">
            <p>
              <span>系统公告</span>
              <span @click="bulletinAllClick()" class="noticeMore" v-if="noticeList.length > 0">
                更多
                <i class="el-icon-d-arrow-right"></i>
              </span>
            </p>
            <div v-if="noticeList.length > 0">
              <div v-for="(item, index) of noticeList.slice(0,7)" :key="index">
                <div class="noticeTitle"  @click="detailClick(item)">
                  <div>
                    <p class="readState" style="color:#666;font-size:12px;" v-if="item.isRead == true">[已读]</p>
                    <p class="readState" style="color:#c30000;font-size:12px;" v-if="item.isRead == false">[未读]</p>
                    <p :title="item.title">{{item.title}}</p>
                  </div>
                  <div>
                    详情
                    <i class="el-icon-d-arrow-right"></i>
                  </div>
                </div>
              </div>
            </div>
            <div v-if="noticeList.length == 0" class="tipNoneData">
              暂无数据
            </div>
          </div>
          <div class="latestNews">
            <p>
              <span>待回复信息</span>
              <span  @click="trendsAllClick()" class="noticeMore" v-if="replyFeedbackData.length > 0">
                更多
                <i class="el-icon-d-arrow-right"></i>
              </span>
            </p>
            <div class="dynamicDetail" v-if="replyFeedbackData.length > 0">
              <div class="dynamicList" v-for="(item, index) of replyFeedbackData.slice(0,6)" :key="index">
                <div class="replayListDetail" @click="replyFeedbackClick()">
                  <div class="replayProblem">
                    <p>{{ item.problemDesc}}</p>
                  </div>
                  <div class="replayTitle">
                    <div>
                      <p
                        :class="item.classification == '投诉' ? 'complaint' : item.classification == '订单咨询' ? 'orderStyle': 'partStyle' ">
                        {{ item.classification}}
                      </p>
                      <p>{{ item.stationName }}</p>
                    </div>
                    <p>{{ item.createdTime | conversion("yyyy-MM-dd HH:mm") }}</p>
                  </div>
                </div>
              </div>
            </div>
            <div class="tipNoneData" v-if="replyFeedbackData.length == 0">
              暂无数据
            </div>
          </div>
        </el-col>
      </el-row>
      <el-row>
      </el-row>
      <el-dialog width="540px !important" v-dialogDrag title="快捷入口设置" :visible.sync="dialogFormVisible">
        <div class="quickEntrySet">
          <div class="tipsTitle" v-if="addedMenuList.length > 0">
            <img :src="require('../../' + imgSrc + 'tipsIcon.png')" alt="" />
            可通过上下拖拽图标，调整快捷入口显示顺序
          </div>
          <div class="menuDetail">
            <!-- 已添加-->
            <div class="addedArea">
              <draggable @end="dragend" :options = "{animation:500}">
                <transition-group type="transition">
                  <div v-for="(item, index) of addedMenuList" :key="index" class="drag-item">
                    <div>
                      <div>
                        <img :src="require('../../' + imgSrc + 'slideIcon.png')" alt="" />
                        <img :src="require('../../' + imgSrc + iconList.get(item.name))" alt="" />
                        <p>{{ item.name }}</p>
                      </div>
                      <div class="removeArea">
                        <p @click="removeMenuClcik(index,item)">移除</p>
                      </div>
                    </div>
                  </div>
                </transition-group>
              </draggable>
            </div>
            <el-divider v-if="addedMenuList.length > 0"></el-divider>
            <!-- 未添加 -->
            <div class="notAdded">
              <div v-for="(item, index) of notAddedMenuList" :key="index">
                <div>
                  <div>
                    <img :src="require('../../' + imgSrc + iconList.get(item.name))" alt="" />
                    <p>{{ item.name }}</p>
                  </div>
                  <div class="addArea">
                    <p @click="addMenuClick(index,item)">添加</p>
                  </div>
                </div>
              </div>
            </div>
            <el-divider v-if="notAddedMenuList.length > 0"></el-divider>
          </div>
        </div>
        <div v-if="addedMenuList.length != 0 || notAddedMenuList.length != 0" class="submitArea" style="margin-top:15px;">
          <el-button type="primary" @click="submitClick()">
            确定
          </el-button>
          <el-button plain @click="cancelClick()">
            取消
          </el-button>
        </div>
        <div v-if="addedMenuList.length == 0 && notAddedMenuList.length == 0" class="tipNoneData">
          暂无数据
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import draggable from 'vuedraggable'
import { addTabs, readState, quickEntryMap, handleAlert } from '@/assets/js/common.js'
import {
  getHomeData,
  quickEntryMenuData,
  quickEntryEdit,
  project,
  dynamic,
  getUserInfo,
  projectSize,
  joinProjectList,
  systemBulletinList
} from '@/api/sysmgt.js'
import { feedbackTypeList } from '@/api/material.js';
import $ from 'jquery'
export default {
  name: 'dashboard',
  data () {
    return {
      imgSrc: "assets/image/indexIcon/",
      greetingsText: '', // 问候
      baseData: [], // 个人信息
      menuDataList: [], // 主页数据
      quickEntryList: [], // 快捷入口
      iconList: [], // 快捷入口图标列表
      noticeList:[], // 系统公告
      replyFeedbackData: [], // 待回复信息
      problemTypeList: [], // 在线反馈问题类型
      lineSX: [], // 横坐标 时间
      lineSY1: [], // 纵坐标 订单
      lineSY2: [], // 纵坐标 访问
      dialogFormVisible: false,
      addedMenuList: [],
      notAddedMenuList: [],
      uploadMenuList:[],
    }
  },
  components: {
    draggable
  },
  methods: {
    // 在线反馈问题类型
    feedbackType() {
      feedbackTypeList().then(res => {
        this.problemTypeList = res.data.data
      }).catch(err => {
        handleAlert('error','系统开小差了...')
      })
    },
    // 2023-08-31 获取主页信息
    dataList(){
      getHomeData().then(res => {
        if(res.data.code == 100){
          this.menuDataList = res.data.data;
          setTimeout(() => {
            this.replyFeedbackData = this.menuDataList.replyFeedback;
            this.replyFeedbackData.forEach((typeCode, index) => {
              this.problemTypeList.forEach((item) => {
                item.children.forEach((itm) => {
                  if (typeCode.problemType == itm.code) {
                    this.replyFeedbackData[index].classification = item.name
                  }
                })
              })
            })
          }, 100)
          this.quickEntryList = this.menuDataList.menuList;
          this.iconList = quickEntryMap;
          this.lineSX = [];
          this.lineSY1 = [];
          this.lineSY2 = [];
          this.menuDataList.orderData.forEach((item) => {
            this.lineSX.push(item.day);
            this.lineSY1.push(item.count);
          });
          this.menuDataList.loginData.forEach((item) => {
            this.lineSY2.push(item.count);
          });
          this.drawLine(this.lineSX, this.lineSY1, this.lineSY2, "myChart");
        }
      })
    },
    // 快捷入口调整
    menuClick(item) {
      this.$router.push(item.url);
      setTimeout(() => {
        addTabs(item.url, item.name);
      })
    },
    quickEntryClick() {
      this.dialogFormVisible = true;
      this.addedMenuList = [];
      this.notAddedMenuList = [];
      quickEntryMenuData().then((res) => {
        if (res.data.code == "100") {
          var list = res.data.data;
          list.forEach((item) => {
            if (item.useFlag == 0) {
              this.addedMenuList.push(item);
            } else {
              this.notAddedMenuList.push(item);
            }
          });
        }
      });
    },
    // 拖拽排序
    dragend(event) {
      this.uploadMenuList = event.target.innerText.split("移除");
    },
    // 移除
    removeMenuClcik(index, item) {
      this.addedMenuList.splice(index, 1);
      setTimeout(() => {
        this.notAddedMenuList.push(item);
      }, 10)
    },
    // 添加
    addMenuClick(index, item) {
      this.notAddedMenuList.splice(index, 1);
      this.addedMenuList.push(item);
    },
    // 确定
    submitClick() {
      var uploadList = [];
      if (this.uploadMenuList.length > 0) {
        for (var i = 0; i < this.uploadMenuList.length; i++) {
          this.addedMenuList.forEach((res) => {
            if (this.uploadMenuList[i].indexOf(res.name) != -1) {
              var params = {
                name: res.name,
                menuCode: res.code
              }
              uploadList.push(params);
            }
          })
        }
      } else {
        this.addedMenuList.forEach((res) => {
          var params = {
            menuCode: res.code
          }
          uploadList.push(params);
        })
      }
      quickEntryEdit(uploadList).then((res) => {
        if (res.data.code == 100) {
          this.dataList();
          this.uploadMenuList = [];
          handleAlert('success', res.data.msg);
          this.dialogFormVisible = false;
        }
      })
    },
    // 取消
    cancelClick() {
      this.dialogFormVisible = false;
      // this.quickEntryClick();
    },
    jumpMenuGo(p){
      if (p == '1' && this.menuDataList.taskMenu && this.menuDataList.taskMenu != '') {
        // 待办任务
        this.menuClick(this.menuDataList.taskMenu)
      }else if(p == '2'  && this.menuDataList.replyMenu && this.menuDataList.replyMenu != ''){
        // 在线反馈
        this.menuClick(this.menuDataList.replyMenu)
      }else{
        // 没有权限
        handleAlert('error', '您没有权限访问')
      }
    },
    // 2023-08-31 跳转菜单
    // jumpMenu(item){
    //   this.$router.push(item.targetUrl)
    //   setTimeout(() => {
    //       addTabs(item.targetUrl, item.action);
    //     }, 120)
    // },
    // 近一周统计
    drawLine(lineX, lineY1, lineY2, id) {
      let myChart = this.$echarts.init(document.getElementById(id));
      let option = {
        backgroundColor: "#fff",
        tooltip: {
          trigger: "axis",
        },
        legend: {
          data: ["订单量", "访问量"],
          icon: 'line',
          right: '5%',
          top: '10',
        },
        grid: {
          left: "3%",
          right: "7%",
          bottom: "3%",
          containLabel: true
        },
        xAxis: {
          name:"时间",
          type: "category",
          boundaryGap: false,
          data: lineX,
        },
        yAxis: {
          name: "数量",
        },
        series:[
          {
            name: "订单量",
            type: "line",
            data: lineY1,
            itemStyle: {
              color: '#FC8A28',
            },
          },{
            name: "访问量",
            type: "line",
            data: lineY2,
            itemStyle: {
              color: '#4A90E2',
            }
          }
        ]
      };
      window.addEventListener("resize", function () {
        myChart.resize();
      });
      myChart.setOption(option);
    },
    //系统公告跳转
    systemNotice(){
      systemBulletinList().then(res => {
        if(res.data.data === null){
          this.noticeList = []
        }else{
          this.noticeList = res.data.data
        }
      });
    },
    bulletinAllClick(){
      this.$router.push({ name: "systemBulletin" });
      addTabs(this.$route.path, "系统公告");
    },
    detailClick(item){
      this.$router.push({ name: 'bulletinDetail', params:{id: item.id}})
      addTabs(this.$route.path, item.title);
    },
    // 待回复
    replyFeedbackClick() {
      this.$router.push('/material/feedback/list')
      setTimeout(() => {
        addTabs(this.$route.path, "在线反馈");
      })
    },
    trendsAllClick() {
      this.replyFeedbackClick()
      // this.$router.push('/material/feedback/list')
      // addTabs(this.$route.path, "在线反馈");
      // this.$router.push({ name: "latestNews" });
      // addTabs(this.$route.path, "待回复信息");
    },
    // 问候语
    greetingsList () {
      const timeNow = new Date()
      // 获取当前小时
      const hours = timeNow.getHours()
      // 设置默认文字
      var text = ''
      // 判断当前时间段
      if (hours >= 5 && hours <= 8) {
        text = "早上好!"
      } else if (hours > 8 && hours <= 13) {
        text = "上午好!"
      } else if (hours > 13 && hours <= 18) {
        text = "下午好!"
      } else if (hours > 18 && hours <= 5) {
        text = "晚上好!"
      }
      this.greetingsText = text
    },
    // 获取用户信息
    userInfo () {
      getUserInfo().then(res =>{
        this.baseData = res.data.data
      })
    },
    // 获取内容区域高度
    areaHeight () {
      var headHeight = $('.el-header').outerHeight(true)
      var layTitle = $('.layTitle').outerHeight(true)
      var headerLay = $('.headerLay').outerHeight(true)
      var tabHeight = $('.tabsTag').outerHeight(true)
      var footHeight = $('.el-footer').outerHeight(true)
      if ($('.container').css('marginTop') !== undefined) {
        var topHeight = 2 * Number($('.container').css('marginTop').split('p')[0])
      } else {
        topHeight = 0
      }
      var h = $(window).height() - headHeight - layTitle - headerLay - tabHeight - footHeight - topHeight
      $('.container').css('height', h + 'px')
      // 获取进行中的项目内容改的和动态的内容高度
      $('.projectList').css('max-height', h - $('.projectContent .pTitle').height())
      $('.dynamicDetail').css('height', h - $('.projectContent .pTitle').height())
      $("#myChart").css({"width": "100%", "height": "300px"});
    },
    sizeDetail () {
      var _this = this
      _this.areaHeight()
      window.addEventListener('resize', function () {
        _this.areaHeight()
      })
    },
    allClick (name, url) {
      this.$router.push({ name: "allProject" });
      addTabs(this.$route.path, name);
    },
  },
  mounted () {
    this.dataList();
    this.userInfo();
    this.systemNotice();
    this.greetingsList()
    this.feedbackType();
    document.body.ondrag = function(event) {
      event.stopPropagation()
      event.preventDefault()
    }
    document.body.ondragover = function(event) {
      event.stopPropagation()
      event.preventDefault()
    }
  },
  created() {
    document.body.ondrag = function(event) {
      event.stopPropagation()
      event.preventDefault()
    }
    document.body.ondragover = function(event) {
      event.stopPropagation()
      event.preventDefault()
    }
  },
  watch: {
    $route(to,from){
      if (to.name == 'dashboard') {
        // 2023-08-31 获取主页信息
        this.dataList()
      }
    }
  },
}
</script>
<style>
/* header */
.workbench {
  background: var(--content-bgColor);
  padding: 15px;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
}
.leftContent > div,
.rightContent > div{
  margin-bottom: 15px;
  border-radius: 2px;
  /* box-shadow: 0 0 5px 0 rgba(0,0,0,0.1) */
}
/* 信息 */
.leftContent .projectInfo {
  display: flex;
  background: var(--dash-color);
  padding: 15px 0;
  justify-content: space-between;
}
.leftContent .projectInfo > div {
  width: 33.33%;
  border-right: 1px solid rgb(229, 229, 229);
  padding: 8px 3%;
}
.leftContent .projectInfo > div:last-child {
  border-right: none;
}
.leftContent .projectInfo .userInfo,
.leftContent .projectInfo .taskInfo,
.leftContent .projectInfo .replyInfo {
  display: flex;
  align-items: center;
}
.leftContent .projectInfo .taskInfo,
.leftContent .projectInfo .replyInfo {
  cursor: pointer;
}
.leftContent .projectInfo .userInfo .headPortrait {
  height: 40px;
  width: 40px;
  overflow: hidden;
  border-radius: 50%;
  margin: 0 10px;
}
.leftContent .projectInfo .userInfo .headPortrait img {
  width: 100%;
  height: 100%;
}
.leftContent .projectInfo .userInfo .greetContent {
  flex: 1;
}
.leftContent .projectInfo .userInfo .greetContent p {
  font-weight: bold;
  font-size: 16px;
  word-break: break-word;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}
.leftContent .projectInfo .taskInfo > div:first-child,
.leftContent .projectInfo .replyInfo > div:first-child {
  width: 60px;
  height: 60px;
  margin-right: 10%;
}
.leftContent .projectInfo .taskInfo > div:first-child img,
.leftContent .projectInfo .replyInfo > div:first-child img {
  width: 100%;
}
.leftContent .projectInfo .taskInfo div p,
.leftContent .projectInfo .replyInfo div p {
  font-size: 15px;
  color: rgb(113, 113, 113);
  margin-bottom: 10px;
}
.leftContent .projectInfo div div .numberInfo {
  color: #000;
  font-size: 30px;
  font-weight: bold;
  margin-bottom: 0px;
}
.quickEntryInfo,
.noticeDetail,
.latestNews,
.statisticsInfo {
  background: var(--dash-color);
  padding: 15px;
}
.quickEntryInfo>p,
.noticeDetail>p,
.latestNews>p,
.statisticsInfo>p {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 15px;
}
.noticeDetail>p,
.latestNews>p{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.noticeDetail>p .noticeMore,
.latestNews>p .noticeMore {
  color:#1890ff;
  font-weight: normal;
  display: flex;
  align-items: center;
  cursor: pointer;
}
.noticeDetail>p .noticeMore i,
.latestNews>p .noticeMore i,
.noticeDetail .noticeTitle>div i {
  color:#1890ff;
  font-size: 16px;
  margin: 0 5px;
  cursor: pointer;
}
.noticeDetail .noticeTitle>div i {
  font-size: 15px;
}
/* 快捷入口 */
.dragActive {
  width: 100%;
  background: #fff;
  /* position: absolute; */
  width: calc(100% - 20px);
  box-shadow: 0 0 5px 0 rgba(0,0,0,0.1);
  z-index: 5;
}
.quickEntryInfo>p {
  display: flex;
  justify-content: space-between;
}
.quickEntryInfo>p span:last-child {
  font-weight: normal;
  color:#1890ff;
  cursor: pointer;
}
.quickEntryInfo .quickEntryArea {
  display: flex;
  flex-wrap: wrap;
}
.quickEntryInfo .quickEntryArea>div {
  width: 20%;
  margin-bottom: 5px;
}
.quickEntryInfo .quickEntryArea>div div {
  width: 100px;
  padding: 8px 10px 2px;
  border-radius: 5px;
  margin: auto;
  text-align: center;
}
.quickEntryInfo .quickEntryArea>div div:hover{
  background: rgb(249, 249, 249);
}
.quickEntryInfo .quickEntryArea>div div img {
  width: 60px;
  height: 60px;
}
.quickEntryInfo .quickEntryArea>div div p {
  font-size: 15px;
  margin: 5px 0px 8px;
  white-space: nowrap;
}
/* 快捷入口设置 */
.quickEntrySet .tipsTitle {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #777;
  margin-bottom: 15px;
}
.quickEntrySet .tipsTitle img {
  width: 18px;
  margin-right: 5px;
}
.quickEntrySet,
.quickEntrySet .menuDetail,
.drag-item > div {
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  -khtml-user-select: none;
  user-select: none;
}
/* .quickEntrySet .menuDetail .addedArea {
  position: relative;
} */
.addedArea .sortable-ghost {
  background: #fff;
  box-shadow: 0 0 5px 0 rgba(0,0,0,0.1);
  cursor: pointer;
}
.addedArea .sortable-ghost .removeArea {
  display: none;
}
.quickEntrySet .menuDetail .addedArea,
.quickEntrySet .menuDetail .notAdded {
  color: #333;
  font-size: 14px;
}
.quickEntrySet .menuDetail .addedArea > div .drag-item,
.quickEntrySet .menuDetail .notAdded > div {
  padding: 8px 10px;
  border-radius: 5px;
}
.quickEntrySet .menuDetail .addedArea > div .drag-item > div,
.quickEntrySet .menuDetail .notAdded > div > div {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.quickEntrySet .menuDetail .addedArea > div {
  -webkit-transition: 0.3s ease-in-out;
  -moz-transition: 0.3s ease-in-out;
  -o-transition: 0.3s ease-in-out;
  transition: 0.3s ease-in-out;
}
.quickEntrySet .menuDetail .addedArea > div .drag-item > div > div:first-child,
.quickEntrySet .menuDetail .notAdded > div > div > div:first-child {
  display: flex;
  align-items: center;
  cursor: pointer;
  flex: 1;
  overflow: hidden;
  margin-right: 10px;
}
.quickEntrySet .menuDetail .addedArea > div .drag-item > div > div:first-child p,
.quickEntrySet .menuDetail .notAdded > div > div > div:first-child p {
  flex: 1;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.quickEntrySet .menuDetail .addedArea img:first-child {
  width: 16px;
  height: 16px;
  margin: 0;
}
.quickEntrySet .menuDetail img {
  width: 32px;
  height: 32px;
  margin: 0 12px 0 8px;
}
.addedArea .removeArea p {
  color: #777;
  border: 1px solid #bbb;
  background: #fff;
  box-sizing: border-box;
  padding: 3px 8px;
  border-radius: 3px;
  cursor: pointer;
}
.notAdded > div {
  margin-left: 16px;
}
.notAdded .addArea p {
  color: #1890ff;
  border: 1px solid #2386ee;
  background: #f7f9fc;
  box-sizing: border-box;
  padding: 3px 8px;
  border-radius: 3px;
  cursor: pointer;
}
/* 系统公告 */
.noticeTitle{
  font-size: 15px;
  display: flex;
  justify-content: space-between;
  line-height: 30px;
  border-bottom: 1px solid #eee;
  margin-bottom: 10px ;
  cursor: pointer;
}
.noticeTitle>div:first-child{
  display: flex;
  flex: 1;
  overflow: hidden;
}
.readState{
  white-space: nowrap;
  margin-right: 10px;
}
.noticeTitle>div:first-child p:last-child{
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.noticeDetail .noticeTitle>div:last-child{
  color: #1890ff;
  white-space: nowrap;
  margin-left: 10px;
  display: flex;
  align-items: center;
}
.noticeDetail>div>div:last-child .noticeTitle{
  border:none;
  margin-bottom: 0;
}
/* 待回复信息 */
.tipNoneData{
  font-size: 15px;
}
.dynamicDetail .dynamicList {
  border-bottom: 1px solid #ccc;
  padding: 0 2px 5px;
  margin-bottom: 5px;
  line-height: 1.5;
  cursor: pointer;
}
.dynamicDetail .dynamicList:last-child {
  border-bottom: none;
  margin-bottom: 0px;
  padding-bottom: 0px;
}
.dynamicDetail .replayListDetail .replayProblem p,
.dynamicDetail .replayListDetail .replayTitle > div,
.dynamicDetail .replayListDetail .replayTitle > div p:last-child {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}
.dynamicDetail .replayListDetail .replayTitle {
  font-size: 14px;
  color: #bbb;
  margin: 4px 0 3px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.partStyle {
  background: rgba(35,134,238,0.1);
  color: #2386ee;
}
.orderStyle {
  background: rgba(238,154,35,0.1);
  color: #ee9a23;
}
.complaint {
  background: rgba(238,35,35,0.1);
  color: #ee2323;
}
.dynamicDetail .replayListDetail .replayTitle > div {
  display: flex;
  align-items: center;
}
.dynamicDetail .replayListDetail .replayTitle > div p:first-child {
  font-size: 12px;
  margin-right: 5px;
  border-radius: 2px;
  padding: 0 6px;
  height: 22px;
  line-height: 22px;
}
.dynamicDetail .replayListDetail .replayProblem {
  font-size: 16px;
  color:#333;
}
@media screen and (max-width: 1366px) and (min-width: 1280px) {
  /* header */
  .workbench {
    padding: 10px;
    height: calc(100% - 10px);
  }
  .leftContent > div,
  .rightContent > div{
    margin-bottom: 10px;
  }
  /* 信息 */
  .leftContent .projectInfo {
    padding: 10px 0;
  }
  .leftContent .projectInfo > div {
    padding: 5px 1%;
  }
  .leftContent .projectInfo .userInfo .headPortrait {
    height: 30px;
    width: 30px;
  }
  .leftContent .projectInfo .userInfo .greetContent p {
    font-size: 12px;
  }
  .leftContent .projectInfo .taskInfo > div:first-child,
  .leftContent .projectInfo .replyInfo > div:first-child {
    width: 40px;
    height: 40px;
  }
  .leftContent .projectInfo .taskInfo div p,
  .leftContent .projectInfo .replyInfo div p {
    font-size: 12px;
    margin-bottom: 5px;
  }
  .leftContent .projectInfo div div .numberInfo {
    font-size: 18px;
    margin-bottom: 0px;
  }
  /* 快捷入口 */
  .quickEntryInfo,
  .noticeDetail,
  .latestNews,
  .statisticsInfo {
    padding: 10px;
  }
  .quickEntryInfo>p,
  .noticeDetail>p,
  .latestNews>p,
  .statisticsInfo>p {
    font-size: 15px;
  }
  .noticeDetail>p,
  .latestNews>p{
    margin-bottom: 15px;
  }
  .noticeDetail>p .noticeMore i,
  .latestNews>p .noticeMore i,
  .noticeDetail .noticeTitle>div i {
    font-size: 14px;
  }
  .noticeDetail .noticeTitle>div i {
    font-size: 12px;
  }
  /* 快捷入口 */
  .quickEntryInfo .quickEntryArea>div {
    width: 20%;
    margin-bottom: 5px;
  }
  .quickEntryInfo .quickEntryArea>div div {
    width: 80px;
  }
  .quickEntryInfo .quickEntryArea>div div img {
    width: 40px;
    height: 40px;
  }
  .quickEntryInfo .quickEntryArea>div div p {
    font-size: 13px;
  }
  /* 系统公告 */
  .noticeTitle{
    font-size: 13px;
    line-height: 25px;
    margin-bottom: 8px;
  }
  .noticeDetail .noticeTitle>div:last-child{
    margin-left: 5px;
  }

  /* 待回复信息 */
  .tipNoneData {
    font-size: 13px;
  }
  .dynamicDetail .replayListDetail .replayTitle {
    font-size: 13px;
  }
  .dynamicDetail .replayListDetail .replayProblem {
    font-size: 14px;
  }
}
@media screen and (max-width: 1024px) {
  /* header */
  .workbench {
    padding: 10px;
    height: calc(100% - 10px);
  }
  .leftContent > div,
  .rightContent > div{
    margin-bottom: 10px;
  }
  /* 信息 */
  .leftContent .projectInfo {
    padding: 10px 0;
  }
  .leftContent .projectInfo > div {
    padding: 5px 1%;
  }
  .leftContent .projectInfo .userInfo .headPortrait {
    height: 30px;
    width: 30px;
  }
  .leftContent .projectInfo .userInfo .greetContent p {
    font-size: 12px;
  }
  .leftContent .projectInfo .taskInfo > div:first-child,
  .leftContent .projectInfo .replyInfo > div:first-child {
    width: 40px;
    height: 40px;
  }
  .leftContent .projectInfo .taskInfo div p,
  .leftContent .projectInfo .replyInfo div p {
    font-size: 12px;
    margin-bottom: 5px;
  }
  .leftContent .projectInfo div div .numberInfo {
    font-size: 18px;
    margin-bottom: 0px;
  }
  /* 快捷入口 */
  .quickEntryInfo,
  .noticeDetail,
  .latestNews,
  .statisticsInfo {
    padding: 10px;
  }
  .quickEntryInfo>p,
  .noticeDetail>p,
  .latestNews>p,
  .statisticsInfo>p {
    font-size: 15px;
  }
  .noticeDetail>p,
  .latestNews>p{
    margin-bottom: 15px;
  }
  .noticeDetail>p .noticeMore i,
  .latestNews>p .noticeMore i,
  .noticeDetail .noticeTitle>div i {
    font-size: 14px;
  }
  .noticeDetail .noticeTitle>div i {
    font-size: 12px;
  }
  /* 快捷入口 */
  .quickEntryInfo .quickEntryArea>div {
    width: 20%;
    margin-bottom: 5px;
  }
  .quickEntryInfo .quickEntryArea>div div {
    width: 80px;
  }
  .quickEntryInfo .quickEntryArea>div div img {
    width: 40px;
    height: 40px;
  }
  .quickEntryInfo .quickEntryArea>div div p {
    font-size: 13px;
  }
  /* 系统公告 */
  .noticeTitle{
    font-size: 13px;
    line-height: 25px;
    margin-bottom: 8px;
  }
  .noticeDetail .noticeTitle>div:last-child{
    margin-left: 5px;
  }

  /* 待回复信息 */
  .tipNoneData{
    font-size: 13px;
  }
  .dynamicDetail .replayListDetail .replayTitle {
    font-size: 12px;
  }
  .dynamicDetail .replayListDetail .replayProblem {
    font-size: 13px;
  }
}
  </style>
