<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" ref="searchForm" :label-width="formLabelWidth" :model="searchForm" class="demo-form-inline">
        <el-form-item label="订单状态" prop="orderStatus">
          <el-select v-model="searchForm.orderStatus" placeholder="请选择订单状态" clearable filterable>
            <el-option v-for="(item, index) of orderStatusList" :key="index" :label="item.name"
              :value="item.code"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="订单编号" prop="orderNo">
          <el-input v-model="searchForm.orderNo" placeholder="请输入订单编号"></el-input>
        </el-form-item>
        <el-form-item label="服务站编码" prop="servicesSiteCode">
          <el-input v-model="searchForm.servicesSiteCode" placeholder="请输入服务站编码"></el-input>
        </el-form-item>
        <el-form-item label="服务站简称" prop="servicesShort">
          <el-input v-model="searchForm.servicesShort" placeholder="请输入服务站简称"></el-input>
        </el-form-item>
        <el-form-item label="订单提交时间">
          <el-date-picker align="center" value-format="yyyy-MM-dd" type="date" placeholder="选择开始日期"
            v-model="searchForm.startDate" :picker-options="pickerBeginTime"></el-date-picker>
          <span class="line">至</span>
          <el-date-picker align="center" value-format="yyyy-MM-dd" type="date" placeholder="选择结束日期"
            v-model="searchForm.endDate" :picker-options="pickerEndTime"></el-date-picker>
        </el-form-item>
        <el-form-item label="订单所属国家">
          <el-select
                v-model="searchForm.countries"
                multiple
                collapse-tags
                placeholder="请选择">
                <el-option
                v-for="country in currentUserCountries"
                  :key="country.code"
                  :label="country.name"
                  :value="country.code">
                </el-option>
              </el-select>
        </el-form-item>
        <!-- <div style="display: flex;">
          <el-collapse style="width: 120vh;margin-right: 5vh;">
            <el-collapse-item title="订单所属国家" name="1">
              <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll"
                @change="handleCheckAllChange">全选</el-checkbox>
              <div style="margin: 15px 0;"></div>
              <el-checkbox-group v-model="searchForm.countries" @change="handleCheckedCitiesChange">
                <el-checkbox v-for="country in currentUserCountries" :label="country.code"
                  :key="country.code">{{ country.name }}</el-checkbox>
              </el-checkbox-group>

            </el-collapse-item>
          </el-collapse>


        </div> -->
        <el-form-item>
            <el-button type="primary" @click="search()" icon="el-icon-search">搜索</el-button>
            <el-button plain @click="resetSearch('searchForm')">重置</el-button>
          </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <div class="tableHandle">
        <el-button type="text" v-if="hasPerm('menuAsimss5AORM_108')" icon="el-icon-download" @click="exportOrderList()">导出订单</el-button>
      </div>
      <!-- 列表内容 -->
      <el-table
        style="width:100%"
        border
        stripe
        ref="table"
        highlight-current-row
        :max-height="maximumHeight"
        :data="resultList"
        :header-cell-style="{
          'text-align': 'center',
          'background-color': 'var(--other-color)',
        }"
        @header-dragend="changeColWidth"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>
        <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
        <el-table-column label="订单编号" prop="orderNo" min-width="180" show-overflow-tooltip></el-table-column>
        <el-table-column label="服务站" prop="servicesSiteCode" min-width="80" show-overflow-tooltip></el-table-column>
        <el-table-column label="服务站简称" prop="servicesShort" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column label="收货人" prop="consignee" align="center" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column label="联系电话" prop="telephone" align="center" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column label="收货地址" prop="address" min-width="160" show-overflow-tooltip></el-table-column>
        <el-table-column label="备注" prop="remark" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column label="发货方式" prop="deliveryType" align="center" min-width="100">
          <template slot-scope="{row}">
            <span v-if="row.deliveryType === 0">物流</span>
            <span v-if="row.deliveryType === 1">自取</span>
          </template>
        </el-table-column>
        <el-table-column label="物流信息" prop="logisticsInfo" min-width="220" show-overflow-tooltip></el-table-column>
        <el-table-column label="状态" prop="status" min-width="80" align="center">
          <template slot-scope="{row}">
            <span v-if="row.status === 1">已提交</span>
            <span v-if="row.status === 2">处理中</span>
            <span v-if="row.status === 3">已取消</span>
            <span v-if="row.status === 4">已完成</span>
          </template>
        </el-table-column>
        <el-table-column label="已删除" prop="wasDeleted" min-width="80" align="center">
          <template slot-scope="{row}">
          <span style="color: red;" v-if="row.wasDeleted">是</span>
          <span v-else>否</span>
          </template>
       </el-table-column>
        <el-table-column label="提交时间" prop="submitTime" min-width="160" align="center" :formatter="dateFormat" show-overflow-tooltip></el-table-column>
        <el-table-column label="更新时间" prop="updateTime" min-width="160" align="center" :formatter="dateFormat" show-overflow-tooltip></el-table-column>
        <el-table-column label="操作" fixed="right" min-width="250">
          <template slot-scope="{row}">
            <div v-if="!row.wasDeleted">
            <el-button type="text" v-if="hasPerm('menuAsimss5AORM_104')" size="small" @click="showDetail(row)">查看</el-button>
            <el-button v-if="row.status == 1 && hasPerm('menuAsimss5AORM_103')" type="text" size="small" @click="confirmOrder(row)">确认订单</el-button>
            <el-button v-if="row.status == 2 && hasPerm('menuAsimss5AORM_103')" type="text" size="small" @click="editOrder(row)">编辑订单</el-button>
            <el-button v-if="(row.status == 1 || row.status == 2) && hasPerm('menuAsimss5AORM_103')" type="text" size="small"
              @click="cancelOrder(row)">撤销订单</el-button>
            <el-button v-if="row.status == 2 && hasPerm('menuAsimss5AORM_103')" type="text" size="small" @click="finishOrder(row)">完成订单</el-button>
          </div>
          <div v-else>
            <el-button v-if="hasPerm('menuAsimss5AORM_104')" type="text" size="small" @click="showDetail(row)">查看</el-button>
          </div>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="currentPage" :limit.sync="pageSize"
        @pagination="dataList" />
    </div>
    <el-dialog v-dialogDrag width="60% !important" :title="scheduleTitle" :visible.sync="detailVisiable" lock-scroll>
      <div class="tableDetail">
        <!-- 列表内容 -->
        <el-table
          style="width:100%"
          border
          stripe
          highlight-current-row
          :data="detailParts"
          :header-cell-style="{
            'text-align': 'center',
            'background-color': 'var(--other-color)',
          }"
        >
          <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
          <el-table-column label="配件编码" prop="partsNo" min-width="150" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="配件名称" prop="partName" min-width="200" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="订购数量" prop="quantity" min-width="100" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="备注" prop="remark" min-width="200" show-overflow-tooltip></el-table-column>
          <el-table-column label="单价" prop="unitPrize" min-width="75" align="center" show-overflow-tooltip></el-table-column>
          <el-table-column label="金额" prop="totalPrice" min-width="75" align="center" show-overflow-tooltip></el-table-column>
        </el-table>
        <pagination v-show="detailTotal > 0" :total="detailTotal" :page.sync="detailCurrentPage"
          :limit.sync="detailPageSize" @pagination="detailList" />
      </div>
      <!-- <el-progress style="width:530px;margin-top:50px;margin-left:10px" color="green" type="line"  :text-inside="true" :stroke-width="20" :percentage="percentage" :disabled="true"></el-progress> -->

    </el-dialog>

    <el-dialog v-dialogDrag width="55% !important" title="编辑订单" :visible.sync="editOrderVisiable" lock-scroll>
      <div style="margin-top:10px;margin-bottom: 10px;font-weight:bold">配件清单信息：</div>
      <el-table
        style="width:100%"
        border
        stripe
        ref="partTable"
        highlight-current-row
        :data="submitOrder.ormOrderPartsDetailList"
        :header-cell-style="{
          'text-align': 'center',
          'background-color': 'var(--other-color)',
        }"
        @header-dragend="changeColWidth"
      >
        <el-table-column label="序号" type="index" width="50" align="center"></el-table-column>
        <el-table-column label="配件编码" prop="partsNo" min-width="150" show-overflow-tooltip></el-table-column>
        <el-table-column label="配件名称" prop="partName" min-width="150"></el-table-column>
        <el-table-column label="订购数量" prop="quantity" min-width="180">
          <template slot-scope="{row}">
            <el-input-number v-model="row.quantity" :min="row.minOrder" :max="1000" label="订购数量"></el-input-number>
          </template>
        </el-table-column>
        <el-table-column label="单价" prop="unitPrize" min-width="70"></el-table-column>
        <el-table-column label="金额" prop="totalPrice" min-width="70"></el-table-column>
        <el-table-column label="操作" fixed="right" min-width="50">
          <template slot-scope="{row}">
            <el-button icon="el-icon-delete" type="text" size="small" @click="delCurrentDetailItem(row)"></el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="margin-top:10px;margin-bottom: 10px;font-weight:bold">订单收货信息：</div>
      <el-form ref="submitOrder" :model="submitOrder" :label-width="formLabelWidth" class="demo-form-inline" :rules="editOrderRule">
        <el-form-item label="收货人" prop="consignee">
          <el-input v-model="submitOrder.consignee" placeholder="收货人" maxlength="50" show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="联系电话" prop="telephone">
          <el-input v-model="submitOrder.telephone" placeholder="联系电话" maxlength="11" show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="收货地址" prop="address">
          <el-input v-model="submitOrder.address" placeholder="收货地址"  maxlength="200" show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="submitOrder.remark" placeholder="备注" maxlength="500" show-word-limit></el-input>
        </el-form-item>
      </el-form>
      <div class="submitArea">
        <el-button type="primary" @click="submitEditOrder">确定</el-button>
        <el-button @click="editOrderVisiable = false">取消</el-button>
      </div>
      <!-- <el-progress style="width:530px;margin-top:50px;margin-left:10px" color="green" type="line"  :text-inside="true" :stroke-width="20" :percentage="percentage" :disabled="true"></el-progress> -->
    </el-dialog>
    <el-dialog v-dialogDrag title="完成订单" :visible.sync="finishOrderVisiable" lock-scroll>
      <el-form ref="submitFinishOrderInfo" :model="submitFinishOrderInfo" class="demo-form-inline" :rules="finishRules">
        <el-form-item label="发货方式" prop="deliveryType" :label-width="formLabelWidth">
          <el-select v-model="submitFinishOrderInfo.deliveryType" placeholder="请选择发货方式" @change="onDeliveryTypeChange">
            <el-option v-for="(item, index) of deliveryTypes" :key="index" :label="item.name"
              :value="item.type"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item v-if="submitFinishOrderInfo.deliveryType == 0" label="物流信息" prop="logisticsInfo"
          :label-width="formLabelWidth">
          <el-input v-model="submitFinishOrderInfo.logisticsInfo" placeholder="请输入物流及物流单号信息"></el-input>
        </el-form-item>
      </el-form>
      <div class="submitArea">
        <el-button type="primary" @click="handSubmitFinish">确定</el-button>
        <el-button @click="finishOrderVisiable = false">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { sysServerUrl, addTabs, handleAlert, tableHeight, tableMaxHeight } from '@/assets/js/common.js'
import Pagination from '@/components/Pagination'
import {
  listOrderInfo, currUserCountryInfo, exportOrderInfo, getOrmOrderPartsDetailList, editOrderRequest, updateOrderStatusRequest,
  getOrderDetail
} from '@/api/ordermgt.js'

export default {
  name: 'order_ordermgt_list',
  components: { Pagination },
  data() {
    return {
      submitOrder: {
      },
      submitFinishOrderInfo: {
        deliveryType: 0,
        logisticsInfo: ""
      },
      //发货方式列表
      deliveryTypes: [
        {
          "type": 0,
          "name": "物流"
        }, {
          "type": 1,
          "name": "自取"
        }
      ],
      finishOrderVisiable: false,
      //订单详情相关
      currentDetailOrderId: "",
      detailTotal: 0,
      detailCurrentPage: 1,
      detailParts: [],
      detailPageSize: 10,
      scheduleTitle: "订单详情",
      detailVisiable: false,
      //编辑订单相关
      editOrderVisiable: false,
      //国家全状态
      checkAll: false,
      currentUserCountries: [],
      isIndeterminate: true,
      orderStatusList: [
        {
          "name": "已提交",
          "code": "1"
        },
        {
          "name": "处理中",
          "code": "2"
        },
        {
          "name": "已取消",
          "code": "3"
        },
        {
          "name": "已完成",
          "code": "4"
        }
      ],
      resultList: [],
      formLabelWidth: '90px',
      // 搜索表单
      searchForm: {
        orderStatus: "",
        countries: [],
        startDate: "",
        endDate: "",
        orderNo: "",
        servicesSiteCode: "",
        servicesShort: ""
      },
      // 当前页
      currentPage: 1,
      // 每页显示的条数
      pageSize: 10,
      // 总条数
      total: 0,
      //展示列表
      // 国家集合
      userCountryList: [],
      dialogaddFormVisible: false,  // 添加弹窗
      dialogeditFormVisible: false,  // 编辑弹窗
      isfinish: true,
      // 校验
      //提交完成信息的校验
      finishRules: {
        logisticsInfo: [{ required: true, message: '物流信息不能为空', trigger: ['blur', 'change'] }],
      },
      //编辑订单的校验
      editOrderRule:{
        consignee: [{ required: true, message: '收货人不能为空', trigger: ['blur', 'change'] }],
        telephone: [
          { required: true, message: '联系电话不能为空', trigger: ['blur', 'change'] },
          { validator: validateMobile.bind(this), trigger: 'blur' },],
        address: [{ required: true, message: '地址不能为空', trigger: ['blur', 'change'] }]
      },
      pickerBeginTime: {
        disabledDate: (time) => {
          return this.searchForm.endDate != null ? time.getTime() > new Date(this.searchForm.endDate) : false //只能选结束日期之前的日期
          //返回---结束时间是否有值？   可选时间小于结束时间   ：  任意时间都可选
        }
      },
      pickerEndTime: {
        disabledDate: (time) => {
          return this.searchForm.startDate != null ? time.getTime() < new Date(this.searchForm.startDate) : false //只能选开始时间之后的日期
          //返回---开始时间是否有值？   可选时间大于开始时间   ：  任意时间都可选
        }
      },
      maximumHeight: 0,
    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth(){
      this.$nextTick(() => {
        this.$refs.table.doLayout();
        if(this.$refs.partTable){
          this.$refs.partTable.doLayout();
        }
      })
    },
    // 数据
    dataList() {
      var _this = this
      _this.$loading.show();
      var params = new URLSearchParams()
      params.append('pageNum', this.currentPage)
      params.append('pageSize', this.pageSize)
      params.append("status", this.searchForm.orderStatus)
      params.append("countries", this.searchForm.countries.join(","))
      params.append("startDate", this.searchForm.startDate)
      params.append("endDate", this.searchForm.endDate)
      params.append("orderNo", this.searchForm.orderNo)
      params.append("servicesSiteCode", this.searchForm.servicesSiteCode)
      params.append("servicesShort", this.searchForm.servicesShort)
      listOrderInfo(params).then(res => {
        if (res.data.code == 100) {
          _this.total = res.data.data.total    // 总条数
          _this.resultList = res.data.data.records   // 数据
        } else {
          handleAlert('error', res.data.msg)
        }
        _this.tableHeightArea()
        _this.$loading.hide();
      }).catch(err => {
        _this.$loading.hide();
        handleAlert('error', '系统开小差了...')
      })
    },
    getCurrUserCountryList() {
      var _this = this
      currUserCountryInfo().then(res => {
        if (res.data.code = 100) {
          _this.currentUserCountries = res.data.data;
        } else {
          handleAlert('error', res.data.msg)
        }
      }).catch(err => {
        handleAlert('error', '系统开小差了...')
      })
    },
    editStatus(row) {

    },
    // 时间转换
    dateFormat(row, column, cellValue, index) {
      if (cellValue !== null) {
        const date = new Date(parseInt(cellValue))
        const dayFormat = date.getFullYear() + "-" + (date.getMonth() + 1).paddingZero() + "-" + date.getDate().paddingZero()
        const hhmmss = date.getHours().paddingZero(2) + ':' + date.getMinutes().paddingZero(2) + ':' + date.getSeconds().paddingZero(2)
        return dayFormat + " " + hhmmss
      }
    },
    keyDown(e) {
      if (e.keyCode === 13) {
        this.search()
      }
    },
    search() {
      this.dataList();
    },
    handleSelectionChange(selections) {
      this.selectIdList = selections.map((item, index) => {
        return item.id;
      });
    },
    /**
     * 处理选中所有
     * @param {*} val
     */
    handleCheckAllChange(val) {
      this.searchForm.countries = val ? this.currentUserCountries.map((item, index) => {
        return item.code
      }) : [];
      this.isIndeterminate = false;
    },
    /**
     * 处理选中改变
     * @param {*} value
     */
    handleCheckedCitiesChange(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.currentUserCountries.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.currentUserCountries.length;
    },
    exportOrderList() {
      if (this.selectIdList == undefined ||  this.selectIdList.length == 0) {
        handleAlert('error', "未选中任何订单")
        return
      }
      var params = new URLSearchParams()
      params.append('orderIdList', this.selectIdList.join(","))
      exportOrderInfo(params).then(res => {
        if (res.headers['content-disposition'] !== undefined) {
          let blob = new Blob([res.data], { type: 'application/vnd.ms-excel;charset=utf-8' })
          let fileName = decodeURI(res.headers['content-disposition'])
          if (fileName) {
            fileName = fileName.substring(fileName.indexOf('=') + 1)
          }
          const elink = document.createElement('a')
          elink.download = fileName
          elink.style.display = 'none'
          elink.href = URL.createObjectURL(blob)
          document.body.appendChild(elink)
          elink.click()
          URL.revokeObjectURL(elink.href)
          document.body.removeChild(elink)
        }else{
          handleAlert('error', new TextDecoder("UTF-8").decode(res.data))
        }
      }).catch(err => {
        handleAlert('error', '系统开小差了...')
      })
    },
    resetSearch(fieldName) {
      this.searchForm.countries = []
      this.searchForm.endDate = ""
      this.searchForm.orderNo = ""
      this.searchForm.startDate = ""
      this.searchForm.orderStatus = ""
      this.searchForm.servicesShort = ""
      this.searchForm.servicesSiteCode = ""
      this.pageNum = 1
      this.pageSize = 10

    },
    showDetail(row) {
      this.currentDetailOrderId = row.id
      this.detailCurrentPage = 1
      this.detailPageSize = 10

      this.detailList()
      this.detailVisiable = true
    },
    detailList() {
      var _this = this
      var params = new URLSearchParams()
      params.append('pageNum', this.detailCurrentPage)
      params.append('pageSize', this.detailPageSize)
      params.append("orderId", this.currentDetailOrderId)
      getOrmOrderPartsDetailList(params).then(res => {
        if (res.data.code = 100) {
          _this.detailTotal = res.data.data.total
          _this.detailParts = res.data.data.records
        } else {
          handleAlert('error', res.data)
        }
      }).catch(err => {
        handleAlert('error', '系统开小差了...')
      })
    },
    confirmOrder(row) {
      if (row.status != 1) {
        handleAlert('error', '无法修改订单状态')
        return
      }
      var _this = this
      this.$confirm('是否确认订单?', '确认订单', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(_ => {
          var params = new URLSearchParams()
          params.append('id', row.id)
          params.append('status', 2)
          updateOrderStatusRequest(params).then(res => {
            if (res.data.code == 100) {
              handleAlert("success", "确认订单成功！")
              this.dataList()
            } else {
              handleAlert('error', res.data.msg)
            }
          }).catch(err => {
            handleAlert('error', '系统开小差了...')
          })
        })
        .catch(_ => { });

    },
    editOrder(row) {
      var _this = this
      var params = new URLSearchParams()
      params.append('orderId', row.id)
      getOrderDetail(params).then(res => {
        if (res.data.code == 100) {
          _this.submitOrder = res.data.data
          _this.editOrderVisiable = true
        } else {
          handleAlert('error', res.data.msg)
        }
      }).catch(err => {
        handleAlert('error', '系统开小差了...')
      })
      this.submitDetailParts
    },
    cancelOrder(row) {
      this.$prompt('撤销原因:', '撤销订单', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /.+/,
        inputPlaceholder: '请输入撤销原因'
        // inputPattern: /[\w!#$%&'*+/=?^_`{|}~-]+(?:\.[\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\w](?:[\w-]*[\w])?\.)+[\w](?:[\w-]*[\w])?/,
        // inputErrorMessage: '邮箱格式不正确'
      }).then(({ value }) => {
        var params = new URLSearchParams()
        params.append('id', row.id)
        params.append('status', 3)
        params.append('rejectMsg', value)
        updateOrderStatusRequest(params).then(res => {
          if (res.data.code == 100) {
            handleAlert("success", "撤销订单成功！")
            this.dataList()
          } else {
            handleAlert('error', res.data.msg)
          }
        }).catch(err => {
          handleAlert('error', '系统开小差了...')
        })
      }).catch(() => {

      });
    },
    finishOrder(row) {
      if (row.status !== 2) {
        handleAlert('error', '无法设置此订单为已完成')
        return
      }
      this.finishOrderVisiable = true
      this.submitFinishOrderInfo.id = row.id
      this.submitFinishOrderInfo.deliveryType = 0
      this.submitFinishOrderInfo.logisticsInfo = ""
    },
    delCurrentDetailItem(row) {
      this.submitOrder.ormOrderPartsDetailList = this.submitOrder.ormOrderPartsDetailList.filter(f => f.id != row.id)
    },
    /**
     * 提交编辑订单
     */
    submitEditOrder() {
      if (this.submitOrder == null) {
        handleAlert('error', '没有提交信息')
        return
      }
      if (this.submitOrder.ormOrderPartsDetailList.length == 0) {
        handleAlert('error', '订购配件不能为空')
        return
      }
      this.$refs['submitOrder'].validate((valid) => {
        if(valid){
          editOrderRequest(this.submitOrder).then(res => {
        if (res.data.code == 100) {
          this.editOrderVisiable = false
          handleAlert("success", "修改订单成功！")
          this.dataList()
        } else {
          handleAlert('error', res.data.msg)
        }
      }).catch(err => {
        handleAlert('error', '系统开小差了...')
      })
        }else{
          handleAlert('error',"请完善信息！")
        }
      })
    },
    handSubmitFinish() {
      if (this.submitFinishOrderInfo == null) {
        handleAlert('error', '未提交任何信息')
        return
      }
      this.$refs['submitFinishOrderInfo'].validate((valid) => {
        if (valid) {
          var params = new URLSearchParams()
          params.append('id', this.submitFinishOrderInfo.id)
          params.append('status', 4)
          params.append('deliveryType', this.submitFinishOrderInfo.deliveryType)
          if (this.submitFinishOrderInfo.deliveryType == 0) {
            params.append('logisticsInfo', this.submitFinishOrderInfo.logisticsInfo)
          }
          updateOrderStatusRequest(params).then(res => {
            this.finishOrderVisiable = false
            if (res.data.code == 100) {
              handleAlert("success", "提交成功！")
            } else {
              handleAlert('error', res.data.msg)
            }
            this.dataList()
          }).catch(error => {
            handleAlert('error', '未提交任何信息')
            this.finishOrderVisiable = false
          })
        } else {
          handleAlert('error', '未提交物流信息')
          return false;
        }
      });
    },
    onDeliveryTypeChange(value) {
      this.submitFinishOrderInfo.logisticsInfo = ""
    },
    maximumArea() {
      setTimeout(() => {
        tableHeight()
        setTimeout(() => {
          this.maximumHeight = tableMaxHeight;
        }, 50)
      })
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    }
  },
  mounted() {
    this.tableHeightArea()
    this.dataList();
    this.getCurrUserCountryList();
    window.addEventListener('keydown', this.keyDown)
  },
  destroyed() {
    window.removeEventListener('keydown', this.keyDown, false)
  }
}
const validateMobile = function (rule, value, callback) {
  let newValue = value.replace(/[^0-9]/gi, '')
  if (value !== newValue) {
    callback(new Error('请输入正确的手机号'))
  } else if (newValue.length !== 11) {
    callback(new Error('请输入正确的手机号'))
  } else {
    callback()
  }
}


</script>
