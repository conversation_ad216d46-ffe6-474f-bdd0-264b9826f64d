<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" ref="formInline" :label-width="formLabelWidth" :model="formInline" class="demo-form-inline">
        <el-form-item label="主题" prop="problemTheme">
          <el-input v-model.trim="formInline.problemTheme" placeholder="请输入问题主题"></el-input>
        </el-form-item>
        <el-form-item label="车系" prop="trainId">
          <el-select v-model="formInline.trainId" placeholder="请选择车系" clearable filterable>
            <el-option-group v-for="group in trainList" :key="group.id" :label="group.nameCh">
              <el-option v-for="item in group.children" :key="item.id" :label="item.nameCh" :value="item.id"></el-option>
            </el-option-group>
          </el-select>
        </el-form-item>
        <el-form-item label="服务店名称" prop="stationName">
          <el-input v-model.trim="formInline.stationName" placeholder="请输入服务店名称"></el-input>
        </el-form-item>
        <el-form-item label="服务店编码" prop="stationCode">
          <el-input v-model.trim="formInline.stationCode" placeholder="请输入服务店编码"></el-input>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="formInline.status" clearable>
            <el-option label="未回复" value="1"></el-option>
            <el-option label="已回复" value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit" icon="el-icon-search">搜索</el-button>
          <el-button plain @click="reset('formInline')">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="tableDetail">
      <!-- 分页查询 -->
      <el-table
        style="width:100%"
        border
        stripe
        highlight-current-row
        :data="resultList"
        :header-cell-style="{
          'text-align': 'center',
          'background-color': 'var(--other-color)',
        }"
      >
        <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
        <el-table-column label="主题" prop="malfunctionTheme" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column label="车系" prop="trainName" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column label="服务店编码" prop="stationCode" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column label="服务店名称" prop="stationName" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column label="提问时间" prop="createdTime" min-width="100" :formatter="dateFormat"></el-table-column>
        <el-table-column label="状态" prop="status" min-width="80" align="center"></el-table-column>
        <el-table-column label="操作" fixed="right" width="130">
          <template slot-scope="{row}">
            <el-button type="text" v-if="hasPerm('menuAsimss3A9B_101')" size="small" @click="handelReply(row)">回复</el-button>
            <el-button type="text" v-if="hasPerm('menuAsimss3A9B_102')" size="small" @click="handelDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total>0" :total="total" :page.sync="currentPage" :limit.sync="pagesize" @pagination="dataList"/>

      <el-dialog v-dialogDrag width="1000px !important" title="回复" :visible.sync="dialogFormVisible">
        <table width="100%" border="1" class="tabtop13">
          <tr>
            <td class="tdTitle" width="15%">服务店编码</td>
            <td width="35%">{{temp.stationCode}}</td>
            <td class="tdTitle" width="15%">服务店名称</td>
            <td width="35%">{{ temp.stationName }}</td>
          </tr>
          <tr>
            <td class="tdTitle" width="15%">提问人姓名</td>
            <td width="35%">{{ temp.questioner }}</td>
            <td class="tdTitle" width="15%">提问人电话</td>
            <td width="35%">{{ temp.phone }}</td>
          </tr>
          <tr>
            <td class="tdTitle" width="15%">车系</td>
            <td width="35%">{{ temp.trainName }}</td>
            <td class="tdTitle" width="15%">车型</td>
            <td width="35%">{{ temp.modelCode }}</td>
          </tr>
          <tr>
            <td class="tdTitle" width="15%">车牌</td>
            <td width="35%">{{ temp.plateNumber }}</td>
            <td class="tdTitle" width="15%">VIN</td>
            <td width="35%">{{ temp.vinCode }}</td>
          </tr>
          <tr>
            <td class="tdTitle" width="15%">发动机/驱动机电机代码</td>
            <td width="35%">{{ temp.engineCode }}</td>
            <td class="tdTitle" width="15%">生产日期</td>
            <td width="35%">{{ temp.producedDate }}</td>
          </tr>
          <tr>
            <td class="tdTitle" width="15%">销售日期</td>
            <td width="35%">{{ temp.salesDate }}</td>
            <td class="tdTitle" width="15%">故障日期</td>
            <td width="35%">{{ temp.malfunctionDate }}</td>
          </tr>
          <tr>
            <td class="tdTitle" width="15%">故障里程</td>
            <td width="35%">{{ temp.malfunctionMileage }}</td>
            <td class="tdTitle" width="15%">故障频次</td>
            <td width="35%">{{ temp.frequency }}</td>
          </tr>
          <tr>
            <td class="tdTitle" width="15%">故障码</td>
            <td width="35%">{{ temp.malfunctionCode }}</td>
            <td class="tdTitle" width="15%">保险公司</td>
            <td width="35%">{{ temp.insuranceCompany }}</td>
          </tr>
          <tr>
            <td class="tdTitle" width="15%">主题</td>
            <td width="85%" colspan="3">{{ temp.malfunctionTheme }}</td>
          </tr>
          <tr>
            <td class="tdTitle" width="15%">现象</td>
            <td width="85%" colspan="3">{{ temp.malfunction }}</td>
          </tr>
          <tr>
            <td class="tdTitle" width="15%">服务店措施</td>
            <td width="85%" colspan="3">{{ temp.measure }}</td>
          </tr>
          <tr>
            <th class="tdTitle" width="15%">附件</th>
            <td width="85%" colspan="3"><el-button v-if="temp.path.length>0" type="text" @click="lookClick(temp.path)">下载查看</el-button></td>
          </tr>
          <!-- </table>
          <div class="historyReply">
            <p>历史回复</p>
            <div>

            </div>

          </div> -->
          <tr style="border-top: 3px solid black;">
            <td class="tdTitle" style="padding-left:20px;color: #459F75;text-align:left"  colspan="4">历史回复</td>
          </tr>
          <!-- 历史回复，显示最新的回复，其他回复查看更多 -->
          <tr>
            <td colspan="4" v-if="temp.newReplyFlag" style="padding:3px 5px 3px 5px;">
              <el-row style="color: black;">
                <el-col :span="8">回复人：{{ temp.newReplyName }}</el-col>
                <el-col :span="8" align="center">&nbsp;<el-button v-if="temp.newReplyAtta.length>0" type="text" @click="lookClick(temp.newReplyAtta)">查看附件</el-button></el-col>
                <el-col :span="8" align="right">回复时间：{{ temp.newReplyTime }}</el-col>
              </el-row>
              <el-row>&emsp;{{ temp.newReplyContent }}</el-row>
              <el-row v-if="temp.replys.length>1">
                <el-button type="text">
                  <span @click="moreAllClick()">更多<i class="el-icon-d-arrow-right"></i></span>
                </el-button>
              </el-row>
            </td>
            <td colspan="4" v-if="!temp.newReplyFlag">&nbsp;</td>
          </tr>
          <tr style="border-top: 3px solid black;">
            <td class="tdTitle" style="padding-left:20px;color: #459F75;text-align:left"  colspan="4">回复</td>
          </tr>
          <!-- 回复的内容 -->
          <tr>
            <td colspan="4">
              <el-input type="textarea" :rows="4" v-model.trim="temp.replyContent" maxlength="160" show-word-limit></el-input>
            </td>
          </tr>
          <tr style="border-top: 3px solid black;">
            <td class="tdTitle" style="padding-left:20px;color: #459F75; text-align:left"  colspan="4">附件</td>
          </tr>
          <!-- 附件上传 -->
          <tr>
            <td colspan="4" style="height:30px;">
              <el-upload
                class="upload-demo"
                style="max-width: 379px;"
                :action="uploadUrl"
                :headers="importHeader"
                :on-success="handlesuccess"
                :on-remove="handleRemove"
                :before-remove="beforeRemove"
                :before-upload="beforeAvatarUpload"
                :on-exceed="handleExceed"
                multiple
                :limit="1"
                :file-list="imgList"
                accept=".jpg, .png, .jpeg, .mp4, .mp3, .xls, .xlsx, .doc, .ppt, .pptx, ,.pdf, .zip"

              >
                <el-button  icon="el-icon-upload" type="text">点击上传</el-button>
                <div slot="tip" class="el-upload__tip" style="width: 900px; margin: 0px;" v-if="isfinish">支持上传jpg, png, jpeg, mp4, mp3, xls, xlsx, doc, ppt, pptx, pdf, zip格式的文件</div>
              </el-upload>
            </td>
          </tr>
          <tr style="border-top: 3px solid black;">
            <td colspan="4" style="height:50px;">
              <el-button v-if="hasPerm('menuAsimss3A9B_101')" type="primary" size="medium" @click="replyClick()">提交</el-button>
            </td>
          </tr>
        </table>
      </el-dialog>

      <el-dialog v-dialogDrag width="800px !important" title="历史回复" :visible.sync="dialogReplyVisible">
        <el-table
          style="width:100%"
          height="540px"
          border
          highlight-current-row
          :data="temp.replys"
          :header-cell-style="{
            'text-align': 'center',
            'background-color': 'var(--other-color)',
          }"
        >
          <el-table-column label="序号" type="index" :index="indexMethod" width="60" align="center"></el-table-column>
          <el-table-column label="回复人" prop="username" min-width="60" show-overflow-tooltip></el-table-column>
          <el-table-column label="回复时间" prop="createdTime" min-width="60" :formatter="dateFormat" show-overflow-tooltip></el-table-column>
          <el-table-column label="附件" prop="path" width="80">
            <template slot-scope="{row}">
              <el-button v-if="row.path.length>0" type="text" size="small" @click="lookClick(row.path)">下载查看</el-button>
            </template>
          </el-table-column>
          <el-table-column label="内容" prop="content" min-width="120" show-overflow-tooltip></el-table-column>
          <el-table-column label="操作" fixed="right" width="80" align="center">
            <template slot-scope="{row}">
              <el-button type="text" v-if="hasPerm('menuAsimss3A9B_102')" size="small" @click="replyDelete(row)">删除</el-button>
            </template>
          </el-table-column>
      </el-table>
      </el-dialog>

    </div>
  </div>
</template>
<script>
import { handleAlert, sysServerUrl} from '@/assets/js/common.js'
import Pagination from '@/components/Pagination'
import { serviceData, serviceInfo, serviceDel, serviceReply, serviceTreeList } from '@/api/material.js';
export default {
  name: 'material_service_list',
  components: { Pagination },
  data () {
    return {
      formInline: {
        problemTheme: '',
        stationName: '',
        stationCode: '',
        trainId: '',
        status: '',
      },
      temp:{
        id: "",
        stationCode: "",
        stationName: '',
        questioner: '',
        phone: "",
        trainName: '',
        modelCode: "",
        engineCode: '',
        vinCode: '',
        producedDate: "",
        salesDate: '',
        malfunctionDate: '',
        malfunctionMileage: "",
        insuranceCompany: '',
        frequency: "",
        malfunctionCode: "",
        malfunctionTheme: "",
        malfunction: "",
        measure: '',
        path: "",
        replys: [],

        // 回复的附件
        newReplyAccessory: '',
        replyContent: '',

        // 最新回复
        newReplyContent: '',  // 内容
        newReplyAtta: '',   // 附件
        newReplyName: '',   // 回复人
        newReplyTime: '',   // 回复时间
        newReplyFlag: false,
      },

      dialogFormVisible: false,
      dialogReplyVisible: false,

      isfinish:true,

      resultList: [],
      trainList: [],
      problemTypeList: [],

      pagesize: 20,
      currentPage: 1,
      total: 0,
      formLabelWidth: '100px',
      replyLabelWidth: '180px',
      urls: [],
      replyContentList: [],



      uploadUrl: '',
      imgList: [],
      isFlag: true,
    }
  },
  computed: {
    importHeader: function () {
      return { Authorization: sessionStorage.token };
    }
  },
  methods: {
    // 分页查询数据
    dataList () {
      var params = {
        page: this.currentPage,   // 当前页
        limit: this.pagesize,     // 每页显示的条数
        stationName: this.formInline.stationName,
        malfunctionTheme: this.formInline.problemTheme,
        stationCode: this.formInline.stationCode,
        trainId: this.formInline.trainId,
        status: this.formInline.status,
      }
      serviceData(params).then(res => {
        this.total = res.data.total    // 总条数
        this.resultList = res.data.data   // 数据

      })
    },

    // 搜索
    onSubmit () {
      this.currentPage=1
      this.dataList();
    },

    indexMethod(index){
      return this.temp.replys.length - index;
    },

   // 回复
   handelReply(row){
    this.resetRealy();

    var params = {
        id: row.id
    }
    // 查询问题详情
    serviceInfo(params).then(res => {

      if (res.data.code == 100 && res.data.data) {
        this.temp = Object.assign({}, res.data.data)
        if (this.temp.replys.length>0) {
          this.temp.newReplyFlag = true;
          let obj = this.temp.replys[0]
          this.temp.newReplyContent = obj.content
          this.temp.newReplyName = obj.username
          this.temp.newReplyTime = this.dateFormat('', '', obj.createdTime, '')
          this.temp.newReplyAtta = obj.path
        }


      }
    })
    this.dialogFormVisible = true;
   },
   // 确定回复
   replyClick(){
    if (!this.temp.replyContent || this.temp.replyContent.length<5) {
      handleAlert('error','回复内容不能少于5个字')
      return
    }
    var params = new URLSearchParams()
    params.append("problemId", this.temp.id)
    params.append("content", this.temp.replyContent)
    if (this.temp.newReplyAccessory && this.temp.newReplyAccessory.length>0) {
      params.append("accessory", this.temp.newReplyAccessory)
    }

    serviceReply(params).then(res => {

      if (res.data.code === 100) {
        handleAlert('success','操作成功')
        this.dataList()
        this.dialogFormVisible = false;
      }else{
        handleAlert('error','操作失败')
      }
    })
   },

    // 删除
    handelDelete (row) {

      var _this=this
      this.$confirm('确定删除【' + row.malfunctionTheme + '】的问题码?', '删除技术问答', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var params = new URLSearchParams()
        params.append("id", row.id)
        params.append("type", "1")
        serviceDel(params).then(res => {
          if (res.data.code === 100) {
            handleAlert('success','删除成功')
            if(this.resultList!=null&&this.resultList.length==1){
              this.currentPage =this.currentPage-1
            }
            this.dataList()
          }else{
            handleAlert('error','删除失败')
          }
        })
      }).catch((error)=>{
        handleAlert('info','取消删除')
      })
    },

    // 回复的更多
    moreAllClick(){

      this.dialogReplyVisible = true
    },
    // 查看附件
    lookClick(path){
      if (path.length<=0) {
        handleAlert('warning','没有附件可以查看')
        return false
      }
      // window.open(path, '_blank');   // 会闪屏
      const ele = document.createElement('a'); //新建一个a标签
      ele.setAttribute('href', path);
      ele.setAttribute('target', '_blank')
      ele.click();

    },

    // 重置
    reset (formInline) {
      if (this.$refs[formInline].resetFields() !== undefined) {
        this.$refs[formInline].resetFields()
      }
      this.formInline.problemTheme = ''
      this.formInline.stationName = ''
      this.formInline.stationCode = ''
      this.formInline.trainId = '';
      this.formInline.status = '';
      this.currentPage = 1
      this.dataList()
    },

    resetRealy(){
      this.temp = {
        id: "",
        stationCode: "",
        stationName: '',
        questioner: '',
        phone: "",
        trainName: '',
        modelCode: "",
        engineCode: '',
        vinCode: '',
        producedDate: "",
        salesDate: '',
        malfunctionDate: '',
        malfunctionMileage: "",
        insuranceCompany: '',
        frequency: "",
        malfunctionCode: "",
        malfunctionTheme: "",
        malfunction: "",
        measure: '',
        path: "",
        replys: [],

        // 回复的附件
        newReplyAccessory: '',
        replyContent: '',

        // 最新回复
        newReplyContent: '',  // 内容
        newReplyAtta: '',   // 附件
        newReplyName: '',   // 回复人
        newReplyTime: '',   // 回复时间
        newReplyFlag: false,
      }
      this.isfinish = true
      this.imgList = []
      this.isFlag = true
    },

    // 获取品牌车系
    getBrandTrainList(){
      serviceTreeList().then(res => {
        this.trainList = res.data.data
      }).catch(err => {
        handleAlert('error','系统开小差了...')
      })
    },



    // ==== 上传附件 ===== //
    beforeAvatarUpload (file) {
      this.temp.newReplyAccessory = ''
      this.isfinish = true
      this.uploadUrl = sysServerUrl + 'sys/upload/attach?flag=temp/service/' + this.temp.id
      let suffix = ['jpg', 'png', 'jpeg', 'mp4', 'mp3', 'xls', 'xlsx', 'doc', 'ppt', 'pptx', 'pdf','zip']
      var fileName = file.name.substring(file.name.lastIndexOf(".")+1).toLowerCase()
      const isLt2M = file.size / 1024 / 1024 < 100
      if (!suffix.includes(fileName)) {
        handleAlert('warning','上传模板只能是jpg, png, jpeg, mp4, mp3, xls, xlsx, doc, ppt, pptx, pdf, zip格式!')
        this.isFlag = false;
        return false;
      }
      if (!isLt2M) {
        handleAlert('warning','上传模板大小不能超过 100MB!')
        this.isFlag = false;
        return false;
      }
      // return isLt2M
    },
    handlesuccess (file, fileList) {

      this.isfinish = false
      this.imgList = []
      this.temp.newReplyAccessory = file.data.fileUrl;
      var img = { name: file.data.fileName, url: sysServerUrl + 'sys/upload/display?filePath=' + file.data.fileUrl }
      this.imgList.push(img)

      this.isFlag = true;
    },
    handleRemove (file, fileList) {
      this.isfinish = true
      this.imgList=[]
      this.isFlag = true;
      this.temp.newReplyAccessory = ''
    },
    handleExceed (files, fileList) {
      handleAlert('warning',`当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
    },
    beforeRemove (file, fileList) {
      if (this.isFlag) {
        return this.$confirm(`确定移除选择文件？`, '删除',{type: 'warning'});
      }
    },

    replyDelete(row){

      var _this=this
      this.$confirm('确定删除【' + row.username + '】的回复吗?', '删除回复', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.temp.replys = this.temp.replys.filter(item => item.id != row.id)
        let obj = this.temp.replys[0]
        this.temp.newReplyContent = obj.content
        this.temp.newReplyName = obj.username
        this.temp.newReplyTime = this.dateFormat('', '', obj.createdTime, '')
        this.temp.newReplyAtta = obj.path

        var params = new URLSearchParams()
        params.append("id", row.id)
        params.append("type", "2")
        serviceDel(params).then(res => {
          if (res.data.code === 100) {
            handleAlert('success','删除成功')
          }else{
            handleAlert('error','删除失败')
          }
        })
        if (this.temp.replys.length<=1) {
          this.dialogReplyVisible = false
          return false
        }
      }).catch((error)=>{
        handleAlert('info','取消删除')
      })
    },


    dateFormat (row, column, cellValue, index) {
      if (cellValue !== null) {
        const date = new Date(parseInt(cellValue))
        const Y = date.getFullYear() + '-'
        const M = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) + '-' : date.getMonth() + 1 + '-'
        const D = date.getDate() < 10 ? '0' + date.getDate() + '' : date.getDate() + ''
        return Y + M + D
      }
    }
  },
  mounted () {
    this.dataList();
    this.getBrandTrainList();
  }
}
</script>
