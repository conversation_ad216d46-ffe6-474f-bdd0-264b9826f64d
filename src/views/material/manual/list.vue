<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" label-width="85px" ref="searchForm" :model="searchForm" class="demo-form-inline">
        <el-form-item label="车型" prop="trainId">
          <el-select v-model="searchForm.trainId" placeholder="请选择车型" @change="getYearList" clearable filterable>
            <el-option-group v-for="group in trainList" :key="group.id" :label="group.nameCh">
              <el-option v-for="item in group.children" :key="item.id" :label="item.nameCh"
                :value="item.id"></el-option>
            </el-option-group>
          </el-select>
        </el-form-item>

        <!-- <el-form-item label="年款" prop="modelYear">
          <el-select v-model="searchForm.modelYear" placeholder="请选择年款" clearable filterable>
              <el-option v-for="(item,index) in yearList" :key="index" :label="item" :value="item"></el-option>
            </el-select>
        </el-form-item> -->
        <el-form-item label="手册类型" prop="manualType">
          <el-select v-model="searchForm.manualType" placeholder="请选择手册类型" clearable filterable>
            <el-option v-for="(item, index) in manualTypeList" :key="index" :label="item.name"
              :value="item.code"></el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="语种" prop="language">
          <el-select v-model="searchForm.language" placeholder="请选择语种" clearable filterable>
            <el-option v-for="(item, index) of languageTypeList" :key="index" :label="item.name" :value="item.code"></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item>
          <el-button type="primary" @click="search()" icon="el-icon-search">搜索</el-button>
          <el-button plain @click="resetSearch('searchForm')">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <div class="tableHandle" v-if="hasPerm('menuAsimss3A7B_101')">
        <el-button type="text" icon="el-icon-plus" @click="addClick()">新增</el-button>
      </div>
      <!-- 列表内容 -->
      <el-table style="width:100%" border stripe ref="table" highlight-current-row :max-height="maximumHeight"
        :data="resultList" :header-cell-style="{
          'text-align': 'center',
          'background-color': 'var(--other-color)',
        }" @header-dragend="changeColWidth">
        <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
        <el-table-column label="手册名称" prop="name" min-width="140" show-overflow-tooltip></el-table-column>
        <el-table-column label="手册分类" prop="typeName" min-width="80" align="center"></el-table-column>
        <!-- <el-table-column label="语种" prop="languageName" min-width="70" align="center"></el-table-column> -->
        <!-- <el-table-column label="适用国家" prop="sltCountry" min-width="100" show-overflow-tooltip></el-table-column> -->
        <el-table-column label="访问次数" prop="visitTimes" width="80" align="center"></el-table-column>
        <el-table-column label="适用车型" prop="trainName" min-width="100" show-overflow-tooltip>
          <template slot-scope="{row}">
            <div style="white-space: pre-wrap;">{{ row.trainName }}</div>
          </template>
        </el-table-column>
        <!-- <el-table-column label="年款" prop="modelYear" min-width="100"></el-table-column> -->
        <el-table-column label="状态" prop="status" min-width="80" align="center">
          <template slot-scope="{row}">
            <span v-if="row.status === 1">草稿</span>
            <span v-if="row.status === 2" style="color:#009933">发布</span>
            <span v-if="row.status === 3" style="color:#c30000">关闭</span>
          </template>
        </el-table-column>
        <el-table-column label="描述" prop="remark" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column label="更新人员" prop="username" width="100" align="center"></el-table-column>
        <el-table-column label="更新日期" prop="updatedTime" width="160" align="center">
          <template slot-scope="{row}">
            <div>
              {{ row.updatedTime | conversion("yyyy-MM-dd HH:mm:ss") }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" min-width="220">
          <template slot-scope="{row}">
            <el-button type="text" size="small" v-if="hasPerm('menuAsimss3A7B_104')"
              @click="headerDetail(row)">详情</el-button>
            <el-button type="text" size="small" v-if="hasPerm('menuAsimss3A7B_104')"
              @click="headerDwon(row)">下载</el-button>
            <el-button v-if="row.status == 2 && hasPerm('menuAsimss3A7B_103')" type="text" size="small"
              @click="editStatus(row, 'close')">关闭</el-button>
            <el-button v-if="row.status == 3 && hasPerm('menuAsimss3A7B_103')" type="text" size="small"
              @click="editStatus(row, 'open')">开启</el-button>
            <el-button v-if="row.status == 1 && hasPerm('menuAsimss3A7B_103')" type="text" size="small"
              @click="editStatus(row, 'issue')">发布</el-button>
            <el-button v-if="row.status == 2 && hasPerm('menuAsimss3A7B_103')" type="text" size="small"
              @click="editStatus(row, 'revocation')">撤回</el-button>
            <el-button v-if="row.status == 1 && hasPerm('menuAsimss3A7B_103')" type="text" size="small"
              @click="editClick(row)">编辑</el-button>
            <el-button v-if="row.status == 1 && hasPerm('menuAsimss3A7B_102')" type="text" size="small"
              @click="del(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="currentPage" :limit.sync="pagesize"
        @pagination="dataList" />

      <!-- 添加手册 -->
      <el-dialog v-dialogDrag title="添加手册" width="800px !important" :visible.sync="dialogaddFormVisible"
        v-if="dialogaddFormVisible" :close-on-click-modal="false">
        <el-form ref='temp' :rules="rules" :label-width="formLabelWidth" :model="temp" label-position="center"
          :validate-on-rule-change="false">
          <!-- <el-form-item label="国家" prop="country">
              <el-select v-model="temp.country" filterable @change="getManualByCountry">
                  <el-option v-for="(item, index) of countryList" :key="index" :label="item.name" :value="item.code"></el-option>
              </el-select>
            </el-form-item> -->
          <el-form-item label="适用车型" prop="trainId">
            <!-- 2023-12-23 车型由单选 改为 多选 -->
            <el-select v-model.trim="temp.trainId" placeholder="请选择车型" multiple clearable filterable>
              <el-option-group v-for="group in trainList" :key="group.id" :label="group.nameCh">
                <el-option v-for="item in group.children" :key="item.id" :label="item.nameCh" :value="item.id">
                  <span class="checkbox"></span>
                  <span class="label-name-box" style="margin-left: 8px;">{{ item.nameCh }}</span>
                </el-option>
              </el-option-group>
            </el-select>
          </el-form-item>

          <el-form-item label="手册类型" prop="type">
            <el-select v-model.trim="temp.type" placeholder="请选择手册类型" clearable filterable>
              <el-option v-for="(item, index) in manualTypeList" :key="index" :label="item.name"
                :value="item.code"></el-option>
            </el-select>
          </el-form-item>

          <!-- <el-form-item label="名称" prop="name">
              <el-input v-model.trim="temp.name" oninput="value=value.replace(/[/\\]/g,'')" show-word-limit maxlength="50" placeholder="请输入手册名称" ></el-input>
            </el-form-item> -->
          <el-form-item label="描述" prop="remark">
            <el-input v-model.trim="temp.remark" placeholder="请输入手册描述" show-word-limit maxlength="200"></el-input>
          </el-form-item>

          <!-- 文件上传 -->
          <el-form-item>
            <el-upload class="upload-demo inline-block" ref="elUpload" action="#" :show-file-list="false" multiple
              :limit="1" :before-upload="onBeforeUpload">
              <el-button type="primary" v-show="showUploadBtn">选择文件</el-button>
              <div slot="tip" class="el-upload__tip" v-if="isfinish">只能上传pdf文件，且【手册名称】为文件名</div>
            </el-upload>

            <el-progress class="el_progress2" style="width:350px;margin-top:50px;margin-left:10px" readonly="readonly"
              v-show="showUploadProcess" color="green" type="line" text-color="#ffffff" :text-inside="true"
              :stroke-width="20" :percentage="percentage"></el-progress>

            <el-upload ref="elUploadResult" action="#" :show-file-list="true" :file-list="zipList" :limit="1"
              :before-remove="handleBeforeRemove">
            </el-upload>
          </el-form-item>
          <div class="submitArea">
            <el-button type="primary" @click="add()">提交</el-button>
            <el-button plain @click="dialogaddFormVisible = false">取消</el-button>
          </div>
        </el-form>
      </el-dialog>

      <!-- 编辑手册 -->
      <el-dialog v-dialogDrag title="编辑手册" width="600px !important" :visible.sync="dialogeditFormVisible"
        :close-on-click-modal="false">
        <el-form :model="temp" label-position="center" :label-width="formLabelWidth" :validate-on-rule-change="false"
          :rules="rulesEdit">
          <!-- <el-form-item label="国家" prop="countryName">
              <el-input v-model.trim="temp.countryName"  readonly="readonly" disabled></el-input>
            </el-form-item> -->
          <el-form-item label="车型" prop="trainName" show-overflow-tooltip>
            <el-select v-model.trim="temp.trainId" placeholder="请选择车型" multiple clearable filterable>
              <el-option-group v-for="group in trainList" :key="group.id" :label="group.nameCh">
                <el-option v-for="item in group.children" :key="item.id" :label="item.nameCh" :value="item.id">
                  <span class="checkbox"></span>
                  <span class="label-name-box" style="margin-left: 8px;">{{ item.nameCh }}</span>
                </el-option>
              </el-option-group>
            </el-select>
          </el-form-item>

          <el-form-item label="手册类型" prop="typeName">
            <el-input v-model.trim="temp.typeName" readonly="readonly" disabled></el-input>
          </el-form-item>

          <el-form-item label="名称" prop="name">
            <el-input v-model.trim="temp.name" oninput="value=value.replace(/[/\\]/g,'')" show-word-limit maxlength="50"
              placeholder="请输入手册名称"></el-input>
          </el-form-item>
          <el-form-item label="描述" prop="remark">
            <el-input v-model.trim="temp.remark" placeholder="请输入手册描述" show-word-limit maxlength="200"></el-input>
          </el-form-item>


          <!-- 文件上传 -->
          <el-form-item>
            <el-upload class="upload-demo inline-block" ref="elUpload" action="#" :show-file-list="false" multiple
              :limit="1" :before-upload="onBeforeUpload">
              <el-button type="primary" v-show="showUploadBtn">替换手册</el-button>
              <div slot="tip" class="el-upload__tip" v-if="isfinish">只能上传pdf文件，且文件以【手册名称】命名</div>
            </el-upload>

            <el-progress class="el_progress2" style="width:350px;margin-top:50px;margin-left:10px;" readonly="readonly"
              v-show="showUploadProcess" color="green" type="line" text-color="#ffffff" :text-inside="true"
              :stroke-width="20" :percentage="percentage"></el-progress>

            <el-upload ref="elUploadResult" action="#" :show-file-list="true" :file-list="zipList" :limit="1"
              :before-remove="handleBeforeRemove">
            </el-upload>
          </el-form-item>
          <div class="submitArea">
            <el-button type="primary" @click="edit()">提交</el-button>
            <el-button plain @click="dialogeditFormVisible = false">取消</el-button>
          </div>
        </el-form>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { sysServerUrl, addTabs, handleAlert, tableHeight, tableMaxHeight } from '@/assets/js/common.js'
import Pagination from '@/components/Pagination'
import { manualTreeList, manualYearList, manualAdd, manualEdit, manualDel, manualStatus, manualData, manualType, manualLanguage, basicFinishUpload, userCountryData, manualDown } from '@/api/material.js'
import { procSplitFile, checkUploadProgress } from '@/api/sysmgt.js'

import SparkMD5 from 'spark-md5'

/** 计算文件md5值
 * @param file 文件
 * @param chunkSize 分片大小
 * @returns Promise
 */
function getmd5(file, chunkSize) {
  return new Promise((resolve, reject) => {
    let blobSlice = File.prototype.slice || File.prototype.mozSlice || File.prototype.webkitSlice;
    let chunks = Math.ceil(file.size / chunkSize);
    let currentChunk = 0;
    let spark = new SparkMD5.ArrayBuffer();
    let fileReader = new FileReader();
    fileReader.onload = function (e) {
      spark.append(e.target.result);
      currentChunk++;
      if (currentChunk < chunks) {
        loadNext();
      } else {
        let md5 = spark.end();
        resolve(md5);

      }
    };
    fileReader.onerror = function (e) {
      reject(e);
    };
    function loadNext() {
      let start = currentChunk * chunkSize;
      let end = start + chunkSize;
      if (end > file.size) {
        end = file.size;
      }
      fileReader.readAsArrayBuffer(blobSlice.call(file, start, end));
    }
    loadNext();
  });
}



export default {
  name: 'material_manual_list',
  components: { Pagination },
  data() {
    return {
      formLabelWidth: '100px',
      // 搜索表单
      searchForm: {
        trainId: '',  // 车系
        modelYear: '',  // 年款
        manualType: '',  // 手册类型
        language: '',  // 语种
      },

      // 当前页
      currentPage: 1,
      // 每页显示的条数
      pagesize: 10,
      // 总条数
      total: 0,

      // 车系的数据默认字段
      defaultProps: {
        parent: 'pid',   // 父级唯一标识
        value: 'id',          // 唯一标识
        label: 'nameCh',       // 标签显示
        children: 'children', // 子级
      },

      // 获取的数据集
      resultList: [],
      // 年款结果集
      yearList: [],
      // 车系结果集
      trainList: [],
      // 手册类型结果集
      manualTypeList: [],
      // 语种结果集
      languageTypeList: [],
      // 国家集合
      userCountryList: [],


      dialogaddFormVisible: false,  // 添加弹窗
      dialogeditFormVisible: false,  // 编辑弹窗

      // 添加的表单
      temp: {
        id: '',  // ID
        name: '',  // 名称
        manualName: '',  // 类型名称
        type: '',  // 手册类型
        typeName: '',
        language: '',   // 语种
        languageName: '',  // 语种名称
        modelYear: '',  // 年款
        trainId: [],  // 车系ID
        trainName: '',
        status: '', // 状态
        path: '', // 手册路径
        remark: '',  // 手册说明
        country: ['cn'],  // 适用国家
      },
      isfinish: true,
      // 分片上传
      fileList: [],
      zipList: [],
      //切片文件
      fileShard: {},
      //当前文件
      curFile: {},
      //文件分割的开始位置
      start: 0,
      //文件分割的结束位置
      end: 0,
      //文件大小
      fileSize: 0,
      fileKey: '',
      fileShardIndex: 0,
      fileShardTotal: 0,
      fileShardSize: 0,
      switchC: false,
      percentage: 0,
      showUploadBtn: false,
      showUploadProcess: false,
      flagType: 'temp/manual',

      // 校验
      rules: {
        trainId: [{ required: true, message: '车型不能为空', trigger: ['blur', 'change'] }],
        // modelYear: [{ required: true, message: '年款不能为空', trigger:['blur', 'change'] }],
        type: [{ required: true, message: '手册类型不能为空', trigger: ['blur', 'change'] }],
        // language: [{ required: true, message: '语种不能为空', trigger: ['blur', 'change'] }],
        name: [{ required: true, message: '名称不能为空', trigger: ['blur', 'change'] }],
      },
      rulesEdit: {
        name: [{ required: true, message: '名称不能为空', trigger: ['blur', 'change'] }],
      },
      maximumHeight: 0,
    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    // 数据
    dataList() {
      this.$loading.show();
      var params = new URLSearchParams()
      params.append('page', this.currentPage)
      params.append('limit', this.pagesize)
      params.append('trainId', this.searchForm.trainId)
      params.append('type', this.searchForm.manualType)
      // params.append('modelYear', this.searchForm.modelYear)
      params.append('language', this.searchForm.language)
      manualData(params).then(res => {
        if (res.data.code == 100) {
          this.total = res.data.total    // 总条数
          this.resultList = res.data.data   // 数据
        } else {
          handleAlert('error', res.data.msg)
        }
        this.tableHeightArea()
        this.$loading.hide();
      }).catch(err => {
        this.$loading.hide();
        handleAlert('error', '系统开小差了...')
      })
    },

    // 搜索
    keyDown(e) {
      if (e.keyCode === 13) {
        this.search()
      }
    },
    search() {
      this.currentPage = 1;
      this.dataList();
    },

    // 重置搜索表单
    resetSearch() {
      this.searchForm.trainId = '';
      this.searchForm.manualType = '';
      this.searchForm.modelYear = '';
      this.searchForm.language = '';
      this.currentPage = 1;
      this.dataList();
    },

    // 重置添加表单
    resetAdd() {
      this.temp = {
        id: '',  // ID
        name: '',  // 名称
        manualName: '',  // 类型名称
        type: '',  // 手册类型
        typeName: '',
        language: '',   // 语种
        languageName: '',  // 语种名称
        modelYear: '',  // 年款
        trainId: [],  // 车系ID
        trainName: '',
        status: '', // 状态
        path: '', // 手册路径
        remark: '',  // 手册说明
        country: ['cn'],  // 适用国家
      };
      let _this = this
      setTimeout(() => {
        if (_this.$refs.temp) {
          _this.$nextTick(function () {
            _this.$refs.temp.clearValidate();
          })
        };
      })
      this.isfinish = true;
      this.fileList = [];
      this.zipList = [];
      this.showUploadBtn = true;
      this.showUploadProcess = false;
      this.percentage = 0;
    },

    // 获取国家
    getUserCountryList() {
      userCountryData().then(res => {
        this.userCountryList = res.data.data
      })
    },

    // 获取品牌车系
    getBrandTrainList() {
      manualTreeList().then(res => {
        this.trainList = res.data.data
      }).catch(err => {
        handleAlert('error', '系统开小差了...')
      })
    },

    // 获取年款
    getYearList(trainId) {
      this.searchForm.modelYear = '';
      var params = {
        tearId: trainId
      }
      manualYearList(params).then(res => {
        this.yearList = res.data.data
      }).catch(err => {
        handleAlert('error', '系统开小差了...')
      })
    },

    // 获取手册类型
    getManualList() {
      manualType().then(res => {
        if (res !== null && res.data.code === 100) {
          this.manualTypeList = res.data.data
        }
      })
    },

    // 获取语种类型
    getLanguage() {
      manualLanguage().then(res => {
        if (res !== null && res.data.code === 100) {
          this.languageTypeList = res.data.data
        }
      })
    },

    // 添加-显示弹窗
    addClick() {
      this.resetAdd();
      this.dialogaddFormVisible = true;
    },

    // 下载手册
    headerDwon(row) {
      if (row && row.path && row.path.length > 0) {
        manualDown(row.path).then(res => {
          if (!res.data) {
            handleAlert("error", "下载失败")
            return false
          }
          var name = row.name + "_" + row.typeName + ".pdf";
          var blob = new Blob([res.data]);
          var url = window.URL.createObjectURL(blob);
          var aLink = document.createElement("a");
          aLink.style.display = "none";
          aLink.href = url;
          aLink.setAttribute("download", name);
          document.body.appendChild(aLink);
          aLink.click();
          document.body.removeChild(aLink); //下载完成移除元素
          window.URL.revokeObjectURL(url); //释放掉blob对象
        }).catch(e => {
          handleAlert("error", "下载失败")
        })
      }
    },

    // 添加
    add() {
      this.$refs['temp'].validate((valid) => {
        if (valid) {
          if (!this.zipList || this.zipList.length <= 0) {
            handleAlert('warning', '手册文档不能为空')
            return false
          }

          let name = this.zipList[0].name
          if (name && name.length >= 50) {
            handleAlert('warning', '手册名称不能大于50个字符')
            return false
          }
          this.$loading.show();
          var params = new URLSearchParams()
          params.append('name', name)
          params.append('trainId', this.temp.trainId.toString())
          // params.append('modelYear', this.temp.modelYear)
          params.append('type', this.temp.type)
          params.append('language', this.temp.language)
          params.append('path', this.zipList[0].filePath)
          params.append('remark', this.temp.remark)
          if (this.temp.country && this.temp.country.length > 0) {
            params.append('sltCountry', this.temp.country.toString())
          }

          manualAdd(params).then(res => {
            if (res.data.code == 100) {
              handleAlert('success', '提交成功')
              this.dataList();
              this.dialogaddFormVisible = false;
              this.zipList = []
            } else {
              handleAlert('error', res.data.msg)
            }
            this.$loading.hide();
          }).catch(err => {
            handleAlert('error', '系统开小差了...');
            this.$loading.hide();
          })
        } else {
          handleAlert('warning', '请完善表单信息')
        }
      })
      // if(!this.temp.name || this.temp.name == '' || this.temp.name == 'null' || this.temp.name == 'undefined'){
      //   handleAlert('error','手册名称不能为空')
      //   return false
      // }
      // if(!this.temp.trainId || this.temp.trainId.length<=0 || this.temp.trainId == '' || this.temp.trainId == 'null' || this.temp.trainId == 'undefined'){
      //   handleAlert('error','车型不能为空')
      //   return false
      // }
      // if(!this.temp.modelYear || this.temp.modelYear == '' || this.temp.modelYear == 'null' || this.temp.modelYear == 'undefined'){
      //   handleAlert('error','年款不能为空')
      //   return false
      // }
      // if(!this.temp.type || this.temp.type == '' || this.temp.type == 'null' || this.temp.type == 'undefined'){
      //   handleAlert('error','手册类型不能为空')
      //   return false
      // }
      // if(!this.temp.language || this.temp.language == ''){
      //   handleAlert('error','语种不能为空')
      // }

    },

    // 编辑
    editClick(row) {
      this.resetAdd();
      this.temp.trainName = row.trainName
      this.temp.typeName = row.typeName
      this.temp.languageName = row.languageName
      this.temp.name = row.name
      // this.temp.modelYear = row.modelYear
      this.temp.id = row.id
      this.temp.remark = row.remark
      this.temp.country = row.countryCodeList
      this.temp.path = row.path
      this.zipList = []
      let obj = {
        name: row.name + ".pdf",
        filePath: row.path
      }
      this.zipList.push(obj)
      setTimeout(() => {
        var length = $(".el-upload-list").children("li").length;
        for (var i = 0; i < length; i++) {
          $(".el-upload-list__item.is-success:nth-child(" + (i + 1) + ")")
            .children("a")
            .children("i")
            .addClass("pdf");
        }
      }, 150);
      this.getBrandTrainList()
      setTimeout(() => {
        let list = row.trainId.split(",");
        for (let i = 0; i < list.length; i++) {
          this.temp.trainId.push(list[i])
        }
        this.dialogeditFormVisible = true
      })
    },

    edit() {
      if (!this.temp.name || this.temp.name == '' || this.temp.name == 'null' || this.temp.name == 'undefined') {
        handleAlert('error', '手册名称不能为空')
        return false;
      }
      this.$loading.show()
      var params = new URLSearchParams()
      params.append("id", this.temp.id)
      if (this.zipList && this.zipList.length > 0 && this.zipList[0].filePath != this.temp.path) {
        params.append('path', this.zipList[0].filePath)
      }

      params.append('name', this.temp.name)
      params.append('remark', this.temp.remark)
      if (this.temp.country && this.temp.country.length > 0) {
        params.append('sltCountry', this.temp.country.toString())
      }
      params.append('trainId', this.temp.trainId.toString())
      manualEdit(params).then(res => {
        if (res.data.code == 100) {
          handleAlert('success', '成功')
          this.dataList();
          this.dialogeditFormVisible = false;
          this.zipList = []
        } else {
          handleAlert('error', res.data.msg)
        }
        this.$loading.hide()
      }).catch(err => {
        handleAlert('error', '系统开小差了...')
        this.$loading.hide()
      })
    },

    // 修改状态
    editStatus(row, sign) {
      var params = new URLSearchParams()
      params.append('id', row.id)
      params.append('sign', sign)
      manualStatus(params).then(res => {
        if (res.data.code === 100) {
          handleAlert('success', '操作成功')
          this.dataList()
        } else {
          handleAlert('error', res.data.msg)
        }
      }).catch(err => {
        handleAlert('error', '系统开小差了...')
      })
    },

    // 删除
    del(row) {
      this.$confirm('确定删除【' + row.name + '】手册吗?', '删除手册', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        manualDel(row.id).then(res => {
          if (res.data.code === 100) {
            handleAlert('success', '删除成功')
            if (this.resultList != null && this.resultList.length == 1) {
              this.currentPage = this.currentPage - 1
            }
            this.dataList();
          } else {
            handleAlert('error', '删除失败，' + res.data.msg)
          }
        })
      }).catch((error) => {
        handleAlert('info', '取消删除')
      })
    },

    // 详情
    headerDetail(row) {
      let title = "手册管理>详情"
      this.$router.push({ name: 'manualDetails', params: { id: row.id } })
      addTabs(this.$route.path, row.name);
    },


    // 上传前的校验
    onBeforeUpload(file) {
      let text = ""
      if (this.zipList.length > 0) {
        text = "已经存在文件，请先删除!";
        this.$message.error(text)
        return false;
      }
      this.flagType = 'temp/manual'

      // 获取文件后缀
      var fileExt = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase();
      // 文件后缀是否是 zip
      const zipExt = fileExt === 'pdf'
      // 文件大小不能超过1G
      const isLimit = file.size / 1024 / 1024 < 1024
      if (!zipExt) {
        text = "上传文件只能是 pdf 格式!";
        this.$message.error(text)
        return false;
      }
      if (!isLimit) {
        text = "上传文件大小不能超过 1GB!";
        this.$message.error(text)
        return false;
      }
      this.fileShardSize = 1 * 1024 * 1024; //每片文件大小
      this.isfinish = false;
      //点击后隐藏上传按钮 ，防止重复点击
      this.showUploadBtn = false
      this.showUploadProcess = true
      this.percentage = 1
      var _this = this
      getmd5(file, _this.fileShardSize).then(e => {
        _this.switchC = false;
        _this.fileShardIndex = 1;//分片索引
        _this.curFile = file;
        _this.fileKey = e;
        _this.fileSize = file.size;
        _this.fileShardTotal = Math.ceil(file.size / _this.fileShardSize);//分片总数
        var fileFullName = file.name;
        _this.fileSuffix = fileFullName.substr(fileFullName.lastIndexOf('.') + 1);
        _this.fileName = fileFullName.substr(0, fileFullName.lastIndexOf('.'));

        //上传参数
        var params = new FormData()
        params.append('fileName', _this.fileName)
        params.append('fileShardTotal', _this.fileShardTotal)
        params.append('fileKey', _this.fileKey)
        params.append('fileSuffix', _this.fileSuffix)
        params.append('fileShardSize', _this.fileShardSize)
        params.append('fileSize', _this.fileSize)
        params.append('fileFlag', _this.flagType)

        _this.updateProgress(file, params)

      })
    },
    // 批量上传
    uploadFile(formData) {
      var _this = this
      // 上传
      procSplitFile(formData).then(res => {
        if (res.data.code == 200) {
          //上传分片完成
          if (res.data.shardIndex < _this.fileShardTotal) {
            _this.fileShardIndex = _this.fileShardIndex + 1;
            _this.start = (_this.fileShardIndex - 1) * _this.fileShardSize;
            _this.end = Math.min(_this.curFile.size, _this.start + _this.fileShardSize);
            _this.fileSize = _this.curFile.size;
            var params = new FormData()
            params.append('fileName', _this.fileName)
            params.append('fileShardTotal', _this.fileShardTotal)
            params.append('fileKey', _this.fileKey)
            params.append('fileSuffix', _this.fileSuffix)
            params.append('fileShardSize', _this.fileShardSize)
            params.append('fileSize', _this.fileSize)
            params.append('fileFlag', _this.flagType)
            params.append('fileShardIndex', _this.fileShardIndex)
            var fileShardtem = _this.curFile.slice(_this.start, _this.end);//从文件中获取当前分片数据
            let fileReader = new FileReader();
            //异步读取本地文件分片数据并转化为base64字符串
            fileReader.readAsDataURL(fileShardtem);
            //本地异步读取成功后，进行上传
            fileReader.onload = function (e) {
              let base64str = e.target.result;
              params.append('base64', base64str)
              _this.uploadFile(params)
            }
            let perentNum = Math.ceil(this.fileShardIndex * 100 / this.fileShardTotal)
            if (perentNum > 100) {
              this.percentage = 100
            } else {
              this.percentage = perentNum
            }
          }
        } else if (res.data.code == 100) {
          var fileId = res.data.id
          //上传完成
          _this.percentage = 100
          _this.switchC = true

          _this.finishUpload(fileId)
        }

      }).catch((error) => {
        if (error.response) {
          console.log(error.response.data)
          console.log(error.response.status)
          console.log(error.response.headers)
        } else {
          console.log(error.message)
        }
      })

    },
    updateProgress(file, params) {
      var _this = this
      var param = new URLSearchParams()
      param.append('shardKey', _this.fileKey)
      // 批量上传
      checkUploadProgress(param).then(res => {
        if (res.data.code == 200) {
          //新文件
          _this.start = (_this.fileShardIndex - 1) * _this.fileShardSize;
          _this.end = Math.min(file.size, _this.start + _this.fileShardSize);
          _this.fileShard = file.slice(_this.start, _this.end);//从文件中获取当前分片数据
        } else if (res.data.code == 220) {
          _this.fileShardIndex = res.data.ShardIndex;
          //有上传未完成的
          _this.start = (_this.fileShardIndex - 1) * _this.fileShardSize;
          _this.end = Math.min(file.size, _this.start + _this.fileShardSize);
          _this.fileShard = file.slice(_this.start, _this.end);//从文件中获取当前分片数据
        } else if (res.data.code == 240) {
          //急速上传
          var fileId = res.data.id
          _this.percentage = 100
          _this.switchC = true
          _this.finishUpload(fileId)
          return false;
        }
        //读取base64str
        let fileReader = new FileReader();
        //异步读取本地文件分片并转化为base64字符串
        fileReader.readAsDataURL(_this.fileShard);
        //本地异步读取成功，进行上传
        fileReader.onload = function (e) {
          let base64str = e.target.result;
          params.append('base64', base64str)
          params.append('fileShardIndex', _this.fileShardIndex)
          if (_this.switchC == false) {
            _this.uploadFile(params)
          }
        }
      }).catch((error) => {
        this.$message.error('上传错误')
      })

    },
    // 上传完成
    finishUpload(fileId) {
      var _this = this
      //进行保存提醒
      _this.uploadMsg = _this.$message({
        duration: 0,
        message: "请稍等，正在保存...",
        type: "warning"
      });
      var param = new URLSearchParams()
      param.append('fileId', fileId)
      basicFinishUpload(param).then(res => {
        if (res.data.code == 100) {
          let uploadFileName = res.data.data.fileName
          let uploadFilePath = res.data.data.filePath
          _this.zipList = []
          if (uploadFileName != null && uploadFileName.length > 0) {
            var fileObj = { name: uploadFileName, filePath: uploadFilePath }
            _this.zipList.push(fileObj)
          }
          //关闭消息提醒
          _this.uploadMsg.close()

          //上传完成提示
          _this.$message({
            duration: 2000,
            message: '上传已完成',
            type: 'success'
          })
          setTimeout(() => {
            var length = $(".el-upload-list").children("li").length;
            for (var i = 0; i < length; i++) {
              var type = uploadFilePath.substring(uploadFilePath.lastIndexOf(".") + 1).toLowerCase();
              $(".el-upload-list__item.is-success:nth-child(" + (i + 1) + ")")
                .children("a")
                .children("i")
                .addClass(type);
            }
          }, 150);
          _this.showUploadProcess = false
          _this.showUploadBtn = false
        }
      })

    },
    // 文件移除
    handleBeforeRemove() {
      if (this.zipList != null && this.zipList.length > 0) {
        // var filePath= this.zipList[0].filePath;
        var _this = this
        this.$confirm('确认删除文件？', '删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          _this.zipList = [];
          _this.showUploadBtn = true;
          _this.isfinish = true;
        }).catch((error) => {
          handleAlert('info', '取消删除')
          return false;
        })
      }
      return false;
    },
    maximumArea() {
      setTimeout(() => {
        tableHeight()
        setTimeout(() => {
          this.maximumHeight = tableMaxHeight;
        }, 50)
      })
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    }
  },
  mounted() {
    this.tableHeightArea()
    this.dataList();
    this.getManualList();
    this.getLanguage();
    this.getBrandTrainList();
    this.getUserCountryList();
    window.addEventListener('keydown', this.keyDown)
  },
  destroyed() {
    window.removeEventListener('keydown', this.keyDown, false)
  },
}
</script>



<style>
.el_progress2 .el-progress-bar__innerText {
  color: #ffffff;
}

.el-dialog .el-upload-list {
  width: 86%;
}
</style>
