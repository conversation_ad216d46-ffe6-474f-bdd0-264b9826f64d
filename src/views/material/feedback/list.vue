<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" ref="formInline" :label-width="formLabelWidth" :model="formInline" class="demo-form-inline">
        <el-form-item label="问题主题" prop="problemTheme">
          <el-input v-model.trim="formInline.problemTheme" placeholder="请输入问题主题"></el-input>
        </el-form-item>
        <el-form-item label="问题分类" prop="problemType">
          <el-select v-model="formInline.problemType" placeholder="请选择问题分类" clearable filterable>
            <el-option-group v-for="group in problemTypeList" :key="group.id" :label="group.name">
              <el-option v-for="item in group.children" :key="item.id" :label="item.name" :value="item.code"></el-option>
            </el-option-group>
          </el-select>
        </el-form-item>

        <el-form-item label="服务店名称" prop="stationName">
          <el-input v-model.trim="formInline.stationName" placeholder="请输入服务店名称"></el-input>
        </el-form-item>
        <el-form-item label="服务店编码" prop="stationCode">
          <el-input v-model.trim="formInline.stationCode" placeholder="请输入服务店编码"></el-input>
        </el-form-item>

        <el-form-item label="车型" prop="trainId">
          <el-select v-model="formInline.trainId" clearable filterable placeholder="请选择车型">
            <el-option-group
              v-for="(item, index) of carModelList"
              :key="index"
              :label="item.brandName"
            >
              <el-option
                v-for="(itm, idx) of item.children"
                :key="idx"
                :label="itm.trainName"
                :value="itm.trainId"
              >
              </el-option>
            </el-option-group>
          </el-select>
        </el-form-item>
        <el-form-item label="提问时间" prop="dateRange">
          <el-date-picker
            v-model="formInline.dateRange"
            end-placeholder="结束日期"
            format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="开始日期"
            style="width: 280px;"
            type="daterange"
            value-format="yyyy-MM-dd">
          </el-date-picker>
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-select v-model="formInline.status" clearable>
            <el-option label="未回复" value="1"></el-option>
            <el-option label="已回复" value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="类型" prop="urgent">
          <el-select v-model="formInline.urgent" clearable>
            <el-option label="加急" value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit" icon="el-icon-search">搜索</el-button>
          <el-button plain @click="reset('formInline')">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <div class="tableHandle">
        <el-button icon="bulkDown-icon" type="text" @click="exportData()">批量下载</el-button>
      </div>
      <!-- 分页查询 -->
      <el-table
        style="width:100%"
        border
        stripe
        ref="table"
        highlight-current-row
        :max-height="maximumHeight"
        :data="resultList"
        :header-cell-style="{
          'text-align': 'center',
          'background-color': 'var(--other-color)',
        }"
        @header-dragend="changeColWidth"
      >
        <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
        <el-table-column label="主题" prop="problemTheme" min-width="120" show-overflow-tooltip>
          <template slot-scope="{row}">
            <el-badge v-if="row.type == 1" is-dot class="item"></el-badge>
            <span style="text-decoration: underline;cursor: pointer;" @click="handelReply(row)">{{
                row.problemTheme
              }}</span>
          </template>
        </el-table-column>
        <el-table-column label="车型" min-width="80" prop="trainName" show-overflow-tooltip></el-table-column>
        <el-table-column label="问题分类" prop="problemType" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column label="服务店编码" min-width="70" prop="stationCode" show-overflow-tooltip></el-table-column>
        <el-table-column label="服务店名称" prop="stationName" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column label="提问时间" prop="createdTime" width="150" align="center">
          <template slot-scope="{row}">
            {{ row.createdTime | conversion("yyyy-MM-dd HH:mm:ss") }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="状态" min-width="60" prop="status"></el-table-column>
        <el-table-column label="类型" prop="urgent" min-width="60" align="center">
          <template slot-scope="{row}">
            <img v-if="row.urgent" :src="require('../../../assets/image/urgentIcon.png')" style="width: 13px;"/>
            <span style="color: rgb(255, 0, 0);">{{ row.urgent }}</span>
          </template>
        </el-table-column>
        <el-table-column label="VIN" min-width="110" prop="vinCode" show-overflow-tooltip></el-table-column>
        <el-table-column label="VIN属性" min-width="70" prop="vinPropName" show-overflow-tooltip></el-table-column>
        <el-table-column align="left" label="分析类型" min-width="120" prop="analyzeName"></el-table-column>
        <el-table-column label="操作" fixed="right" width="130">
          <template slot-scope="{row}">
            <el-button v-if="hasPerm('menuAsimss3A11B_101')" size="small" type="text" @click="handelReply(row)">回复
            </el-button>
            <el-button v-if="hasPerm('menuAsimss3A11B_102')" size="small" type="text" @click="handelDelete(row)">删除
            </el-button>
            <el-button v-if="hasPerm('menuAsimss3A11B_103')" size="small" type="text" @click="handelAnalyze(row)">分析
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total>0" :total="total" :page.sync="currentPage" :limit.sync="pagesize" @pagination="dataList"/>
      <!-- 国内回复 -->
      <el-dialog v-dialogDrag :close-on-click-modal="false" :destroy-on-close="true" :visible.sync="dialogFormVisible" title="回复" width="1100px !important">
        <feedbackDetail :replyId="replyId"></feedbackDetail>
      </el-dialog>

      <!-- 分析弹窗 -->
      <el-dialog
        v-dialogDrag
        :close-on-click-modal="false"
        :destroy-on-close="true"
        :visible.sync="analyzeDialogVisible"
        title="问题分析"
        width="500px"
        @close="cancelAnalyze">
        <div class="analyze-content">
          <el-form :model="analyzeForm" label-width="100px">
            <el-form-item label="分析类型：" required>
              <el-radio-group v-model="analyzeForm.selectedType" @change="onAnalyzeTypeChange">
                <el-radio
                  v-for="item in analyzeTypeList"
                  :key="item.code"
                  :label="item.code"
                  style="display: block; margin-bottom: 10px;">
                  {{ item.name }}
                </el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item v-if="analyzeForm.selectedType === '7'" label="详细说明：" required>
              <el-input
                v-model="analyzeForm.analyzeContent"
                :rows="3"
                maxlength="200"
                placeholder="请输入具体分析内容"
                show-word-limit
                type="textarea">
              </el-input>
            </el-form-item>
          </el-form>
        </div>

        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitAnalyze">
            {{ isEditAnalyze ? '修改' : '提交' }}
          </el-button>
          <el-button @click="cancelAnalyze">取消</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import feedbackDetail from './feedbackDetail.vue';
import {handleAlert, tableHeight, tableMaxHeight} from '@/assets/js/common.js'
import Pagination from '@/components/Pagination'
import {
  feedbackAnalyzeType,
  feedbackData,
  feedbackDel,
  feedbackExport,
  feedbackTypeList,
  getTrainModelList,
  submitFeedbackAnalyze,
} from '@/api/material.js';

export default {
  name: 'material_feedback_list',
  components: {Pagination, feedbackDetail},
  data() {
    return {
      formInline: {
        problemTheme: '',
        stationName: '',
        urgent: '',
        stationCode: '',
        trainId: '',
        problemType: '',
        status: '',
        dateRange: [],
      },
      replyId: '',
      dialogFormVisible: false,
      // 分析弹窗相关
      analyzeDialogVisible: false,
      analyzeTypeList: [],
      analyzeForm: {
        selectedType: '',
        analyzeContent: '',
        feedbackId: ''
      },
      isEditAnalyze: false, // 是否为编辑模式
      resultList: [],
      problemTypeList: [], //问题分类
      carModelList: [], //车型
      pagesize: 20,
      currentPage: 1,
      total: 0,
      formLabelWidth: '80px',
      maximumHeight: 0,
      inactivityTimer: null, // 用户无操作计时器
      inactivityTimeout: 60000, // 无操作超时时间，1分钟
      throttleDelay: 300, // 节流延迟300毫秒
    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth() {
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    // 分页查询数据
    dataList() {
      this.$loading.show();
      var params = new URLSearchParams()
      params.append('page', this.currentPage)
      params.append('limit', this.pagesize)
      params.append('stationName', this.formInline.stationName)
      params.append('problemTheme', this.formInline.problemTheme)
      params.append('urgent', this.formInline.urgent)
      params.append('stationCode', this.formInline.stationCode)
      params.append('problemType', this.formInline.problemType)
      params.append('status', this.formInline.status)
      params.append('trainId', this.formInline.trainId)
      if (this.formInline.dateRange && this.formInline.dateRange.length === 2) {
        params.append('startTime', this.formInline.dateRange[0])
        params.append('endTime', this.formInline.dateRange[1])
      }
      feedbackData(params).then(res => {
        if (res.data.code == "100") {
          this.total = res.data.total;
          this.resultList = res.data.data;
        } else {
          handleAlert('error', res.data.msg);
        }
        this.tableHeightArea()
        this.$loading.hide();
      })
    },
    // 搜索
    keyDown(e) {
      if (e.keyCode === 13) {
        this.onSubmit()
      }
    },
    onSubmit() {
      this.currentPage = 1
      this.dataList();
    },
    indexMethod(index) {
      return this.temp.replys.length - index;
    },
    // 回复
    handelReply(row) {
      this.replyId = row.id;
      this.dialogFormVisible = true;
    },
    // 填写分析
    handelAnalyze(row) {
      // 设置表单数据，实现回显
      this.analyzeForm = {
        selectedType: row.analyzeCode || '', // 回显分析类型代码
        analyzeContent: row.analyzeContent || '', // 回显分析内容
        feedbackId: row.id,
        id: null
      };

      // 判断是否为编辑模式
      this.isEditAnalyze = !!(row.analyzeCode || row.analyzeContent);

      console.log('分析表单数据:', this.analyzeForm);
      this.getAnalyzeTypeList();
      this.analyzeDialogVisible = true;
    },

    // 获取分析类型列表
    getAnalyzeTypeList() {
      this.$loading.show();
      feedbackAnalyzeType().then(res => {
        if (res.data.code === 100) {
          this.analyzeTypeList = res.data.data || [];
        } else {
          handleAlert('error', res.data.msg || '获取分析类型失败');
        }
      }).catch(err => {
        handleAlert('error', '获取分析类型失败');
      }).finally(() => {
        this.$loading.hide();
      });
    },

    // 分析类型选择变化
    onAnalyzeTypeChange(value) {
      this.analyzeForm.selectedType = value;
      // 如果不是选择其他(code为7)，清空详细说明
      // 注意：在编辑模式下，如果用户主动改变类型，才清空内容
      if (value !== '7') {
        this.analyzeForm.analyzeContent = '';
      }
    },

    // 提交分析
    submitAnalyze() {
      if (!this.analyzeForm.selectedType) {
        handleAlert('warning', '请选择分析类型');
        return;
      }

      if (this.analyzeForm.selectedType === '7' && !this.analyzeForm.analyzeContent.trim()) {
        handleAlert('warning', '请填写其他详细说明');
        return;
      }

      // 这里可以添加提交分析的API调用
      const params = {
        feedbackId: this.analyzeForm.feedbackId,
        analyzeCode: this.analyzeForm.selectedType,
        analyzeContent: this.analyzeForm.analyzeContent
      };
      console.log('提交分析参数:', params);
      submitFeedbackAnalyze(params).then(res => {
        if (res.data.code === 100) {
          handleAlert('success', '分析提交成功');
          this.dataList();
          this.cancelAnalyze();
        } else {
          handleAlert('warn', res.data.msg)
        }
      })
    },

    // 取消分析
    cancelAnalyze() {
      this.analyzeDialogVisible = false;
      this.isEditAnalyze = false;
      this.analyzeForm = {
        selectedType: '',
        analyzeContent: '',
        feedbackId: ''
      };
    },
    // 删除
    handelDelete(row) {
      this.$confirm('确定删除问题【' + row.problemTheme + '】吗?', '删除反馈记录', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        var params = new URLSearchParams()
        params.append("id", row.id)
        params.append("type", "1")
        feedbackDel(params).then(res => {
          if (res.data.code === 100) {
            handleAlert('success', '删除成功')
            if (this.resultList != null && this.resultList.length == 1) {
              this.currentPage = this.currentPage - 1
            }
            this.dataList()
            window.systemReminder();
          } else {
            handleAlert('error', '删除失败')
          }
        })
      })
    },
    // 重置
    reset(formInline) {
      if (this.$refs[formInline]) {
        if (this.$refs[formInline].resetFields() !== undefined) {
          this.$refs[formInline].resetFields()
        }
      }
      this.formInline.problemTheme = ''
      this.formInline.stationName = ''
      this.formInline.stationCode = ''
      this.formInline.urgent = ''
      this.formInline.problemType = '';
      this.formInline.trainId = '';
      this.formInline.status = '';
      this.formInline.dateRange = [];
      this.currentPage = 1
      this.dataList()
    },
    // 问题分类
    getFeedbackTypeList() {
      feedbackTypeList().then(res => {
        this.problemTypeList = res.data.data
      }).catch(err => {
        handleAlert('error', '系统开小差了...')
      })
    },
    // 获取车型列表
    getCarModelList() {
      getTrainModelList().then(res => {
        if (res.data.code == 100) {
          this.carModelList = res.data.data
        } else {
          handleAlert('error', res.data.msg)
        }
      }).catch(err => {
        handleAlert('error', '系统开小差了...')
      })
    },
    // 导出数据
    exportData() {
      this.$loading.show();
      const formData = new FormData();
      Object.entries(this.formInline).forEach(([key, value]) => {
        // 过滤掉空值
        if (value !== '' && value != null) {
          // 处理日期范围特殊情况
          if (key === 'dateRange' && Array.isArray(value) && value.length === 2) {
            formData.append('startTime', value[0]);
            formData.append('endTime', value[1]);
          } else {
            formData.append(key, value);
          }
        }
      });
      feedbackExport(formData).then((res) => {
        this.$loading.hide();
        this.downloadRes(res);
        handleAlert('success', '导出成功');
      }).catch(err => {
        this.$loading.hide();
        handleAlert('error', '导出失败');
      });
    },
    downloadRes(res) {
      let fileName = decodeURI(res.headers['content-disposition'])
      if (fileName) {
        fileName = fileName.substring(fileName.indexOf('=') + 1)
      }
      let blob = new Blob([res.data]);
      let url = window.URL.createObjectURL(blob);
      let aLink = document.createElement("a");
      aLink.style.display = "none";
      aLink.href = url;
      aLink.setAttribute("download", fileName);
      document.body.appendChild(aLink);
      aLink.click();
      document.body.removeChild(aLink); //下载完成移除元素
      window.URL.revokeObjectURL(url); //释放掉blob对象
      this.$loading.hide();
    },
    maximumArea() {
      setTimeout(() => {
        tableHeight()
        setTimeout(() => {
          this.maximumHeight = tableMaxHeight;
        }, 50)
      })
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    },
    // 启动无操作计时器
    startInactivityTimer() {
      this.inactivityTimer = setTimeout(() => {
        // 当计时器到期时，刷新数据
        this.dataList();
        // 重新启动计时器以便继续监控
        this.startInactivityTimer();
      }, this.inactivityTimeout);
    },
    // 重置无操作计时器
    resetInactivityTimer() {
      this.clearInactivityTimer();
      this.startInactivityTimer();
    },
    // 清除无操作计时器
    clearInactivityTimer() {
      if (this.inactivityTimer) {
        clearTimeout(this.inactivityTimer);
        this.inactivityTimer = null;
      }
    },
    // 定义节流函数工厂
    throttle(fn, delay) {
      let lastTime = 0;
      // 返回一个新函数，这就是节流版本的函数
      return function (...args) {
        const now = Date.now();
        if (now - lastTime >= delay) {
          lastTime = now;
          fn.apply(this, args);
        }
      };
    },
  },
  mounted() {
    // 初始化无操作计时器
    this.startInactivityTimer();
    // 添加用户操作事件监听器
    document.addEventListener('click', this.resetInactivityTimer);
    document.addEventListener('keydown', this.resetInactivityTimer);
    document.addEventListener('mousemove', this.throttledResetInactivityTimer);
    document.addEventListener('scroll', this.resetInactivityTimer);

    this.tableHeightArea();
    this.dataList();
    this.getFeedbackTypeList();
    this.getCarModelList();
    window.addEventListener('keydown', this.keyDown)
  },
  created() {
    this.throttledResetInactivityTimer = this.throttle(
      this.resetInactivityTimer,
      this.throttleDelay
    );
  },
  beforeDestroy() {
    // 组件销毁前清除计时器和事件监听
    this.clearInactivityTimer();
    document.removeEventListener('click', this.resetInactivityTimer);
    document.removeEventListener('keydown', this.resetInactivityTimer);
    document.removeEventListener('mousemove', this.throttledResetInactivityTimer);
    document.removeEventListener('scroll', this.resetInactivityTimer);
    window.removeEventListener('keydown', this.keyDown, false)
  },
  destroyed() {
    window.removeEventListener('keydown', this.keyDown, false)
  },
}
</script>

<style scoped>
.analyze-content {
  padding: 20px 0;
}

.analyze-content .el-radio {
  line-height: 32px;
}

.dialog-footer {
  text-align: right;
}
</style>
