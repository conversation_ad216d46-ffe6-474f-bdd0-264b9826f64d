<template>
  <div>
    <!-- 信息 -->
    <div>
      <table class="tabtop13" width="100%">
        <tr>
          <td class="tdTitle" width="15%">服务店编码</td>
          <td width="35%">{{ temp.stationCode }}</td>
          <td class="tdTitle" width="15%">服务店名称</td>
          <td width="35%">{{ temp.stationName }}</td>
        </tr>
        <tr>
          <td class="tdTitle" width="15%">提问人姓名</td>
          <td width="35%">{{ temp.questioner }}</td>
          <td class="tdTitle" width="15%">提问人电话</td>
          <td width="35%">{{ temp.phone }}</td>
        </tr>
        <tr>
          <td class="tdTitle" width="15%">VIN</td>
          <td width="35%">{{ temp.vinCode }}</td>
          <td class="tdTitle" width="15%">车型</td>
          <td width="35%">{{ temp.trainName }}</td>
        </tr>
        <tr>
          <td class="tdTitle" width="15%">配件名称</td>
          <td width="35%">{{ temp.partsName }}</td>
          <td class="tdTitle" width="15%">配件编码</td>
          <td width="35%">{{ temp.partsCode }}</td>
        </tr>
        <tr>
          <td class="tdTitle" width="15%">问题分类</td>
          <td colspan="3" width="85%">{{ temp.problemType }}</td>
        </tr>
        <tr>
          <td class="tdTitle" width="15%">问题主题</td>
          <td colspan="3" width="85%">{{ temp.problemTheme }}</td>
        </tr>
        <tr>
          <td class="tdTitle" width="15%">问题描述</td>
          <td colspan="3" width="85%">{{ temp.problemDesc }}</td>
        </tr>
        <tr>
          <td class="tdTitle" width="15%">附件</td>
          <td colspan="3" width="85%">
            <div class="uploadFileInfo">
              <div v-for="accessory in temp.accessorys" :key="accessory.id" @click="previewFormat.includes(accessory.format) ? previewImage(temp, accessory) : handleDownload(accessory)">
                <div>
                  <img :src="getIoc(accessory, 'smallIoc')"/>
                </div>
                <div>
                  {{ accessory.fileName }}
                </div>
              </div>
            </div>
          </td>
        </tr>
      </table>
      <!-- 历史回复 -->
      <div class="historyReply">
        <p>历史回复</p>
        <div>
          <div v-for="(item) in temp.replys" :key="item.id">
            <p class="topInfo">
              <span>
                <span>{{ item.replyName }}</span>{{ item.replyType }}：
              </span>
              <span> {{ item.createdTime | conversion("yyyy-MM-dd HH:mm:ss") }}</span>
            </p>
            <el-row style="padding: 2px 12px;word-break: break-all;white-space: pre-line;">
              {{item.content}}
            </el-row>
            <div class="accessoryInfo" v-if="item.accessorys.length>0">
              <div v-for="(itm) in item.accessorys" :key="itm.id">
                <div @click="previewFormat.includes(itm.format) ? previewImage(item, itm) : ''">
                  <img :src="getIoc(itm, 'bigIoc')">
                </div>
                <div>
                  <p>{{ itm.fileName }}</p>
                  <p>
                    <span v-if="previewFormat.includes(itm.format)" @click="previewImage(item, itm)">预览</span>
                    <span @click="handleDownload(itm)">下载</span>
                  </p>
                </div>
              </div>
            </div>
            <el-row class="rightDel">
              <el-col :span="24">
                <span v-if="hasPerm('menuAsimss3A11B_102')" style="cursor: pointer;" @click="replyDelete(item)">删除</span>
              </el-col>
            </el-row>
          </div>
          <div v-if="temp.replys.length == 0">暂无回复</div>
        </div>
      </div>
      <!-- 回复  -->
      <div class="replyArea">
        <p>回复</p>
        <div>
          <el-input
            v-model="temp.replyContent"
            :rows="5"
            placeholder="请输入回复内容"
            maxlength="500"
            show-word-limit
            type="textarea"
          ></el-input>
        </div>
      </div>
      <!-- 附加 -->
      <div class="attachmentInfo">
        <p>附加</p>
        <div @paste="handlePaste">
          <el-upload
            ref="upload"
            class="upload-demo"
            drag
            multiple
            action="#"
            :http-request="uploadAttach"
            :on-remove="handleRemove"
            :before-remove="beforeRemove"
            :before-upload="beforeAvatarUpload"
            :on-exceed="handleExceed"
            :limit="5"
            :file-list="imgList"
          >
            <div class="el-upload_text">
              <svg-icon icon-class="fileUpload"></svg-icon>
              <p>拖拽附件至此或点击空白区域后粘贴上传</p>
              <el-button type="primary" @click.stop="triggerUpload">点击上传</el-button>
            </div>
            <div slot="tip" class="el-upload__tip">
              支持上传jpg, png, mp4, mp3, xls, xlsx, doc, docx, zip格式的文件，且文件数量不超过5个，单个文件大小不能超过100M
            </div>
          </el-upload>
          <el-progress
            v-if="progressFlag"
            :percentage="percentage"
          ></el-progress>
        </div>
      </div>
      <div class="submitArea">
        <el-button v-if="hasPerm('menuAsimss3A11B_101')" size="medium" type="primary" @click="replyClick('0')">
          提交
        </el-button>
        <el-button v-if="hasPerm('menuAsimss3A11B_101')" size="medium" style="background-color: #D91C1C !important;border-color:#D91C1C !important;" type="primary" @click="endFeedback()">
          结束反馈
        </el-button>
      </div>
    </div>
    <image-viewer
      v-if="showViewer"
      :initial-index="imgSrcIndex"
      :on-close="closeViewer"
      :url-list="imageShowList"
    />
  </div>
</template>
<script>
import ImageViewer from '@/components/imageViewer/imageViewer.vue';
import {handleAlert, sysServerUrl, iconZoom } from '@/assets/js/common.js'
import {
  feedbackDel,
  feedbackEnd,
  feedbackInfo,
  feedbackReply,
} from '@/api/material.js';
export default {
  name: 'feedbackDetail',
  components: { ImageViewer },
  props:{
    replyId: String,
  },
  watch:{
    replyId(newValue){
      if (newValue && newValue!="") {
        this.getfeedbackInfo();
      }else {
        this.replyId = "";
      }
    }
  },
  data() {
    return {
      previewFormat: ['jpg', 'png'],
      temp: {
        trainId: "",
        stationCode: "",
        engineCode: '',
        partsCode: '',
        serialNumber: '',
        problemDesc: "",
        figureNumber: '',
        questioner: "",
        replyTime: '',
        vinCode: '',
        problemTheme: "",
        phone: '',
        modelCode: '',
        trainName: "",
        createdTime: '',
        stationName: "",
        id: "",
        accessorys: [],
        urgent: "",
        problemType: "",
        partsName: '',
        status: "",
        replys: [],
        newReplyAccessory: [],// 回复的附件
        replyContent: '',
        // 最新回复
        newReplyContent: '',  // 内容
        newReplyAtta: '',   // 附件
        newReplyName: '',   // 回复人
        newReplyTime: '',   // 回复时间
        newReplyFlag: false,
        newReplyType: '',
        translateContent: '',
        translateTheme: '',
        translateDesc: '',
      },
      isfinish: true,
      isFlag: true,
      imgList: [],
      // 进度条
      progressFlag: false,
      percentage: 0,
      fileList: [],
      fileNum: 0,
      // 图片放大展示
      showViewer: false,
      imageShowList: [],
      imgSrcIndex: 0, // 默认显示的预览图下
      uploadPath: sysServerUrl + 'sys/upload/display?filePath=',
    }
  },
  methods: {
    getfeedbackInfo() {
      var _this = this;
      _this.resetRealy();
      var params = {
        id: _this.replyId,
      }
      feedbackInfo(params).then(res => {
        if (res.data.code == 100 && res.data.data) {
          _this.temp = Object.assign({}, res.data.data)
          if (_this.temp.replys.length > 0) {
            _this.temp.newReplyFlag = true;
            let obj = _this.temp.replys[0]
            _this.temp.newReplyContent = obj.content
            _this.temp.newReplyName = obj.replyName
            _this.temp.newReplyType = obj.replyType
            _this.temp.newReplyTime = _this.$options.filters.conversion(obj.createdTime, "yyyy-MM-dd HH:mm:ss");
            if (obj.accessorys.length > 0) {
              _this.temp.newReplyAtta = obj.accessorys[0]
            }
          }
        }
      });
      _this.uploadFileShow()
      setTimeout(() => {
        const dragArea = this.$refs.upload.$el.querySelector('.el-upload-dragger');
        dragArea.addEventListener('click', (e) => {
          if (!e.target.closest('.el-button')) {
            this.$refs.upload.$el.querySelector('.el-upload__input').focus();
            e.stopPropagation();  // 阻止点击空白区域触发上传
          }
        });
      }, 100);
    },
    resetRealy() {
      this.temp = {
        trainId: "",
        stationCode: "",
        engineCode: '',
        partsCode: '',
        serialNumber: '',
        problemDesc: "",
        figureNumber: '',
        questioner: "",
        replyTime: '',
        vinCode: '',
        problemTheme: "",
        phone: '',
        modelCode: '',
        trainName: "",
        createdTime: '',
        stationName: "",
        id: "",
        accessorys: [],
        urgent: "",
        problemType: "",
        partsName: '',
        status: "",
        replys: [],
        newReplyAccessory: '',
        replyContent: '',
        // 最新回复
        newReplyContent: '',  // 内容
        newReplyAtta: '',   // 附件
        newReplyName: '',   // 回复人
        newReplyTime: '',   // 回复时间
        newReplyFlag: false,
      }
      this.isfinish = true
      this.imgList = []
      this.isFlag = true
    },
    // ==== 上传附件 ===== //
    beforeAvatarUpload(file) {
      if (file == null) {
        handleAlert('warning', '请选择上传文件')
        return false;
      }
      this.isfinish = true
      let suffix = ['jpg', 'png', 'mp4', 'mp3', 'xls', 'xlsx', 'doc', 'docx', 'zip']
      var fileName = file.name.substring(file.name.lastIndexOf(".") + 1).toLowerCase()
      const isLt2M = file.size / 1024 / 1024 <= 100
      if (!suffix.includes(fileName)) {
        handleAlert('warning', '支持上传 jpg, png, mp4, mp3, xls, xlsx, doc, docx, zip格式的文件，且文件数量不超过5个，单个文件大小不能超过100M')
        this.isFlag = false;
        return false;
      }
      if (!isLt2M) {
        handleAlert('warning', '文件大小不能超过 100MB!')
        this.isFlag = false;
        return false;
      }
      // 判断文件是否存在重名
      // if (this.imgList.length > 0) {
      //   let b = false
      //   var list = [];
      //   for (let i = 0; i < this.imgList.length; i++) {
      //     const obj = this.imgList[i];
      //     if (file.name.includes(obj.name)) {
      //       b = true
      //       break
      //     }
      //   }
      //   if (b) {
      //     handleAlert('warning', '文件已上传')
      //     this.isFlag = false;
      //     return false;
      //   }
      // }
      // return isLt2M
      return true;
    },
    initialState() {
      setTimeout(() => {
        this.percentage = 0;
        this.progressFlag = false;
      }, 100);
    },
    uploadAttach(param) {
      var _this = this
      _this.fileList.push(param);
      _this.isFlag = true
      var formData = new FormData();
      formData.append('file', param.file);
      formData.append('flag', "temp/feedback/" + this.temp.id);
      _this.progressFlag = true;
      var url = sysServerUrl + "sys/upload/attach";
      _this.$axios.post(url, formData, {
        onUploadProgress: (progressEvent) => {
          const complete = parseInt(((progressEvent.loaded / progressEvent.total) * 100) | 0, 10);
          _this.percentage = complete;
        },
      }).then((res) => {
        if (res.data.code == 100) {
          _this.isfinish = false
          _this.percentage = 100;
          let obj = {
            fileName: res.data.data.fileName,
            name: res.data.data.fileName,
            path: res.data.data.fileUrl,
            uid: param.file.uid,
          }
          _this.imgList.push(obj);
          _this.fileNum += 1;
        } else {
          _this.fileNum += 1;
        }
        if (_this.fileList.length == _this.fileNum) {
          _this.initialState();
          _this.fileList = [];
          _this.fileNum = 0;
        }
        _this.isFlag = true;
        setTimeout(() => {
          var length = $(".el-upload-list").children("li").length;
          for (var i = 0; i < length; i++) {
            var text = $(".el-upload-list").children("li")[i].innerText;
            var type = text
              .substring(text.lastIndexOf(".") + 1)
              .toLowerCase();
            $(".el-upload-list__item.is-success:nth-child(" + (i + 1) + ")")
              .children("a")
              .children("i")
              .addClass(type);
          }
          $('.el-upload-list__item-name').mouseenter((e) => {
              $(e.target).css('text-decoration', 'underline');
          }).mouseleave((e) => {
            $(e.target).removeAttr('style');
          });
        }, 150);
      })
    },
    uploadFileShow() {
      var _this = this;
      $(document).on('click','.el-upload-list__item.is-success a', function(e){
        _this.imageShowList = [];
        let nameFile = $(e.target)[0].innerText;
        let imgIndex = $(this).closest('.el-upload-list__item').index();
        let typeFile = nameFile.substring(nameFile.lastIndexOf('.') + 1).toLowerCase();
        if (_this.previewFormat.includes(typeFile)) {
          _this.imgList.forEach((res, idx) => {
            var formatType = res.name.substring(res.name.lastIndexOf('.') + 1).toLowerCase();
            if (_this.previewFormat.includes(formatType)) {
              iconZoom();
              _this.showViewer = true;
              _this.imageShowList.push(_this.uploadPath + res.path);
              if (res.name == nameFile && idx == imgIndex) {
                _this.imageShowList.forEach((pathItem, index) => {
                  if (pathItem == _this.uploadPath + res.path) {
                    _this.imgSrcIndex = index;
                  }
                });
              }
            }
          });
        } else {
          _this.imgList.forEach((res, idx) => {
            if (res.name == nameFile && idx == imgIndex) {
              var params = {
                path: _this.uploadPath + res.path,
                fileName: res.fileName,
              };
              _this.handleDownload(params)
            }
          });
        }
      })
    },
    // 点击上传
    triggerUpload() {
      this.$refs.upload.$el.querySelector('.el-upload__input').click()
    },
    // 附加复制粘贴上传
    handlePaste(e) {
      var clipboardData = e.clipboardData; // IE
      if (!clipboardData) {
        //chrome
        clipboardData = e.originalEvent.clipboardData;
      }
      var items='';
      items = (e.clipboardData || window.clipboardData).items;
      let file = null;
      if (!items || items.length === 0) {
        handleAlert('warning', '当前浏览器不支持粘贴本地图片，请打开图片复制后再粘贴！');
        return;
      }
      if (this.imgList.length + items.length > 5) {
        this.handleOnExceed();
        e.preventDefault();
        return
      }
      // 搜索剪切板items
      for (let i = 0; i < items.length; i++) {
        // 限制上传文件类型
        if (items[i].kind === 'file') {
          file = items[i].getAsFile();
          if (!this.beforeAvatarUpload(file)) {
            e.preventDefault();
            return
          }
          let fileData = {};
          fileData.file = file;
          if (file) {//对复制黏贴的类型进行判断，若是非文件类型，比如复制黏贴的文字，则不会调用上传文件的函数
        	  this.uploadAttach(fileData);//再次调用上传文件的函数
          }
        }
      }
    },
    handleRemove(file, fileList) {
      for (let i = 0; i < this.imgList.length; i++) {
        const obj = this.imgList[i];
        if (obj.uid === file.uid) {
          this.imgList.splice(i, 1)
          break;
        }
      }
      if (fileList.length <= 0) {
        this.isfinish = true
        this.imgList = []
        this.isFlag = true;
      }
    },
    handleExceed(files, fileList) {
      handleAlert('warning', `当前限制选择 5 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
      return;
    },
    beforeRemove(file, fileList) {
      if (this.isFlag) {
        return this.$confirm(`确定移除选择文件？`, '删除', {type: 'warning'});
      }
    },
    replyDelete(row) {
      this.$confirm('确定删除【' + row.replyName + '】的回复吗?', '删除回复', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.temp.replys = this.temp.replys.filter(item => item.id != row.id)
        var params = new URLSearchParams()
        params.append("id", row.id)
        params.append("type", "2")
        feedbackDel(params).then(res => {
          if (res.data.code === 100) {
            handleAlert('success', '删除成功')
          } else {
            handleAlert('error', '删除失败')
          }
        })
        if (this.temp.replys.length <= 1) {
          this.dialogReplyVisible = false
          return false
        }
      })
    },
    // 图标
    getIoc(itm, type) {
      if (itm.format == null || itm.format == undefined || itm.format.length <= 0) {
        itm.format = itm.path.substring(itm.path.lastIndexOf(".") + 1)
      }
      if (type === 'bigIoc' && (itm.format === 'png' || itm.format === 'jpg')) {
        return itm.path;
      }
      if (itm.format === 'png') {
        return require('../../../assets/image/fileUploadIoc/' + type + '/png.png')
      } else if (itm.format === 'jpg') {
        return require('../../../assets/image/fileUploadIoc/' + type + '/jpg.png')
      } else if (itm.format === 'zip') {
        return require('../../../assets/image/fileUploadIoc/' + type + '/zip.png')
      } else if (itm.format === 'mp3') {
        return require('../../../assets/image/fileUploadIoc/' + type + '/mp3.png')
      } else if (itm.format === 'mp4') {
        return require('../../../assets/image/fileUploadIoc/' + type + '/mp4.png')
      } else if (itm.format === 'doc' || itm.format === 'docx') {
        return require('../../../assets/image/fileUploadIoc/' + type + '/doc.png')
      } else if (itm.format === 'xls' || itm.format === 'xlsx') {
        return require('../../../assets/image/fileUploadIoc/' + type + '/xls.png')
      }
    },
    // 预览
    closeViewer() {
      this.showViewer = false;
      this.imgSrcIndex = 0;
      this.imageShowList = [];
    },
    previewImage(item, itm) {
      this.showViewer = true;
      iconZoom();
      item.accessorys.forEach((res) => {
        var name = res.format.substring(res.format.lastIndexOf('.') + 1).toLowerCase();
        if (this.previewFormat.includes(name)) {
          this.imageShowList.push(res.path);
          this.imageShowList.forEach((pathItem, index) => {
            if (pathItem == itm.path) {
              this.imgSrcIndex = index;
            }
          });
        }
      })
    },
    // 下载
    getBolb(fileUrl) {
      return new Promise((resolve, reject) => {
        // 审查xhr对象是否存在
        let xhr;
        if (window.XMLHttpRequest) {
          xhr = new XMLHttpRequest();
        } else {
          // 兼容旧版本的IE
          xhr = new ActiveXObject("Microsoft.XMLHTTP");
        }
        // 发起 GET 请求获取文件内容
        xhr.open("GET", fileUrl, true);
        xhr.responseType = "blob";
        xhr.onload = function (e) {
          if (xhr.status === 200) {
            // 获取到的文件内容存储在xhr.response中
            resolve(xhr.response);
          } else {
            reject(new Error('系统异常'))
          }
        };
        xhr.onerror = () => {
          reject(new Error('系统异常'))
        };
        xhr.send();
      })
    },
    handleDownload(item) {
      this.getBolb(item.path).then(blob => {
        var url = window.URL.createObjectURL(blob);
        var aLink = document.createElement("a");
        aLink.style.display = "none";
        aLink.href = url;
        aLink.setAttribute("download", item.fileName);
        document.body.appendChild(aLink);
        aLink.click();
        document.body.removeChild(aLink); //下载完成移除元素
        window.URL.revokeObjectURL(url); //释放掉blob对象
      }).catch(e => {
        handleAlert('error', '系统异常，请稍后再试')
      })
    },
    // 确定回复
    replyClick() {
      if (!this.temp.replyContent || this.temp.replyContent.length <= 0) {
        handleAlert('error', '回复内容不能为空')
        return
      }
      this.replyFun();
    },
    replyFun() {
      this.$loading.show();
      let params = {
        "id": this.temp.id,
        "replyContent": this.temp.replyContent,
        "translateContent": this.temp.translateContent,
        "feedbackAccessoryList": this.imgList
      }
      feedbackReply(params).then(res => {
        if (res.data.code === 100) {
          handleAlert('success', '操作成功')
          this.$parent.$parent.dataList();
          this.$parent.$parent.dialogFormVisible = false;
        } else {
          handleAlert('error', res.data.msg)
        }
        this.$loading.hide();
      }).catch(() => {
        this.$loading.hide();
      })
    },
    // 结束反馈
    endFeedback() {
      this.$confirm('确定结束问题【' + this.temp.problemTheme + '】吗?', '结束反馈', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        feedbackEnd(this.temp.id).then(res => {
          if (res.data.code === 100) {
            handleAlert('success', '操作成功')
            // this.rowObj.type = 3
          } else {
            handleAlert('error', res.data.msg)
          }
        }).catch(e => {
          handleAlert('error', "系统异常")
        })
      })
    },
  },
  mounted () {
    this.getfeedbackInfo()
  }
}
</script>
