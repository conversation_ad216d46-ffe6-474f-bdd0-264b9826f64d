<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" ref="searchForm" label-width="60px" :model="searchForm" class="demo-form-inline">
        <el-form-item label="车型" prop="trainId" >
          <el-select v-model="searchForm.trainId" placeholder="请选择车系" @change="getYearList" clearable filterable>
            <el-option-group v-for="group in trainList" :key="group.id" :label="group.nameCh">
              <el-option v-for="item in group.children" :key="item.id" :label="item.nameCh" :value="item.id"></el-option>
            </el-option-group>
          </el-select>
        </el-form-item>

        <el-form-item label="年款" prop="modelYear">
          <el-select v-model="searchForm.modelYear" placeholder="请选择年款" clearable filterable>
              <el-option v-for="(item,index) in yearList" :key="index" :label="item" :value="item"></el-option>
            </el-select>
        </el-form-item>
        <!-- <el-form-item label="模块" prop="moduleCode">
          <el-select v-model="searchForm.moduleCode" placeholder="请选择模块" clearable filterable>
              <el-option v-for="(item,index) in moduleCodeList" :key="index" :label="item.name" :value="item.code"></el-option>
            </el-select>
        </el-form-item>
        <el-form-item label="语种" prop="language">
          <el-select v-model="searchForm.language" placeholder="请选择语种" clearable filterable>
            <el-option v-for="(item, index) of languageTypeList" :key="index" :label="item.name" :value="item.code"></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item>
          <el-button type="primary" @click="search()" icon="el-icon-search">搜索</el-button>
          <el-button plain @click="resetSearch('searchForm')">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <div class="tableHandle">
        <!-- <el-button type="text" icon="el-icon-s-data"  @click="statisticsClick()">统计</el-button> -->
        <el-button v-if="hasPerm('menuAsimss3A8B_103')" style="color: #409EFF;" type="text" size="small" icon="el-icon-edit" @click="editStatus(null)">处理</el-button>
      </div>
      <!-- 列表内容 -->
      <el-table
        style="width:100%"
        border
        stripe
        ref="table"
        highlight-current-row
        :max-height="maximumHeight"
        :data="resultList"
        :header-cell-style="{
          'text-align': 'center',
          'background-color': 'var(--other-color)',
        }"
        @header-dragend="changeColWidth"
        @selection-change="handleSelectionChange"
      >
        <!-- <el-table-column label="序号" type="index" width="60" align="center"></el-table-column> -->
        <el-table-column type="selection" width="40" fixed="left" align="center"></el-table-column>
        <el-table-column label="序号" type="index" width="60" align="center"></el-table-column>
        <el-table-column label="目录" prop="catalogName" min-width="200" show-overflow-tooltip></el-table-column>
        <!-- <el-table-column label="模块" prop="module_code" min-width="60" align="center">
          <template slot-scope="{row}">
            <span v-if="row.module_code === 'circuit'">电路</span>
            <span v-if="row.module_code === 'sparePart'">配件</span>
            <span v-if="row.module_code === 'service'">维修</span>
          </template>
        </el-table-column> -->
        <!-- <el-table-column label="语种" prop="languageName" min-width="70" align="center"></el-table-column> -->
        <el-table-column label="品牌" prop="brandName" min-width="100"></el-table-column>
        <el-table-column label="车型" prop="trainName" min-width="100"></el-table-column>
        <el-table-column label="年款" prop="trainYear" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column label="评分" prop="score" width="60"  align="center"></el-table-column>
        <el-table-column label="状态" prop="status" min-width="80"  align="center">
          <template slot-scope="{row}">
            <span v-if="row.status === 1">未处理</span>
            <span v-if="row.status === 2" style="color:#009933">已处理</span>
          </template>
        </el-table-column>
        <el-table-column label="评价时间" prop="operateTime" min-width="150" align="center">
          <template slot-scope="{row}">
            <div>
              {{ row.operateTime | conversion("yyyy-MM-dd HH:mm:ss") }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" min-width="160">
           <template slot-scope="{row}">
            <el-button type="text" size="small" v-if="hasPerm('menuAsimss3A8B_104')" @click="headerDetail(row)">详情</el-button>
            <el-button v-if="row.status == 1 && hasPerm('menuAsimss3A8B_103')" type="text" size="small" @click="editStatus(row)">处理</el-button>
            <el-button v-if="hasPerm('menuAsimss3A8B_102')" type="text" size="small" @click="del(row)">删除</el-button>
           </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total>0" :total="total" :page.sync="currentPage" :limit.sync="pagesize" @pagination="dataList"/>

      <!-- 详情 -->
      <el-dialog v-dialogDrag title="评分详情"  width="730px !important" :visible.sync="dialogdetailsFormVisible" :close-on-click-modal="false">
        <div style="width:100%; max-height: 570px;">
          <table width="100%" class="tabtop13">
          <tr>
            <td class="tdTitle" width="15%">品牌</td>
            <td width="85%">{{ temp.brandName }}</td>
          </tr>
          <tr>
            <td class="tdTitle" width="15%">车型</td>
            <td width="85%">{{ temp.trainName }}</td>
          </tr>
          <tr>
            <td class="tdTitle" width="15%">年款</td>
            <td width="85%">{{ temp.modelYear }}</td>
          </tr>
          <tr>
            <td class="tdTitle" width="15%">目录</td>
            <td width="85%">{{ temp.description }}</td>
          </tr>
          <tr>
            <td class="tdTitle" width="15%">编码</td>
            <td width="85%">{{ temp.targetCode }}</td>
          </tr>
          <tr>
            <td class="tdTitle" width="15%">评分</td>
            <td width="85%">{{ temp.score }}</td>
          </tr>
          <tr>
            <td class="tdTitle" width="15%">选项</td>
            <td width="85%">
              <el-row v-for="(itm, index) in temp.optionNames" :key="index" style="margin: 5px 0px;">
                <span>{{ index+1 }}</span><span>&nbsp;&nbsp; {{ itm }}</span>
              </el-row>
            </td>
          </tr>
          <tr>
            <td class="tdTitle" width="15%">内容</td>
            <td width="85%">
              <el-row>
                {{ temp.content }}
              </el-row>
            </td>
          </tr>
        </table>
        </div>
      </el-dialog>

    </div>
  </div>
</template>
<script>
import { sysServerUrl, addTabs, handleAlert, tableHeight, tableMaxHeight } from '@/assets/js/common.js'
import Pagination from '@/components/Pagination'
import { manualTreeList, manualYearList,  commentDel, commentStatus, commentData, manualLanguage, commentdetail } from '@/api/material.js'


export default {
  name: 'material_comment_list',
  components: { Pagination },
  data () {
    return {
      formLabelWidth: '100px',
      // 搜索表单
      searchForm:{
        trainId: '',  // 车系
        modelYear: '',  // 年款
        moduleCode: '',  // 模块
        language: '',  // 语种
      },
      // 当前页
      currentPage: 1,
      // 每页显示的条数
      pagesize: 10,
      // 总条数
      total: 0,
      // 车系的数据默认字段
      defaultProps: {
        parent: 'pid',   // 父级唯一标识
        value: 'id',          // 唯一标识
        label: 'nameCh',       // 标签显示
        children: 'children', // 子级
      },
      // 获取的数据集
      resultList: [],
      // 年款结果集
      yearList: [],
      // 车系结果集
      trainList: [],
      // 语种结果集
      languageTypeList: [],
      dialogdetailsFormVisible:false,   // 详情弹窗
      moduleCodeList: [
        { name: '配件', code: 'sparePart' },
        { name: '电路', code: 'circuit' },
        { name: '维修', code: 'service' }
      ],
      temp:{
        brandName: "",
        trainName: '',  // 车系
        modelYear: '',   // 年款
        moduleCode: '',   // 模块
        languageName: '',  // 语种
        description: '',  // 目录
        optionNames: [],  // 选项
        score: '',  // 评分
        content: '',  // 内容
        translateContent: '',
      },
      // 鼠标移入
      showTooltip: false,
      deleteList: [],
      maximumHeight: 0,
    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth(){
      this.$nextTick(() => {
        this.$refs.table.doLayout();
      })
    },
    // 数据
    dataList(){
      this.$loading.show();
      var params = new URLSearchParams()
      params.append('page', this.currentPage)
      params.append('limit', this.pagesize)
      params.append('trainId', this.searchForm.trainId)
      // params.append('moduleCode', this.searchForm.moduleCode)
      params.append('trainYear', this.searchForm.modelYear)
      // params.append('language', this.searchForm.language)
      commentData(params).then(res => {
        if(res.data.code == 100) {
          this.total = res.data.total    // 总条数
          if(res.data.data != null){
            this.resultList = res.data.data   // 数据
          }
        } else {
          handleAlert('error',res.data.msg)
        }
        this.$loading.hide();
        this.tableHeightArea()
      }).catch(err => {
        this.$loading.hide();
        handleAlert('error','系统开小差了...')
      })
    },

    // 搜索
    keyDown(e) {
      if (e.keyCode === 13) {
        this.search()
      }
    },
    search(){
      this.currentPage = 1;
      this.dataList();
    },

    // 重置搜索表单
    resetSearch(){
      this.searchForm.trainId = '';
      this.searchForm.modelYear = '';
      this.searchForm.language = '';
      this.searchForm.moduleCode = '';
      this.currentPage = 1;
      this.dataList();
    },


    // 获取品牌车系
    getBrandTrainList(){
      manualTreeList().then(res => {
        this.trainList = res.data.data
      }).catch(err => {
        handleAlert('error','系统开小差了...')
      })
    },

    // 获取年款
    getYearList(trainId){
      this.searchForm.modelYear = '';
      var params = {
        tearId: trainId
      }
      manualYearList(params).then(res => {
        this.yearList = res.data.data
      }).catch(err => {
        handleAlert('error','系统开小差了...')
      })
    },

    // 获取语种类型
    getLanguage(){
      manualLanguage().then(res => {
        if (res !== null && res.data.code === 100) {
          this.languageTypeList = res.data.data
        }
      })
    },

    // 批量删除
    handleSelectionChange (val) {
      this.deleteList = val
    },


    // 修改状态
    editStatus(row){

      let ids = ""
      if (row != null) {
        ids = row.id
      }else{
        this.deleteList.forEach(item => {
          ids += item.id + ","
        })
      }
      if (ids.length <= 0) {
        handleAlert("error", '请选择未处理的问题')
        return false;
      }
      var params = new URLSearchParams()
      params.append('ids', ids)
      commentStatus(params).then(res => {
        if (res.data.code === 100) {
          handleAlert('success','操作成功')
          this.dataList()
        }else{
          handleAlert('error', res.data.msg)
        }
      }).catch(err => {
        handleAlert('error','系统开小差了...')
      })
    },

    // 删除
    del(row){
      this.$confirm('确定删除【' + row.catalogName + '】的评分吗?', '删除评分', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        commentDel(row.id).then(res => {
          if (res.data.code === 100) {
            handleAlert('success','删除成功')
            if (this.resultList != null && this.resultList.length == 1) {
              this.currentPage = this.currentPage - 1;
            }
            this.dataList();
          }else{
            handleAlert('error','删除失败，' + res.data.msg)
          }
        })
      }).catch((error)=>{
        handleAlert('info','取消删除')
      })
    },

    // 详情
    headerDetail(row){
      this.temp = {
        brandName: row.brandName,
        trainName: row.trainName,  // 车系
        modelYear: row.trainYear,   // 年款
        description: row.catalogName,  // 目录
        score: row.score + ' 分',  // 评分
        content: row.content,  // 内容
        targetCode: row.targetCode,  // 编码
        optionNames: [],
        translateContent: row.translateContent,
      };
      commentdetail(row.id).then(res => {
        this.temp.optionNames = res.data.data
        this.dialogdetailsFormVisible = true
      }).catch(res => {
        this.dialogdetailsFormVisible = true
      })



    },
    maximumArea() {
      setTimeout(() => {
        tableHeight()
        setTimeout(() => {
          this.maximumHeight = tableMaxHeight;
        }, 50)
      })
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    }

    // statisticsClick(){
    //   let title ="统计"
    //   this.$router.push({ name: 'commentstatistics'})
    //   addTabs(this.$route.path, title);
    // },
  },
  mounted () {
    this.tableHeightArea();
    this.dataList();
    this.getLanguage();
    this.getBrandTrainList();
    window.addEventListener('keydown', this.keyDown)
  },
  destroyed() {
    window.removeEventListener('keydown', this.keyDown, false)
  },
}
</script>
<style>
/* //设置输入框超出长度隐藏并显示省略号 */
.el-input.is-disabled .el-input__inner {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

</style>

<style scoped>
  .inputDeep>>>.el-textarea__inner {
    border: 0;
    resize: none;/* 这个是去掉 textarea 下面拉伸的那个标志，如下图 */
  }
</style>
