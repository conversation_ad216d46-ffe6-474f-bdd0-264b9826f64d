<template>
  <div class="layoutContainer">
    <div class="secondFloat">
      <el-form :inline="true" ref="searchForm" label-width="90px" :model="searchForm" class="demo-form-inline">
        <el-form-item label="源字符" prop="sourceWord">
          <el-input v-model.trim="searchForm.sourceWord" placeholder="请输入查找的源字符"></el-input>
        </el-form-item>
        <el-form-item label="语言类型" prop="langType">
          <el-select v-model="searchForm.langType" placeholder="请选择语言类型" clearable filterable>
            <el-option v-for="(item, index) of langTypeList" :key="index" :label="item.name" v-show="item.code !='zh'"
              :value="item.code"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="适用业务类型" prop="businessType">
          <el-select v-model="searchForm.businessType" placeholder="请选择业务类型" clearable filterable>
            <el-option v-for="(item, index) of businessTypeList" :key="index" :label="item.name"
              :value="item.code"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit" icon="el-icon-search">搜索</el-button>
          <el-button plain @click="reset('formInline')">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="tableDetail">
      <div class="tableHandle">
        <el-button type="text" v-if="hasPerm('menuAsimss3A20B_101')" icon="el-icon-plus"  @click="addNewClick()">新增</el-button>
        <el-button type="text"  v-if="hasPerm('menuAsimss3A20B_108')" icon="el-icon-download"  @click="exportUntranslate()">导出待翻译表</el-button>
          <el-button type="text" v-if="hasPerm('menuAsimss3A20B_107')" icon="el-icon-upload2"  @click="importTranslate()">导入翻译表</el-button>
          <el-button type="text" v-if="hasPerm('menuAsimss3A20B_104')" icon="el-icon-info" @click="showHandProcess()">查看处理进度</el-button>
      </div>
      <!-- 列表内容 -->
      <el-table
        style="width:100%"
        border
        stripe
        ref="table"
        highlight-current-row
        :max-height="maximumHeight"
        :data="resultList"
        :header-cell-style="{
          'text-align': 'center',
          'background-color': 'var(--other-color)',
        }"
        @header-dragend="changeColWidth"
      >
        <el-table-column label="序号" type="index" width="50" align="center"></el-table-column>
        <el-table-column label="源字符" prop="sourceWord" min-width="200" show-overflow-tooltip></el-table-column>
        <el-table-column label="翻译字符" prop="transWord" min-width="200" show-overflow-tooltip></el-table-column>
        <el-table-column label="语言类型" prop="langType" min-width="80" align="center">
          <template slot-scope="{row}">
            <span v-for="item in langTypeList" :value="item.code" :key="item.code" v-show="row.langType ==item.code">{{ item.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="业务类型" prop="businessType" min-width="80" align="center">
          <template  slot-scope="{row}">
            <span v-if="row.businessType == null">
                通用
            </span>
            <span v-else>
              <span v-for="item in businessTypeList" :value="item.code" :key="item.code" v-show="row.businessType ==item.code">
                {{ item.name }}
              </span>
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="100">
          <template slot-scope="{row}">
            <el-button type="text" v-if="hasPerm('menuAsimss3A20B_103')"  size="small" @click="editClick(row)">编辑</el-button>
            <el-button type="text" v-if="hasPerm('menuAsimss3A20B_102')"  size="small" @click="delectClick(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total>0" :total="total" :page.sync="currentPage" :limit.sync="pagesize" @pagination="dataList"/>
    </div>
    <el-dialog v-dialogDrag width="55% !important" title="导出翻译" :visible.sync="exportTranslateVisiable" lock-scroll>
      <el-form :inline="false" ref="exportForm" :label-width="formLabelWidth" :model="exportForm" class="demo-form-inline" :rules="exportFormRule">
        <el-form-item label="语言类型" prop="langType">
          <el-select v-model="exportForm.langType" placeholder="请选择语言类型" clearable filterable>
            <el-option v-for="(item, index) of langTypeList" :key="index" :label="item.name" v-show="item.code !='zh'"
              :value="item.code"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="业务类型" prop="businessType">
          <el-select v-model="exportForm.businessType" placeholder="请选择业务类型" clearable filterable>
            <el-option v-for="(item, index) of businessTypeList" :key="index" :label="item.name"
              :value="item.code"></el-option>
          </el-select>
          <div v-if=" !('businessType' in  exportForm) || (exportForm.businessType == '' || exportForm.businessType == null )">
            <span style="color: red;">未选择类型导出时默认只包含通用翻译数据</span>
          </div>
        </el-form-item>
        <el-form-item label="包含已有翻译" prop="includeExistTranslate">
          <el-switch
            v-model="exportForm.includeExistTranslate"
            active-text="是"
            inactive-text="否">
          </el-switch>
        </el-form-item>
        <!-- <el-form-item label="仅导出业务类型未翻译" prop="onlyBusinessType" v-if="(!exportForm.includeExistTranslate) && exportForm.businessType != '' ">
          <el-switch
          v-model="exportForm.onlyBusinessType"
            active-text="是"
            inactive-text="否">
            </el-switch>
        </el-form-item> -->
        <div class="submitArea">
          <el-button type="primary" @click="subitExportUntranslate">导出</el-button>
        </div>
      </el-form>
      </el-dialog>
      <el-dialog v-dialogDrag width="40% !important" title="新增翻译" :before-close="resetSubmitForm" :visible.sync="addNewTranslateVisiable" lock-scroll >
        <el-form :inline="false" ref="submitTranslateForm" :label-width="formLabelWidth" :model="submitTranslateForm" class="demo-form-inline" :rules="submitTranslateFormRule">
            <el-form-item label="源字符" prop="sourceWord">
          <el-input v-model.trim="submitTranslateForm.sourceWord"  maxlength="500" show-word-limit placeholder="请输入源字符"></el-input>
        </el-form-item>
          <el-form-item label="语言类型" prop="langType">
          <el-select v-model="submitTranslateForm.langType" placeholder="请选择语言类型" clearable filterable>
            <el-option v-for="(item, index) of langTypeList" :key="index" :label="item.name"
              :value="item.code"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="业务类型" prop="businessType">
          <el-select v-model="submitTranslateForm.businessType" placeholder="请选择业务类型" clearable filterable>
            <el-option v-for="(item, index) of businessTypeList" :key="index" :label="item.name"
              :value="item.code"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="翻译字符" prop="transWord">
          <el-input v-model.trim="submitTranslateForm.transWord" maxlength="500" show-word-limit placeholder="请输入翻译字符"></el-input>
        </el-form-item>

          </el-form>
         <div class="submitArea">
          <el-button type="primary" @click="submitAdd()">提交</el-button>
          <el-button plain @click="addNewTranslateVisiable = false;resetSubmitForm()">取消</el-button>
        </div>
        </el-dialog>
        <el-dialog v-dialogDrag width="40% !important" title="编辑翻译" :visible.sync="editTranslateVisiable" lock-scroll :before-close="resetSubmitForm">
          <el-form :inline="false" ref="submitTranslateForm" :label-width="formLabelWidth" :model="submitTranslateForm" class="demo-form-inline"  :rules="submitTranslateFormRule">
            <el-form-item label="源字符" prop="sourceWord">
              <el-input v-model.trim="submitTranslateForm.sourceWord" maxlength="500" show-word-limit  readonly="readonly" disabled placeholder="请输入源字符"></el-input>
            </el-form-item>
            <el-form-item label="语言类型" prop="langType">
              <el-select  readonly="readonly" disabled v-model="submitTranslateForm.langType" placeholder="请选择语言类型" clearable filterable>
                <el-option v-for="(item, index) of langTypeList" :key="index" :label="item.name"
                  :value="item.code"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="业务类型" prop="businessType">
              <el-select v-show="submitTranslateForm.businessType  && submitTranslateForm.businessType != ''"  readonly="readonly" disabled v-model="submitTranslateForm.businessType" placeholder="请选择业务类型" clearable filterable>
                <el-option v-for="(item, index) of businessTypeList" :key="index" :label="item.name"
                  :value="item.code"></el-option>
              </el-select>
              <el-select model  v-show="submitTranslateForm.businessType == undefined || submitTranslateForm.businessType == null " readonly="readonly"  v-model="normalValue" disabled>
                <el-option  value="通用" label="通用"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="翻译字符" prop="transWord">
              <el-input v-model.trim="submitTranslateForm.transWord" maxlength="500" show-word-limit placeholder="请输入翻译字符"></el-input>
            </el-form-item>
          </el-form>
         <div class="submitArea">
          <el-button type="primary" @click="submitEdit()">提交</el-button>
          <el-button plain @click="editTranslateVisiable = false;resetSubmitForm()">取消</el-button>
        </div>
      </el-dialog>
      <el-dialog v-dialogDrag width="55% !important" title="导入翻译"  :visible.sync="importTranslateVisiable" lock-scroll>
        <el-form :inline="false" ref="importForm" :label-width="formLabelWidth" :model="importForm" class="demo-form-inline" :rules="importFormRule">
          <el-form-item label="翻译文件" prop="translateFile">
            <el-upload class="upload-demo"
              drag
              :file-list="fileList"
              action="#"
              :on-change="handleTranslateFileChange"
              :data="{ type: 'other' }"
              :limit="1"
              accept=".xlsx, .xls"
              :multiple = "false"
              :auto-upload="false">
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            </el-upload>
          </el-form-item>
          <el-form-item label="语言类型" prop="langType">
            <el-select v-model="importForm.langType" placeholder="请选择语言类型" clearable filterable>
              <el-option v-for="(item, index) of langTypeList" :key="index" :label="item.name"
                :value="item.code"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="业务类型" prop="businessType">
            <el-select v-model="importForm.businessType" placeholder="请选择业务类型" clearable filterable>
              <el-option v-for="(item, index) of businessTypeList" :key="index" :label="item.name"
                :value="item.code"></el-option>
            </el-select>
            <div v-if=" !('businessType' in  importForm) || (importForm.businessType == '' || importForm.businessType == null )">
              <span style="color: red;">未选择类型则默认只用于添加或更新通用翻译数据</span>
            </div>
            <div v-if=" ('businessType' in  importForm) &&  (importForm.businessType != '' && importForm.businessType != null )">
              <span style="color: red;">不同业务类型翻译会优先导入为通用翻译，当导入的翻译源文字与通用翻译不同时，转为专用业务类型翻译</span>
            </div>
          </el-form-item>
          <div class="submitArea">
            <el-button type="primary" @click="submitImportTranslate">导入</el-button>
          </div>
        </el-form>
      </el-dialog>
      <el-dialog
        v-dialogDrag
        width="55% !important"
        title="处理进度"
        :visible.sync="showProcessVisiable"
        lock-scroll
      >
        <el-table
          style="width:100%"
          border
          stripe
          ref="processTable"
          highlight-current-row
          :data="processList"
          :header-cell-style="{
            'text-align': 'center',
            'background-color': 'var(--other-color)',
          }"
          @header-dragend="changeColWidth"
        >
        <el-table-column label="序号" type="index" width="50" align="center"></el-table-column>
        <el-table-column label="处理信息" prop="fileInfo" min-width="200" show-overflow-tooltip></el-table-column>
        <el-table-column label="处理类型" prop="handType" min-width="80">
          <template  slot-scope="{row}">
            <span v-if="row.handType == 0">导入翻译</span>
            <span v-if="row.handType == 1">导出翻译</span>
          </template>
        </el-table-column>

        <el-table-column label="状态" prop="status" min-width="100" align="center">
          <template  slot-scope="{row}">
            <span v-if="row.status == 0">处理中</span>
            <span v-if="row.status == 1">处理完成</span>
            <span v-if="row.status == 2">处理失败</span>
          </template>
        </el-table-column>
        <el-table-column label="提交时间" prop="submitTime" min-width="150" align="center" :formatter="dateFormat"></el-table-column>
        <el-table-column label="完成时间" prop="finishTime" min-width="150" :formatter="dateFormat"></el-table-column>
        <el-table-column label="失败原因" prop="errMsg" min-width="200"></el-table-column>
        <el-table-column label="操作" fixed="right" min-width="80">
          <template slot-scope="{row}">
            <el-button v-if="row.handType == 1 && row.status == 1" type="text" size="small" @click="downLoadFile(row)">下载</el-button>
            <el-button v-if="row.status == 1 && row.handType == 0" type="text" size="small" @click="deleteProcessRecord(row)">完成导入</el-button>
            <el-button v-if="row.status == 2" type="text" size="small" @click="deleteProcessRecord(row)">删除</el-button>
            </template>
        </el-table-column>
      </el-table>
      </el-dialog>
 </div>
</template>
<script>
import { addTabs, handleAlert, tableHeight, tableMaxHeight } from '@/assets/js/common.js'
import Pagination from '@/components/Pagination'
import { requestTranslateList,requestlangList,requestTranslateBusinessList,requestExportUnTranslate,requestImportTranslate
  ,requestProcessList,requestDeleteProcessRecord,requestDownLoadFile,requestAddNew,requestEditTranslate,requestDeleteOne} from '@/api/material.js'

export default {
  name: 'material_translate_list',
  components: { Pagination },
  data () {
    return {
      normalValue:"通用",
      searchForm: {
        sourceWord: '',
        langType: '',
        businessType:''
      },
      langTypeList:[

      ],
      fileList:[],
      exportForm:{
        businessType:'',
        includeExistTranslate:false,
        langType:'',
        onlyBusinessType:false
      },
      processList:[

      ],
      exportFormRule:{
        langType: [{ required: true, message: '语言类型不能为空', trigger: ['blur', 'change'] }],
      },
      importFormRule:{
        translateFile: [{ required: true, message: '上传文件不能为空', trigger: ['blur', 'change'] }],
        langType: [{ required: true, message: '语言类型不能为空', trigger: ['blur', 'change'] }]
      },
      submitTranslateFormRule:{
        langType: [{ required: true, message: '语言类型不能为空', trigger: ['blur', 'change'] }],
        transWord: [{ required: true, message: '翻译字符不能为空', trigger: ['blur', 'change'] }],
        sourceWord: [{ required: true, message: '源字符不能为空', trigger: ['blur', 'change'] }],
      },
      importForm:{
        businessType:'',
        langType:'',
        translateFile:null
      },
      submitTranslateForm:{
         businessType:'',
        langType:'',
        transWord:'',
        sourceWord:''
      },
      importTranslateVisiable:false,
      exportTranslateVisiable:false,
      showProcessVisiable:false,
      addNewTranslateVisiable:false,
      editTranslateVisiable:false,
      businessTypeList:[],
      formLabelWidth: '100px',
      resultList: [],
      pagesize: 20,
      currentPage: 1,
      total: 0,
      maximumHeight: 0,
    }
  },
  methods: {
    // 监听表格列宽变化
    changeColWidth(){
      this.$nextTick(() => {
        this.$refs.table.doLayout();
        if(this.$refs.processTable){
          this.$refs.processTable.doLayout();
        }
      })
    },
    // 数据
    dataList () {
      var _this = this
      _this.$loading.show();
      var params = new URLSearchParams()
      params.append('pageNum', this.currentPage)
      params.append('pageSize', this.pagesize)
      params.append('sourceWord', this.searchForm.sourceWord)
      params.append('langType', this.searchForm.langType)
      params.append('businessType', this.searchForm.businessType);
      requestTranslateList(params).then(res=>{
        if (res.data.code == 100) {
          _this.total = res.data.data.total    // 总条数
          _this.resultList = res.data.data.records   // 数据
          this.tableHeightArea()
        } else {
          handleAlert('error', res.data.msg)
        }
        _this.$loading.hide();
      }).catch(err => {
        _this.$loading.hide();
        handleAlert('error', '系统开小差了...')
      })
    },
    langList(){
      var _this = this
      requestlangList().then(res=>{
        if (res.data.code == 100) {
            _this.langTypeList = res.data.data
        }else{
          handleAlert('error', res.data.msg)
        }
      }).catch(err => {
        handleAlert('error', '系统开小差了...')
      })
    },
    businessList(){
      var _this = this
      requestTranslateBusinessList().then(res=>{
        if (res.data.code == 100) {
            _this.businessTypeList = res.data.data
        }else{
          handleAlert('error', res.data.msg)
        }
      }).catch(err => {
        handleAlert('error', '系统开小差了...')
      })
    },
    // 搜索
    keyDown(e) {
      if (e.keyCode === 13) {
        this.onSubmit()
      }
    },
    onSubmit () {
      this.currentPage=1
      this.dataList()
    },
    // 重置
    reset (formInline) {
      this.searchForm = {
        sourceWord: '',
        langType: '',
        businessType:''
      };
      this.currentPage = 1;
      this.dataList();
    },
    importTranslate(){
      this.importForm = {
        translateFile:null,
       businessType:'',
        langType:'',
      }
      this.fileList = []
      this.importTranslateVisiable = true;
    },
    exportUntranslate(){
      this.exportForm.includeExistTranslate = false
      this.exportForm.businessType = ''
      this.exportForm.langType = ''
      this.exportTranslateVisiable = true;
    },
    showHandProcess(){
      var _this = this
      this.showProcessVisiable=true;
      requestProcessList().then(res=>{
          if(res.data.code ==100){
            _this.processList = res.data.data
          }else{
            handleAlert('error', res.data.msg)
          }
      }).catch(err => {
        handleAlert('error', '系统开小差了...')
      })
    },
    subitExportUntranslate(){
      var _this = this
      this.$refs['exportForm'].validate((valid) => {
        if(!valid){
          handleAlert('error',"请完善信息！");
          return;
        }
        this.$loading.show()
      var params = new URLSearchParams()
      params.append('langType',  this.exportForm.langType )
      params.append('businessType', this.exportForm.businessType)
      params.append("includeExistTranslate",this.exportForm.includeExistTranslate?'1':'2')
      requestExportUnTranslate(params).then(res=>{
          if (res.data.code == 100){
            this.exportTranslateVisiable = false;
            handleAlert('success','提交成功！')
          }else{
              handleAlert('error', res.data.msg)
          }
          this.$loading.hide()
      }).catch(err => {
        handleAlert('error', '系统开小差了...')
        this.$loading.hide()
      })
    })
    },
    submitImportTranslate(){
      this.$refs['importForm'].validate((valid) =>{
        if(!valid){
          handleAlert('error',"请完善信息！");
          return
        }
        this.$loading.show()
      const form = new FormData();
      form.append("file",this.importForm.translateFile.raw)
       form.append("businessType",this.importForm.businessType)
       form.append("langType",this.importForm.langType)
      requestImportTranslate(form).then(res=>{
        if (res.data.code == 100){
           this.importTranslateVisiable = false;
            handleAlert('success','提交成功！')
        }else{
          handleAlert('error', res.data.msg)
        }
        this.$loading.hide()
      }).catch(error=>{
        handleAlert('error', '系统开小差了...')
        this.$loading.hide()
      })
    }
      )
    },
    deleteProcessRecord(row){
      var params = new URLSearchParams()
      params.append('id',  row.id)
      requestDeleteProcessRecord(params).then(res=>{
        if (res.data.code == 100){
           this.showProcessVisiable = false;
            handleAlert('success','处理成功！')
        }else{
          handleAlert('error', res.data.msg)
        }
      }).catch(err=>{
        handleAlert('error', '系统开小差了...')
      })
    },
    downLoadFile(row){
      var params = new URLSearchParams()
      params.append("id",row.id)
      requestDownLoadFile(params).then(res=>{
          if (res.headers['content-disposition'] !== undefined) {
          let blob = new Blob([res.data], { type: 'application/vnd.ms-excel;charset=utf-8' })
          let fileName = decodeURI(res.headers['content-disposition'])
          if (fileName) {
            fileName = fileName.substring(fileName.indexOf('=') + 1)
          }
          const elink = document.createElement('a')
          elink.download = fileName
          elink.style.display = 'none'
          elink.href = URL.createObjectURL(blob)
          document.body.appendChild(elink)
          elink.click()
          URL.revokeObjectURL(elink.href)
          document.body.removeChild(elink)
          this.showProcessVisiable = false;
        }else{
          handleAlert('error', new TextDecoder("UTF-8").decode(res.data))
        }
      }).catch(err => {
        handleAlert('error', '系统开小差了...')
      })
    },
    /**
     * 新增按钮点击
     */
    addNewClick(){
      this.submitTranslateForm = {}
      this.addNewTranslateVisiable = true;

    },
    /**
     * 监听提交文件变化
     * @param {*} file 更改的文件
     */
    handleTranslateFileChange(file){
        this.importForm.translateFile = file
    },
    /**
     * 提交新增
     */
    submitAdd(){
      this.$refs['submitTranslateForm'].validate((valid)=>{
        if(!valid){
          handleAlert('error', '请核对输入')
          return
        }
        this.$loading.show()
        requestAddNew(this.submitTranslateForm).then(res=>{
          if(res.data.code == 100){
            handleAlert('success',res.data.msg)
            this.resetSubmitForm()
            this.dataList()
          }else{
            handleAlert('error', res.data.msg)
          }
          this.$loading.hide()
        }).catch(err=>{
          handleAlert('error', '系统开小差了...')
          this.$loading.hide()
        })
      })
    },
    /**
     * 编辑按钮点击
     * @param {*} row 当前行数据
     */
    editClick(row){
      this.submitTranslateForm = Object.assign({}, row)
      this.editTranslateVisiable = true;
    },
    /**
     * 提交编辑
     */
    submitEdit(){
      this.$refs['submitTranslateForm'].validate((valid)=>{
        if(!valid){
          handleAlert('error', '请核对输入')
          return
        }
        this.$loading.show()
        requestEditTranslate(this.submitTranslateForm).then(res=>{
          if(res.data.code == 100){
            handleAlert('success',"编辑成功！")
            this.resetSubmitForm()
            this.dataList()
          }else{
            handleAlert('error', res.data.msg)
          }
          this.$loading.hide()
        }).catch(err=>{
          handleAlert('error', '系统开小差了...')
          this.$loading.hide()
        })
      })
    },
    delectClick(row){
      this.$confirm('此操作将永久删除该翻译, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
         var params =  new URLSearchParams();
         params.append("id",row.id)
          requestDeleteOne(params).then(res=>{
            if(res.data.code == 100){
              handleAlert('success',"删除成功！")
              if(this.resultList!=null&&this.resultList.length==1){
                this.currentPage =this.currentPage-1
              }
              this.dataList()
            }else{
              handleAlert('error', res.data.msg)
            }
          }).catch(error=>{
            handleAlert('error', '系统开小差了...')
          })
        })
    },
    resetSubmitForm(){
      this.editTranslateVisiable = false;
      this.addNewTranslateVisiable = false
      this.$refs.submitTranslateForm.resetFields();
    },
     // 时间转换
     dateFormat(row, column, cellValue, index) {
      if (cellValue !== null) {
        const date = new Date(parseInt(cellValue))
        const dayFormat = date.getFullYear() + "-" + (date.getMonth() + 1).paddingZero() + "-" + date.getDate().paddingZero()
        const hhmmss = date.getHours().paddingZero(2) + ':' + date.getMinutes().paddingZero(2) + ':' + date.getSeconds().paddingZero(2)
        return dayFormat + " " + hhmmss
      }
    },
    maximumArea() {
      setTimeout(() => {
        tableHeight()
        setTimeout(() => {
          this.maximumHeight = tableMaxHeight;
        }, 50)
      })
    },
    tableHeightArea() {
      var _this = this;
      _this.maximumArea();
      window.addEventListener("resize", function () {
        _this.maximumArea();
      });
    }
  },
  mounted () {
    this.tableHeightArea()
    this.dataList()
    this.langList()
    this.businessList()
    window.addEventListener('keydown', this.keyDown)
  },
  destroyed() {
    window.removeEventListener('keydown', this.keyDown, false)
  },
}
</script>
<style>
/* .el-icon-info,.el-icon-upload2 {
  color: #1890ff;
} */
</style>
