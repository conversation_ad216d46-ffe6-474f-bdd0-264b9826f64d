import { post,  get,down,getByArrayBufferRespose } from "../plugins/request";
import { sysServerUrl, cmsServeerUrl } from '../assets/js/common'


// 获取订单信息列表 分页
export const listOrderInfo = (params) => get(sysServerUrl + 'ormOrderInfo/listOrderInfoForBackStage', params); 
//获取当前用户管理的所有国家
export const currUserCountryInfo = (params) => get(sysServerUrl + 'ormOrderInfo/currentUserCountryInfo', params); 

/**
 * 获取某订单详情  不分页
 * @param {*} params 
 * @returns 
 */
export const getOrderDetail = (params) => get(sysServerUrl + 'ormOrderInfo/getOrderDetailForBackStage', params); 
/**
 * 编辑订单请求
 * @param {*} params 
 * @returns 
 */
export const editOrderRequest = (params) => post(sysServerUrl + 'ormOrderInfo/editOrderForBackStage', params); 

/**
 * 更新订单状态请求
 * @param {*} params 
 * @returns 
 */
export const updateOrderStatusRequest = (params) => get(sysServerUrl + 'ormOrderInfo/updateOrderStatusForBackStage', params); 


/**
 * 导出订单表格
 * @param {*} params 
 * @returns 
 */
export const exportOrderInfo = (params) => getByArrayBufferRespose(sysServerUrl + 'ormOrderInfo/exportOrderInfoForBackStage', params); 


/**
 * 获取订单配件详情 分页
 * @param {*} params 
 * @returns 
 */
export const getOrmOrderPartsDetailList = (params) => get(sysServerUrl + 'ormOrderPartsDetail/detailList', params); 