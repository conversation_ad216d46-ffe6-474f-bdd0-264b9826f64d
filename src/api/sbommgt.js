import {post, get, down, del, download, getWithArray} from "../plugins/request";
import {sysServerUrl} from '../assets/js/common'

export const getMatchRule = (params) => get(sysServerUrl + 'sbom/todo/getMatchRule', params);
export const listVINTodo = (params) => get(sysServerUrl + 'sbom/todo/listVIN', params);// SBOM管理-VIN待办列表
export const listBrandTree = (params) => get(sysServerUrl + 'sbom/todo/listBrandTree', params);// SBOM管理-品牌（车型-配置）树
export const listYear = (params) => get(sysServerUrl + 'sbom/sbom/listYear', params);// SBOM管理-品牌（车型-年款）树
export const assignConfig = (params) => get(sysServerUrl + 'sbom/todo/assignConfig', params);// SBOM管理-分配VIN车型
export const listTodo = (params) => get(sysServerUrl + 'sbom/todo/listTodo', params);// SBOM管理-VIN待办列表
export const confirmMbomAction = (params) => get(sysServerUrl + 'sbom/todo/confirmMbomAction', params);// SBOM管理-确定MBOM操作
export const countTodo = (params) => get(sysServerUrl + 'sbom/todo/countTodo', params);// SBOM管理-查询待办数量
export const todoYearTree = (params) => get(sysServerUrl + 'sbom/todo/todoYearTree', params);// SBOM管理-查询待办数量
export const getCNGType = (params) => get(sysServerUrl + 'sbom/todo/getCNGType', params);// SBOM管理-查询待办数量
export const ignoreVIN = (params) => get(sysServerUrl + `sbom/todo/ignoreVIN`, params);// SBOM管理-信息列表
export const revertIgnoreVIN = (params) => get(sysServerUrl + `sbom/todo/revertIgnoreVIN`, params);// SBOM管理-信息列表


export const saveData = (params, tableName) => post(sysServerUrl + `sbom/${tableName}/save`, params);// SBOM管理-信息列表
export const updateData = (params, tableName) => get(sysServerUrl + `sbom/${tableName}/update`, params);// SBOM管理-信息列表
export const deleteData = (params, tableName) => get(sysServerUrl + `sbom/${tableName}/delete`, params);// SBOM管理-信息列表
export const updateBatchById = (params, tableName) => getWithArray(sysServerUrl + `sbom/${tableName}/updateBatchById`, params);// SBOM管理-信息列表
export const listData = (params, tableName) => get(sysServerUrl + `sbom/${tableName}/list`, params);// SBOM管理-信息列表
export const importData = (params, tableName) => post(sysServerUrl + `sbom/${tableName}/import`, params);// SBOM管理-信息导入
export const exportData = (params, tableName) => download(sysServerUrl + `sbom/${tableName}/export`, params);// SBOM管理-信息导出
export const downloadTemplate = (filename) => down(sysServerUrl + `static/excel/sbom/${filename}`);// SBOM管理-信息导出
// export const downTemplate = (params) => down(sysServerUrl + 'static/excel/用户导入模板.xlsx' + params); // 用户管理-模板下载


export const listPartAggregation = (params) => get(sysServerUrl + `sbom/afterSalePart/listPartAggregation`, params);// SBOM管理-信息列表
export const ignorePartAggregation = (params) => get(sysServerUrl + `sbom/materialAggregation/ignorePartAggregation`, params);// SBOM管理-信息列表
export const listSelection = () => get(sysServerUrl + `sbom/materialAggregation/listSelection`);// SBOM管理-信息列表
export const onlineImport = (params) => getWithArray(sysServerUrl + `sbom/afterSalePart/onlineImport`, params);// SBOM管理-信息列表
export const listSBOM = (params) => get(sysServerUrl + 'sbom/sbom/list', params);// SBOM管理-车辆信息列表
export const listConfigList = (params) => get(sysServerUrl + 'sbom/sbom/listConfigList', params);// SBOM管理-车辆信息列表
export const exportSBOM = (params) => download(sysServerUrl + 'sbom/sbom/export', params);// SBOM管理-车辆信息列表导出
export const matchSbom = (params) => download(sysServerUrl + 'sbom/materialCountry/matchSbom', params);// SBOM管理-车辆信息列表导出
export const importSBOM = (params) => post(sysServerUrl + 'sbom/sbom/import', params);// SBOM管理-sbom导入
export const clearSBOM = (params) => post(sysServerUrl + 'sbom/sbom/clearSbom', params);// SBOM管理-清空sbom
export const coverSBOM = (params) => post(sysServerUrl + 'sbom/sbom/coverSbom', params);// SBOM管理-清空sbom

export const listConfigByYearId = (params) => get(sysServerUrl + 'sbom/sbom/listConfigByYearId', params);

export const listMBOM = (params) => get(sysServerUrl + 'sbom/mbom/list', params);// SBOM管理-车辆信息列表
export const exportMBOM = (params) => download(sysServerUrl + 'sbom/mbom/export', params);// SBOM管理-车辆信息列表导出

export const listVIN = (params) => post(sysServerUrl + 'sbom/vehicleInfo/list', params);// SBOM管理-车辆信息列表
export const startVIN = (params) => post(sysServerUrl + 'sbom/vehicleInfo/start', params);// SBOM管理-车辆信息列表
export const exportVehicleInfo = (params) => download(sysServerUrl + 'sbom/vehicleInfo/export', params);// SBOM管理-车辆信息列表导出

export const listChanges = (params) => get(sysServerUrl + 'sbom/designChange/list', params);// SBOM管理-设变件列表
export const exportChanges = (params) => download(sysServerUrl + 'sbom/designChange/export', params);// SBOM管理-设变件列表导出
export const importChanges = (params) => post(sysServerUrl + 'sbom/designChange/import', params);// SBOM管理-设变件列表导入
export const startChanges = () => get(sysServerUrl + 'sbom/designChange/start');
