import { post,  get, down, download } from "../plugins/request";
import { sysServerUrl, cmsServerUrl } from '../assets/js/common'


// =============== 主组管理 ===================//
export const groupList = (params) => get(cmsServerUrl + 'cms/epc/mainGroup/list', params);   // 分页查询
export const groupAdd = (params) => post(cmsServerUrl + 'cms/epc/mainGroup/add', params);    // 添加
export const groupEdit = (params) => post(cmsServerUrl + 'cms/epc/mainGroup/edit', params);  // 编辑
export const groupDel = (params) => post(cmsServerUrl + 'cms/epc/mainGroup/del', params);   // 删除

export const groupAll = (params) => get(cmsServerUrl + 'cms/epc/directory/getMainGroup', params);   // 查询全部


// =============== 分组管理 ===================//
export const directoryList = (params) => get(cmsServerUrl + 'cms/epc/directory/list', params);   // 分页查询
export const directoryAdd = (params) => post(cmsServerUrl + 'cms/epc/directory/add', params);    // 添加
export const directoryEdit = (params) => post(cmsServerUrl + 'cms/epc/directory/edit', params);  // 编辑
export const directoryDel = (params) => post(cmsServerUrl + 'cms/epc/directory/del', params);   // 删除
export const directoryDels = (params) => post(cmsServerUrl + 'cms/epc/directory/dels', params); // 批量删除
export const directoryEditPage = (params) => post(cmsServerUrl + 'cms/epc/directory/edit_page', params); // 获取分组所选中的车型
export const directoryTreeList = (params) => get(sysServerUrl + 'sys/car/train/getBrandTrainList', params);  // 查询 品牌-车系
export const directoryTrainYear = (params) => get(sysServerUrl + 'sys/car/model/getModelList', params);   // 查询 年款 - 车型
export const directorySvg = (params) => get(cmsServerUrl + 'cms/epc/directory/getSvg', params);
export const directoryTemplate = (params) => down(sysServerUrl + 'static/excel/配件批量导入模板.zip' + params); // 模板下载
export const directoryFinishUpload = (params) => get(cmsServerUrl + 'cms/epc/directory/finishUpload', params);  // 数据包上传完成
export const directoryImportZip = (params) => post(cmsServerUrl + 'cms/epc/directory/importBatchZip', params);  // 导入
export const directoryClearAll = (params) => post(cmsServerUrl + 'cms/epc/directory/clearAll', params); // 清空数据

// 2024-03-09 编辑配件
export const findRelModel = (params) => get(cmsServerUrl + 'cms/epc/basic/model/parts', params);



// =============== 配件管理 ===================//
export const basicList = (params) => get(cmsServerUrl + 'cms/epc/basic/list', params);   // 分页查询
export const basicAdd = (params) => post(cmsServerUrl + 'cms/epc/basic/add', params);    // 添加
export const basicEdit = (params) => post(cmsServerUrl + 'cms/epc/basic/update', params);  // 编辑
export const basicDel = (params) => post(cmsServerUrl + 'cms/epc/basic/del', params);   // 删除
export const basicDels = (params) => post(cmsServerUrl + 'cms/epc/basic/dels', params); // 批量删除
// export const basicImportZip = (params) => post(cmsServerUrl + 'cms/epc/basic/importBatchZip', params); // 导入 zip
export const basicDirectoryCarModelList = (params) => post(cmsServerUrl + 'cms/epc/basic/directoryCarModelList', params);  // 查询使用车型
export const basicEditPage = (params) => post(cmsServerUrl + 'cms/epc/basic/edit_page', params); // 获取配件所选中的车型
// export const basicClearAll = (params) => post(cmsServerUrl + 'cms/epc/basic/clearAll', params); // 清空数据
export const basicTemplate = (params) => down(sysServerUrl + 'static/excel/分组及配件批量导入模板.zip' + params); // 模板下载
export const basicFinishUpload = (params) => get(cmsServerUrl + 'cms/epc/basic/finishUpload', params); // 上传数据包完成
// export const basicManHour = (params) => get(cmsServerUrl + 'cms/epc/basic/getManHour', params); // 获取工时
// export const basicAddManHour = (params) => post(cmsServerUrl + 'cms/epc/basic/addManHour', params); // 添加工时
// export const basicDelManHour = (params) => post(cmsServerUrl + 'cms/epc/basic/delManHour/'+params); // 删除工时

export const basicReplaceChain = (params) => post(cmsServerUrl + 'cms/epc/replacement/chain', params); // 查询替换链
export const basicImage = (params) => post(cmsServerUrl + 'cms/epc/basic/image/'+ params); // 查询实物图
export const basicImportImage = (params) => post(cmsServerUrl + 'cms/epc/basic/importImages', params); // 导入 zip
export const basicreplaceImage = (params) => post(cmsServerUrl + 'cms/epc/basic/replaceImage', params);  // 清空
export const basicdownImage = (params) => download(cmsServerUrl + 'cms/epc/basic/downImage', params);  // 下载
export const partsImageTemplate = () => down(sysServerUrl + 'static/excel/实物图模板.zip'); // 实物图模板下载
export const partsInfoTemplate = () => down(sysServerUrl + 'static/excel/配件明细模板.xlsx'); // 明细模板下载
export const partsImportInfo = (params) => post(sysServerUrl + 'parts/Info/api/importInfo', params); // 上传明细
export const partsExportInfo = (params) => post(sysServerUrl + 'parts/Info/api/export/' + params); // 明细模板下载
export const partsUpdateInfo = (params) => post(sysServerUrl + 'parts/Info/api/updateInfo', params); // 修改明细
export const partsUpdateInfoTemplate = () => down(sysServerUrl + 'static/excel/修改配件名称模板.xlsx'); // 明细模板下载



// =============== 替换件管理 ===================//
export const replacementList = (params) => get(cmsServerUrl + 'cms/epc/replacement/list', params);   // 分页查询
export const replacementAdd = (params) => post(cmsServerUrl + 'cms/epc/replacement/add', params);    // 添加
export const replacementEdit = (params) => post(cmsServerUrl + 'cms/epc/replacement/edit', params);  // 编辑
export const replacementDel = (params) => post(cmsServerUrl + 'cms/epc/replacement/del', params);   // 删除
export const replacementDels = (params) => post(cmsServerUrl + 'cms/epc/replacement/dels', params); // 批量删除
export const replacementImportExcel = (params) => post(cmsServerUrl + 'cms/epc/replacement/importExcel', params); // 导入 excel
export const replacementShowlist = (params) => post(cmsServerUrl + 'cms/epc/replacement/showlist', params); // 导入 excel
export const replacementUpdated = (params) => post(cmsServerUrl + 'cms/epc/replacement/updated', params);
export const replacementTemplate = () => down(sysServerUrl + 'static/excel/替换关系备注导入模板.xlsx'); // 模板下载
export const replacementExport = (params) => post(sysServerUrl + 'cms/epc/replacement/timeslot', params); // 导出数据




// =============== 目录管理 ===================//
export const catalogCarList = (params) => get(cmsServerUrl + 'epc/catalog/getTrainYearList', params);   // 品牌-车型-年款  结构树
export const catalogDataList = (params) => get(cmsServerUrl + 'epc/catalog/catalogList', params);   // 根据年款查询目录
export const catalogModel = (params) => get(cmsServerUrl + 'epc/catalog/findByYearId', params);   // 根据年款配置

export const getPartsLists = (params) => get(cmsServerUrl + 'cms/epc/basic/getPartsList', params);   // 点击目录显示配件
export const downSvg = (params) => down(params);  // 下载SVG图
export const uploadSvg = (params) => post(cmsServerUrl + 'cms/epc/directory/uploadSvg', params);
export const catalogUpdate = (params) => post(cmsServerUrl + 'epc/catalog/update', params);
export const catalogClearAll = (params) => post(cmsServerUrl + 'cms/epc/basic/clearAll', params); // 清空数据
export const catalogDel = (params) => post(cmsServerUrl + 'epc/catalog/delete', params); // 清空数据
export const catalogFinishUpload = (params) => post(cmsServerUrl + 'epc/catalog/finishUpload', params); // 清空数据
export const importCatalog = (params) => post(cmsServerUrl + 'epc/catalog/importCatalog', params);
export const importParts = (params) => post(cmsServerUrl + 'epc/catalog/importParts', params);
export const catalogTemplate = () => down(sysServerUrl + 'static/excel/目录模板.zip'); // 目录模板下载
export const partsTemplate = () => down(sysServerUrl + 'static/excel/配件模板.xlsx'); // 配件模板下载
export const uploadProgress = (params) => get(cmsServerUrl + 'epc/catalog/progress', params);  // 上传进度
export const delProgress = (params) => post(cmsServerUrl + 'epc/catalog/del/progress', params);  // 上传进度的删除
export const updateShow = (params) => post(cmsServerUrl + 'epc/catalog/updateShow', params);  // 打包要下载数据包
export const packZip = (params) => post(cmsServerUrl + 'epc/catalog/packZip', params);  // 打包要下载数据包
export const atlasExcel = (params) => post(cmsServerUrl + 'epc/catalog/atlas/excel', params);  // 打包要下载数据包
export const emptyData = (params) => post(cmsServerUrl + 'epc/catalog/empty/data', params);  // 清空年款下的数据
export const catalogLangeList = (params) => get(sysServerUrl + 'sys/dict/query?dictType=language', params);  // 语言
export const atlasSplit = (params) => post(cmsServerUrl + 'epc/catalog/atlas/split', params);
// 保存
export const catalogSaveParts = (params) => post(cmsServerUrl + 'epc/catalog/saveParts', params);
// 删除
export const catalogDelParts = (params) => post(cmsServerUrl + 'epc/catalog/delParts/' + params);
// 修改总成的适用配置
export const updateDirectoryModel = (params) => post(cmsServerUrl + 'epc/catalog/updateModel', params);
// 配件搜索
export const accessoriesSearch = (params) => post(cmsServerUrl + 'epc/catalog/accessoriesSearch', params);


// =============== 常显件管理 ===================//
export const showList = (params) => post(cmsServerUrl + 'epc/parts/show/list', params); // 分页查询
export const showAdd = (params) => post(cmsServerUrl + 'epc/parts/show/add', params);  // 添加
export const showDel = (params) => post(cmsServerUrl + 'epc/parts/show/del/' + params);    // 删除
export const showImport = (params) => post(cmsServerUrl + 'epc/parts/show/importParts', params);  // 导入
export const partsShowTemplate = () => down(sysServerUrl + 'static/excel/常显件导入模板.xlsx'); // 目录模板下载
export const showDown = (params) => down(cmsServerUrl + 'epc/parts/show/batch?' + params);

