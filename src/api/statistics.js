import {download, get, post} from "../plugins/request";
import {cmsServerUrl, sysServerUrl} from '../assets/js/common'


// ========= 获取 品牌-车型-年款
export const getCarTrainModelList = (params) => post(cmsServerUrl + 'sys/car/model/getTrainModelList', params);
export const getCarTrainList = (params) => get(sysServerUrl + 'sys/car/train/getBrandTrainList', params);  // 查询 品牌-车系
export const getFeedbackList = (params) => get(sysServerUrl + 'epc/front/getFeedbackType', params);  // 查询 问题分类
//
export const getStatisticsUnion = (params) => post(cmsServerUrl + 'statistics/union/list', params);

// ========== 分类(总成)访问次数
export const visits = (params) => post(cmsServerUrl + 'epc/statistics/visits', params);


// ========== 分类(总成)访问人数
export const people = (params) => post(cmsServerUrl + 'epc/statistics/people', params);


// ========== 在线反馈统计 - 反馈分类
export const feedbackClass = (params) => post(cmsServerUrl + 'epc/statistics/feedbackClass', params);

// ========== 在线反馈统计 - 反馈数量
export const feedbackNum = (params) => post(cmsServerUrl + 'epc/statistics/feedbackNum', params);


// ========== 反馈评价统计
export const commentProportion = (params) => post(cmsServerUrl + 'epc/statistics/commentProportion', params);

// ========== 服务店访问量
export const serviceNumber = (params) => post(cmsServerUrl + 'epc/statistics/serviceNum', params);
export const serviceDown = (params) => download(cmsServerUrl + 'epc/statistics/batch', params);
