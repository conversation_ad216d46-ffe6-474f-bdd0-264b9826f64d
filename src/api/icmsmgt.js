import {post, get, down, del, download} from "../plugins/request";
import {sysServerUrl} from '../assets/js/common'

export const circuitImport = (params) => post(sysServerUrl + 'tis/icms/batchImport', params); // 电路管理-导入数据
export const clearPackageData = (params) => post(sysServerUrl + 'tis/icms/clearData', params); // 电路管理-清除数据
export const listPackage = (params) => get(sysServerUrl + 'tis/icms/listPackage', params); // 电路管理-数据包列表
export const deletePackage = (params) => post(sysServerUrl + 'tis/icms/deletePackage', params); // 电路管理-删除数据包
export const downloadPackage = (params) => download(sysServerUrl + 'tis/icms/downloadPackage', params); // 电路管理-下载数据包
// export const downloadPackage = (params) => get(sysServerUrl + 'tis/icms/api/getDetail', params); // 电路管理-内容数据
export const modelWithPackage = (params) => get(sysServerUrl + 'tis/icms/modelWithPackage', params);// 电路管理-车型列表（包含车型是否存在数据)
