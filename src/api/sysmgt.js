import { post, get, down, del } from "../plugins/request";
import { sysServerUrl, cmsServerUrl } from '../assets/js/common'

import axios from 'axios'
export const onLogin = (params) => post(sysServerUrl + 'sys/user/login', params); // 登录
import store from '@/store/index';
//必须使用此种方式,否则拦截器会在header上加token
export const requestTokenRefresh = (params) =>  axios.create()({// 刷新token
    url: sysServerUrl + 'sys/user/refresh',
    method: 'post',
    data: params
  })

export const captCha = (params) => get(sysServerUrl + 'sys/captcha/get', params); // 验证码

export const initialPsw = (params) => post(sysServerUrl + 'sys/user/initialPsw', params); // 登录
export const logout = (params) => post(sysServerUrl + 'sys/user/logout', params); // 登录

// export const project = (params) => get(cmsServerUrl + 'cms/project/list/all', params) ; // 全部项目
export const getUserInfo = (params) => get(sysServerUrl + 'sys/user/getCurrentInfo', params); // 用户信息
export const catalog = (params) => get(sysServerUrl + 'sys/user/current/menutreee', params); // 目录列表
export const dynamic = (params) => get(sysServerUrl + 'sys/log/dynamic/list', params); // 动态列表
export const userDefinedInfo = (params) => post(sysServerUrl + 'sys/user/update', params); // 用户自定义基本信息
export const userDefinedPwd = (params) => post(sysServerUrl + 'sys/user/updatePsw', params); // 用户自定义基本信息
export const projectSize = (params) => post(cmsServerUrl + 'cms/project/size/myProject', params); // 用户自定义基本信息
export const currentUserInfo = (params) => get(sysServerUrl + 'sys/user/current/info', params); // 用户管理-当前用户信息
// 主题修改
export const updatedTheme = (params) => post(sysServerUrl + "sys/user/updatedTheme", params); // 修改主题
// 我的主页
export const joinProjectList = (params) => get(cmsServerUrl + 'cms/project/participation', params); // 主页-查询参与的项目
export const systemBulletinList = (params) => get(sysServerUrl + 'sys/notice/getNoticeByRelease', params); // 主页-查询参与的项目
export const unreadData = (params) => get(cmsServerUrl + 'sys/notice/unread', params); // 主页-查询参与的项目
export const systemBulletinInfo = (params) => get(sysServerUrl + 'sys/notice/info/' + params); // 系统公告-查询系统公告明细
export const systemBulletinRead = (params) => get(sysServerUrl + 'sys/notice/editRead/' + params); // 系统公告-修改公告已读

// 2023-08-31 获取主页信息
export const getHomeData = (params) => get(sysServerUrl + 'epc/statistics/home', params); // 系统公告-查询系统公告明细
export const getRecordsData = (params) => get(sysServerUrl + 'epc/statistics/records', params); // 我的主页-访问记录
export const quickEntryMenuData = (params) => get(sysServerUrl + 'sys/quickEntry/api/quickEntryList', params); // 快捷入口-查询添加和未添加的菜单
export const quickEntryEdit = (params) => post(sysServerUrl + 'sys/quickEntry/api/edit', params); // 快捷入口-修改快捷入口

// 汇总消息
export const messageData = (params) => get(sysServerUrl + 'epc/statistics/message', params); // 汇总消息


// 文件上传
export const importAttach = (params) => post(sysServerUrl + 'sys/upload/attach', params);

export const uploadProgressApi = (params) => get(sysServerUrl +  'progress/getRedisResult/' + params); // 图片上传进度



// ========== 系统管理 ========== //
// 数据字典
export const dictData = (params) => get(sysServerUrl + 'sys/dict/list', params); // 数据字典-内容数据
export const dictTypeList = (params) => get(sysServerUrl + 'sys/dict/list/type', params); // 数据字典-类型数据
export const dictAdd = (params) => get(sysServerUrl + 'sys/dict/add', params);// 数据字典-增加
export const dictEdit = (params) => get(sysServerUrl + 'sys/dict/edit', params); //数据字典-编辑
export const dictDel = (params) => get(sysServerUrl + 'sys/dict/del/' + params); // 数据字典-删除

// 用户管理
export const userData = (params) => get(sysServerUrl + 'sys/user/list', params);// 用户管理-内容数据
export const userDataList = (params) => get(sysServerUrl + 'sys/user/findList', params);// 用户管理-内容数据
export const userRoleData = (params) => get(sysServerUrl + 'sys/role/list/all', params);// 用户管理-角色信息
export const assignRoleData = (params) => get(sysServerUrl + 'sys/role/findAll', params);// 用户管理-角色信息
export const userAdd = (params) => post(sysServerUrl + 'sys/user/add', params);// 用户管理-增加
export const userEdit = (params) => post(sysServerUrl + 'sys/user/edit', params); // 用户管理-编辑
export const userDel = (params) => get(sysServerUrl + 'sys/user/del/' + params); // 用户管理-删除
export const userBatchDel = (params) => post(sysServerUrl + 'sys/user/dels/', params); // 用户管理-批量删除
export const assignRole = (params) => get(sysServerUrl + 'sys/user/info' + params);// 用户管理-分配角色(展示)
export const updateAssignRole = (params) => post(sysServerUrl + 'sys/user/assignRole', params);  // 用户管理-分配角色(分配)
export const userPasswordReset = (params) => get(sysServerUrl + 'sys/user/initPsw' + params); // 用户管理-密码重置
export const downTemplate = (params) => down(sysServerUrl + 'static/excel/用户导入模板.xlsx' + params); // 用户管理-模板下载
export const userBatchDown = (params) => down(sysServerUrl + 'sys/user/batchExport' + params); // 用户管理-导出用户
export const userDown = (params) => down(sysServerUrl + 'sys/user/batch?' + params); // 用户管理-导出用户
export const userCountryData = (params) => get(sysServerUrl + 'sys/region/api/getCountry'); // 用户管理 - 获取国家
export const userCompleteCountryData = (params) => get(sysServerUrl + 'sys/region/api/complete/findTree'); // 用户管理 - 获取国家
export const getUserCountry = (params) => get(sysServerUrl + 'sys/user/getUserCountry/' + params);  // 获取用户的国家
export const updatedCountry = (params) => post(sysServerUrl + 'sys/user/updatedCountry', params);  // 修改国家
export const getUserBrandTrainTree = (params) => post(sysServerUrl + 'sys/car/train/getUserBrandTrainTree', params);
export const getUserTrain = (params) => post(sysServerUrl + 'sys/user/getUserTrain', params);
export const updateUserTrain = (params) => post(sysServerUrl + 'sys/user/updateUserTrain', params);
export const getCountryAll = (params) => post(sysServerUrl + 'sys/region/api/country/all', params); // 获取全部的国家信息包含大洲

//经销商管理

export const dealerData = (params) => get(sysServerUrl + 'sys/dealer/list', params);//经销商管理-内容数据
export const dealerChildData = (params) => get(sysServerUrl + 'sys/dealer/child/list', params);// 经销商管理-树结构单个节点子节点数据获取
export const downDealerTemplate = (params) => down(sysServerUrl + 'static/excel/服务店导入模板.xlsx' + params); // 经销商-模板下载
export const dealerBatchDown = (params) => down(sysServerUrl + 'sys/dealer/batchExport?' + params); // 经销商导出
export const dealerBatchSubAccount = (params) => post(sysServerUrl + 'sys/dealer/subAccount', params); // 经销商导出
export const getUserTrainByUserId = (params) => post(sysServerUrl + 'sys/user/getUserTrainByUserId', params);

export const getUserInfoId = (params) => post(sysServerUrl + 'sys/user/getUserInfo', params);
export const getCountryTree = (params) => post(sysServerUrl + 'sys/region/api/getCountryTree', params);



// 角色管理
export const roleData = (params) => get(sysServerUrl + 'sys/role/list', params); //角色管理-内容数据
export const roleAdd = (params) => post(sysServerUrl + 'sys/role/add', params); //角色管理-增加
export const roleEdit = (params) => post(sysServerUrl + 'sys/role/edit', params);//角色管理-编辑
export const roleDel = (params) => get(sysServerUrl + 'sys/role/del/' + params);//角色管理-删除

// 菜单管理
export const menuData = (params) => get(sysServerUrl + 'sys/menu/getMenuTree', params); // 菜单管理-内容数据
export const menuAssignList = (params) => get(sysServerUrl + 'sys/permission/list/all', params); // 菜单管理-分配按钮列表
export const menuAdd = (params) => post(sysServerUrl + 'sys/menu/add', params); // 菜单管理-增加
export const menuEdit = (params) => post(sysServerUrl + 'sys/menu/edit', params);  // 菜单管理-编辑
export const menuCheck = (params) => get(sysServerUrl + 'sys/menu/info' + params); // 菜单管理-查看
export const menuDel = (params) => get(sysServerUrl + 'sys/menu/del' + params); // 菜单管理-删除
export const menuAssignInfo = (params) => get(sysServerUrl + 'sys/permission/list/bymenu' + params); //菜单管理-分配按钮信息
export const menuAssignUpdate = (params) => post(sysServerUrl + 'sys/menu/assign', params); //菜单管理-分配按钮提交

// 按钮
export const permissionList = (params) => post(sysServerUrl + 'sys/menu/permission/list', params);   // 按钮列表
export const operatePermission = (params) => post(sysServerUrl + 'sys/menu/permission', params);   // 保存
export const delPermission = (params) => post(sysServerUrl + 'sys/menu/permission/del', params);   // 删除


// 权限管理
export const oauthData = (params) => get(sysServerUrl + 'sys/permission/list', params);//权限管理-内容数据
export const oauthRoleList = (params) => get(sysServerUrl + 'sys/role/list/all', params);//权限管理-角色列表
export const oauthUserList = (params) => get(sysServerUrl + 'sys/user/findList/all', params);//权限管理-用户列表
export const oauthSubmit = (params) => post(sysServerUrl + 'sys/permission/assign', params);//权限管理-提交
export const userRefresh = (params) => post(sysServerUrl + 'sys/permission/list/user', params);//权限管理-刷新用户
export const roleRefresh = (params) => post(sysServerUrl + 'sys/permission/list/role', params);//权限管理-刷新角色

// 部门管理
export const departmentData = (params) => get(sysServerUrl + 'sys/dept/getTree', params); //部门管理-查询树
export const departmentType = (params) => get(sysServerUrl + 'sys/dept/getDept', params); // 部门管理-查询所以部门
export const departmentTeam = (params) => get(sysServerUrl + 'sys/dept/getGruop/' + params); // 部门管理-查询部门下的小组
export const departmentAdd = (params) => post(sysServerUrl + 'sys/dept/add', params); // 部门管理-添加
export const departmentEdit = (params) => post(sysServerUrl + 'sys/dept/update', params); // 部门管理-修改
export const departmentDel = (params) => del(sysServerUrl + 'sys/dept/' + params); // 部门管理-删除
// 系统公告
export const noticeTypeList = (params) => get(sysServerUrl + 'sys/dict/query?dictType=noticeType', params);//系统公告-公告类型
export const noticeData = (params) => get(sysServerUrl + 'sys/notice/list', params);//系统公告-内容数据
export const noticeStateEdit = (params) => post(sysServerUrl + 'sys/notice/statusEdit', params); // 系统公告-状态
export const noticeAdd = (params) => post(sysServerUrl + 'sys/notice/add', params); // 系统公告-新增
export const noticeEdit = (params) => post(sysServerUrl + 'sys/notice/edit', params); // 系统公告-编辑
export const noticeDel = (params) => post(sysServerUrl + 'sys/notice/del/' + params); // 系统公告-删除
export const noticeInfo = (params) => post(sysServerUrl + 'sys/notice/getNoticeById/' + params); // 系统公告-明细
export const languageTypeList = (params) => get(sysServerUrl + 'sys/dict/query?dictType=language', params);//系统公告-语种类型
export const noticTarget = (params) => post(sysServerUrl + "sys/region/api/getTarget", params);   // 获取对象
export const noticEmpower = (params) => post(sysServerUrl + "sys/notice/empower/" + params);   // 获取对象


//附件管理
export const procSplitFile = (params) => post(sysServerUrl + "sys/upload/procFile", params); // 系统管理-文件管理-批量上传
export const checkUploadProgress = (params) => post(sysServerUrl + "sys/upload/checkProgress", params); // 系统管理-文件管理-批量上传分片


// ========== 车型管理 ========== //
// 车系管理
export const trainData = (params) => get(sysServerUrl + 'sys/car/train/list', params);//车系管理-内容数据
export const trainTypeDate = (params) => get(sysServerUrl + 'sys/dict/query?dictType=trainType', params);//车系管理-节点类型
export const getTrainInfo = (params) => post(sysServerUrl + 'sys/car/train/find' + params);//车系管理-获取当前信息
export const trainAdd = (params) => post(sysServerUrl + 'sys/car/train/addNode', params);//车系管理-增加节点
export const trainUpdate = (params) => post(sysServerUrl + 'sys/car/train/update', params); //车系管理-修改
export const trainDel = (params) => post(sysServerUrl + 'sys/car/train/delete', params); // 车系管理-删除
export const importTrain = (params) => post(sysServerUrl + 'sys/car/train/importTrain', params); // 批量上传
export const trainCarTypeList = (params) => get(sysServerUrl + 'sys/car/train/carTypeList', params);
export const trainTemplate = () => down(sysServerUrl + 'static/excel/车型模板.xlsx'); // 车型管理-车型模板下载


// 车型管理
// export const modelData = (params) => get(sysServerUrl + 'sys/car/model/list?modelType=1', params); // 车型管理-内容数据/
export const modelData = (params) => get(sysServerUrl + 'sys/car/model/findTree', params);
export const modelTrainList = (params) => get(sysServerUrl + 'sys/car/train/tree/list', params); // 车型管理-车型数据
export const modelImport = (params) => post(sysServerUrl + 'sys/car/model/batchImport', params); // 车型管理-内容数据
export const modelAdd = (params) => post(sysServerUrl + 'sys/car/model/add', params); //车型管理-增加
export const modelYearAdd = (params) => post(sysServerUrl + 'sys/car/model/addYearGroup', params); //车型管理-增加
export const modelEdit = (params) => post(sysServerUrl + 'sys/car/model/edit', params); // 车型管理-编辑
export const modelDel = (params) => post(sysServerUrl + 'sys/car/model/delete', params); //车型管理-删除
export const modelYearDel = (params) => post(sysServerUrl + 'sys/car/model/delYear', params); //车型管理-删除
export const modelConfigData = (params) => get(sysServerUrl + 'sys/car/model/cfglist' + params); // 车型管理-车型配置
export const configBatchDel = (params) => get(sysServerUrl + 'sys/car/model/deleteAllCfg' + params); //车型管理-删除全部配置代码
export const modelConfigDel = (params) => get(sysServerUrl + 'sys/car/model/deleteConfig' + params); //车型管理-删除当前行配置代码
export const configSave = (params) => post(sysServerUrl + 'sys/car/model/saveConfig', params); //车型管理-车型配置保存
export const modelTemplate = (params) => down(sysServerUrl + 'static/excel/车型导入模板.xlsx' + params); // 车型管理-车型模板下载
export const configTemplate = (params) => down(sysServerUrl + 'static/excel/车型配置表.xlsx' + params); // 车型管理-配置模板下载
export const editUseFlag = (params) => post(sysServerUrl + 'sys/car/model/editUseFlag', params);
export const modelEditYear = (params) => post(sysServerUrl + 'sys/car/model/editYear', params);  // 修改年款




//日志相关


export const requestLogList = (params) => post(sysServerUrl + 'sys/log/list', params);


export const requestLogModularList = (params) => get(sysServerUrl + 'sys/log/getLogModularList', params);

export const requestLogStackInfo = (params) => get(sysServerUrl + 'sys/log/getLogStack', params);



// ========= 区域管理 ====== //

export const regionData = (params) => get(sysServerUrl + 'sys/region/api/findTree', params);  // 获取结构树
export const regionType = (params) => get(sysServerUrl + 'sys/region/api/typeList', params);  // 获取类型
export const regionAdd = (params) => get(sysServerUrl + 'sys/region/api/add', params);  // 添加
export const regionEdit = (params) => get(sysServerUrl + 'sys/region/api/edit', params);  // 修改
export const regionDel = (params) => get(sysServerUrl + 'sys/region/api/del/' + params);  // 删除
export const regionImportInfo = (params) => post(sysServerUrl + 'sys/region/api/batchImport', params);  // 导入
export const regionTemplate = (params) => down(sysServerUrl + 'static/excel/' + params); // 明细模板下载

