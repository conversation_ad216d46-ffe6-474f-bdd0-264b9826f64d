// =========== 手册管理 ============
import {down, download, formPost, get, getByArrayBufferRespose, post} from "../plugins/request";
import {cmsServerUrl, sysServerUrl} from '../assets/js/common'

export const manualTreeList = (params) => get(sysServerUrl + 'sys/car/train/getBrandTrainList', params);  // 查询 品牌-车系
export const manualYearList = (params) => get(sysServerUrl + 'sys/car/model/getYearList', params);  // 查询 年款
export const userCountryData = (params) => get(sysServerUrl + 'sys/region/api/getCountry'); // 用户管理 - 获取国家
export const manualAdd = (params) => post(cmsServerUrl + 'tis/manual/api', params);  // 添加
export const manualEdit = (params) => post(cmsServerUrl + 'tis/manual/api/edit', params);  // 修改
export const manualDel = (params) => post(cmsServerUrl + 'tis/manual/api/del/' + params);  // 删除
export const manualStatus = (params) => post(cmsServerUrl + 'tis/manual/api/status', params);   // 修改状态
export const manualData = (params) => post(cmsServerUrl + 'tis/manual/api/findList', params);  // 数据
export const manualInfo = (params) => get(cmsServerUrl + 'tis/manual/api/info/' + params);  // 获取详情
export const manualDown = (params) => down(params);  // 下载
export const manualType = (params) => get(sysServerUrl + 'sys/dict/query?dictType=manualType', params);  // 手册类型
export const manualLanguage = (params) => get(sysServerUrl + 'sys/dict/query?dictType=language', params);

export const basicFinishUpload = (params) => get(cmsServerUrl + 'tis/manual/api/finishUpload', params); // 上传数据包完成



// =========== 评分管理 ============
export const commentData = (params) => post(cmsServerUrl + 'cms/manual/comment/findList', params);  // 数据
export const commentStatus = (params) => post(cmsServerUrl + 'cms/manual/comment/solve', params);   // 修改状态
export const commentDel = (params) => post(cmsServerUrl + 'cms/manual/comment/del/' + params);  // 删除
export const commentStatistics = (params) => post(cmsServerUrl + 'cms/manual/comment/statistics', params);  // 统计
export const commentLineChart = (params) => post(cmsServerUrl + 'cms/manual/comment/lineChart', params);  // 统计
export const commentTreeList = (params) => get(sysServerUrl + 'sys/car/train/getBrandTrainList', params);  // 查询 品牌-车系
export const commentTrainYear = (params) => get(sysServerUrl + 'sys/car/model/getYearList', params);   // 查询 年款 - 车型
export const commentdetail = (params) => post(cmsServerUrl + 'cms/manual/comment/detail/' + params);  // 统计



// =========== 技术问答 ============
export const serviceData = (params) => get(cmsServerUrl + 'epc/serviceFeedback/list', params);   // 数据
export const serviceDel = (params) => post(cmsServerUrl + 'epc/serviceFeedback/del', params);  // 删除
export const serviceInfo = (params) => get(cmsServerUrl + 'epc/serviceFeedback/getServiceFeedbackDetails', params);    // 获取详情
export const serviceReply = (params) => post(cmsServerUrl + 'epc/serviceFeedback/reply', params);   // 回复
export const serviceTreeList = (params) => get(sysServerUrl + 'sys/car/train/getBrandTrainList', params);  // 查询 品牌-车系



// 反馈管理
export const feedbackData = (params) => get(sysServerUrl + 'sys/epc/feedback/list', params); // 分页查询列表
export const feedbackInfo = (params) => get(sysServerUrl + 'sys/epc/feedback/editPage', params); // 查询单条
export const feedbackDel = (params) => post(sysServerUrl + 'sys/epc/feedback/del', params);  // 删除
export const feedbackReply = (params) => post(sysServerUrl + 'sys/epc/feedback/reply', params);  // 回复
export const feedbackTreeList = (params) => get(sysServerUrl + 'sys/car/train/getBrandTrainList', params);  // 查询 品牌-车系
export const feedbackTypeList = (params) => get(sysServerUrl + 'sys/epc/feedback/getFeedbackType', params);  // 查询 品牌-车系
export const feedbackEnd = (params) => post(sysServerUrl + 'sys/epc/feedback/end/' + params);   // 结束反馈
export const getTrainModelList = (params) => get(sysServerUrl + "sys/car/model/getCarTrainModelList", params); // 获取车型列表
export const feedbackExport = (params) => download(sysServerUrl + "sys/epc/feedback/export" , params); // 导出反馈列表
export const feedbackAnalyzeType = (params) => get(sysServerUrl + 'sys/dict/query?dictType=analyzeType', params); // 获取类型
export const feedbackVinPropType = (params) => get(sysServerUrl + 'sys/dict/query?dictType=vinProperty', params); // 获取类型
export const submitFeedbackAnalyze = (params) => post(sysServerUrl + 'partsFeedbackAnalyze/saveOrUpdate', params); // 获取类型


// =========== 维修案例 ===========
export const caseData = (params) => post(cmsServerUrl + 'epc/serviceCase/api/list', params);   // 数据
export const caseDel = (params) => post(cmsServerUrl + 'epc/serviceCase/api/del/' + params);   // 删除
export const caseInfo = (params) => get(cmsServerUrl + 'epc/serviceCase/api/info/' + params);   // 详情
export const caseEdit = (params) => post(cmsServerUrl + 'epc/serviceCase/api/edit', params);   // 编辑
export const caseAdd = (params) => post(cmsServerUrl + 'epc/serviceCase/api/add', params);   // 编辑
export const caseStatus = (params) => post(cmsServerUrl + 'epc/serviceCase/api/statusEdit', params);   // 编辑状态
export const caseTreeList = (params) => get(sysServerUrl + 'sys/car/train/getBrandTrainList', params);  // 查询 品牌-车系
export const caseTrainYear = (params) => get(sysServerUrl + 'sys/car/model/getModelList', params);   // 查询 年款 - 车型







// =========== 翻译管理 ===========

export const requestTranslateList = (params) => get(sysServerUrl + 'translateMgt/listTranslate', params);   //条件查询翻译列表

export const requestlangList = (params) => get(sysServerUrl + 'translateMgt/langList', params);   //获取所有语言列表


export const requestTranslateBusinessList = (params) => get(sysServerUrl + 'translateMgt/translateBusinessList', params);   //获取所有业务类型列表


export const requestExportUnTranslate = (params) => get(sysServerUrl + 'translateMgt/exportUnTranslate', params);   //获取所有业务类型列表

export const requestImportTranslate = (params) => formPost(sysServerUrl + 'translateMgt/importTranslate', params);   //获取所有业务类型列表



export const requestProcessList = (params) => get(sysServerUrl + 'translateMgt/processList', params);   //获取处理进展列表


export const requestDeleteProcessRecord = (params) => get(sysServerUrl + 'translateMgt/deleteProcessRecord', params);   //请求删除处理进展



export const requestDownLoadFile = (params) => getByArrayBufferRespose(sysServerUrl + 'translateMgt/downloadFile', params);   //请求下载文件


export const requestAddNew = (params) => post(sysServerUrl + 'translateMgt/addNew', params);   //新增一个翻译

export const requestEditTranslate = (params) => post(sysServerUrl + 'translateMgt/editTranslate', params);   //编辑某个翻译

export const requestDeleteOne = (params) => get(sysServerUrl + 'translateMgt/delTranslate', params);   //删除某个翻译
