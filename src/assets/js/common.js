import Vue from 'vue';
import $ from 'jquery';
import store from "@/store/index";
import {Message} from 'element-ui';

const env = {
  prod: {
    sysServerUrl: 'https://epc.shineray-motors.com/epc-server/',
    frontHome: '/epc/#/sparePartIndex',
    frontLogin: '/epc/#/',
    useCommonLogin: true,
    showLog: false,
  },
  dev: {
    sysServerUrl: 'http://192.168.1.164:8502/',
    // sysServerUrl: 'http://192.168.1.166/srm-epc/',
    frontHome: '/epc/#/sparePartIndex',
    frontLogin: '/epc/#/',
    useCommonLogin: false,
    showLog: true,
  }
}
// export const sysServerUrl = 'http://192.168.1.166/srm-epc/'
const current = env.dev;
window.showLog = current.showLog;
export const sysServerUrl = current.sysServerUrl;
export const cmsServerUrl = sysServerUrl
export const frontHome = current.frontHome;
export const frontLogin = current.frontLogin;
export const useCommonLogin = current.useCommonLogin;
import axios from "axios";

/**
 * 数字格式化为n位，不足n位则前面补0
 * @param {*} n
 */
Number.prototype.paddingZero = function (n = 2) {
  let s = "";
  for (let i = 1; i < n; i++) {
    s += '0';
  }
  return (s + this).slice(-1 * n);
}

export function addTabs(path, title) {
  var name = path.replaceAll("/", "");
  store.commit("addKeepAliveCache", name);
  var submenu = {
    path: path,
    name: name,
    label: title,
  };
  store.commit("selectMenu", submenu);
}

// tab删除
export function removeTabs(routeVal) {
  var oldTabList = store.state.tabList;
  // 删除tabList中的该对象
  for (let i = 0; i < oldTabList.length; i++) {
    var item = oldTabList[i];
    if (item.path === routeVal.path) {
      oldTabList.splice(i, 1);
    }
  }
  // 删除keepAlive缓存
  store.commit("removeKeepAliveCache", routeVal.name);
}

export function getMyDate(str) {
  var date = new Date(str)
  var Y = date.getFullYear() + '-'
  var M = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) + '-' : date.getMonth() + 1 + '-'
  var D = date.getDate() < 10 ? '0' + date.getDate() + '' : date.getDate() + ''
  var time = Y + M + D
  return time
}

export function projectDate() {
  store.commit("removeKeepAliveCache", 'cmsmgtmanuallist');
  store.commit("removeKeepAliveCache", 'cmsmgttasklist');
  store.commit("removeKeepAliveCache", 'cmsmgtauditlist');
  store.commit("removeKeepAliveCache", 'releasemgtissuelist');
  store.commit("removeKeepAliveCache", 'releasemgtpublishSetlist');
}

// 统计区域高度
export function statisticalSize() {
  setTimeout(() => {
    let tabHeight = 0;
    if ($(".el-tabs__header").length != 0) {
      tabHeight = $(".el-tabs__header").outerHeight(true);
    }
    let allHeight = $(".layoutContainer").outerHeight(true);
    let searchHeight = $(".secondFloat").outerHeight(true);
    let val = allHeight - searchHeight - tabHeight - 17;
    $(".statisticalArea").css({"height": val, "overflow": "auto"});
  })
}

export function statisticalHeight() {
  statisticalSize();
  window.addEventListener("resize", function () {
    statisticalSize();
  })
}

// 数列表内容高度
export function contentSize() {
  setTimeout(() => {
    let butHeight = $('.topButton').outerHeight(true) == null ? 0 : $('.topButton').outerHeight(true)
    let distance = $('.leftData').outerHeight(true) - butHeight;
    $(".scrollClass").css('height', distance)
    window.addEventListener("resize", function () {
      let butHeight = $('.topButton').outerHeight(true) == null ? 0 : $('.topButton').outerHeight(true)
      let distance = $('.leftData').outerHeight(true) - butHeight;
      $(".scrollClass").css('height', distance)
    })
  })
}

// 表格高度
export let tableMaxHeight = "";

export function tableHeight() {
  setTimeout(() => {
    if ($('.el-table').length != 0) {
      let allHeight = $(".layoutContainer").height() == null ? 0 : $(".layoutContainer").height();
      let tabHeight = $(".el-tabs__header").outerHeight(true) == null ? 0 : $(".el-tabs__header").outerHeight(true);
      let searchHeight = 0;
      if ($(".el-tabs").length != 0) {
        var idVal = $(".el-tabs__item.is-active").attr("aria-controls");
        searchHeight = $(`.el-tabs__content #${idVal} .secondFloat`).outerHeight(true) == null ? 0 : $(`.el-tabs__content #${idVal} .secondFloat`).outerHeight(true);
      } else {
        searchHeight = $(".secondFloat").outerHeight(true) == null ? 0 : $(".secondFloat").outerHeight(true);
      }
      let handleHeight = $(".tableHandle").outerHeight(true) == null ? 0 : $(".tableHandle").outerHeight(true);
      let pagHeight = $(".pagination-container").outerHeight(true) == null ? 0 : 33;
      var val = allHeight - tabHeight - searchHeight - handleHeight - pagHeight - 5;
      tableMaxHeight = val;
    }
  })
}

// 提示信息
export function handleAlert(type, text) {
  if (document.getElementsByClassName("el-message").length == 0) {
    return Message({
      type: type,
      message: text,
      duration: "1200"
    })
  }
}

// 目录展开状态
export function expandEvents(data) {
  if (data[0].children.length > 0) {
    if (data[0].children[0].children.length > 0) {
      if (data[0].children[0].children[0].children.length > 0) {
        if (data[0].children[0].children[0].children[0].children.length > 0) {
          if (data[0].children[0].children[0].children[0].children[0].children.length > 0) {
            if (data[0].children[0].children[0].children[0].children[0].children[0].children.length > 0) {
            } else {
              return data[0].children[0].children[0].children[0].children[0].children[0]
            }
          } else {
            return data[0].children[0].children[0].children[0].children[0]
          }
        } else {
          return data[0].children[0].children[0].children[0]
        }
      } else {
        return data[0].children[0].children[0]
      }
    } else {
      return data[0].children[0]
    }
  } else {
    return data[0]
  }
}

export function expandTree(data) {
  if (data[0].children !== null) {
    if (data[0].children[0].children !== null) {
      if (data[0].children[0].children[0].children !== null) {
        if (data[0].children[0].children[0].children[0].children !== null) {
          if (data[0].children[0].children[0].children[0].children[0].children !== null) {
            if (data[0].children[0].children[0].children[0].children[0].children[0].children !== null) {
            } else {
              return data[0].children[0].children[0].children[0].children[0].children[0]
            }
          } else {
            return data[0].children[0].children[0].children[0].children[0]
          }
        } else {
          return data[0].children[0].children[0].children[0]
        }
      } else {
        return data[0].children[0].children[0]
      }
    } else {
      return data[0].children[0]
    }
  } else {
    return data[0]
  }
}

// 语种数据
export function languageType() {
  axios.get(`${sysServerUrl}sys/dict/query?dictType=languageType`).then(res => {
    if (res.data.code == 100) {
      sessionStorage.setItem('language', JSON.stringify(res.data.data))
    }
  })
}

// 记录访问的菜单
export function accessRecordLog(menu) {
  // axios.get(`${sysServerUrl}sys/access/accessMenu?menu=` + menu).then(res => {

  // })
  axios.post(`${sysServerUrl}sys/menu/accessMenu`, menu).then(res => {

  })
}

export const quickEntryMap = new Map([
  ["待办任务", "toDoTask.png"],
  ["配件管理", "accessoryManage.png"],
  ["SBOM管理", "SBOMIocn.png"],
  ["替换关系", "replaceRelation.png"],
  ["在线反馈", "feedback.png"],
  ["手册管理", "manualManage.png"],
  ["技术手册", "manualManage.png"],
  ["订单管理", "orderManage.png"],
  ["车辆信息查询", "vehicleInfo.png"],
  ["技术通告", "technicalCircular.png"],
  ["系统公告", "systemNotice.png"],
]);

export function iconZoom() {
  setTimeout(() => {
    $(".el-image-viewer__mask").css("pointer-events", "none");
    $(".el-icon-full-screen").on("click", () => {
      $(".el-icon-c-scale-to-original").attr("title","还原");
      $(".el-icon-full-screen").attr("title", "全屏");
    });
    $(".el-icon-zoom-out").attr("title", "缩小");
    $(".el-icon-zoom-in").attr("title", "放大");
    $(".el-icon-full-screen").attr("title",  "全屏");
    $(".el-icon-c-scale-to-original").attr("title", "还原");
    $(".el-icon-refresh-left").attr("title", "右旋转");
    $(".el-icon-refresh-right").attr("title", "左旋转");
    $(".el-image-viewer__prev").attr("title", "上一张");
    $(".el-image-viewer__next").attr("title", "下一张");
    var imgSrc = $(".el-image-viewer__canvas img").attr("src");
    $(".el-image-viewer__canvas img").attr("src", imgSrc);
    Vue.$loading.show();
    $(".el-image-viewer__canvas img").on("load", function() {
      Vue.$loading.hide();
    });
    $(".el-image-viewer__canvas img").on("error",function() {
      Vue.$loading.hide();
    })
    $(".el-image-viewer__prev").on("click", () => {
      Vue.$loading.show();
      $(".el-image-viewer__canvas img").on("load", function() {
        Vue.$loading.hide();
      });
      $(".el-image-viewer__canvas img").on("error",function() {
        Vue.$loading.hide()
      })
    });
    $(".el-image-viewer__next").on("click", () => {
      Vue.$loading.show();
      $(".el-image-viewer__canvas img").on("load", function() {
        Vue.$loading.hide();
      });
      $(".el-image-viewer__canvas img").on("error",function() {
        Vue.$loading.hide()
      })
    });
  }, 100);
}
