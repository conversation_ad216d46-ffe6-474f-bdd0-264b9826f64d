:root {
  --border-color: #dddddd;
  --border-radius: 4px;
  --text-color: #333;
  --disabled-color: #bbbbbb;
  --font-color: #fff;
}
.el-container.is-vertical {
  background: var(--content-bgColor);
}
.tabContent,
#app .el-footer {
  /* background: var(--cell-bgColor); */
  background: #fff;
}
[v-cloak] {
  display: none !important;
}
/** 清除内外边距 **/
body, h1, h2, h3, h4, h5, h6, hr, p, blockquote, /* structural elements 结构元素 */
dl, dt, dd, ul, ol, li, /* list elements 列表元素 */
pre, /* text formatting elements 文本格式元素 */
form, fieldset, legend, button, input, textarea, /* form elements 表单元素 */
th, td/* table elements 表格元素 */ {
  margin: 0;
  padding: 0;
}
/** 设置默认字体 **/
body,
button, input, select, textarea /* for ie */ {
  font: 12px/1.5;
  font-family: Arial, Microsoft YaHei, "微软雅黑", Helvetica, \5b8b\4f53, sans-serif !important;
  box-sizing: border-box;
}
h1, h2, h3, h4, h5, h6 { font-size: 100%; }
address, cite, dfn, em, var { font-style: normal; } /* 将斜体扶正 */
code, kbd, pre, samp { font-family: courier new, courier, monospace; } /* 统一等宽字体 */
small { font-size: 12px; } /* 小于 12px 的中文很难阅读，让 small 正常化 */
 
/** 重置列表元素 **/
ul, ol { 
  list-style: none;
 }
 
/** 重置文本格式元素 **/
a { text-decoration: none; }
a:hover { text-decoration: none; }
 
 
/** 重置表单元素 **/
legend { color: #000; } /* for ie6 */
fieldset, img { border: 0; } /* img 搭车：让链接里的 img 无边框 */
button, input, select, textarea { font-size: 100%; } /* 使得表单元素在 ie 下能继承字体大小 */
/* 注：optgroup 无法扶正 */
 
/** 重置表格元素 **/
table { border-collapse: collapse;border-collapse: unset; border-spacing: 0; }
 
/* 清除浮动 */
.ks-clear:after, .clear:after {
  content: '\20';
  display: block;
  height: 0;
  clear: both;
}
.ks-clear, .clear {
  *zoom: 1;
}
/* 弹框 */
.alert {
  display: none;
	position: fixed !important;
	top: 50% !important;
	left: 50% !important;
	transform: translate(-50%,-50%) !important;
	z-index: 99999 !important;
	padding:10px 15px !important;
  border-radius: 3px !important;
  font-size: 15px !important;
}
.alert-success {
  font-size: 15px !important;
  color: #fff !important;
  background-color:rgba(0,0,0,0.8) !important;
}
/* 滚动条样式 */
.el-scrollbar__wrap{
  overflow-x: hidden;
  margin-bottom: 0 !important;
  /* margin-right: 0 !important; */
}
::-webkit-scrollbar {
  /* 滚动条整体样式 */
  width: 10px;
  height: 10px;
}
/* .el-scrollbar__wrap::-webkit-scrollbar {
  width: 6px;
} */
.el-textarea__inner::-webkit-scrollbar {
  width: 6px;
}
::-webkit-scrollbar-thumb {
  /* 滚动条里面小方块 */
  border-radius: 25px;
  background: rgb(221, 222, 224);
  cursor: pointer !important;
}
::-webkit-scrollbar-thumb:hover {
  background: rgb(199, 201, 204);
  cursor: pointer;
}
::-webkit-scrollbar-track {
  border-radius: 25px;
  /* 滚动条里面轨道 */
  background: rgb(249, 249, 249);
}
.el-select-dropdown__wrap::-webkit-scrollbar-track {
  background: transparent;
}
.el-table::-webkit-scrollbar-thumb,
.el-table--scrollable-x .el-table__body-wrapper::-webkit-scrollbar {
  height: 10px;
  position: absolute;
  cursor: pointer;
}
@media screen and (max-width: 1600px) and (min-width: 1400px){
  ::-webkit-scrollbar {
    /* 滚动条整体样式 */
    width: 10px;
    height: 10px;
  }
  .el-table::-webkit-scrollbar-thumb,
  .el-table--scrollable-x .el-table__body-wrapper::-webkit-scrollbar {
    height: 10px;
  }
}
@media screen and (max-width: 1366px) and (min-width: 1280px) {
  ::-webkit-scrollbar {
    /* 滚动条整体样式 */
    width: 10px;
    height: 10px;
  }
  .el-table::-webkit-scrollbar-thumb,
  .el-table--scrollable-x .el-table__body-wrapper::-webkit-scrollbar {
    height: 10px;
  }
}
@media screen and (max-width: 1280px) and (min-width: 1024px) {
  ::-webkit-scrollbar {
    /* 滚动条整体样式 */
    width: 9px;
    height: 9px;
  }
  .el-table::-webkit-scrollbar-thumb,
  .el-table--scrollable-x .el-table__body-wrapper::-webkit-scrollbar {
    height: 9px ;
  }
}
@media screen and (max-width: 1024px) {
  ::-webkit-scrollbar {
    /* 滚动条整体样式 */
    width: 8px;
    height: 8px;
  }
  .el-table::-webkit-scrollbar-thumb,
  .el-table--scrollable-x .el-table__body-wrapper::-webkit-scrollbar {
    height: 8px;
  }
}
