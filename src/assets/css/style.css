/* el-tab */
.el-tabs.el-tabs--top .el-tabs__nav {
  padding: 10px 0;
}

.el-tabs.el-tabs--top .el-tabs__nav-wrap::after {
  height: 1px !important;
  background-color: var(--border-color) !important;
}

.el-tabs.el-tabs--top .el-tabs__item {
  height: 36px;
  line-height: 35px;
  border-radius: 4px;
  border: 1px solid var(--line-color);
  background-color: var(--cell-bgColor);
  margin-right: 30px;
}

.el-tabs.el-tabs--top .el-tabs__item.is-top:nth-child(2) {
  padding-left: 20px;
}

.el-tabs.el-tabs--top .el-tabs__item.is-top:last-child {
  padding-right: 20px;
}

.el-tabs.el-tabs--top .tab-title {
  margin-left: 0;
}

.el-tabs.el-tabs--top .el-tabs__item:hover {
  border: 1px solid var(--theme-color);
  color: var(--theme-color);
}

.el-tabs.el-tabs--top .el-tabs__item.is-active {
  border: 1px solid var(--theme-color);
  background-color: var(--type-bgColor);
  color: var(--theme-color);
  font-weight: bold;
}

.el-tabs.el-tabs--top .el-tabs__active-bar {
  display: none;
}
.el-tabs.el-tabs--top .el-badge__content.is-fixed {
  right: -8px;
}
.el-tabs.el-tabs--top .el-tabs__nav-next {
  line-height: 56px;
}

.el-tabs.el-tabs--top .el-tabs__nav-prev {
  line-height: 56px;
}
/* el-icon */
.el-icon-close-tip{
  display: none !important;
}
.el-select{
  vertical-align: middle
}
.el-textarea {
  background-color: #FFF;
  border: 1px solid #DCDFE6;
  border-radius: 3px;
  box-sizing: border-box;
}
.el-textarea__inner{
  resize: none !important;
  color: #333 !important;
  line-height: 1.5 !important;
  font-size: 14px !important;
  padding: 3px 5px !important;
  margin-bottom: 20px !important;
  border: none !important;
}
.el-textarea__inner:hover,
.el-textarea__inner:focus {
  border: 1px solid var(--theme-color);
}
/* 搜索输入框 */
.authority,.menuManager,.authority,.roleManage,.userManage,
.trainList,.modelList,
.proManagement,.manualPage,.auditPage,.imagePage,.stylePage,
.layoutContainer,.latestNewsPage{
  padding:15px 15px 2px;
  box-sizing: border-box;
  overflow-x: hidden;
  height: 100%;
  overflow-y: hidden;
}
.custom-tree-node,
.el-tree-node__label
 {
  font-size: 14px;
  flex: 1;
  display: flex;
  justify-content: space-between;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.el-tree-node__label{
 display: inline-block;
}
.custom-tree-node>span{
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.custom-tree-node .attribute{
  text-overflow: initial
}
.el-tree>.el-tree-node>.el-tree-node__children>.el-tree-node>.el-tree-node__content>.custom-tree-node{
  text-decoration: underline
}
.attribute{
  padding: 0 10px;
  /* pointer-events: none; */
}
.el-input.is-disabled .el-input__inner::placeholder {
  color: transparent
}
.el-input.is-disabled .el-input__inner {
  /* background-color: transparent; */
  border: 1px solid #DCDFE6;
  /* color: #606266; */
  cursor: text !important;
}
.el-input.is-disabled .el-input__icon {
  cursor: text !important;
}
/* 排序框 */
.el-input-number.is-controls-right {
  width: 86%;
}
.el-input-number.is-controls-right .el-input {
  width: 100% !important;
}
.el-input-number.is-controls-right .el-input .el-input__inner {
  padding: 0 22px 0 8px !important;
  text-align: left;
}
.el-input-number.is-controls-right:hover .el-input .el-input__inner {
  border: 1px solid var(--theme-color) !important;
}
.el-input-number.is-controls-right .el-input-number__decrease:hover,
.el-input-number.is-controls-right .el-input-number__increase:hover {
  color: var(--theme-color) !important;
}
.el-input-number.is-controls-right .el-input-number__decrease.is-disabled:hover,
.el-input-number.is-controls-right .el-input-number__increase.is-disabled:hover {
  color: #c0c4cc !important;
  cursor: not-allowed;
}
.el-input-number.is-controls-right .el-icon-arrow-up::before {
  content: "\e78f";
}
.el-input-number.is-controls-right .el-icon-arrow-down::before {
  content: "\e790";
}
.el-input-number.is-controls-right .el-input-number__decrease,
.el-input-number.is-controls-right .el-input-number__increase {
  background: transparent;
  border: none;
  width: 20px;
  font-size: 14px;
  line-height: 18px;
}
.el-input-number.is-controls-right .el-input-number__decrease {
  bottom: 4px;
}
.el-input-number.is-controls-right .el-input-number__increase {
  top: 3px;
}
/* 查询筛选条件 */
.secondFloat {
  /* border: 1px solid #eee; */
  margin-bottom: 8px;
  box-sizing: border-box;
}
/* 输入框 */
.secondFloat .el-form-item {
  margin: 8px 15px 8px 0px;
}
.secondFloat .el-form-item:last-child {
  margin: 8px 0px 8px 0px;
}
.secondFloat .el-button+.el-button{
  margin-left: 0px !important;
}
.secondFloat .el-input__inner{
  padding: 0 7px;
  width: 200px;
  height: 32px;
  line-height: 32px;
}
.el-popover__reference-wrapper .el-input.el-input--suffix .el-input__inner,
.el-select .el-input__inner,
.el-dialog .el-select .el-input__inner {
  padding-right: 25px;
}
.el-switch__label {
  height: 33px;
}
.el-switch__label.is-active {
  color: var(--theme-color) !important;
}
.el-checkbox.is-bordered.is-checked,
.el-range-editor.is-active,
.el-range-editor.is-active:hover,
.el-select .el-input.is-focus .el-input__inner,
.el-select .el-input__inner:focus,
.el-select .el-input__inner:hover,
.el-input__inner:hover,
.el-input__inner:focus {
  border-color: var(--theme-color) !important;
}
.el-input.is-disabled .el-input__inner {
  /* background-color: #F5F7FA;
  border-color: #E4E7ED; */
  border: 1px solid #DCDFE6 !important;
}
.el-select-group .el-select-dropdown__item {
  font-size: 15px;
  padding: 0 20px 0 30px;
  color: #606266;
  height: 34px;
  line-height: 34px;
  color:var(--text-color) !important;
}
.el-select-group .el-select-dropdown__item.selected.hover,
.el-select-group .el-select-dropdown__item.selected {
  font-weight: bold !important;
  color: var(--theme-color) !important;
}
.el-select-dropdown__item.hover,
.el-select-group .el-select-dropdown__item.hover {
  background-color: var(--hover-color) !important;
  color: var(--text-color) !important;
}
.el-select-group__title {
  padding-left: 15px;
  color: #bbbbbb;
  margin-bottom: 2px;
}
.el-date-editor.el-input, .el-date-editor.el-input__inner{
  width: 200px;
}
.el-date-editor .el-range-input {
  /* text-align: left; */
}
.secondFloat .el-form-item__label {
  font-size: 13px;
  /* padding: 0; */
  line-height: 32px;
  /* margin-right: 10px; */
  /* min-width: 50px !important; */
  color: #000;
  /* text-align: left; */
}
.secondFloat .el-input{
  font-size: 12px !important;
}
.secondFloat .el-form-item__content {
  line-height: 32px;
  height: 32px;
  font-size: 12px;
}
.secondFloat .line{
  margin: 0 3px;
  vertical-align: top;
}
.secondFloat .el-select__caret,
.secondFloat .el-input__icon,
.el-dialog .el-input__icon {
  line-height: 32px ;
  font-size: 12px !important;
}
.fromRight .el-form-item__content>.el-input .el-input__inner{
  padding: 0 50px 0 5px !important;
}
.fromRight .el-form-item__content>.el-input .el-input__inner[type="number"],
.el-input .el-input__inner[type="number"]{
  padding-right: 0 !important
}
.el-input .el-input__count .el-input__count-inner,
.el-input .el-input__count {
  padding: 0 !important;
  line-height: 1 !important;
  position: absolute;
  left: 0px;
  width: 50px;
  background: transparent;
}
.secondFloat .el-input--prefix .el-input__inner{
  padding: 0 25px 0;
}
/* 按钮 */
.headerContent .el-button {
  border: 1px solid var(--theme-color) !important;
  box-sizing: border-box;
  background-color: var(--theme-color) !important;
  color: var(--font-color) !important;
}
.secondFloat .el-button{
  font-size: 12px !important;
  height: 32px;
  line-height: 31px;
  letter-spacing: 1px !important;
  padding: 0 10px !important;
  margin: 0 10px 0 0 !important;
}
/* 表格区域 */
.tableDetail{
  /* background: #fff; */
  box-sizing: border-box;
}
/* 表格操作区域 */
.tableHandle{
  /* background: #fafafa; */
  background: var(--dialog-color);
  height: 31px;
  line-height: 31px;
  font-size: 14px;
  padding: 0 10px;
  /* border: 1px solid #cfd5de; */
  border: 1px solid var(--table-color);
  border-bottom: none;
}
.el-date-table td.available:hover,
.el-date-table td.today span {
  /* color: var(--theme-color); */
}
/*按钮 */
.tableHandle .el-button--text,
.topButton .el-button--text,
.formTitle .el-button--text,
.navRight .el-button--text,
.taskCenter .el-button--text {
  color: #333;
}

.tableHandle .el-button {
  font-size: 14px !important;
  height: 32px;
  line-height: 32px;
  padding: 0 !important;
  margin: 0 5px;
}
.tableHandle .el-button+.el-button {
  margin: 0 5px !important;
}
.el-icon-plus,
.tableHandle .el-icon-delete,
.topButton .el-icon-delete,
.el-icon-delete,
.tableHandle .el-icon-s-data,
.topButton .el-icon-s-data,
.el-icon-s-data,
.tableHandle .el-icon-setting,
.topButton .el-icon-setting,
.el-icon-odometer,
.taskCenter .el-icon-search,
.el-icon-s-promotion,
.tableHandle .el-icon-document-delete,
.topButton .el-icon-document-delete,
.tableHandle .el-icon-upload,
.topButton .el-icon-upload,
.el-icon-info,
.el-icon-upload2,
.el-icon-download,
.tableHandle .el-icon-magic-stick,
.tableHandle .el-icon-video-play,
.tableHandle .el-icon-coin,
.topButton .el-icon-coin,
.topButton .el-icon-finished,
.formTitle .el-icon-refresh,
.topButton .el-icon-connection,
.el-icon-connection{
  color: #1890ff;
  font-size: 15px;
}

.tableHandle .el-icon-document-checked,
.topButton .el-icon-document-checked {
  color: #0e8e0d;
  font-size: 15px;
}

.tableHandle .el-icon-document-remove,
.topButton .el-icon-document-remove{

  color: #f45050;
  font-size: 15px;
}

.bulkDown-icon::before,
.import-icon::before,
.bulkImport-icon::before,
.adopt-icon::before,
.reject-icon::before,
.opinion-icon::before,
.push-icon::before,
.preview-icon::before,
.preserve-icon::before,
.save-icon::before,
.power-icon::before,
.checkIn-icon::before,
.checkOut-icon::before,
.svgLook-icon::before,
.deleteRed-icon::before {
  content: "";
  display: inline-block;
  width: 13px;
  height: 13px;
  background: url("../image/templateIcon.png") no-repeat;
  background-size: 100%;
  margin-right: 5px;
  vertical-align: middle;
  margin-top: -3px;
}
.import-icon::before {
  background: url("../image/importIcon.png") no-repeat;
}
.bulkImport-icon::before{
  background: url("../image/bulkImport.png") no-repeat;
}
.adopt-icon::before {
  background: url("../image/adoptIcon.png") no-repeat;
}
.opinion-icon::before {
  background: url("../image/opinionIcon.png") no-repeat;
}
.power-icon::before {
  background: url("../image/powerIcon.png") no-repeat;
}
.reject-icon::before {
  background: url("../image/rejectIcon.png") no-repeat;
}
.push-icon::before {
  background: url("../image/pushIcon.png") no-repeat;
}
.checkIn-icon::before {
  background: url("../image/checkIn.png") no-repeat;
}
.checkOut-icon::before {
  background: url("../image/checkOut.png") no-repeat;
}
.preview-icon::before {
  background: url("../image/preview.png") no-repeat;
}
.preserve-icon::before {
  background: url("../image/preserve.png") no-repeat;
}
.save-icon::before {
  background: url("../image/saveAs.png") no-repeat;
}
.svgLook-icon::before {
  background: url("../image/viewIcon.png") no-repeat;
}
.deleteRed-icon::before {
  background: url("../image/deleteIcon.png") no-repeat;
}
.scrollClass .el-checkbox__inner::after{
  border: 2px solid #fff;
  border-left: 0;
  border-top: 0;
  height: 6px !important;
  left: 4px !important;
  width: 3px !important;
}
.el-checkbox__inner::after{
  border: 2px solid #fff;
  border-left: 0;
  border-top: 0;
  height: 7px !important;
  left: 4px !important;
  width: 4px !important;
}
.formTitle .el-button {
  margin-right: 15px;
}
/* 表格 */
.el-table__body,
.el-table__footer,
.el-table__header {
  table-layout: fixed;
  border-spacing: 0;
  border-collapse: collapse;
  border-collapse: unset;

}
.el-table {
  color: #333;
  font-size: 14px;
}
.el-table thead {
  color: #333;
  font-size: 15px;
}
.el-table,
.el-table__expanded-cell,
.el-table tr {
  /* background: transparent; */
}
.el-table__expand-icon {
  height: auto !important;
  line-height: 0px !important;
}
/* 表格边框线 */
.el-table--border, .el-table--group {
  border: 1px solid var(--table-color);
}
.el-table--border {
  border-right: none;
  border-bottom: none;
}
.el-table thead.is-group th.el-table__cell {
  border-right: 1px solid var(--table-color);
}
.el-table thead.is-group th.el-table__cell,
.el-table--border .el-table__cell,
.el-table__body-wrapper .el-table--border.is-scrolling-left~.el-table__fixed {
  border-right: 1px solid var(--table-color);
}
.el-table td.el-table__cell,
.el-table th.el-table__cell.is-leaf,
.el-table--border th.el-table__cell,
.el-table__fixed-right-patch {
  border-bottom: 1px solid var(--table-color);
}
.el-table tr:last-child td.el-table__cell {
  border-bottom: 1px solid transparent;
}
.el-table .el-table__cell {
  padding: 5px 0;
  box-sizing: border-box;
}
.el-table__empty-block {
  border-bottom: 1px solid var(--table-color);
}
.el-table--border::after,
.el-table--group::after,
.el-table::before {
  background-color: var(--table-color);
  z-index: 5;
}
.el-table__fixed-right::before,
.el-table__fixed::before{
  background-color: unset !important;
}
.el-table__fixed, .el-table__fixed-right{
  box-shadow: 0px -2px 3px 0px var(--table-color) !important;
}
.el-table__fixed {
  border-right: 1px solid var(--table-color);
}
.el-table__fixed-right {
  border-left: 1px solid var(--table-color);
}
.el-table .cell,
.el-table--border .el-table__cell:first-child .cell,
.el-table th.el-table__cell>.cell {
  padding-left: 5px;
  word-break: break-word;
}
.el-table .cell,
.el-table th.el-table__cell>.cell {
  padding-right: 5px;
  word-break: break-word;
}
.el-table td.el-table__cell>div,
.el-table td.el-table__cell .cell>div {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
/* 表格hover */
.el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell,
.el-table__body .el-table__row.hover-row td {
  background: var(--module-bgColor) !important;
}
/* 表格点击背景 */
.el-table .el-table__body tr.current-row:hover>td.el-table__cell,
.el-table .el-table__body .el-table__row.current-row td {
  background: #ffebc4 !important;
}
/* 表格斑马线颜色 */
.el-table--striped .el-table__body tr.el-table__row td.el-table__cell {
  background: var(--dialog-color);
}
.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
  background: var(--cell-bgColor);
}
.tabtop13 {
  border: none;
	margin: 0px 0 15px;
  border-collapse:collapse;
}
.tabtop13 tr,
.tabtop13 td {
	background-color:#ffffff;
	/*height:25px;
	line-height:150%;
  padding-left: 5px; */
  border: 1px solid var(--table-color);
  padding: 8px 10px;
  word-break: break-all;
  color: #333333;
}
.tabtop13 .tdTitle {
  font-weight: bold;
  background: var(--other-color);
  text-align: right;
}
.tabtop13 .uploadFileInfo {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.tabtop13 .uploadFileInfo > div {
  display: flex;
  align-items: center;
  margin: 3px 20px 3px 0px;
}
.tabtop13 .uploadFileInfo > div img {
  width: 16px;
  height: 16px;
  margin-right: 3px;
  vertical-align: middle;
}
.tabtop13 .uploadFileInfo > div > div:last-child:hover {
  text-decoration: underline;
}
/* 历史回复  */
.historyReply,
.replyArea,
.attachmentInfo {
  border-top: 1px solid var(--line-color);
  /* margin: 15px 0; */
  padding: 15px 0;
}
.historyReply > p,
.replyArea > p,
.attachmentInfo > p {
  font-weight: bold;
  background: var(--other-color);
  padding: 8px 10px;
  border: 1px solid var(--table-color);
  border-bottom: none;
}
.historyReply > div {
  min-height: 180px;
  padding: 8px 10px;
  border: 1px solid var(--table-color);
}
.historyReply > div > div {
  padding: 5px 0;
  border-bottom: 1px solid var(--border-color);
}
.historyReply > div > div:last-child {
  border-bottom: none;
}
/* top */
.historyReply > div > div .topInfo {
  display: flex;
  justify-content: space-between;
}
.historyReply > div > div .topInfo span:first-child{
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.historyReply > div > div .topInfo > span > span {
  color: #999;
  margin-right: 8px;
}
.historyReply > div > div .topInfo > span:last-child {
  color: #999;
  margin-left: 8px;
}
/* 附加信息 */
.historyReply .accessoryInfo > div{
  display: flex;
  align-items: center;
  margin: 8px 0 4px;
}
.historyReply .accessoryInfo > div > div:first-child {
  width: 30px;
  height: 30px;
  margin-right: 5px;
}

.historyReply .accessoryInfo > div > div:first-child img {
  width: 100%;
  max-height: 100%;
}
.historyReply .accessoryInfo > div > div:last-child > p:last-child {
  margin-top: 2px;
}
.historyReply .accessoryInfo > div > div:last-child > p:last-child span{
  color: #409EFF;
  cursor: pointer;
  margin-right: 8px;

}
/* 删除 */
.historyReply .rightDel {
  text-align: right;
  color: #f44336;
}
/* 回复 */
.replyArea > div {
  border: 1px solid var(--table-color);
}
.replyArea .el-textarea {
  border: none;
}
.replyArea .el-textarea__inner {
  resize: none;
  border-radius: 0;
}
/* 附加上传 */
.attachmentInfo > div {
  padding: 8px 10px;
  border: 1px solid var(--table-color);
}
.attachmentInfo .upload-demo{
  line-height: 1.6;
}
.attachmentInfo .upload-demo .el-upload {
  width: 100%;
}
.attachmentInfo  .el-upload__input {
  display: none;
}
.attachmentInfo .upload-demo .el-upload .el-upload-dragger {
  width: 100%;
  height: 140px;
  border: 1px dashed var(--table-color) !important;
  background-color: var(--dialog-color) !important;
  border-radius: 2px;
}
.attachmentInfo .upload-demo .el-upload .el-upload-dragger .el-upload_text {
  margin-top: 2%;
  color: var(--text-color);
}
.attachmentInfo .upload-demo .el-upload .el-upload-dragger .el-upload_text .el-button {
  padding: 8px 10px;
  margin-top: 8px;
}
.attachmentInfo div svg {
  width: 35px;
  height: 35px;
  color: var(--theme-color) !important;
}
.upload-demo .el-upload-list__item {
  transition: none !important;
}
/* 表格按钮 */
.el-table .el-button {
  padding: 0;
}
.el-button.el-button--text [class*=el-icon-]+span {
  margin-left: 0px;
}
.el-button--small.is-round {
  padding: 0 !important;
}
.scrollClass .el-tree-node__expand-icon,
.el-dialog .el-tree-node__expand-icon{
  color: #333 ;
}
.scrollClass .el-tree-node__expand-icon.is-leaf,
.el-dialog .el-tree-node__expand-icon.is-leaf{
  color: transparent
}
.scrollClass .el-tree-node__content{
  height: 33px !important;
  color: #333;
}
.scrollClass .el-tree-node__content>label.el-checkbox{
  margin-right: 0px;
}
.el-tree-node__content>label.el-checkbox {
  margin-right: 3px;
}
.el-tree-node__content:hover,
.scrollClass .el-tree-node__content:hover,
.scrollClass .el-upload-list__item:hover {
  background-color:var(--hover-color);
}
.scrollClass .el-tree-node.is-current > .el-tree-node__content {
  background-color: var(--other-color);
  color: var(--theme-color);
}
.scrollClass .el-checkbox__inner{
  width: 15px;
  height: 15px;
  margin-top: 1px;
  margin-right: 3px;
}
/* 开关 */
.el-switch {
  height: 32px !important;
  line-height: 32px !important;
}
/* 表格按钮 */
.tableDetail .el-button--small{
  padding: 0px !important;
  margin: 0 !important
}
.el-button+.el-button,.tableDetail .el-dropdown{
  margin-left: 12px !important;
}
/* .el-dropdown-menu .el-dropdown-menu__item .upload-demo{
  margin: 0
} */
.upload-demo .el-upload-list.el-upload-list--picture {
  width: 150px;
}
.upload-demo .el-button {
  /* margin: 0 !important; */
}
.upload-demo.inline-block{
  display: inline-block
}
.el-table td.el-table__cell .upload-demo.inline-block{
  line-height: 1;
  margin-right: 12px !important;
  vertical-align: middle;
  margin-top: -3px;
}
/* 列表信息 */
.infoDetail .leftData>div {
  margin-right:15px;
}
.infoDetail,
.infoDetail .el-row,
.infoDetail .leftData,
.infoDetail .leftData>div{
  height: 100%;
}
.infoDetail .leftData>div .topButton,
.infoDetail .leftData>div .taskCenter {
  background: #fafafa;
  border: 1px solid #cfd5de;
  border-bottom: none;
  color: #000;
  box-sizing: border-box;
  padding: 0 13px;
}
.infoDetail .leftData>div .topButton{
  overflow: hidden;
}
.topButton .el-button{
  margin-left: 0 !important;
  margin: 0 7px !important;
}
.inline-block{
  display: inline-block
}
.infoDetail .leftData .scrollClass,
.infoDetail .leftData .taskManualList,
.releaseContainer .releaseDetail{
  background: #fff;
  border: 1px solid #cfd5de;
  box-sizing: border-box;
}
.infoDetail .leftData .taskManualList{
  overflow-y: auto;
}
.releaseContainer .releaseDetail{
  height: calc(100% - 52px);
}
.infoDetail .fromRight{
  background: #fff;
  border: 1px solid #cfd5de;
  height: calc(100% - 0px);
  overflow-x: hidden;
  overflow-y: auto;
}
.infoDetail .fromRight .formTitle{
  background: #fafafa;
  border-bottom:1px solid #e7ebef;
  box-sizing: border-box;
  font-weight: bold;
  font-size: 14px;
  text-align: center;
  box-sizing: border-box;
  height: 42px;
  line-height: 42px;
}
.infoDetail .fromRight .rightTitle {
  padding-left: 20px;
  height: 42px;
  line-height: 42px;
  background: #fafafa;
  border-bottom:1px solid #e7ebef;
  box-sizing: border-box;

}
.rightTitle .el-button--text {
  color: #000;
}
.infoDetail .fromRight .butArea .el-form-item__content{
  width: 100% !important;
  display: inline-block !important;
  text-align: center !important;
  margin: 0 auto !important
}
.infoDetail .fromRight .el-select{
  width: 100%
}
.infoDetail .el-scrollbar {
  height:100% !important;
}
.infoDetail .fromRight .el-form-item{
  padding: 10px 70px 10px 50px;
  border-bottom: 1px solid #e7ebef;
  margin-bottom: 0px !important;
  box-sizing: border-box;
}
.infoDetail .fromRight .el-form-item:last-child{
  border-bottom: none;
}
.fromRight .el-input__inner{
  border: 1px solid #cfd5de;
  padding: 0 13px !important;
  color: #333 !important;
}
.fromRight .el-form{
  height: calc(100% - 42px);
  overflow-y: auto;
}
.el-form-item__label{
  color: #333;
}
.fromRight.auditRight iframe{
  height: calc(100% - 47px);
}
/* Dialog 弹框 */
.el-dialog__wrapper {
  overflow: hidden;
  display: flex;
  align-items: center;
}
.el-dialog{
  width:600px !important;
  border-radius: 5px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  /* position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%,-50%); */
  max-height: calc(100% - 20px);
  max-width: calc(100% - 20px);
  min-width: 600px;
  margin: auto !important;
  /* margin: 0 !important; */
  /* background: var(--dialog-color); */
}
.el-dialog .el-input__suffix {
  right: 0
}
.el-dialog__title{
  font-size: 16px;
}
.el-dialog__header,
.el-message-box__header {
  font-weight: bold;
  padding: 12px 20px 8px;
  background: #f5f5f5;
  border-bottom: 1px solid #eee;
  position: relative;
}
.el-message-box__message {
  overflow-x: auto;
  max-height: 500px;
}
.el-dialog__headerbtn,
.el-message-box__headerbtn{
  top: 50%;
  transform: translate(0, -47%)
}
.el-dialog__headerbtn .el-dialog__close,
.el-message-box__headerbtn .el-message-box__close{
  color: #000 !important;
  font-weight: bold;
  font-size: 18px;
}
.el-dialog .el-form-item__content{
  line-height: 35px;
}
.el-dialog .el-form-item__label{
  font-weight: bold;
  line-height: 35px;
}
.el-dialog .el-input__inner{
  height: 35px;
  line-height: 35px;
  padding: 0 10px
}
.el-dialog .el-form-item{
  margin-bottom: 20px;
}
.el-dialog__body{
  color: var(--text-color);
  padding: 20px 20px !important;
  flex: 1;
  font-size: 14px;
  overflow-x: hidden;
  overflow-y: auto;
}
.el-dialog .el-input {
  width: 86% !important
}
.el-dialog .el-select{
  width: 86%
}
.el-dialog .el-select .el-input.el-input--suffix,.fromRight .el-input.el-input--suffix{
  width: 100% !important;
  max-width: none;
}
.fromRight .el-pagination .el-select .el-input.el-input--suffix{
  width: 100px !important;
}
.el-dialog .el-form-item__content>span{
  width: 86% !important;
  font-size: 12px;
  line-height: 1.5px;
  color: #999;
  display: inline-block;
}
.el-popover__reference-wrapper{
  width: 50%
}
.el-dialog .el-popover__reference-wrapper .el-input.el-input--suffix{
  width: 100% !important;
  max-width: none;
}
.el-dialog .el-date-editor.el-input.el-input--prefix.el-input--suffix.el-date-editor--date{
  width: 85% !important;
  max-width: none;
}
.el-dialog .el-icon-date:before {
  content: "" !important;
}
.el-dialog .el-input--prefix .el-input__inner {
  padding: 0 10px;
}
.el-dialog .memberInfo .el-form-item__label {
  line-height: 26px;
}
.el-dialog .memberInfo .el-form-item__content{
  line-height: 26px;
}
.memberInfo .el-tree{
  max-height: 240px;
  overflow: auto;
}
.el-dialog-div {
  height: 80vh;
  overflow-x: hidden;
}
.el-tooltip__popper {
  max-width: 800px;
}
/* 删除弹框 MessageBox*/
.el-message-box{
  padding-bottom: 15px;
}
.el-message-box__title{
  font-size: 16px;
  color: #000;
  font-weight: bold
}
.el-message-box__header{
  padding: 15px 20px 12px;
}
.el-message-box__content {
  padding: 20px 15px 15px;
}

.el-message-box__status{
  font-size: 22px !important;
}
.el-select{
  vertical-align: top
}
/* 无背景按钮 */
.el-button.is-plain,
.el-button.el-button--default {
  color: var(--theme-color) !important;
  border:1px solid var(--theme-color) !important;
  background-color: var(--cell-bgColor)!important;
}
/* 普通按钮 */
.tableDetail .el-button.el-button--primary{
  border-color: transparent
}
.el-button.el-button--primary,
.el-button--primary,
.el-button.el-button--default.el-button--small.el-button--primary {
  color: #FFF !important;
  background-color: var(--theme-color) !important;
  border-color: var(--theme-color) !important;
}
.el-button:hover {
  opacity: 0.8;
}
.el-select-dropdown.is-multiple .el-select-dropdown__item.selected,
.el-select-dropdown__item.selected{
  color: var(--theme-color);
}
/* 单选 */
.el-radio__inner::after {
  width: 8px !important;
  height: 8px !important;
}
.el-radio__inner{
  width: 20px !important;
  height: 20px !important;
}
.el-radio__input .el-radio__inner:hover{
  border-color: var(--theme-color)
}
.el-switch.is-checked .el-switch__core,
.el-radio__input.is-checked .el-radio__inner {
  border-color: var(--theme-color);
  background: var(--theme-color);
}
.el-radio__input.is-checked+.el-radio__label {
  color: var(--theme-color);
}
/* 多选 */
.el-checkbox.is-bordered {
  margin-bottom: 15px;
}
.el-checkbox.is-bordered+.el-checkbox.is-bordered {
  margin-left: 0px;
}
.el-checkbox {
  margin-right: 20px;
}
.el-checkbox__inner {
  width: 16px;
  height: 16px;
}
.el-checkbox__inner::after{
  border: 2px solid #fff;
  border-left: 0;
  border-top: 0;
  height: 9px;
  left: 5px;
  width: 4px;
}
.el-checkbox__inner:hover {
  border-color: var(--theme-color);
}
.el-checkbox__input.is-checked .el-checkbox__inner, .el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: var(--theme-color) !important;
  border-color: var(--theme-color) !important;
}
.el-checkbox__input.is-checked+.el-checkbox__label {
  color: var(--theme-color) !important;
}
.el-checkbox__input.is-disabled+span.el-checkbox__label{
  color: #333;
}
.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after {
  border-color: #fff;
}
.el-tree-node  .is-leaf + .el-checkbox .el-checkbox__inner{
  display: inline-block;
}
/* 上传图片区域 */
.el-upload-list--picture .el-upload-list__item {
  padding: 10px 20px 10px 10px !important;
  height: 100px;
  background: #eee !important;
}
.el-upload-list--picture .el-upload-list__item-thumbnail {
  height: 80px;
  width: auto !important;
  margin-left: 0 !important;
  background: transparent !important;
}

/* ============= 下拉多选添加复选框 开始 =============== */
.el-select-dropdown.is-multiple .el-select-dropdown__item:after {
  content: "";
}
.el-select-dropdown.is-multiple .el-select-dropdown__item.selected:after {
  content: "";
}

/*参考el-checkbox实现checkbox样式*/
.el-select-dropdown.is-multiple .el-select-dropdown__item .checkbox {
  display: inline-block;
  position: relative;
  border: 1px solid #dcdfe6;
  border-radius: 2px;
  box-sizing: border-box;
  width: 14px;
  height: 14px;
  background-color: #fff;
  z-index: 1;
  transition: border-color .25s cubic-bezier(.71, -.46, .29, 1.46), background-color .25s cubic-bezier(.71, -.46, .29, 1.46);
}

.el-select-dropdown.is-multiple .el-select-dropdown__item.selected .checkbox {
  background-color: #409eff;
  border-color: #409eff;
}

/*参考el-select多选对号样式实现checkbox中对号的样式*/
.el-select-dropdown.is-multiple .el-select-dropdown__item.selected .checkbox::after {
  position: absolute;
  top: -11px;
  font-family: element-icons;
  content: "\e6da";
  font-size: 12px;
  font-weight: 700;
  -webkit-font-smoothing: antialiased;
  color: #fff;

}

/*设置置灰内容样式*/
.el-select-dropdown.is-multiple .el-select-dropdown__item.selected.is-disabled .checkbox {
  background-color: #f2f6fc;
  border-color: #dcdfe6;
}

.el-select-dropdown.is-multiple .el-select-dropdown__item.selected.is-disabled .checkbox::after {
  color: #c0c4cc;
}

.el-select-dropdown.is-multiple .el-select-dropdown__item.selected.is-disabled .label-name-box {
  color: #c0c4cc;
  font-weight: 400;
}

.el-select-dropdown__item {
  display: flex;
  align-items: center;
}
/* ============= 下拉多选添加复选框 结束 =============== */
/* 提交提醒 */
.loadMessage.el-loading-parent--relative {
  position: absolute !important;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 9999
}
.el-loading-mask {
  transition: 0s !important;
}
.loadMessage .el-loading-mask {
  background-color: transparent;
  transition: 0s !important;
}
.loadMessage .el-loading-spinner  {
  background: #fff;
  top: 50%;
  margin-top: 0px;
  width: auto;
  text-align: center;
  position: absolute;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  box-shadow: 0px 0px 5px 0px #d8d8d8;
  padding: 10px 25px;
  border-radius: 5px;
}
.loadMessage .el-loading-spinner i {
  margin: 0 5px;
  font-size: 25px;
}
.loadMessage .el-loading-spinner p {
  display: inline-block;
}
.loadMessage .el-loading-spinner .el-loading-text {
  color: #333;
  font-size: 15px;
  margin: 0 5px;
}
/* 点击实物图放大 */
.el-image-viewer__mask {
  opacity: 0.7;
}
.el-image-viewer__wrapper {
  z-index: 3000 !important;
}
.el-image-viewer__close {
  top: 30px !important;
  right: 5% !important;
}
.el-image-viewer__canvas {
  width: 85% !important;
  overflow: hidden;
  margin: auto;
  height: calc(100% - 95px) !important;
  margin-top: 20px !important;
  color: #fff;
}
.el-image-viewer__actions {
  bottom: 20px !important;
}
.el-image-viewer__btn {
  opacity: 1 !important;
}
.el-image-viewer__next,.el-image-viewer__prev {
  top: auto !important;
  bottom: 20px !important;
  transform: translateY(0%) !important;
}
.el-image-viewer__next {
  right: 33% !important;
}
.el-image-viewer__prev {
  left: 33% !important;
}
[class*=" el-icon-"], [class^=el-icon-] {
  cursor: pointer;
}
@media screen and (max-width: 1600px) and (min-width: 1400px) {
  /* el-table */
  .el-table {
    font-size: 13px;
  }
  .el-table thead {
    font-size: 14px;
  }
  .el-table .el-table__cell {
    padding: 3px 0;
  }
  /* 输入框 */
  .el-select-group .el-select-dropdown__item {
    font-size: 14px;
    padding: 0 16px 0 30px;
    color: #606266;
  }
  /* 点击实物图放大 */
  .el-image-viewer__close {
    width: 35px !important;
    height: 35px !important;
    font-size: 18px !important;
  }
  .el-image-viewer__actions {
    height: 40px !important;
    padding: 0 18px !important;
  }
  .el-image-viewer__actions__inner {
    font-size: 22px !important;
  }
  .el-image-viewer__next, .el-image-viewer__prev {
    width: 40px !important;
    height: 40px !important;
    font-size: 20px !important;
  }
  .el-image-viewer__next {
    right: 32% !important;
  }
  .el-image-viewer__prev {
    left: 32% !important;
  }
}
@media screen and (max-width: 1366px) and (min-width: 1280px) {
  .el-select-group .el-select-dropdown__item {
    font-size: 14px;
    padding: 0 14px 0 30px;
    color: #606266;
    /* height: 28px;
    line-height: 28px; */
  }
  /* 点击实物图放大 */
  .el-image-viewer__close {
    width: 32px !important;
    height: 32px !important;
    font-size: 16px !important;
  }
  .el-image-viewer__actions {
    width: 240px !important;
    height: 35px !important;
    padding: 0 16px !important;
  }
  .el-image-viewer__actions__inner {
    font-size: 19px !important;
  }
  .el-image-viewer__next, .el-image-viewer__prev {
    width: 35px !important;
    height: 35px !important;
    font-size: 16px !important;
  }
  .el-image-viewer__next {
    right: 32% !important;
  }
  .el-image-viewer__prev {
    left: 32% !important;
  }
}
@media screen and (max-width: 1280px) and (min-width: 1024px) {
  /* el-table */
  .el-table {
    font-size: 12px;
  }
  .el-table thead {
    font-size: 13px;
  }
  .el-table .el-table__cell {
    padding: 1px 0;
  }
  /* 输入框 */
  .el-select-group .el-select-dropdown__item {
    font-size: 14px;
    padding: 0 12px 0 30px;
    color: #606266;
    /* height: 26px;
    line-height: 26px; */
  }
  /* 点击实物图放大 */
  .el-image-viewer__close {
    width: 30px !important;
    height: 30px !important;
    font-size: 16px !important;
  }
  .el-image-viewer__actions {
    width: 230px !important;
    height: 32px !important;
    padding: 0 15px !important;
  }
  .el-image-viewer__actions__inner {
    font-size: 18px !important;
  }
  .el-image-viewer__next, .el-image-viewer__prev {
    width: 32px !important;
    height: 32px !important;
    font-size: 16px !important;
  }
  .el-image-viewer__next {
    right: 30% !important;
  }
  .el-image-viewer__prev {
    left: 30% !important;
  }
}
@media screen and (max-width: 1024px) {
  /* el-table */
  .el-table {
    font-size: 12px;
  }
  .el-table thead {
    font-size: 12px;
  }
  .el-table .el-table__cell {
    padding: 1px 0;
  }
  /* 输入框 */
  .el-select-group .el-select-dropdown__item {
    font-size: 14px;
    padding: 0 12px 0 30px;
    color: #606266;
    /* height: 28px;
    line-height: 28px; */
  }
  /* 点击实物图放大 */
  .el-image-viewer__close {
    width: 30px !important;
    height: 30px !important;
    font-size: 16px !important;
  }
  .el-image-viewer__actions {
    width: 230px !important;
    height: 32px !important;
    padding: 0 15px !important;
  }
  .el-image-viewer__actions__inner {
    font-size: 18px !important;
  }
  .el-image-viewer__next, .el-image-viewer__prev {
    width: 32px !important;
    height: 32px !important;
    font-size: 16px !important;
  }
  .el-image-viewer__next {
    right: 28% !important;
  }
  .el-image-viewer__prev {
    left: 28% !important;
  }
}
.el-icon-document::before {
  content: "";
  display: inline-block;
  width: 14px;
  height: 14px;
  vertical-align: middle;
  margin-top: -3px;
}
.el-icon-document.jpg::before {
  background: url("../image/fileUploadIoc/smallIoc/jpg.png") no-repeat center;
  background-size: 100% 100%;
}
.el-icon-document.png::before {
  background: url("../image/fileUploadIoc/smallIoc/png.png") no-repeat center;
  background-size: 100% 100%;
}
.el-icon-document.mp3::before {
  background: url("../image/fileUploadIoc/smallIoc/mp3.png") no-repeat center;
  background-size: 100% 100%;
}
.el-icon-document.mp4::before {
  background: url("../image/fileUploadIoc/smallIoc/mp4.png") no-repeat center;
  background-size: 100% 100%;
}
.el-icon-document.xls::before,
.el-icon-document.xlsx::before {
  background: url("../image/fileUploadIoc/smallIoc/xls.png") no-repeat center;
  background-size: 100% 100%;
}
.el-icon-document.doc::before,
.el-icon-document.docx::before {
  background: url("../image/fileUploadIoc/smallIoc/doc.png") no-repeat center;
  background-size: 100% 100%;
}
.el-icon-document.zip::before {
  background: url("../image/fileUploadIoc/smallIoc/zip.png") no-repeat center;
  background-size: 100% 100%;
}
.el-icon-document.pdf::before {
  background: url("../image/fileUploadIoc/smallIoc/PDF.png") no-repeat center;
  background-size: 100% 100%;
}
