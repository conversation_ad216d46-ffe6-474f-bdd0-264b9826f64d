/* StreamSaver完全隐藏样式 */
/* 确保所有StreamSaver相关的弹窗和iframe都被完全隐藏 */

/* 隐藏所有StreamSaver相关的iframe */
iframe[src*="streamsaver"],
iframe[src*="mitm"],
iframe[src*="blob:"] {
  display: none !important;
  position: fixed !important;
  top: -99999px !important;
  left: -99999px !important;
  width: 0 !important;
  height: 0 !important;
  opacity: 0 !important;
  pointer-events: none !important;
  z-index: -99999 !important;
  visibility: hidden !important;
  overflow: hidden !important;
}

/* 隐藏所有可能的StreamSaver容器 */
[class*="streamsaver"],
[id*="streamsaver"],
.streamsaver-iframe,
.streamsaver-popup {
  display: none !important;
  position: fixed !important;
  top: -99999px !important;
  left: -99999px !important;
  width: 0 !important;
  height: 0 !important;
  opacity: 0 !important;
  pointer-events: none !important;
  z-index: -99999 !important;
  visibility: hidden !important;
}

/* 隐藏所有可能的弹窗元素 */
div[style*="position: absolute"][style*="z-index"],
div[style*="position: fixed"][style*="z-index"] {
  display: none !important;
}

/* 确保body不会出现滚动条（由于隐藏的元素） */
body {
  overflow-x: hidden;
}
