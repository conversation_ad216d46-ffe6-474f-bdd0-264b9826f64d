import addNotice from '@/views/sysmgt/notice/addNotice'
import noticeDetail from '@/views/sysmgt/notice/noticeDetail'
const sysmgtRouter = [
  {
    path: '/sysmgt/dict/list',
    component: () => import('@/views/sysmgt/dict/list'),
    name: 'sysmgt_dict_list'
  },
  {
    path: '/sysmgt/user/list',
    component: () => import('@/views/sysmgt/user/list'),
    name: "sysmgt_user_list"
  },
  {
    path: '/sysmgt/dealer/list',
    component: () => import('@/views/sysmgt/dealer/list'),
    name: "sysmgt_dealer_list"
  },
  {
    path: '/sysmgt/role/list',
    component: () => import('@/views/sysmgt/role/list'),
    name: 'sysmgt_role_list'
  },
  {
    path: '/sysmgt/menu/list',
    component: () => import('@/views/sysmgt/menu/list'),
    name: 'sysmgt_menu_list'
  },
  {
    path: '/sysmgt/oauth/list',
    component: () => import('@/views/sysmgt/oauth/list'),
    name: 'sysmgt_oauth_list'
  },
  {
    path: '/sysmgt/department/list',
    component: () => import('@/views/sysmgt/department/list'),
    name:'sysmgt_department_list'
  },
  {
    path: '/sysmgt/notice/list',
    component: () => import('@/views/sysmgt/notice/list'),
    name: 'sysmgt_notice_list'
  },
  {
    path: '/addNotice/:id',
    component:addNotice,
    name: 'addNotice'
  },
  {
    path: '/noticeDetail/:id',
    component:noticeDetail,
    name: 'noticeDetail'
  },
  {
    path: '/sysLogMgt/list',
    component:()=>import('@/views/sysmgt/log/list'),
    name: 'sysLogMgt_List'
  },
  {
    path: '/sysmgt/region/list',
    component:()=>import('@/views/sysmgt/region/list'),
    name: 'sysmgt_region_list'
  }
]
export default sysmgtRouter;
