const epcmgtRouter = [
    {
        path: '/epcmgt/maingroup/list',
        component: () => import('@/views/epcmgt/maingroup/list'),
        name: 'epcmgtmaingrouplist'
    },
    {
        path: '/epcmgt/grouping/list',
        component: () => import('@/views/epcmgt/grouping/list'),
        name: 'epcmgtgroupinglist'
    },
    {
        path: '/epcmgt/parts/list',
        component: () => import('@/views/epcmgt/parts/list'),
        name: 'epcmgtpartslist'
    },
    {
        path: '/epcmgt/replacement/list',
        component: () => import('@/views/epcmgt/replacement/list'),
        name: 'epcmgtreplacementlist'
    },
    {
        path: '/epcmgt/catalog/list',
        component: () => import('@/views/epcmgt/catalog/list'),
        name: 'epcmgtcataloglist'
    },
    {
        path: '/epcmgt/show/list',
        component: () => import('@/views/epcmgt/show/list'),
        name: 'epcmgtshowlist'
    }
]
export default epcmgtRouter;