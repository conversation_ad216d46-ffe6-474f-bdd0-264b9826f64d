
import manualDetails from '@/views/material/manual/details.vue'
import commentstatistics from '@/views/material/comment/statistics.vue'
import maintainAdd from '@/views/material/maintain/addCase.vue'
import maintainDetail from '@/views/material/maintain/detailCase.vue'

const materialRouter = [
    {
        path: '/manualDetails/:id',
        name: 'manualDetails',
        component: manualDetails
    },
    {
        path: '/material/manual/list',
        component: () => import('@/views/material/manual/list'),
        name: 'material_manual_list'
    },
    {
        path: '/commentstatistics',
        name: 'commentstatistics',
        component: commentstatistics
    },
    {
        path: '/material/comment/list',
        component: () => import('@/views/material/comment/list'),
        name: 'material_comment_list'
    },
    {
        path: '/material/service/list',
        component: () => import('@/views/material/service/list'),
        name: 'material_service_list'
    },
    {
        // 维修案例
        path: '/material/maintain/list',
        component: () => import('@/views/material/maintain/list'),
        name: 'material_maintain_list'
    },
    {
        path: '/maintainAdd',
        name: 'maintainAdd',
        component: maintainAdd
    },
    {
        path: '/maintainDetail/:id',
        name: 'maintainDetail',
        component: maintainDetail
    },
    {
      path: '/material/feedback/list',
      component: () => import('@/views/material/feedback/list'),
      name: 'material_feedback_list'
    },
    {
        path: '/material/translate/list',
        component: () => import('@/views/material/translate/list'),
        name: 'material_translate_list'
      }



]
export default materialRouter;