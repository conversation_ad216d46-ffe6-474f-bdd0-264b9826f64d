const sbommgtRouter = [
  {
    path: '/sbom/todo/list',
    component: () => import('@/views/sbommgt/todo/list'),
    name: 'sbom_todo_list'
  },
  {
    path: '/sbom/config/list',
    component: () => import('@/views/sbommgt/config/list'),
    name: 'sbomconfiglist'
  },
  {
    path: '/sbom/sbom/list',
    component: () => import('@/views/sbommgt/sbom/list'),
    name: 'sbomsbomlist'
  }, {
    path: '/sbom/mbom/list',
    component: () => import('@/views/sbommgt/mbom/list'),
    name: 'sbommbomlist'
  }, {
    path: '/sbom/vehicle/list',
    component: () => import('@/views/sbommgt/vehicle/list'),
    name: 'sbomvehiclelist'
  }, {
    path: '/sbom/changes/list',
    component: () => import('@/views/sbommgt/changes/list'),
    name: 'sbomchangeslist'
  }, {
    path: '/sbom/afterSalePart/list',
    component: () => import('@/views/sbommgt/part/list'),
    name: 'sbomafterSalePartlist'
  },{
    path: '/sbom/afterSalePart/part',
    component: () => import('@/views/sbommgt/part/part'),
    name: 'sbomafterSalePartpart'
  },
]
export default sbommgtRouter;
