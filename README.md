# SRM-EPC-Web 项目

## 项目简介
这是一个基于Vue.js的企业级Web应用，主要用于SBOM（Software Bill of Materials）管理和EPC（Electronic Parts Catalog）相关功能。

## 项目设置
```
npm install
```

### 开发环境运行
```
npm run serve
```

### 生产环境构建
```
npm run build
```

### 代码检查和修复
```
npm run lint
```

## 主要功能模块

### SBOM管理模块 (`/src/views/sbommgt/`)

#### 待办事项管理 (`todo/list.vue`)
- **VIN车型配置处理**: 处理VIN码与车型配置的对照关系
- **MBOM配件供货处理**: 管理MBOM配件的供货状态和处理
- **售后专用件**: 处理售后专用件的在线导入和管理

##### 新增功能：电泳件设置
在MBOM配件供货处理表格中新增了"设置电泳件"功能：

**功能描述**：
- 在操作列中提供"设置电泳件"按钮
- 点击按钮打开电泳件设置弹窗
- 自动读取当前行的配件编码和配件名称
- 电泳件名称默认为"配件名称+电泳"

**使用方法**：
1. 在MBOM配件供货处理列表中找到需要设置电泳件的配件
2. 点击该行的"设置电泳件"按钮
3. 在弹出的对话框中：
   - 配件编码和配件名称会自动填充（只读）
   - 电泳件名称会自动设置为"配件名称+电泳"
   - 手动输入电泳件编码
   - 可以修改电泳件名称
4. 点击"确认"保存，或点击"取消"关闭弹窗

**表单验证**：
- 电泳件编码：必填项
- 电泳件名称：必填项

#### 配置管理 (`config/list.vue`)
- **电泳件清单**: 管理电泳件的配置信息
- **拆分件清单**: 管理总成件和拆分件的关系
- **不供货清单**: 管理不供货的配件清单
- **物料代码车型对照清单**: 管理物料代码与车型的对照关系
- **VIN对照清单**: 管理VIN码的对照关系
- **颜色件清单**: 管理颜色件的配置
- **发动机拆分清单**: 管理发动机相关的拆分件

## 技术栈
- Vue.js 2.x
- Element UI
- JavaScript ES6+
- Axios (HTTP客户端)

## 项目结构
```
src/
├── views/
│   └── sbommgt/
│       ├── todo/
│       │   └── list.vue          # 待办事项管理页面
│       └── config/
│           └── list.vue          # 配置管理页面
├── api/
│   └── sbommgt.js                # SBOM相关API接口
└── components/
    └── Pagination.vue            # 分页组件
```

## 开发规范
- 方法名使用英文命名
- 使用多行注释描述方法功能
- 类属性和枚举值使用多行注释
- 行注释放在代码行上方
- 遵循SOLID原则进行代码设计

### 自定义配置
参考 [Configuration Reference](https://cli.vuejs.org/config/)。
